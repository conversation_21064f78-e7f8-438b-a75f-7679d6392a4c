import { Operation } from 'fast-json-patch';

export interface JSONPatch {
  operations: Operation[];
  description: string;
  confidence: number;
}

export interface PlanRequest {
  prompt: string;
  context?: Record<string, any>;
  previousAttempts?: JSONPatch[];
}

export interface CritiqueRequest {
  patch: JSONPatch;
  originalPrompt: string;
  context?: Record<string, any>;
}

export interface CritiqueResult {
  score: number; // 0-1 scale
  feedback: string;
  suggestions: string[];
  isAcceptable: boolean;
}

// Provider Types
export type ProviderType = 'openai' | 'vertex-ai' | 'anthropic';

export interface ProviderConfig {
  type: ProviderType;
  apiKey?: string;
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt?: string;
  tools?: any[];
  // Provider-specific settings
  openai?: {
    baseURL?: string;
    organization?: string;
    project?: string;
  };
  vertexAI?: {
    projectId: string;
    location: string;
    credentials?: any;
  };
  anthropic?: {
    baseURL?: string;
    version?: string;
  };
}

export interface TokenUsage {
  inputTokens: number;
  outputTokens: number;
  totalTokens: number;
  cost: number;
  provider: ProviderType;
  model: string;
  timestamp: Date;
}

export interface ProviderResponse<T> {
  data: T;
  usage: TokenUsage;
  requestId?: string;
  latency: number;
}

export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  jitter: boolean;
}

export interface CostGuard {
  maxCostPerLoop: number;
  currentCost: number;
  enabled: boolean;
}

// Legacy AgentConfig for backward compatibility
export interface AgentConfig {
  provider?: ProviderConfig;
  // Legacy fields - deprecated
  model?: 'gemini-2.5' | 'gpt-4-turbo';
  temperature?: number;
  maxTokens?: number;
}

export interface LoopResult {
  finalPatch: JSONPatch;
  score: number;
  iterations: number;
  logs: Array<{
    iteration: number;
    plan: string;
    critique: string;
    score: number;
    patch: JSONPatch;
  }>;
  prUrl?: string;
  totalCost?: number;
  totalTokens?: number;
}
