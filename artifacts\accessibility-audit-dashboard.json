{"page": "dashboard", "url": "http://localhost:8080/dashboard", "timestamp": "2025-06-19T19:29:45.009Z", "score": 0, "status": "FAILED", "target_score": 97, "summary": {"violations": 4, "incomplete": 3, "passes": 41, "total": 48}, "violations": [{"id": "aria-valid-attr-value", "impact": "critical", "description": "Ensures all ARIA attributes have valid values", "help": "ARIA attributes must conform to valid values", "helpUrl": "https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr-value?application=axeAPI", "nodes": 2, "tags": ["cat.aria", "wcag2a", "wcag412", "EN-301-549", "EN-*******"], "nodeDetails": [{"target": ["div[data-lov-id=\"src\\\\pages\\\\Dashboard.tsx:659:10\"]"], "html": "<div data-lov-id=\"src\\pages\\Dashboard.tsx:659:10\" data-lov-name=\"ResizableHandle\" data-component-path=\"src\\pages\\Dashboard.tsx\" data-component-line=\"659\" data-component-file=\"Dashboard.tsx\" data-compo"}, {"target": ["div[data-lov-id=\"src\\\\pages\\\\Dashboard.tsx:699:10\"]"], "html": "<div data-lov-id=\"src\\pages\\Dashboard.tsx:699:10\" data-lov-name=\"ResizableHandle\" data-component-path=\"src\\pages\\Dashboard.tsx\" data-component-line=\"699\" data-component-file=\"Dashboard.tsx\" data-compo"}]}, {"id": "color-contrast", "impact": "serious", "description": "Ensures the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds", "help": "Elements must meet minimum color contrast ratio thresholds", "helpUrl": "https://dequeuniversity.com/rules/axe/4.8/color-contrast?application=axeAPI", "nodes": 19, "tags": ["cat.color", "wcag2aa", "wcag143", "TTv5", "TT13.c", "EN-301-549", "EN-*******", "ACT"], "nodeDetails": [{"target": ["button[data-lov-id=\"src\\\\pages\\\\Dashboard.tsx:409:16\"]"], "html": "<button data-lov-id=\"src\\pages\\Dashboard.tsx:409:16\" data-lov-name=\"Button\" data-component-path=\"src\\pages\\Dashboard.tsx\" data-component-line=\"409\" data-component-file=\"Dashboard.tsx\" data-component-n"}, {"target": [".hover\\:bg-secondary\\/80"], "html": "<div data-lov-id=\"src\\pages\\Dashboard.tsx:420:18\" data-lov-name=\"Badge\" data-component-path=\"src\\pages\\Dashboard.tsx\" data-component-line=\"420\" data-component-file=\"Dashboard.tsx\" data-component-name="}, {"target": ["span[data-lov-id=\"src\\\\pages\\\\Dashboard.tsx:425:20\"]"], "html": "<span data-lov-id=\"src\\pages\\Dashboard.tsx:425:20\" data-lov-name=\"span\" data-component-path=\"src\\pages\\Dashboard.tsx\" data-component-line=\"425\" data-component-file=\"Dashboard.tsx\" data-component-name="}]}, {"id": "focus-order-semantics", "impact": "minor", "description": "Ensures elements in the focus order have a role appropriate for interactive content", "help": "Elements in the focus order should have an appropriate role", "helpUrl": "https://dequeuniversity.com/rules/axe/4.8/focus-order-semantics?application=axeAPI", "nodes": 2, "tags": ["cat.keyboard", "best-practice", "experimental"], "nodeDetails": [{"target": ["div[data-lov-id=\"src\\\\pages\\\\Dashboard.tsx:659:10\"]"], "html": "<div data-lov-id=\"src\\pages\\Dashboard.tsx:659:10\" data-lov-name=\"ResizableHandle\" data-component-path=\"src\\pages\\Dashboard.tsx\" data-component-line=\"659\" data-component-file=\"Dashboard.tsx\" data-compo"}, {"target": ["div[data-lov-id=\"src\\\\pages\\\\Dashboard.tsx:699:10\"]"], "html": "<div data-lov-id=\"src\\pages\\Dashboard.tsx:699:10\" data-lov-name=\"ResizableHandle\" data-component-path=\"src\\pages\\Dashboard.tsx\" data-component-line=\"699\" data-component-file=\"Dashboard.tsx\" data-compo"}]}, {"id": "region", "impact": "moderate", "description": "Ensures all page content is contained by landmarks", "help": "All page content should be contained by landmarks", "helpUrl": "https://dequeuniversity.com/rules/axe/4.8/region?application=axeAPI", "nodes": 4, "tags": ["cat.keyboard", "best-practice"], "nodeDetails": [{"target": [".tracking-tight"], "html": "<h3 data-lov-id=\"src\\components\\ConfigurationStatus.tsx:90:12\" data-lov-name=\"CardTitle\" data-component-path=\"src\\components\\ConfigurationStatus.tsx\" data-component-line=\"90\" data-component-file=\"Conf"}, {"target": ["p[data-lov-name=\"CardDescription\"]"], "html": "<p data-lov-id=\"src\\components\\ConfigurationStatus.tsx:122:8\" data-lov-name=\"CardDescription\" data-component-path=\"src\\components\\ConfigurationStatus.tsx\" data-component-line=\"122\" data-component-file"}, {"target": [".hover\\:bg-primary\\/80 > .ml-1[data-component-line=\"61\"][data-lov-name=\"span\"]"], "html": "<span data-lov-id=\"src\\components\\ConfigurationStatus.tsx:61:8\" data-lov-name=\"span\" data-component-path=\"src\\components\\ConfigurationStatus.tsx\" data-component-line=\"61\" data-component-file=\"Configur"}]}], "incomplete": [{"id": "aria-valid-attr-value", "impact": "critical", "description": "Ensures all ARIA attributes have valid values", "help": "ARIA attributes must conform to valid values", "nodes": 1}, {"id": "color-contrast", "impact": "serious", "description": "Ensures the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds", "help": "Elements must meet minimum color contrast ratio thresholds", "nodes": 16}, {"id": "heading-order", "impact": "moderate", "description": "Ensures the order of headings is semantically correct", "help": "Heading levels should only increase by one", "nodes": 6}], "recommendations": ["Fix ResizableHandle components with invalid ARIA attributes", "Improve color contrast for 19 elements to meet WCAG AA standards", "Add proper roles to focusable ResizableHandle elements", "Wrap page content in proper landmark regions (main, nav, aside)", "Fix heading hierarchy to follow semantic order", "Test with screen readers to ensure proper navigation"]}