import * as React from "react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { VStack, HStack } from "@/components/ui/layout"
import { Typography } from "@/components/ui/typography"
import { ErrorIcon, RefreshIcon, BugIcon } from "@/components/ui/icon"

// Error boundary state
interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
  errorId: string
}

// Error boundary props
interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<ErrorFallbackProps>
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
  isolate?: boolean
  level?: 'page' | 'section' | 'component'
  resetKeys?: Array<string | number>
  resetOnPropsChange?: boolean
}

// Error fallback props
export interface ErrorFallbackProps {
  error: Error
  errorInfo: React.ErrorInfo
  resetError: () => void
  errorId: string
  level: 'page' | 'section' | 'component'
}

// Main error boundary component
export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private resetTimeoutId: number | null = null

  constructor(props: ErrorBoundaryProps) {
    super(props)
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    }
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
      errorId: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({ errorInfo })
    
    // Log error
    this.logError(error, errorInfo)
    
    // Call custom error handler
    this.props.onError?.(error, errorInfo)
    
    // Report to error tracking service
    this.reportError(error, errorInfo)
  }

  componentDidUpdate(prevProps: ErrorBoundaryProps) {
    const { resetKeys, resetOnPropsChange } = this.props
    const { hasError } = this.state

    if (hasError && prevProps.resetKeys !== resetKeys) {
      if (resetKeys?.some((key, idx) => prevProps.resetKeys?.[idx] !== key)) {
        this.resetError()
      }
    }

    if (hasError && resetOnPropsChange && prevProps.children !== this.props.children) {
      this.resetError()
    }
  }

  componentWillUnmount() {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId)
    }
  }

  resetError = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    })
  }

  logError(error: Error, errorInfo: React.ErrorInfo) {
    const errorData = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      level: this.props.level || 'component'
    }

    console.group('🚨 Error Boundary Caught Error')
    console.error('Error:', error)
    console.error('Error Info:', errorInfo)
    console.error('Error Data:', errorData)
    console.groupEnd()
  }

  reportError(error: Error, errorInfo: React.ErrorInfo) {
    // Report to error tracking service (Sentry, LogRocket, etc.)
    if (typeof window !== 'undefined' && 'gtag' in window) {
      (window as any).gtag('event', 'exception', {
        description: error.message,
        fatal: this.props.level === 'page'
      })
    }

    // Could also send to your own error reporting endpoint
    if (process.env.NODE_ENV === 'production') {
      fetch('/api/errors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: error.message,
          stack: error.stack,
          componentStack: errorInfo.componentStack,
          level: this.props.level,
          timestamp: new Date().toISOString()
        })
      }).catch(() => {
        // Silently fail if error reporting fails
      })
    }
  }

  render() {
    const { hasError, error, errorInfo, errorId } = this.state
    const { children, fallback: Fallback, level = 'component' } = this.props

    if (hasError && error && errorInfo) {
      if (Fallback) {
        return (
          <Fallback
            error={error}
            errorInfo={errorInfo}
            resetError={this.resetError}
            errorId={errorId}
            level={level}
          />
        )
      }

      return (
        <DefaultErrorFallback
          error={error}
          errorInfo={errorInfo}
          resetError={this.resetError}
          errorId={errorId}
          level={level}
        />
      )
    }

    return children
  }
}

// Default error fallback component
function DefaultErrorFallback({ 
  error, 
  errorInfo, 
  resetError, 
  errorId, 
  level 
}: ErrorFallbackProps) {
  const [showDetails, setShowDetails] = React.useState(false)
  const [isRetrying, setIsRetrying] = React.useState(false)

  const handleRetry = async () => {
    setIsRetrying(true)
    
    // Add a small delay to show loading state
    await new Promise(resolve => setTimeout(resolve, 500))
    
    resetError()
    setIsRetrying(false)
  }

  const handleReload = () => {
    window.location.reload()
  }

  const levelConfig = {
    page: {
      title: 'Page Error',
      description: 'Something went wrong with this page.',
      showReload: true,
      className: 'min-h-screen'
    },
    section: {
      title: 'Section Error',
      description: 'This section encountered an error.',
      showReload: false,
      className: 'min-h-[200px]'
    },
    component: {
      title: 'Component Error',
      description: 'This component failed to render.',
      showReload: false,
      className: 'min-h-[100px]'
    }
  }

  const config = levelConfig[level]

  return (
    <div className={cn(
      "flex items-center justify-center p-6 border border-destructive/20 rounded-lg bg-destructive/5",
      config.className
    )}>
      <VStack spacing="md" align="center" className="max-w-md text-center">
        <div className="p-3 rounded-full bg-destructive/10">
          <ErrorIcon className="w-8 h-8 text-destructive" />
        </div>

        <VStack spacing="sm" align="center">
          <Typography variant="h5" className="text-foreground">
            {config.title}
          </Typography>
          <Typography variant="body-sm" className="text-muted-foreground">
            {config.description}
          </Typography>
        </VStack>

        <HStack spacing="sm">
          <Button
            onClick={handleRetry}
            disabled={isRetrying}
            className="min-w-[100px]"
          >
            {isRetrying ? (
              <>
                <RefreshIcon className="w-4 h-4 mr-2 animate-spin" />
                Retrying...
              </>
            ) : (
              <>
                <RefreshIcon className="w-4 h-4 mr-2" />
                Try Again
              </>
            )}
          </Button>

          {config.showReload && (
            <Button variant="outline" onClick={handleReload}>
              Reload Page
            </Button>
          )}
        </HStack>

        {process.env.NODE_ENV === 'development' && (
          <VStack spacing="sm" className="w-full">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
              className="text-xs"
            >
              <BugIcon className="w-3 h-3 mr-1" />
              {showDetails ? 'Hide' : 'Show'} Error Details
            </Button>

            {showDetails && (
              <div className="w-full p-3 bg-muted rounded text-left">
                <VStack spacing="xs">
                  <Typography variant="caption" className="font-mono text-destructive">
                    Error ID: {errorId}
                  </Typography>
                  <Typography variant="caption" className="font-mono">
                    {error.message}
                  </Typography>
                  {error.stack && (
                    <pre className="text-xs overflow-auto max-h-32 whitespace-pre-wrap">
                      {error.stack}
                    </pre>
                  )}
                </VStack>
              </div>
            )}
          </VStack>
        )}
      </VStack>
    </div>
  )
}

// Specialized error boundaries for different contexts
export function PageErrorBoundary({ children, ...props }: Omit<ErrorBoundaryProps, 'level'>) {
  return (
    <ErrorBoundary level="page" {...props}>
      {children}
    </ErrorBoundary>
  )
}

export function SectionErrorBoundary({ children, ...props }: Omit<ErrorBoundaryProps, 'level'>) {
  return (
    <ErrorBoundary level="section" {...props}>
      {children}
    </ErrorBoundary>
  )
}

export function ComponentErrorBoundary({ children, ...props }: Omit<ErrorBoundaryProps, 'level'>) {
  return (
    <ErrorBoundary level="component" {...props}>
      {children}
    </ErrorBoundary>
  )
}

// HOC for wrapping components with error boundaries
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) {
  const WrappedComponent = React.forwardRef<any, P>((props, ref) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} ref={ref} />
    </ErrorBoundary>
  ))

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

// Hook for error handling in functional components
export function useErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null)

  const resetError = React.useCallback(() => {
    setError(null)
  }, [])

  const captureError = React.useCallback((error: Error) => {
    setError(error)
  }, [])

  React.useEffect(() => {
    if (error) {
      throw error
    }
  }, [error])

  return { captureError, resetError }
}

// Global error handler for unhandled promise rejections
export function setupGlobalErrorHandling() {
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    
    // Report to error tracking
    if (typeof window !== 'undefined' && 'gtag' in window) {
      (window as any).gtag('event', 'exception', {
        description: `Unhandled Promise Rejection: ${event.reason}`,
        fatal: false
      })
    }
  })

  // Handle global errors
  window.addEventListener('error', (event) => {
    console.error('Global error:', event.error)
    
    // Report to error tracking
    if (typeof window !== 'undefined' && 'gtag' in window) {
      (window as any).gtag('event', 'exception', {
        description: `Global Error: ${event.error?.message || event.message}`,
        fatal: false
      })
    }
  })
}
