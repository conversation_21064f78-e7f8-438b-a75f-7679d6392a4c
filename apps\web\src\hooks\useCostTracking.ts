import { useState, useEffect, useCallback } from 'react';
import { CostTracker, CostEntry, BudgetConfig, UsageSummary, CostAlert } from '@/lib/cost/costTracker';

// Global cost tracker instance
let costTrackerInstance: CostTracker | null = null;

const getCostTrackerInstance = () => {
  if (!costTrackerInstance) {
    costTrackerInstance = new CostTracker({
      dailyLimit: 10.0,
      monthlyLimit: 100.0,
      perTransformationLimit: 3.0,
      alertThresholds: {
        daily: 0.8,
        monthly: 0.8,
        perTransformation: 0.9,
      },
      enableAlerts: true,
    });
  }
  return costTrackerInstance;
};

export const useCostTracking = () => {
  const [costTracker] = useState(() => getCostTrackerInstance());
  const [usageSummary, setUsageSummary] = useState<UsageSummary>(costTracker.getUsageSummary());
  const [alerts, setAlerts] = useState<CostAlert[]>(costTracker.getActiveAlerts());
  const [isLoading, setIsLoading] = useState(false);

  // Update usage summary and alerts
  const updateState = useCallback(() => {
    setUsageSummary(costTracker.getUsageSummary());
    setAlerts(costTracker.getActiveAlerts());
  }, [costTracker]);

  // Refresh data periodically
  useEffect(() => {
    const interval = setInterval(updateState, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, [updateState]);

  // Record a cost entry
  const recordCost = useCallback(async (entry: Omit<CostEntry, 'id' | 'timestamp'>) => {
    setIsLoading(true);
    try {
      const entryId = costTracker.recordCost(entry);
      updateState();
      return entryId;
    } finally {
      setIsLoading(false);
    }
  }, [costTracker, updateState]);

  // Check if operation can be afforded
  const canAffordOperation = useCallback((estimatedCost: number) => {
    return costTracker.canAffordOperation(estimatedCost);
  }, [costTracker]);

  // Get cost breakdown by provider
  const getCostBreakdownByProvider = useCallback(() => {
    return costTracker.getCostBreakdownByProvider();
  }, [costTracker]);

  // Get cost breakdown by model
  const getCostBreakdownByModel = useCallback(() => {
    return costTracker.getCostBreakdownByModel();
  }, [costTracker]);

  // Get cost by date range
  const getCostByDateRange = useCallback((startDate: Date, endDate: Date) => {
    return costTracker.getCostByDateRange(startDate, endDate);
  }, [costTracker]);

  // Get cost by transformation
  const getCostByTransformation = useCallback((transformationId: string) => {
    return costTracker.getCostByTransformation(transformationId);
  }, [costTracker]);

  // Acknowledge alert
  const acknowledgeAlert = useCallback((alertId: string) => {
    const success = costTracker.acknowledgeAlert(alertId);
    if (success) {
      updateState();
    }
    return success;
  }, [costTracker, updateState]);

  // Clear old alerts
  const clearOldAlerts = useCallback((olderThanDays: number = 7) => {
    const cleared = costTracker.clearOldAlerts(olderThanDays);
    updateState();
    return cleared;
  }, [costTracker, updateState]);

  // Update budget configuration
  const updateBudgetConfig = useCallback((config: Partial<BudgetConfig>) => {
    costTracker.updateBudgetConfig(config);
    updateState();
  }, [costTracker, updateState]);

  // Export cost data
  const exportCostData = useCallback(() => {
    return costTracker.exportCostData();
  }, [costTracker]);

  // Get budget status
  const getBudgetStatus = useCallback(() => {
    const summary = costTracker.getUsageSummary();
    const budgetConfig = (costTracker as any).budgetConfig; // Access private property for demo
    
    return {
      daily: {
        used: summary.today.cost,
        limit: budgetConfig?.dailyLimit || 10,
        percentage: (summary.today.cost / (budgetConfig?.dailyLimit || 10)) * 100,
        remaining: (budgetConfig?.dailyLimit || 10) - summary.today.cost,
      },
      monthly: {
        used: summary.thisMonth.cost,
        limit: budgetConfig?.monthlyLimit || 100,
        percentage: (summary.thisMonth.cost / (budgetConfig?.monthlyLimit || 100)) * 100,
        remaining: (budgetConfig?.monthlyLimit || 100) - summary.thisMonth.cost,
      },
    };
  }, [costTracker]);

  // Get cost trends (last 7 days)
  const getCostTrends = useCallback(() => {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 7);
    
    const entries = costTracker.getCostByDateRange(startDate, endDate);
    
    // Group by date
    const dailyCosts: Record<string, number> = {};
    entries.forEach(entry => {
      const dateKey = entry.timestamp.toISOString().split('T')[0];
      dailyCosts[dateKey] = (dailyCosts[dateKey] || 0) + entry.cost;
    });
    
    // Fill in missing dates with 0
    const trends = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateKey = date.toISOString().split('T')[0];
      trends.push({
        date: dateKey,
        cost: dailyCosts[dateKey] || 0,
      });
    }
    
    return trends;
  }, [costTracker]);

  // Calculate estimated monthly cost based on current usage
  const getEstimatedMonthlyCost = useCallback(() => {
    const summary = costTracker.getUsageSummary();
    const today = new Date();
    const daysInMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0).getDate();
    const dayOfMonth = today.getDate();
    
    if (dayOfMonth === 0) return 0;
    
    const dailyAverage = summary.thisMonth.cost / dayOfMonth;
    return dailyAverage * daysInMonth;
  }, [costTracker]);

  return {
    // State
    usageSummary,
    alerts,
    isLoading,
    
    // Actions
    recordCost,
    canAffordOperation,
    acknowledgeAlert,
    clearOldAlerts,
    updateBudgetConfig,
    
    // Queries
    getCostBreakdownByProvider,
    getCostBreakdownByModel,
    getCostByDateRange,
    getCostByTransformation,
    getBudgetStatus,
    getCostTrends,
    getEstimatedMonthlyCost,
    exportCostData,
    
    // Tracker instance (for advanced usage)
    costTracker,
  };
};

// Hook for budget alerts
export const useBudgetAlerts = () => {
  const { alerts, acknowledgeAlert, clearOldAlerts } = useCostTracking();
  
  const criticalAlerts = alerts.filter(alert => 
    alert.type === 'budget_exceeded' || alert.current >= 0.9
  );
  
  const warningAlerts = alerts.filter(alert => 
    alert.current >= 0.8 && alert.current < 0.9
  );
  
  const infoAlerts = alerts.filter(alert => 
    alert.current < 0.8
  );

  return {
    alerts,
    criticalAlerts,
    warningAlerts,
    infoAlerts,
    acknowledgeAlert,
    clearOldAlerts,
    hasUnacknowledgedAlerts: alerts.length > 0,
  };
};

// Hook for cost estimation
export const useCostEstimation = () => {
  const { canAffordOperation } = useCostTracking();
  
  const estimateTransformationCost = useCallback((
    codeLength: number,
    promptLength: number,
    model: string = 'gpt-4-turbo'
  ) => {
    // Rough estimation based on token count
    // Average: 4 characters per token
    const estimatedTokens = Math.ceil((codeLength + promptLength) / 4);
    
    // Model pricing (per 1K tokens)
    const pricing: Record<string, { input: number; output: number }> = {
      'gpt-4-turbo': { input: 0.01, output: 0.03 },
      'gpt-4': { input: 0.03, output: 0.06 },
      'gpt-3.5-turbo': { input: 0.002, output: 0.002 },
      'claude-3-opus': { input: 0.015, output: 0.075 },
      'claude-3-sonnet': { input: 0.003, output: 0.015 },
      'claude-3-haiku': { input: 0.00025, output: 0.00125 },
    };
    
    const modelPricing = pricing[model] || pricing['gpt-4-turbo'];
    
    // Estimate input/output tokens (rough 50/50 split)
    const inputTokens = estimatedTokens * 0.6;
    const outputTokens = estimatedTokens * 0.4;
    
    const inputCost = (inputTokens / 1000) * modelPricing.input;
    const outputCost = (outputTokens / 1000) * modelPricing.output;
    
    return {
      estimatedCost: inputCost + outputCost,
      inputTokens: Math.ceil(inputTokens),
      outputTokens: Math.ceil(outputTokens),
      totalTokens: Math.ceil(estimatedTokens),
      canAfford: canAffordOperation(inputCost + outputCost),
    };
  }, [canAffordOperation]);

  return {
    estimateTransformationCost,
  };
};
