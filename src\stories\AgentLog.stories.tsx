import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { AgentLog } from '../components/AgentLog';

// Mock data for stories
const mockLogs = [
  {
    id: 1,
    agent: 'planner' as const,
    message: 'Starting code analysis for React component optimization...',
    timestamp: new Date('2024-01-01T10:00:00Z'),
    level: 'info' as const,
  },
  {
    id: 2,
    agent: 'critic' as const,
    message: 'Evaluating generated patch for performance improvements...',
    timestamp: new Date('2024-01-01T10:01:00Z'),
    level: 'success' as const,
  },
  {
    id: 3,
    agent: 'system' as const,
    message: 'Memory usage optimized, reducing bundle size by 15%',
    timestamp: new Date('2024-01-01T10:02:00Z'),
    level: 'success' as const,
  },
  {
    id: 4,
    agent: 'planner' as const,
    message: 'Applying TypeScript strict mode configurations...',
    timestamp: new Date('2024-01-01T10:03:00Z'),
    level: 'info' as const,
  },
  {
    id: 5,
    agent: 'critic' as const,
    message: 'Warning: Potential breaking change detected in API interface',
    timestamp: new Date('2024-01-01T10:04:00Z'),
    level: 'warning' as const,
  },
  {
    id: 6,
    agent: 'system' as const,
    message: 'Error: Failed to connect to external API endpoint',
    timestamp: new Date('2024-01-01T10:05:00Z'),
    level: 'error' as const,
  },
];

const meta: Meta<typeof AgentLog> = {
  title: 'Components/AgentLog',
  component: AgentLog,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: `
# Agent Log Component

A real-time logging interface for the dual-agent system that displays:

- **Planner Agent**: Code generation and planning activities
- **Critic Agent**: Code evaluation and scoring feedback  
- **System**: Infrastructure and operational messages

## Features

- 🔄 **Real-time Updates**: Live streaming of agent activities
- 🎨 **Agent Differentiation**: Color-coded badges for each agent type
- 📊 **Log Levels**: Support for info, success, warning, and error levels
- ♿ **Accessibility**: Screen reader friendly with ARIA live regions
- 🎯 **Interactive Controls**: Pause/resume, clear, and scroll controls
- 📱 **Responsive**: Optimized for all device sizes

## Agent Types

- **Planner**: Generates code patches and transformations
- **Critic**: Evaluates and scores generated code
- **System**: Infrastructure, errors, and operational status

## Log Levels

- **Info**: General information and progress updates
- **Success**: Successful operations and completions
- **Warning**: Potential issues that need attention
- **Error**: Failures and critical problems
        `,
      },
    },
    a11y: {
      config: {
        rules: [
          {
            id: 'color-contrast',
            enabled: true,
          },
          {
            id: 'aria-live-region',
            enabled: true,
          },
        ],
      },
    },
  },
  argTypes: {
    logs: {
      control: 'object',
      description: 'Array of log entries to display',
    },
    enableRealTime: {
      control: 'boolean',
      description: 'Enable real-time log streaming',
    },
    maxLogs: {
      control: { type: 'number', min: 10, max: 500, step: 10 },
      description: 'Maximum number of logs to display',
    },
    onClear: {
      action: 'clear-logs',
      description: 'Callback when clear button is clicked',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  name: 'Default Log View',
  args: {
    logs: mockLogs,
    enableRealTime: false,
    maxLogs: 100,
  },
  parameters: {
    docs: {
      description: {
        story: 'Default agent log view with sample log entries from all agent types.',
      },
    },
  },
};

export const EmptyState: Story = {
  name: 'Empty State',
  args: {
    logs: [],
    enableRealTime: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Empty state when no logs are available.',
      },
    },
  },
};

export const RealTimeEnabled: Story = {
  name: 'Real-time Streaming',
  args: {
    logs: mockLogs.slice(0, 3),
    enableRealTime: true,
    maxLogs: 50,
  },
  parameters: {
    docs: {
      description: {
        story: 'Agent log with real-time streaming enabled, showing connection status and controls.',
      },
    },
  },
};

export const PlannerOnly: Story = {
  name: 'Planner Agent Only',
  args: {
    logs: mockLogs.filter(log => log.agent === 'planner'),
    enableRealTime: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Log view showing only planner agent activities.',
      },
    },
  },
};

export const CriticOnly: Story = {
  name: 'Critic Agent Only',
  args: {
    logs: mockLogs.filter(log => log.agent === 'critic'),
    enableRealTime: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Log view showing only critic agent activities.',
      },
    },
  },
};

export const SystemOnly: Story = {
  name: 'System Logs Only',
  args: {
    logs: mockLogs.filter(log => log.agent === 'system'),
    enableRealTime: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Log view showing only system messages and errors.',
      },
    },
  },
};

export const ErrorsAndWarnings: Story = {
  name: 'Errors and Warnings',
  args: {
    logs: mockLogs.filter(log => log.level === 'error' || log.level === 'warning'),
    enableRealTime: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Log view filtered to show only errors and warnings.',
      },
    },
  },
};

export const WithClearFunction: Story = {
  name: 'With Clear Function',
  args: {
    logs: mockLogs,
    enableRealTime: true,
    onClear: () => console.log('Clear logs clicked'),
  },
  parameters: {
    docs: {
      description: {
        story: 'Agent log with clear functionality enabled.',
      },
    },
  },
};

export const LimitedLogs: Story = {
  name: 'Limited Log Count',
  args: {
    logs: mockLogs,
    enableRealTime: false,
    maxLogs: 3,
  },
  parameters: {
    docs: {
      description: {
        story: 'Agent log with limited maximum log count.',
      },
    },
  },
};

export const MobileView: Story = {
  name: 'Mobile View',
  args: {
    logs: mockLogs,
    enableRealTime: true,
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
    docs: {
      description: {
        story: 'Agent log optimized for mobile devices.',
      },
    },
  },
};

export const DarkTheme: Story = {
  name: 'Dark Theme',
  args: {
    logs: mockLogs,
    enableRealTime: true,
  },
  parameters: {
    backgrounds: {
      default: 'dark',
    },
    docs: {
      description: {
        story: 'Agent log in dark theme mode.',
      },
    },
  },
};

export const HighContrast: Story = {
  name: 'High Contrast',
  args: {
    logs: mockLogs,
    enableRealTime: false,
  },
  parameters: {
    a11y: {
      config: {
        rules: [
          {
            id: 'color-contrast',
            enabled: true,
            options: {
              level: 'AAA',
            },
          },
        ],
      },
    },
    docs: {
      description: {
        story: 'Agent log with high contrast for improved accessibility.',
      },
    },
  },
};

export const LongMessages: Story = {
  name: 'Long Messages',
  args: {
    logs: [
      {
        id: 1,
        agent: 'planner' as const,
        message: 'This is a very long log message that demonstrates how the component handles text wrapping and layout when dealing with extensive content that might span multiple lines and contain detailed information about the code transformation process.',
        timestamp: new Date(),
        level: 'info' as const,
      },
      {
        id: 2,
        agent: 'critic' as const,
        message: 'Another extremely long message that shows how the agent log component maintains readability and proper spacing even when dealing with verbose output from the AI agents during complex code analysis and evaluation procedures.',
        timestamp: new Date(),
        level: 'warning' as const,
      },
    ],
    enableRealTime: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Agent log handling long messages with proper text wrapping.',
      },
    },
  },
};
