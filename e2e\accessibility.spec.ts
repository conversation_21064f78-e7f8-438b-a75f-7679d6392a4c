import { test, expect } from '@playwright/test';

/**
 * Comprehensive Accessibility Tests
 * Tests WCAG 2.1 AA compliance across all pages
 * Target: ≥97 accessibility score
 */

test.describe('Accessibility Compliance', () => {
  test.beforeEach(async ({ page }) => {
    // Inject axe-core for accessibility testing
    await page.addInitScript(() => {
      const script = document.createElement('script');
      script.src = 'https://unpkg.com/axe-core@4.8.2/axe.min.js';
      document.head.appendChild(script);
    });
  });

  test('Dashboard page should meet WCAG 2.1 AA standards', async ({ page }) => {
    await page.goto('/dashboard');
    await expect(page.getByRole('main')).toBeVisible();

    // Run axe accessibility audit
    const accessibilityResults = await page.evaluate(async () => {
      // Wait for axe to load
      while (typeof axe === 'undefined') {
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      const results = await axe.run(document, {
        tags: ['wcag2a', 'wcag2aa', 'wcag21aa'],
        rules: {
          'color-contrast': { enabled: true },
          'keyboard-navigation': { enabled: true },
          'aria-labels': { enabled: true },
          'landmark-one-main': { enabled: true },
          'page-has-heading-one': { enabled: true },
          'region': { enabled: true }
        }
      });

      return {
        violations: results.violations.length,
        incomplete: results.incomplete.length,
        passes: results.passes.length,
        violationDetails: results.violations.map(v => ({
          id: v.id,
          impact: v.impact,
          description: v.description,
          nodes: v.nodes.length
        }))
      };
    });

    // Calculate accessibility score
    const totalTests = accessibilityResults.violations + accessibilityResults.incomplete + accessibilityResults.passes;
    const violationPenalty = accessibilityResults.violations * 10;
    const incompletePenalty = accessibilityResults.incomplete * 2;
    const score = totalTests > 0 ? Math.max(0, Math.min(100, ((accessibilityResults.passes - violationPenalty - incompletePenalty) / totalTests) * 100)) : 0;

    console.log(`Dashboard Accessibility Score: ${Math.round(score)}/100`);
    console.log(`Violations: ${accessibilityResults.violations}, Incomplete: ${accessibilityResults.incomplete}, Passes: ${accessibilityResults.passes}`);

    if (accessibilityResults.violations > 0) {
      console.log('Accessibility violations:', accessibilityResults.violationDetails);
    }

    // Expect score to be ≥97
    expect(score).toBeGreaterThanOrEqual(97);
    expect(accessibilityResults.violations).toBe(0);
  });

  test('Settings page should meet WCAG 2.1 AA standards', async ({ page }) => {
    await page.goto('/settings');
    await expect(page.getByRole('heading', { name: /settings/i })).toBeVisible();

    // Run axe accessibility audit
    const accessibilityResults = await page.evaluate(async () => {
      // Wait for axe to load
      while (typeof axe === 'undefined') {
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      const results = await axe.run(document, {
        tags: ['wcag2a', 'wcag2aa', 'wcag21aa'],
        rules: {
          'color-contrast': { enabled: true },
          'keyboard-navigation': { enabled: true },
          'aria-labels': { enabled: true },
          'landmark-one-main': { enabled: true },
          'page-has-heading-one': { enabled: true },
          'region': { enabled: true },
          'label': { enabled: true },
          'button-name': { enabled: true }
        }
      });

      return {
        violations: results.violations.length,
        incomplete: results.incomplete.length,
        passes: results.passes.length,
        violationDetails: results.violations.map(v => ({
          id: v.id,
          impact: v.impact,
          description: v.description,
          nodes: v.nodes.length
        }))
      };
    });

    // Calculate accessibility score
    const totalTests = accessibilityResults.violations + accessibilityResults.incomplete + accessibilityResults.passes;
    const violationPenalty = accessibilityResults.violations * 10;
    const incompletePenalty = accessibilityResults.incomplete * 2;
    const score = totalTests > 0 ? Math.max(0, Math.min(100, ((accessibilityResults.passes - violationPenalty - incompletePenalty) / totalTests) * 100)) : 0;

    console.log(`Settings Accessibility Score: ${Math.round(score)}/100`);
    console.log(`Violations: ${accessibilityResults.violations}, Incomplete: ${accessibilityResults.incomplete}, Passes: ${accessibilityResults.passes}`);

    if (accessibilityResults.violations > 0) {
      console.log('Accessibility violations:', accessibilityResults.violationDetails);
    }

    // Expect score to be ≥97
    expect(score).toBeGreaterThanOrEqual(97);
    expect(accessibilityResults.violations).toBe(0);
  });

  test('All pages should have proper semantic structure', async ({ page }) => {
    const pages = ['/dashboard', '/settings'];

    for (const pagePath of pages) {
      await page.goto(pagePath);
      
      // Check for required landmarks
      await expect(page.getByRole('banner')).toBeVisible(); // header
      await expect(page.getByRole('main')).toBeVisible(); // main content
      
      // Check for proper heading hierarchy
      const h1 = page.getByRole('heading', { level: 1 });
      await expect(h1).toBeVisible();
      
      // Check that all interactive elements are keyboard accessible
      const buttons = page.getByRole('button');
      const buttonCount = await buttons.count();
      
      for (let i = 0; i < Math.min(buttonCount, 10); i++) { // Test first 10 buttons
        const button = buttons.nth(i);
        if (await button.isVisible()) {
          await expect(button).toBeFocusable();
        }
      }
    }
  });

  test('Color contrast should meet WCAG AA standards', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Check color contrast for key elements
    const contrastResults = await page.evaluate(() => {
      const elements = document.querySelectorAll('button, a, .text-primary, .text-secondary');
      const results = [];
      
      for (const element of elements) {
        const styles = window.getComputedStyle(element);
        const color = styles.color;
        const backgroundColor = styles.backgroundColor;
        
        if (color && backgroundColor && color !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'rgba(0, 0, 0, 0)') {
          results.push({
            element: element.tagName + (element.className ? '.' + element.className.split(' ')[0] : ''),
            color,
            backgroundColor
          });
        }
      }
      
      return results;
    });
    
    // At minimum, we should have some elements with proper contrast
    expect(contrastResults.length).toBeGreaterThan(0);
  });

  test('Keyboard navigation should work properly', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Test tab navigation
    await page.keyboard.press('Tab');
    
    // Should focus on first interactive element
    const focusedElement = page.locator(':focus');
    await expect(focusedElement).toBeVisible();
    
    // Test that we can navigate through multiple elements
    for (let i = 0; i < 5; i++) {
      await page.keyboard.press('Tab');
      const newFocusedElement = page.locator(':focus');
      await expect(newFocusedElement).toBeVisible();
    }
    
    // Test that Enter/Space work on buttons
    const firstButton = page.getByRole('button').first();
    if (await firstButton.isVisible()) {
      await firstButton.focus();
      // Note: We don't actually press Enter to avoid side effects
      await expect(firstButton).toBeFocused();
    }
  });

  test('Screen reader compatibility', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Check that all images have alt text
    const images = page.getByRole('img');
    const imageCount = await images.count();
    
    for (let i = 0; i < imageCount; i++) {
      const image = images.nth(i);
      if (await image.isVisible()) {
        const alt = await image.getAttribute('alt');
        const ariaLabel = await image.getAttribute('aria-label');
        expect(alt || ariaLabel).toBeTruthy();
      }
    }
    
    // Check that all form inputs have labels
    const inputs = page.getByRole('textbox').or(page.getByRole('spinbutton'));
    const inputCount = await inputs.count();
    
    for (let i = 0; i < inputCount; i++) {
      const input = inputs.nth(i);
      if (await input.isVisible()) {
        const ariaLabel = await input.getAttribute('aria-label');
        const ariaLabelledBy = await input.getAttribute('aria-labelledby');
        expect(ariaLabel || ariaLabelledBy).toBeTruthy();
      }
    }
  });
});
