// Real-time performance monitoring utilities

// Performance metrics interface
interface PerformanceMetrics {
  fcp: number // First Contentful Paint
  lcp: number // Largest Contentful Paint
  fid: number // First Input Delay
  cls: number // Cumulative Layout Shift
  ttfb: number // Time to First Byte
  domContentLoaded: number
  loadComplete: number
  memoryUsage?: {
    used: number
    total: number
    limit: number
  }
  bundleSize?: number
  renderTime?: number
}

// Performance observer for Core Web Vitals
class PerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {}
  private observers: PerformanceObserver[] = []
  private callbacks: Array<(metrics: PerformanceMetrics) => void> = []

  constructor() {
    this.initializeObservers()
    this.measureBasicMetrics()
  }

  private initializeObservers() {
    // Largest Contentful Paint
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1] as any
          this.metrics.lcp = lastEntry.startTime
          this.notifyCallbacks()
        })
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
        this.observers.push(lcpObserver)
      } catch (e) {
        console.warn('LCP observer not supported')
      }

      // First Input Delay
      try {
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry: any) => {
            this.metrics.fid = entry.processingStart - entry.startTime
            this.notifyCallbacks()
          })
        })
        fidObserver.observe({ entryTypes: ['first-input'] })
        this.observers.push(fidObserver)
      } catch (e) {
        console.warn('FID observer not supported')
      }

      // Cumulative Layout Shift
      try {
        const clsObserver = new PerformanceObserver((list) => {
          let clsValue = 0
          const entries = list.getEntries()
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value
            }
          })
          this.metrics.cls = clsValue
          this.notifyCallbacks()
        })
        clsObserver.observe({ entryTypes: ['layout-shift'] })
        this.observers.push(clsObserver)
      } catch (e) {
        console.warn('CLS observer not supported')
      }

      // Navigation timing
      try {
        const navigationObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry: any) => {
            this.metrics.ttfb = entry.responseStart - entry.requestStart
            this.metrics.domContentLoaded = entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart
            this.metrics.loadComplete = entry.loadEventEnd - entry.loadEventStart
            this.notifyCallbacks()
          })
        })
        navigationObserver.observe({ entryTypes: ['navigation'] })
        this.observers.push(navigationObserver)
      } catch (e) {
        console.warn('Navigation observer not supported')
      }

      // Paint timing
      try {
        const paintObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry: any) => {
            if (entry.name === 'first-contentful-paint') {
              this.metrics.fcp = entry.startTime
              this.notifyCallbacks()
            }
          })
        })
        paintObserver.observe({ entryTypes: ['paint'] })
        this.observers.push(paintObserver)
      } catch (e) {
        console.warn('Paint observer not supported')
      }
    }
  }

  private measureBasicMetrics() {
    // Memory usage
    if ('memory' in performance) {
      const memory = (performance as any).memory
      this.metrics.memoryUsage = {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit
      }
    }

    // Bundle size estimation
    if ('getEntriesByType' in performance) {
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
      let totalSize = 0
      resources.forEach(resource => {
        if (resource.transferSize) {
          totalSize += resource.transferSize
        }
      })
      this.metrics.bundleSize = totalSize
    }
  }

  public onMetricsUpdate(callback: (metrics: PerformanceMetrics) => void) {
    this.callbacks.push(callback)
  }

  private notifyCallbacks() {
    this.callbacks.forEach(callback => {
      callback(this.metrics as PerformanceMetrics)
    })
  }

  public getMetrics(): Partial<PerformanceMetrics> {
    return { ...this.metrics }
  }

  public destroy() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    this.callbacks = []
  }
}

// Component performance tracking
export class ComponentPerformanceTracker {
  private renderTimes = new Map<string, number[]>()
  private mountTimes = new Map<string, number>()

  startRender(componentName: string) {
    this.mountTimes.set(componentName, performance.now())
  }

  endRender(componentName: string) {
    const startTime = this.mountTimes.get(componentName)
    if (startTime) {
      const renderTime = performance.now() - startTime
      
      if (!this.renderTimes.has(componentName)) {
        this.renderTimes.set(componentName, [])
      }
      
      const times = this.renderTimes.get(componentName)!
      times.push(renderTime)
      
      // Keep only last 10 render times
      if (times.length > 10) {
        times.shift()
      }
      
      this.mountTimes.delete(componentName)
      
      // Log slow renders
      if (renderTime > 16) { // More than one frame at 60fps
        console.warn(`Slow render detected for ${componentName}: ${renderTime.toFixed(2)}ms`)
      }
      
      return renderTime
    }
    return 0
  }

  getAverageRenderTime(componentName: string): number {
    const times = this.renderTimes.get(componentName)
    if (!times || times.length === 0) return 0
    
    return times.reduce((sum, time) => sum + time, 0) / times.length
  }

  getAllMetrics() {
    const metrics: Record<string, { average: number; samples: number; latest: number }> = {}
    
    this.renderTimes.forEach((times, componentName) => {
      metrics[componentName] = {
        average: this.getAverageRenderTime(componentName),
        samples: times.length,
        latest: times[times.length - 1] || 0
      }
    })
    
    return metrics
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor()
export const componentTracker = new ComponentPerformanceTracker()

// React hook for performance monitoring
export function usePerformanceMonitoring(componentName: string) {
  React.useEffect(() => {
    componentTracker.startRender(componentName)
    
    return () => {
      componentTracker.endRender(componentName)
    }
  })

  const [metrics, setMetrics] = React.useState<Partial<PerformanceMetrics>>({})

  React.useEffect(() => {
    const updateMetrics = (newMetrics: PerformanceMetrics) => {
      setMetrics(newMetrics)
    }

    performanceMonitor.onMetricsUpdate(updateMetrics)
    setMetrics(performanceMonitor.getMetrics())
  }, [])

  return metrics
}

// Performance budget checker
export function checkPerformanceBudget(metrics: PerformanceMetrics) {
  const budget = {
    fcp: 1500,  // 1.5s
    lcp: 2500,  // 2.5s
    fid: 100,   // 100ms
    cls: 0.1,   // 0.1
    ttfb: 600   // 600ms
  }

  const violations: string[] = []

  if (metrics.fcp > budget.fcp) {
    violations.push(`FCP exceeded budget: ${metrics.fcp}ms > ${budget.fcp}ms`)
  }
  if (metrics.lcp > budget.lcp) {
    violations.push(`LCP exceeded budget: ${metrics.lcp}ms > ${budget.lcp}ms`)
  }
  if (metrics.fid > budget.fid) {
    violations.push(`FID exceeded budget: ${metrics.fid}ms > ${budget.fid}ms`)
  }
  if (metrics.cls > budget.cls) {
    violations.push(`CLS exceeded budget: ${metrics.cls} > ${budget.cls}`)
  }
  if (metrics.ttfb > budget.ttfb) {
    violations.push(`TTFB exceeded budget: ${metrics.ttfb}ms > ${budget.ttfb}ms`)
  }

  return {
    passed: violations.length === 0,
    violations
  }
}

// Performance reporting
export function reportPerformanceMetrics(metrics: PerformanceMetrics) {
  // Send to analytics
  if (typeof window !== 'undefined' && 'gtag' in window) {
    (window as any).gtag('event', 'performance_metrics', {
      fcp: Math.round(metrics.fcp),
      lcp: Math.round(metrics.lcp),
      fid: Math.round(metrics.fid),
      cls: Math.round(metrics.cls * 1000) / 1000,
      ttfb: Math.round(metrics.ttfb)
    })
  }

  // Send to custom analytics endpoint
  if (process.env.NODE_ENV === 'production') {
    fetch('/api/performance', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ...metrics,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        url: window.location.href
      })
    }).catch(() => {
      // Silently fail if reporting fails
    })
  }
}

// Performance optimization suggestions
export function getPerformanceOptimizations(metrics: PerformanceMetrics): string[] {
  const suggestions: string[] = []

  if (metrics.fcp > 2000) {
    suggestions.push('Consider optimizing critical rendering path for faster FCP')
  }
  if (metrics.lcp > 3000) {
    suggestions.push('Optimize largest content element loading for better LCP')
  }
  if (metrics.fid > 200) {
    suggestions.push('Reduce JavaScript execution time to improve FID')
  }
  if (metrics.cls > 0.15) {
    suggestions.push('Add size attributes to images and reserve space for dynamic content')
  }
  if (metrics.ttfb > 800) {
    suggestions.push('Optimize server response time or consider using a CDN')
  }
  if (metrics.memoryUsage && metrics.memoryUsage.used > metrics.memoryUsage.limit * 0.8) {
    suggestions.push('High memory usage detected, consider optimizing memory consumption')
  }

  return suggestions
}

// Initialize performance monitoring
export function initializePerformanceMonitoring() {
  // Start monitoring immediately
  performanceMonitor.onMetricsUpdate((metrics) => {
    const budget = checkPerformanceBudget(metrics)
    if (!budget.passed) {
      console.warn('Performance budget violations:', budget.violations)
    }

    // Report metrics periodically
    if (Math.random() < 0.1) { // 10% sampling
      reportPerformanceMetrics(metrics)
    }
  })

  // Clean up on page unload
  window.addEventListener('beforeunload', () => {
    performanceMonitor.destroy()
  })
}

// Import React for hooks
import * as React from 'react'
