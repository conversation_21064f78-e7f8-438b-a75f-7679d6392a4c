import { ProviderConfig, ProviderType } from '../types.js';
import { AgentProvider, ProviderError } from './AgentProvider.js';

/**
 * Factory class for creating AI provider instances
 */
export class ProviderFactory {
  private static providers: Map<ProviderType, new (config: ProviderConfig) => AgentProvider> = new Map();

  /**
   * Register a provider implementation
   */
  static registerProvider(type: ProviderType, providerClass: new (config: ProviderConfig) => AgentProvider): void {
    this.providers.set(type, providerClass);
  }

  /**
   * Create a provider instance based on configuration
   */
  static createProvider(config: ProviderConfig): AgentProvider {
    const ProviderClass = this.providers.get(config.type);
    
    if (!ProviderClass) {
      throw new ProviderError(
        `Provider type '${config.type}' is not registered. Available providers: ${Array.from(this.providers.keys()).join(', ')}`,
        config.type,
        'PROVIDER_NOT_FOUND'
      );
    }

    try {
      const provider = new ProviderClass(config);
      return provider;
    } catch (error) {
      throw new ProviderError(
        `Failed to create provider '${config.type}': ${error instanceof Error ? error.message : 'Unknown error'}`,
        config.type,
        'PROVIDER_CREATION_FAILED'
      );
    }
  }

  /**
   * Get list of registered provider types
   */
  static getRegisteredProviders(): ProviderType[] {
    return Array.from(this.providers.keys());
  }

  /**
   * Check if a provider type is registered
   */
  static isProviderRegistered(type: ProviderType): boolean {
    return this.providers.has(type);
  }

  /**
   * Create multiple providers from configurations
   */
  static createProviders(configs: ProviderConfig[]): AgentProvider[] {
    return configs.map(config => this.createProvider(config));
  }

  /**
   * Create provider with fallback configuration
   */
  static createProviderWithFallback(
    primaryConfig: ProviderConfig, 
    fallbackConfig?: ProviderConfig
  ): { primary: AgentProvider; fallback?: AgentProvider } {
    const primary = this.createProvider(primaryConfig);
    const fallback = fallbackConfig ? this.createProvider(fallbackConfig) : undefined;
    
    return { primary, fallback };
  }

  /**
   * Validate provider configuration without creating instance
   */
  static validateConfig(config: ProviderConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check required fields
    if (!config.type) {
      errors.push('Provider type is required');
    }

    if (!config.model) {
      errors.push('Model is required');
    }

    if (typeof config.temperature !== 'number' || config.temperature < 0 || config.temperature > 2) {
      errors.push('Temperature must be a number between 0 and 2');
    }

    if (typeof config.maxTokens !== 'number' || config.maxTokens <= 0) {
      errors.push('maxTokens must be a positive number');
    }

    // Provider-specific validation
    switch (config.type) {
      case 'openai':
        if (!config.apiKey && !process.env.OPENAI_API_KEY) {
          errors.push('OpenAI API key is required (config.apiKey or OPENAI_API_KEY env var)');
        }
        break;

      case 'vertex-ai':
        if (!config.vertexAI?.projectId) {
          errors.push('Vertex AI project ID is required');
        }
        if (!config.vertexAI?.location) {
          errors.push('Vertex AI location is required');
        }
        break;

      case 'anthropic':
        if (!config.apiKey && !process.env.ANTHROPIC_API_KEY) {
          errors.push('Anthropic API key is required (config.apiKey or ANTHROPIC_API_KEY env var)');
        }
        break;

      default:
        if (config.type) {
          errors.push(`Unknown provider type: ${config.type}`);
        }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Get default configuration for a provider type
   */
  static getDefaultConfig(type: ProviderType): Partial<ProviderConfig> {
    const baseConfig = {
      temperature: 0.7,
      maxTokens: 2000,
    };

    switch (type) {
      case 'openai':
        return {
          ...baseConfig,
          type,
          model: 'gpt-4o',
          openai: {
            baseURL: 'https://api.openai.com/v1'
          }
        };

      case 'vertex-ai':
        return {
          ...baseConfig,
          type,
          model: 'gemini-2.0-flash',
          vertexAI: {
            projectId: '',
            location: 'us-central1'
          }
        };

      case 'anthropic':
        return {
          ...baseConfig,
          type,
          model: 'claude-opus-4-20250514',
          anthropic: {
            baseURL: 'https://api.anthropic.com',
            version: '2023-06-01'
          }
        };

      default:
        return baseConfig;
    }
  }
}

/**
 * Auto-register providers when they're imported
 * This will be called by each provider implementation
 */
export function registerProvider(type: ProviderType, providerClass: new (config: ProviderConfig) => AgentProvider): void {
  ProviderFactory.registerProvider(type, providerClass);
}
