# Dual Agent System

A TypeScript implementation of a dual-agent system for code generation and critique with multi-provider AI support.

## Overview

This package implements a dual-agent architecture with enterprise-grade features:
- **PlanAgent**: Generates JSON patches for code changes using OpenAI, Google Vertex AI, or Anthropic
- **CritiqueAgent**: Evaluates and scores the generated patches with detailed feedback
- **Provider Abstraction**: Unified interface across multiple AI providers with automatic failover
- **Cost Management**: Built-in cost tracking and budget controls
- **Streaming Support**: Real-time patch generation with Server-Sent Events
- **Parallel Execution**: Intelligent orchestration for optimal performance

## Features

### 🤖 **Multi-Provider AI Support**
- **OpenAI**: GPT-4o, GPT-4 Turbo with Responses API and built-in tools
- **Google Vertex AI**: Gemini 2.5 with Agent Development Kit and grounding
- **Anthropic**: Claude 4 Opus/Sonnet with versioned tool support

### 🔄 **Enterprise Reliability**
- **Automatic Failover**: Seamless switching between providers on errors
- **Exponential Backoff**: Smart retry logic with jitter and rate limit handling
- **Health Monitoring**: Real-time provider health tracking and recovery

### 💰 **Cost Management**
- **Budget Controls**: Configurable cost caps with real-time monitoring
- **Usage Tracking**: Detailed token and cost analytics per provider
- **Cost Projection**: Estimate costs before execution

### ⚡ **Performance Features**
- **Streaming**: Real-time patch generation with SSE events
- **Parallel Execution**: Concurrent provider execution with intelligent orchestration
- **Adaptive Strategies**: Automatic selection of optimal execution strategy

### 🧪 **Quality Assurance**
- **90%+ Test Coverage**: Comprehensive unit and integration tests
- **Type Safety**: Full TypeScript support with strict typing
- **Validation**: Zod schemas for configuration and runtime validation

## Installation

```bash
npm install @code-alchemy/agents
```

## Quick Start

### Basic Usage

```typescript
import { PlanAgent, CritiqueAgent } from '@code-alchemy/agents';

// Initialize with provider configuration
const planner = new PlanAgent({
  provider: {
    type: 'openai',
    model: 'gpt-4o',
    temperature: 0.7,
    maxTokens: 2000,
    apiKey: process.env.OPENAI_API_KEY
  }
});

const critic = new CritiqueAgent({
  provider: {
    type: 'vertex-ai',
    model: 'gemini-2.5-flash',
    temperature: 0.3,
    maxTokens: 1500,
    vertexAI: {
      projectId: 'your-project-id',
      location: 'us-central1'
    }
  }
});

// Generate and critique a patch
const patch = await planner.generatePatch({
  prompt: 'Add user authentication to the login component',
  context: { framework: 'React', language: 'TypeScript' }
});

const critique = await critic.scorePatch({
  patch,
  originalPrompt: 'Add user authentication to the login component'
});

console.log(`Patch confidence: ${patch.confidence}`);
console.log(`Critique score: ${critique.score} (${critique.isAcceptable ? 'ACCEPTABLE' : 'NEEDS_WORK'})`);
```

### Advanced Configuration with Failover

```typescript
import { PlanAgent, CostGuard, TokenMonitor } from '@code-alchemy/agents';

// Initialize shared utilities
const costGuard = new CostGuard({ maxCostPerLoop: 5.0 });
const tokenMonitor = new TokenMonitor();

// Create agent with failover providers
const planner = new PlanAgent({
  provider: {
    type: 'openai',
    model: 'gpt-4o',
    temperature: 0.7,
    maxTokens: 2000,
    apiKey: process.env.OPENAI_API_KEY
  }
}, costGuard, tokenMonitor);

// Configure failover providers
planner.initializeFailover([
  {
    type: 'vertex-ai',
    model: 'gemini-2.5-flash',
    temperature: 0.7,
    maxTokens: 2000,
    vertexAI: {
      projectId: process.env.VERTEX_AI_PROJECT_ID,
      location: 'us-central1'
    }
  },
  {
    type: 'anthropic',
    model: 'claude-opus-4-20250514',
    temperature: 0.7,
    maxTokens: 2000,
    apiKey: process.env.ANTHROPIC_API_KEY
  }
]);

// Generate patch with automatic failover
const patch = await planner.generatePatch({
  prompt: 'Implement OAuth2 authentication flow',
  context: { framework: 'Next.js', database: 'PostgreSQL' }
});

// Check cost and usage statistics
const stats = planner.getStats();
console.log(`Total cost: $${stats.totalCost.toFixed(4)}`);
console.log(`Tokens used: ${stats.totalTokens}`);
```

### Streaming with Real-time Updates

```typescript
import { PlanAgent, StreamingManager } from '@code-alchemy/agents';

const streamingManager = new StreamingManager();
const planner = new PlanAgent({ provider: config }, undefined, undefined, streamingManager);

// Stream patch generation
const streamGenerator = planner.generatePatchStream({
  prompt: 'Add real-time chat functionality',
  context: { framework: 'React', backend: 'Socket.io' }
});

for await (const chunk of streamGenerator) {
  console.log('Partial patch:', chunk);
  // Update UI with streaming progress
}

// Set up Server-Sent Events for web clients
const { stream } = streamingManager.createSSEStream();
// Pipe stream to HTTP response for real-time updates
```

### Parallel Execution with Orchestrator

```typescript
import { ParallelOrchestrator, ProviderFactory } from '@code-alchemy/agents';

const orchestrator = new ParallelOrchestrator(streamingManager, costGuard, tokenMonitor);

// Create multiple providers
const providers = [
  ProviderFactory.createProvider({ type: 'openai', model: 'gpt-4o', apiKey: '...' }),
  ProviderFactory.createProvider({ type: 'vertex-ai', model: 'gemini-2.5-flash', vertexAI: {...} }),
  ProviderFactory.createProvider({ type: 'anthropic', model: 'claude-opus-4-20250514', apiKey: '...' })
];

// Execute dual-agent loop with intelligent orchestration
const result = await orchestrator.executeDualAgentLoop(
  providers, // planner providers
  providers, // critic providers
  { prompt: 'Build a microservice architecture' },
  3 // max iterations
);

console.log(`Final patch: ${result.finalPatch.description}`);
console.log(`Completed in ${result.iterations.length} iterations`);
console.log(`Total cost: $${result.totalCost.toFixed(4)}`);
```

## Configuration

### Provider Configuration

Each provider requires specific configuration:

#### OpenAI
```typescript
{
  type: 'openai',
  model: 'gpt-4o' | 'gpt-4o-mini' | 'gpt-4-turbo',
  apiKey: string,
  temperature: number,
  maxTokens: number,
  openai?: {
    baseURL?: string,
    organization?: string,
    project?: string
  }
}
```

#### Vertex AI
```typescript
{
  type: 'vertex-ai',
  model: 'gemini-2.5-flash' | 'gemini-2.5-pro' | 'gemini-2.0-flash',
  temperature: number,
  maxTokens: number,
  vertexAI: {
    projectId: string,
    location: string,
    credentials?: object
  }
}
```

#### Anthropic
```typescript
{
  type: 'anthropic',
  model: 'claude-opus-4-20250514' | 'claude-sonnet-4-20250514',
  apiKey: string,
  temperature: number,
  maxTokens: number,
  anthropic?: {
    baseURL?: string,
    version?: string
  }
}
```

### Cost Guard Configuration

```typescript
const costGuard = new CostGuard({
  maxCostPerLoop: 3.0,        // Maximum cost per execution loop
  enabled: true,              // Enable/disable cost checking
});
```

### Token Monitor Configuration

```typescript
const tokenMonitor = new TokenMonitor({
  enabled: true,
  maxEvents: 10000,           // Maximum events to store
  alertThresholds: {
    tokensPerHour: 1000000,   // Alert if exceeding 1M tokens/hour
    costPerHour: 10.0,        // Alert if exceeding $10/hour
    requestsPerMinute: 100    // Alert if exceeding 100 requests/minute
  },
  logLevel: 'basic'           // 'none' | 'basic' | 'detailed'
});
```

## API Reference

See [docs/agent_provider.md](./docs/agent_provider.md) for detailed API documentation.

## Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch

# Run CI tests with coverage threshold
npm run test:coverage:ci
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure coverage ≥ 90%
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
