# Agent Provider API Documentation

This document provides comprehensive API documentation for the Dual Agent System's provider architecture.

## Table of Contents

- [Provider Interface](#provider-interface)
- [Provider Factory](#provider-factory)
- [Provider Implementations](#provider-implementations)
- [Utility Classes](#utility-classes)
- [Configuration Examples](#configuration-examples)
- [Error Handling](#error-handling)
- [Best Practices](#best-practices)

## Provider Interface

### AgentProvider (Abstract Base Class)

The `AgentProvider` abstract class defines the interface that all AI providers must implement.

```typescript
abstract class AgentProvider {
  // Core methods
  abstract getType(): ProviderType;
  abstract getModel(): string;
  abstract getCost(): number;
  abstract supportsStreaming(): boolean;
  
  // Configuration and validation
  abstract validateConfig(): Promise<boolean>;
  abstract updateConfig(updates: Partial<ProviderConfig>): void;
  
  // Main operations
  abstract generatePatch(request: PlanRequest): Promise<ProviderResponse<JSONPatch>>;
  abstract scorePatch(request: CritiqueRequest): Promise<ProviderResponse<CritiqueResult>>;
  
  // Streaming operations (optional)
  abstract streamGeneratePatch?(request: PlanRequest): AsyncGenerator<Partial<JSONPatch>, ProviderResponse<JSONPatch>>;
  abstract streamScorePatch?(request: CritiqueRequest): AsyncGenerator<Partial<CritiqueResult>, ProviderResponse<CritiqueResult>>;
  
  // Metrics and monitoring
  abstract getTokenUsage(): TokenUsage;
  abstract resetMetrics(): void;
}
```

### ProviderResponse Interface

All provider operations return a standardized response:

```typescript
interface ProviderResponse<T> {
  data: T;                    // The actual response data
  usage: TokenUsage;          // Token and cost information
  requestId: string;          // Unique request identifier
  latency: number;            // Response time in milliseconds
}
```

### TokenUsage Interface

Token usage tracking across all providers:

```typescript
interface TokenUsage {
  inputTokens: number;        // Input tokens consumed
  outputTokens: number;       // Output tokens generated
  totalTokens: number;        // Total tokens (input + output)
  cost: number;               // Cost in USD
  provider: ProviderType;     // Provider that generated this usage
  model: string;              // Model used
  timestamp: Date;            // When the usage occurred
}
```

## Provider Factory

### ProviderFactory Class

The `ProviderFactory` provides a centralized way to create and manage provider instances.

```typescript
class ProviderFactory {
  static createProvider(config: ProviderConfig): AgentProvider;
  static registerProvider(type: ProviderType, factory: ProviderFactoryFunction): void;
  static getAvailableProviders(): ProviderType[];
  static validateProviderConfig(config: ProviderConfig): boolean;
}
```

### Usage Example

```typescript
import { ProviderFactory } from '@code-alchemy/agents';

// Create OpenAI provider
const openaiProvider = ProviderFactory.createProvider({
  type: 'openai',
  model: 'gpt-4o',
  apiKey: process.env.OPENAI_API_KEY,
  temperature: 0.7,
  maxTokens: 2000
});

// Create Vertex AI provider
const vertexProvider = ProviderFactory.createProvider({
  type: 'vertex-ai',
  model: 'gemini-2.5-flash',
  temperature: 0.7,
  maxTokens: 2000,
  vertexAI: {
    projectId: 'my-project',
    location: 'us-central1'
  }
});
```

## Provider Implementations

### OpenAIProvider

Implements the OpenAI Responses API with structured tools.

#### Configuration

```typescript
interface OpenAIConfig {
  type: 'openai';
  model: 'gpt-4o' | 'gpt-4o-mini' | 'gpt-4-turbo';
  apiKey: string;
  temperature: number;
  maxTokens: number;
  openai?: {
    baseURL?: string;
    organization?: string;
    project?: string;
  };
}
```

#### Features

- **Structured Tools**: Uses OpenAI's built-in tool system
- **Streaming Support**: Real-time response streaming
- **Error Handling**: Comprehensive error classification and retry logic
- **Cost Tracking**: Accurate token-based cost calculation

#### Example

```typescript
const openaiProvider = new OpenAIProvider({
  type: 'openai',
  model: 'gpt-4o',
  apiKey: process.env.OPENAI_API_KEY,
  temperature: 0.7,
  maxTokens: 2000
});

const patch = await openaiProvider.generatePatch({
  prompt: 'Add error handling to the API endpoint',
  context: { framework: 'Express.js', language: 'TypeScript' }
});
```

### VertexAIProvider

Implements Google's Vertex AI Agent Development Kit with Gemini models.

#### Configuration

```typescript
interface VertexAIConfig {
  type: 'vertex-ai';
  model: 'gemini-2.5-flash' | 'gemini-2.5-pro' | 'gemini-2.0-flash';
  temperature: number;
  maxTokens: number;
  vertexAI: {
    projectId: string;
    location: string;
    credentials?: object;
  };
}
```

#### Features

- **Agent Development Kit**: Uses Vertex AI's latest agent framework
- **System Instructions**: Supports Gemini 2.x system instruction format
- **Grounding**: Cloud-based grounding capabilities
- **Session Management**: Managed conversation sessions

#### Example

```typescript
const vertexProvider = new VertexAIProvider({
  type: 'vertex-ai',
  model: 'gemini-2.5-flash',
  temperature: 0.7,
  maxTokens: 2000,
  vertexAI: {
    projectId: 'my-gcp-project',
    location: 'us-central1'
  }
});

const critique = await vertexProvider.scorePatch({
  patch: generatedPatch,
  originalPrompt: 'Add error handling to the API endpoint'
});
```

### AnthropicProvider

Implements Anthropic's Claude API with versioned tool support.

#### Configuration

```typescript
interface AnthropicConfig {
  type: 'anthropic';
  model: 'claude-opus-4-20250514' | 'claude-sonnet-4-20250514';
  apiKey: string;
  temperature: number;
  maxTokens: number;
  anthropic?: {
    baseURL?: string;
    version?: string;
  };
}
```

#### Features

- **Tool Versioning**: Handles versioned tool types for compatibility
- **System Prompts**: Advanced system prompt engineering
- **Streaming**: Real-time response streaming
- **Safety**: Built-in safety and content filtering

#### Example

```typescript
const anthropicProvider = new AnthropicProvider({
  type: 'anthropic',
  model: 'claude-opus-4-20250514',
  apiKey: process.env.ANTHROPIC_API_KEY,
  temperature: 0.7,
  maxTokens: 2000
});

// Stream patch generation
const streamGenerator = anthropicProvider.streamGeneratePatch({
  prompt: 'Implement OAuth2 authentication',
  context: { framework: 'React', backend: 'Node.js' }
});

for await (const chunk of streamGenerator) {
  console.log('Streaming chunk:', chunk);
}
```

## Utility Classes

### CostGuard

Monitors and controls costs across all providers.

```typescript
class CostGuard {
  constructor(config: CostGuardConfig);
  
  // Cost checking
  checkCostLimit(estimatedCost: number): void;
  recordCost(provider: ProviderType, model: string, operation: string, usage: TokenUsage): CostEntry;
  
  // Cost tracking
  getCurrentCost(): number;
  getRemainingBudget(): number;
  getCostByProvider(provider: ProviderType): number;
  isNearLimit(threshold: number = 0.8): boolean;
  
  // Projections
  projectCost(plannedOperations: PlannedOperation[]): CostProjection;
  getCostEfficiency(): CostEfficiency;
  
  // Configuration
  updateCostLimit(newLimit: number): void;
  setEnabled(enabled: boolean): void;
  resetCosts(): void;
  
  // Data export
  getStats(): CostStats;
  getCostEntries(filter?: CostFilter): CostEntry[];
  exportCostData(): CostExport;
}
```

### TokenMonitor

Tracks token usage and provides analytics.

```typescript
class TokenMonitor {
  constructor(config: TokenMonitorConfig);
  
  // Recording
  recordUsage(provider: ProviderType, model: string, operation: string, usage: TokenUsage, requestId?: string): TokenEvent;
  
  // Analytics
  getStats(filter?: TokenFilter): TokenAggregation;
  getUsageByProvider(): Map<ProviderType, TokenAggregation>;
  getUsageByModel(): Map<string, TokenAggregation>;
  getTrends(days: number): TokenTrends;
  
  // Efficiency
  getEfficiencyMetrics(): TokenEfficiencyMetrics;
  
  // Events and alerts
  getRecentEvents(limit: number): TokenEvent[];
  onAlert(callback: (alert: TokenAlert) => void): void;
  
  // Data management
  clear(): void;
  exportData(format: 'json' | 'csv'): string;
}
```

### ProviderFailover

Manages automatic failover between providers.

```typescript
class ProviderFailover {
  constructor(primaryConfig: ProviderConfig, fallbackConfigs: ProviderConfig[], config?: FailoverConfig);
  
  // Execution
  executeWithFailover<T>(operation: (provider: AgentProvider) => Promise<T>, operationName: string): Promise<T>;
  
  // Manual control
  forceFailover(providerType: ProviderType): Promise<boolean>;
  resetToPrimary(): Promise<boolean>;
  
  // Monitoring
  getCurrentProvider(): AgentProvider;
  getHealthStatus(): Map<ProviderType, ProviderHealth>;
  getFailoverHistory(): FailoverEvent[];
  getFailoverStats(): FailoverStats;
  
  // Cleanup
  cleanup(): void;
}
```

### RetryUtil

Provides exponential backoff retry logic.

```typescript
class RetryUtil {
  static withRetry<T>(
    fn: () => Promise<T>,
    config?: RetryConfig,
    onAttempt?: (attempt: AttemptInfo) => void
  ): Promise<RetryResult<T>>;
  
  static withProviderRetry<T>(
    fn: () => Promise<T>,
    provider: ProviderType,
    onAttempt?: (attempt: AttemptInfo) => void
  ): Promise<RetryResult<T>>;
  
  static createRetryWrapper<T extends (...args: any[]) => Promise<any>>(
    fn: T,
    config?: RetryConfig
  ): T;
  
  static batchRetry<T>(operations: BatchOperation<T>[]): Promise<RetryResult<T>[]>;
  static getRetryStats(results: RetryResult<any>[]): RetryStats;
}
```

## Configuration Examples

### Production Configuration

```typescript
import { PlanAgent, CritiqueAgent, CostGuard, TokenMonitor, ProviderFailover } from '@code-alchemy/agents';

// Initialize monitoring
const costGuard = new CostGuard({
  maxCostPerLoop: 10.0,
  enabled: true
});

const tokenMonitor = new TokenMonitor({
  enabled: true,
  maxEvents: 50000,
  alertThresholds: {
    tokensPerHour: 5000000,
    costPerHour: 50.0,
    requestsPerMinute: 500
  },
  logLevel: 'basic'
});

// Primary configuration
const primaryConfig = {
  type: 'openai' as const,
  model: 'gpt-4o',
  apiKey: process.env.OPENAI_API_KEY!,
  temperature: 0.7,
  maxTokens: 4000
};

// Fallback configurations
const fallbackConfigs = [
  {
    type: 'vertex-ai' as const,
    model: 'gemini-2.5-pro',
    temperature: 0.7,
    maxTokens: 4000,
    vertexAI: {
      projectId: process.env.VERTEX_AI_PROJECT_ID!,
      location: 'us-central1'
    }
  },
  {
    type: 'anthropic' as const,
    model: 'claude-opus-4-20250514',
    apiKey: process.env.ANTHROPIC_API_KEY!,
    temperature: 0.7,
    maxTokens: 4000
  }
];

// Initialize agents
const planner = new PlanAgent({ provider: primaryConfig }, costGuard, tokenMonitor);
const critic = new CritiqueAgent({ provider: primaryConfig }, costGuard, tokenMonitor);

// Configure failover
planner.initializeFailover(fallbackConfigs);
critic.initializeFailover(fallbackConfigs);

// Set up monitoring alerts
tokenMonitor.onAlert((alert) => {
  console.warn(`Token alert: ${alert.message}`);
  // Send to monitoring system
});

costGuard.onAlert?.((alert) => {
  console.error(`Cost alert: ${alert.message}`);
  // Send to alerting system
});
```

### Development Configuration

```typescript
// Simplified development setup
const devConfig = {
  type: 'openai' as const,
  model: 'gpt-4o-mini',  // Cheaper model for development
  apiKey: process.env.OPENAI_API_KEY!,
  temperature: 0.8,
  maxTokens: 2000
};

const costGuard = new CostGuard({
  maxCostPerLoop: 1.0,  // Lower limit for development
  enabled: true
});

const planner = new PlanAgent({ provider: devConfig }, costGuard);
const critic = new CritiqueAgent({ provider: devConfig }, costGuard);
```

## Error Handling

### Error Types

The system defines specific error types for different scenarios:

```typescript
// Base provider error
class ProviderError extends Error {
  constructor(message: string, provider: ProviderType, code: string, status?: number);
}

// Rate limiting
class RateLimitError extends ProviderError {
  constructor(provider: ProviderType, retryAfter?: number);
}

// Cost limits
class CostLimitError extends Error {
  constructor(currentCost: number, limit: number, estimatedCost: number);
}

// Configuration errors
class ConfigurationError extends Error {
  constructor(message: string, field?: string);
}
```

### Error Handling Patterns

```typescript
try {
  const patch = await planner.generatePatch(request);
} catch (error) {
  if (error instanceof RateLimitError) {
    // Handle rate limiting
    console.log(`Rate limited, retry after ${error.retryAfter}ms`);
  } else if (error instanceof CostLimitError) {
    // Handle cost limits
    console.log(`Cost limit exceeded: $${error.currentCost}/$${error.limit}`);
  } else if (error instanceof ProviderError) {
    // Handle provider-specific errors
    console.log(`Provider ${error.provider} error: ${error.message}`);
  } else {
    // Handle unexpected errors
    console.error('Unexpected error:', error);
  }
}
```

## Best Practices

### 1. Provider Selection

- Use **OpenAI** for general-purpose tasks with good tool support
- Use **Vertex AI** for Google Cloud integration and cost optimization
- Use **Anthropic** for safety-critical applications and complex reasoning

### 2. Cost Management

- Always initialize `CostGuard` in production
- Set appropriate cost limits based on your budget
- Monitor cost trends and adjust limits as needed
- Use cheaper models for development and testing

### 3. Error Handling

- Implement comprehensive error handling for all provider operations
- Use failover for critical applications
- Log errors appropriately for debugging and monitoring

### 4. Performance Optimization

- Use streaming for long-running operations
- Implement parallel execution for multiple operations
- Cache results when appropriate
- Monitor token usage and optimize prompts

### 5. Security

- Never hardcode API keys in source code
- Use environment variables or secure key management
- Implement proper access controls
- Monitor for unusual usage patterns

### 6. Testing

- Mock provider responses in unit tests
- Use sandbox/test endpoints when available
- Implement integration tests with real providers
- Maintain high test coverage (≥90%)

### 7. Monitoring

- Set up alerts for cost and usage thresholds
- Monitor provider health and failover events
- Track performance metrics and latency
- Export usage data for analysis
