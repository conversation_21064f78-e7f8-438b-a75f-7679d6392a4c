import React, { useState, useRef } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  Download, 
  Upload, 
  FileText, 
  Shield, 
  CheckCircle, 
  AlertTriangle,
  Copy,
  Eye,
  EyeOff,
  Trash2
} from 'lucide-react';
import { 
  EnhancedUserSettings, 
  validateEnhancedUserSettings 
} from '@/types/settings';

interface ImportExportManagerProps {
  currentSettings: EnhancedUserSettings;
  onImport: (settings: EnhancedUserSettings) => Promise<void>;
  onExport?: () => Promise<string>;
}

interface ExportOptions {
  includeApiKeys: boolean;
  includePersonalData: boolean;
  format: 'json' | 'yaml';
}

interface ImportResult {
  success: boolean;
  settings?: EnhancedUserSettings;
  errors: string[];
  warnings: string[];
}

export const ImportExportManager: React.FC<ImportExportManagerProps> = ({
  currentSettings,
  onImport,
  onExport,
}) => {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    includeApiKeys: false,
    includePersonalData: true,
    format: 'json',
  });
  
  const [importData, setImportData] = useState('');
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [showImportPreview, setShowImportPreview] = useState(false);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      let exportData: any = { ...currentSettings };

      // Handle API keys based on options
      if (!exportOptions.includeApiKeys) {
        exportData.api_keys = exportData.api_keys ? {
          openai: exportData.api_keys.openai ? '***MASKED***' : undefined,
          anthropic: exportData.api_keys.anthropic ? '***MASKED***' : undefined,
          vertex_ai_credentials: exportData.api_keys.vertex_ai_credentials ? '***MASKED***' : undefined,
        } : undefined;
      }

      // Handle personal data
      if (!exportOptions.includePersonalData) {
        delete exportData.id;
        delete exportData.user_id;
        delete exportData.github_repo_owner;
        delete exportData.github_repo_name;
        delete exportData.created_at;
        delete exportData.updated_at;
      }

      const exportObject = {
        settings: exportData,
        metadata: {
          exportedAt: new Date().toISOString(),
          version: currentSettings.config_version,
          includeApiKeys: exportOptions.includeApiKeys,
          includePersonalData: exportOptions.includePersonalData,
          format: exportOptions.format,
        },
      };

      let content: string;
      let filename: string;
      let mimeType: string;

      if (exportOptions.format === 'json') {
        content = JSON.stringify(exportObject, null, 2);
        filename = `dual-agent-settings-${new Date().toISOString().split('T')[0]}.json`;
        mimeType = 'application/json';
      } else {
        // YAML format (simplified)
        content = `# Dual-Agent Settings Export\n# Exported: ${new Date().toISOString()}\n\n${JSON.stringify(exportObject, null, 2)}`;
        filename = `dual-agent-settings-${new Date().toISOString().split('T')[0]}.yaml`;
        mimeType = 'text/yaml';
      }

      // Create and download file
      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setImportData(content);
      validateImportData(content);
    };
    reader.readAsText(file);
  };

  const validateImportData = (data: string) => {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      const parsed = JSON.parse(data);
      
      // Check if it's a valid export format
      if (!parsed.settings && !parsed.metadata) {
        // Assume it's raw settings data
        validateEnhancedUserSettings(parsed);
        setImportResult({
          success: true,
          settings: parsed,
          errors,
          warnings: ['Imported raw settings data without metadata'],
        });
        return;
      }

      // Validate the settings structure
      if (!parsed.settings) {
        errors.push('No settings data found in import file');
      } else {
        try {
          validateEnhancedUserSettings(parsed.settings);
        } catch (validationError: any) {
          if (validationError.errors) {
            errors.push(...validationError.errors.map((e: any) => e.message));
          } else {
            errors.push(validationError.message);
          }
        }
      }

      // Check version compatibility
      if (parsed.metadata?.version && parsed.metadata.version < currentSettings.config_version) {
        warnings.push(`Importing older settings version (v${parsed.metadata.version}). Some features may need reconfiguration.`);
      }

      // Check for masked API keys
      if (parsed.settings?.api_keys) {
        const maskedKeys = Object.entries(parsed.settings.api_keys)
          .filter(([_, value]) => value === '***MASKED***')
          .map(([key]) => key);
        
        if (maskedKeys.length > 0) {
          warnings.push(`API keys are masked: ${maskedKeys.join(', ')}. You'll need to re-enter them.`);
        }
      }

      setImportResult({
        success: errors.length === 0,
        settings: parsed.settings,
        errors,
        warnings,
      });

    } catch (error) {
      setImportResult({
        success: false,
        errors: ['Invalid JSON format: ' + (error instanceof Error ? error.message : 'Parse error')],
        warnings: [],
      });
    }
  };

  const handleImport = async () => {
    if (!importResult?.success || !importResult.settings) return;

    setIsImporting(true);
    
    try {
      await onImport(importResult.settings);
      setImportData('');
      setImportResult(null);
    } catch (error) {
      console.error('Import failed:', error);
    } finally {
      setIsImporting(false);
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(importData);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* Export Section */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Download className="w-5 h-5 text-blue-400" />
            Export Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Export Options */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-slate-300">Include API Keys</label>
              <input
                type="checkbox"
                checked={exportOptions.includeApiKeys}
                onChange={(e) => setExportOptions(prev => ({ ...prev, includeApiKeys: e.target.checked }))}
                className="rounded border-slate-600 bg-slate-700"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <label className="text-slate-300">Include Personal Data</label>
              <input
                type="checkbox"
                checked={exportOptions.includePersonalData}
                onChange={(e) => setExportOptions(prev => ({ ...prev, includePersonalData: e.target.checked }))}
                className="rounded border-slate-600 bg-slate-700"
              />
            </div>

            <div className="flex items-center justify-between">
              <label className="text-slate-300">Format</label>
              <select
                value={exportOptions.format}
                onChange={(e) => setExportOptions(prev => ({ ...prev, format: e.target.value as 'json' | 'yaml' }))}
                className="bg-slate-700 border-slate-600 text-white rounded px-2 py-1"
              >
                <option value="json">JSON</option>
                <option value="yaml">YAML</option>
              </select>
            </div>
          </div>

          {/* Security Warning */}
          {exportOptions.includeApiKeys && (
            <Alert className="border-yellow-500/20 bg-yellow-500/10">
              <Shield className="h-4 w-4 text-yellow-500" />
              <AlertDescription className="text-yellow-200">
                <div className="font-medium">Security Warning</div>
                <p className="text-sm mt-1">
                  Including API keys in exports creates security risks. Only enable this for secure environments.
                </p>
              </AlertDescription>
            </Alert>
          )}

          <Button
            onClick={handleExport}
            disabled={isExporting}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Download className="w-4 h-4 mr-2" />
            {isExporting ? 'Exporting...' : 'Export Settings'}
          </Button>
        </CardContent>
      </Card>

      {/* Import Section */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Upload className="w-5 h-5 text-green-400" />
            Import Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* File Upload */}
          <div className="space-y-2">
            <input
              ref={fileInputRef}
              type="file"
              accept=".json,.yaml,.yml"
              onChange={handleFileImport}
              className="hidden"
            />
            <Button
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              className="w-full border-slate-600 text-slate-300 hover:text-white"
            >
              <FileText className="w-4 h-4 mr-2" />
              Choose File
            </Button>
          </div>

          {/* Manual Input */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-slate-300">Or paste settings data:</label>
              <div className="flex gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowImportPreview(!showImportPreview)}
                  className="h-6 w-6 p-0 text-slate-400 hover:text-white"
                >
                  {showImportPreview ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                </Button>
                {importData && (
                  <>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={copyToClipboard}
                      className="h-6 w-6 p-0 text-slate-400 hover:text-white"
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setImportData('')}
                      className="h-6 w-6 p-0 text-slate-400 hover:text-red-400"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </>
                )}
              </div>
            </div>
            <Textarea
              value={showImportPreview ? importData : ''}
              onChange={(e) => {
                setImportData(e.target.value);
                if (e.target.value) {
                  validateImportData(e.target.value);
                } else {
                  setImportResult(null);
                }
              }}
              placeholder="Paste your exported settings JSON here..."
              className="bg-slate-700 border-slate-600 text-white min-h-[120px] font-mono text-sm"
              style={{ display: showImportPreview ? 'block' : 'none' }}
            />
            {!showImportPreview && importData && (
              <div className="p-3 bg-slate-700 rounded border border-slate-600">
                <p className="text-slate-300 text-sm">
                  Settings data loaded ({importData.length} characters)
                </p>
              </div>
            )}
          </div>

          {/* Import Validation Results */}
          {importResult && (
            <div className="space-y-3">
              {/* Errors */}
              {importResult.errors.length > 0 && (
                <Alert className="border-red-500/20 bg-red-500/10">
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                  <AlertDescription className="text-red-200">
                    <div className="font-medium mb-1">Import Errors</div>
                    <ul className="list-disc list-inside space-y-1">
                      {importResult.errors.map((error, index) => (
                        <li key={index} className="text-sm">{error}</li>
                      ))}
                    </ul>
                  </AlertDescription>
                </Alert>
              )}

              {/* Warnings */}
              {importResult.warnings.length > 0 && (
                <Alert className="border-yellow-500/20 bg-yellow-500/10">
                  <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  <AlertDescription className="text-yellow-200">
                    <div className="font-medium mb-1">Import Warnings</div>
                    <ul className="list-disc list-inside space-y-1">
                      {importResult.warnings.map((warning, index) => (
                        <li key={index} className="text-sm">{warning}</li>
                      ))}
                    </ul>
                  </AlertDescription>
                </Alert>
              )}

              {/* Success */}
              {importResult.success && (
                <Alert className="border-green-500/20 bg-green-500/10">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <AlertDescription className="text-green-200">
                    <div className="font-medium">Settings Valid</div>
                    <p className="text-sm mt-1">
                      The imported settings have been validated and are ready to apply.
                    </p>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {/* Import Button */}
          <Button
            onClick={handleImport}
            disabled={!importResult?.success || isImporting}
            className="w-full bg-green-600 hover:bg-green-700 text-white"
          >
            <Upload className="w-4 h-4 mr-2" />
            {isImporting ? 'Importing...' : 'Import Settings'}
          </Button>
        </CardContent>
      </Card>

      {/* Usage Instructions */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <FileText className="w-5 h-5 text-purple-400" />
            Usage Instructions
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm text-slate-300">
          <div>
            <h4 className="font-medium text-white mb-1">Export</h4>
            <p>Create a backup of your current settings. Choose whether to include sensitive data like API keys.</p>
          </div>
          <div>
            <h4 className="font-medium text-white mb-1">Import</h4>
            <p>Restore settings from a backup file or share configurations between environments.</p>
          </div>
          <div>
            <h4 className="font-medium text-white mb-1">Security</h4>
            <p>API keys are masked by default in exports. Re-enter them after importing if needed.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
