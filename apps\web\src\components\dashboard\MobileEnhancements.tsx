import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { 
  Menu, 
  X, 
  ChevronLeft, 
  ChevronRight,
  MoreVertical,
  Maximize2,
  Minimize2,
  RotateCcw,
  Settings,
  Activity,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBreakpoint } from '@/components/ui/mobile-layout';
import { useSwipe } from '@/hooks/useGestures';
import { HStack, VStack } from '@/components/ui/layout';
import { Typography } from '@/components/ui/typography';

// Enhanced Mobile Dashboard Container
interface MobileDashboardContainerProps {
  children: React.ReactNode;
  className?: string;
}

export const MobileDashboardContainer: React.FC<MobileDashboardContainerProps> = ({
  children,
  className
}) => {
  const { isMobile, isTablet } = useBreakpoint();
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait');

  useEffect(() => {
    const handleOrientationChange = () => {
      setOrientation(window.innerHeight > window.innerWidth ? 'portrait' : 'landscape');
    };

    handleOrientationChange();
    window.addEventListener('resize', handleOrientationChange);
    window.addEventListener('orientationchange', handleOrientationChange);

    return () => {
      window.removeEventListener('resize', handleOrientationChange);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, []);

  if (!isMobile && !isTablet) {
    return <div className={className}>{children}</div>;
  }

  return (
    <div 
      className={cn(
        "min-h-screen bg-background",
        "touch-manipulation",
        "overflow-x-hidden",
        orientation === 'landscape' && "landscape:overflow-y-auto",
        className
      )}
      style={{
        // Prevent zoom on double tap
        touchAction: 'manipulation',
        // Improve scrolling performance
        WebkitOverflowScrolling: 'touch',
        // Prevent text selection on touch
        WebkitUserSelect: 'none',
        userSelect: 'none'
      }}
    >
      {children}
    </div>
  );
};

// Mobile Widget Grid
interface MobileWidgetGridProps {
  widgets: React.ReactNode[];
  columns?: 1 | 2;
  spacing?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const MobileWidgetGrid: React.FC<MobileWidgetGridProps> = ({
  widgets,
  columns = 1,
  spacing = 'md',
  className
}) => {
  const { isMobile } = useBreakpoint();
  
  const spacingClasses = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6'
  };

  // Force single column on mobile
  const actualColumns = isMobile ? 1 : columns;

  return (
    <div 
      className={cn(
        "grid w-full",
        actualColumns === 1 ? 'grid-cols-1' : 'grid-cols-1 sm:grid-cols-2',
        spacingClasses[spacing],
        className
      )}
    >
      {widgets.map((widget, index) => (
        <div key={index} className="w-full">
          {widget}
        </div>
      ))}
    </div>
  );
};

// Swipeable Panel Container
interface SwipeablePanelProps {
  panels: Array<{
    id: string;
    title: string;
    content: React.ReactNode;
    icon?: React.ReactNode;
  }>;
  activePanel: string;
  onPanelChange: (panelId: string) => void;
  className?: string;
}

export const SwipeablePanel: React.FC<SwipeablePanelProps> = ({
  panels,
  activePanel,
  onPanelChange,
  className
}) => {
  const { isMobile, isTablet } = useBreakpoint();
  const [isTransitioning, setIsTransitioning] = useState(false);

  const swipeHandlers = useSwipe({
    onSwipeLeft: () => {
      if (isMobile || isTablet) {
        const currentIndex = panels.findIndex(p => p.id === activePanel);
        const nextIndex = (currentIndex + 1) % panels.length;
        handlePanelChange(panels[nextIndex].id);
      }
    },
    onSwipeRight: () => {
      if (isMobile || isTablet) {
        const currentIndex = panels.findIndex(p => p.id === activePanel);
        const prevIndex = currentIndex === 0 ? panels.length - 1 : currentIndex - 1;
        handlePanelChange(panels[prevIndex].id);
      }
    },
    threshold: 50,
    velocityThreshold: 0.3
  });

  const handlePanelChange = (panelId: string) => {
    if (panelId === activePanel) return;
    
    setIsTransitioning(true);
    onPanelChange(panelId);
    
    // Reset transition state after animation
    setTimeout(() => setIsTransitioning(false), 300);
  };

  const activeIndex = panels.findIndex(p => p.id === activePanel);
  const activeContent = panels[activeIndex]?.content;

  return (
    <div className={cn("w-full h-full flex flex-col", className)}>
      {/* Panel Navigation */}
      <div className="flex-shrink-0 border-b border-border bg-card">
        <div className="flex overflow-x-auto scrollbar-hide">
          {panels.map((panel, index) => (
            <button
              key={panel.id}
              onClick={() => handlePanelChange(panel.id)}
              className={cn(
                "flex items-center space-x-2 px-4 py-3 text-sm font-medium transition-colors",
                "border-b-2 border-transparent",
                "min-w-0 flex-shrink-0",
                "touch-manipulation",
                panel.id === activePanel
                  ? "text-primary border-primary bg-primary/5"
                  : "text-muted-foreground hover:text-foreground hover:bg-muted/50"
              )}
            >
              {panel.icon && (
                <span className="flex-shrink-0">{panel.icon}</span>
              )}
              <span className="truncate">{panel.title}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Panel Content */}
      <div 
        className="flex-1 overflow-hidden relative"
        {...(isMobile || isTablet ? swipeHandlers : {})}
      >
        <div 
          className={cn(
            "w-full h-full transition-transform duration-300 ease-out",
            isTransitioning && "transform"
          )}
        >
          {activeContent}
        </div>
        
        {/* Swipe Indicators */}
        {(isMobile || isTablet) && panels.length > 1 && (
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
            <div className="flex space-x-2 bg-background/80 backdrop-blur-sm rounded-full px-3 py-2">
              {panels.map((_, index) => (
                <div
                  key={index}
                  className={cn(
                    "w-2 h-2 rounded-full transition-colors",
                    index === activeIndex ? "bg-primary" : "bg-muted-foreground/30"
                  )}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Mobile-Optimized Card
interface MobileCardProps {
  title: string;
  subtitle?: string;
  children: React.ReactNode;
  actions?: React.ReactNode;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
  className?: string;
}

export const MobileCard: React.FC<MobileCardProps> = ({
  title,
  subtitle,
  children,
  actions,
  collapsible = false,
  defaultCollapsed = false,
  className
}) => {
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);
  const { isMobile } = useBreakpoint();

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader 
        className={cn(
          "pb-3",
          collapsible && "cursor-pointer touch-manipulation"
        )}
        onClick={collapsible ? () => setIsCollapsed(!isCollapsed) : undefined}
      >
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className={cn(
              "text-lg",
              isMobile && "text-base"
            )}>
              {title}
            </CardTitle>
            {subtitle && (
              <Typography variant="body-sm" className="text-muted-foreground mt-1">
                {subtitle}
              </Typography>
            )}
          </div>
          
          <div className="flex items-center space-x-2 flex-shrink-0">
            {actions}
            {collapsible && (
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                {isCollapsed ? (
                  <ChevronRight className="w-4 h-4" />
                ) : (
                  <ChevronLeft className="w-4 h-4" />
                )}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      {!isCollapsed && (
        <CardContent className={cn(
          "pt-0",
          isMobile && "px-4 pb-4"
        )}>
          {children}
        </CardContent>
      )}
    </Card>
  );
};

// Mobile Action Sheet
interface MobileActionSheetProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  actions: Array<{
    label: string;
    icon?: React.ReactNode;
    onClick: () => void;
    variant?: 'default' | 'destructive';
    disabled?: boolean;
  }>;
}

export const MobileActionSheet: React.FC<MobileActionSheetProps> = ({
  isOpen,
  onClose,
  title,
  actions
}) => {
  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="bottom" className="h-auto max-h-[80vh]">
        <SheetHeader>
          <SheetTitle>{title}</SheetTitle>
        </SheetHeader>
        
        <div className="mt-6 space-y-2">
          {actions.map((action, index) => (
            <Button
              key={index}
              variant={action.variant === 'destructive' ? 'destructive' : 'ghost'}
              onClick={() => {
                action.onClick();
                onClose();
              }}
              disabled={action.disabled}
              className="w-full justify-start h-12 text-base"
            >
              {action.icon && (
                <span className="mr-3">{action.icon}</span>
              )}
              {action.label}
            </Button>
          ))}
        </div>
        
        <div className="mt-6 pt-4 border-t border-border">
          <Button
            variant="outline"
            onClick={onClose}
            className="w-full h-12 text-base"
          >
            Cancel
          </Button>
        </div>
      </SheetContent>
    </Sheet>
  );
};

// Mobile-Optimized Scroll Area
interface MobileScrollAreaProps {
  children: React.ReactNode;
  className?: string;
  maxHeight?: string;
  showScrollIndicator?: boolean;
}

export const MobileScrollArea: React.FC<MobileScrollAreaProps> = ({
  children,
  className,
  maxHeight = "400px",
  showScrollIndicator = true
}) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [canScrollDown, setCanScrollDown] = useState(false);

  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;
    setIsScrolled(scrollTop > 0);
    setCanScrollDown(scrollTop + clientHeight < scrollHeight - 10);
  };

  return (
    <div className={cn("relative", className)}>
      <ScrollArea 
        className="w-full"
        style={{ maxHeight }}
        onScrollCapture={handleScroll}
      >
        {children}
      </ScrollArea>
      
      {showScrollIndicator && (
        <>
          {/* Top scroll indicator */}
          {isScrolled && (
            <div className="absolute top-0 left-0 right-0 h-4 bg-gradient-to-b from-background to-transparent pointer-events-none z-10" />
          )}
          
          {/* Bottom scroll indicator */}
          {canScrollDown && (
            <div className="absolute bottom-0 left-0 right-0 h-4 bg-gradient-to-t from-background to-transparent pointer-events-none z-10" />
          )}
        </>
      )}
    </div>
  );
};

// Touch-friendly button with haptic feedback
interface TouchButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'sm' | 'md' | 'lg';
  hapticFeedback?: boolean;
}

export const TouchButton: React.FC<TouchButtonProps> = ({
  children,
  className,
  hapticFeedback = true,
  onClick,
  ...props
}) => {
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    // Trigger haptic feedback on supported devices
    if (hapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(10); // Very short vibration
    }
    
    onClick?.(event);
  };

  return (
    <Button
      {...props}
      onClick={handleClick}
      className={cn(
        "touch-manipulation transition-transform active:scale-95",
        "min-h-[44px] min-w-[44px]", // Ensure minimum touch target size
        className
      )}
    >
      {children}
    </Button>
  );
};

// Mobile Dashboard Layout Integration
interface MobileDashboardLayoutProps {
  performanceWidget: React.ReactNode;
  costWidget: React.ReactNode;
  usageWidget: React.ReactNode;
  healthWidget: React.ReactNode;
  activityWidget: React.ReactNode;
  notificationWidget: React.ReactNode;
  settingsWidget: React.ReactNode;
  className?: string;
}

export const MobileDashboardLayout: React.FC<MobileDashboardLayoutProps> = ({
  performanceWidget,
  costWidget,
  usageWidget,
  healthWidget,
  activityWidget,
  notificationWidget,
  settingsWidget,
  className
}) => {
  const { isMobile, isTablet } = useBreakpoint();
  const [activeTab, setActiveTab] = useState('overview');

  const dashboardPanels = [
    {
      id: 'overview',
      title: 'Overview',
      icon: <Activity className="w-4 h-4" />,
      content: (
        <MobileScrollArea className="p-4" maxHeight="calc(100vh - 200px)">
          <MobileWidgetGrid
            widgets={[
              performanceWidget,
              costWidget,
              healthWidget
            ]}
            spacing="md"
          />
        </MobileScrollArea>
      )
    },
    {
      id: 'analytics',
      title: 'Analytics',
      icon: <Zap className="w-4 h-4" />,
      content: (
        <MobileScrollArea className="p-4" maxHeight="calc(100vh - 200px)">
          <MobileWidgetGrid
            widgets={[
              usageWidget,
              activityWidget
            ]}
            spacing="md"
          />
        </MobileScrollArea>
      )
    },
    {
      id: 'notifications',
      title: 'Alerts',
      icon: <Menu className="w-4 h-4" />,
      content: (
        <div className="p-4">
          {notificationWidget}
        </div>
      )
    },
    {
      id: 'settings',
      title: 'Settings',
      icon: <Settings className="w-4 h-4" />,
      content: (
        <div className="p-4">
          {settingsWidget}
        </div>
      )
    }
  ];

  if (!isMobile && !isTablet) {
    // Desktop layout - return regular grid
    return (
      <div className={cn("grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6", className)}>
        {performanceWidget}
        {costWidget}
        {usageWidget}
        {healthWidget}
        {activityWidget}
        {notificationWidget}
        {settingsWidget}
      </div>
    );
  }

  // Mobile layout with swipeable panels
  return (
    <MobileDashboardContainer className={className}>
      <SwipeablePanel
        panels={dashboardPanels}
        activePanel={activeTab}
        onPanelChange={setActiveTab}
        className="h-full"
      />
    </MobileDashboardContainer>
  );
};

export default {
  MobileDashboardContainer,
  MobileWidgetGrid,
  SwipeablePanel,
  MobileCard,
  MobileActionSheet,
  MobileScrollArea,
  TouchButton,
  MobileDashboardLayout
};
