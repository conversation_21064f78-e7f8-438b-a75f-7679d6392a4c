import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  FolderOpen, 
  Save, 
  Plus, 
  Edit, 
  Trash2, 
  Download, 
  Upload,
  Copy,
  Star,
  StarOff,
  Clock,
  Code,
  Settings,
  FileText
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { HStack, VStack } from '@/components/ui/layout';
import { Typography } from '@/components/ui/typography';
import { formatDistanceToNow } from 'date-fns';

interface WorkspaceItem {
  id: string;
  name: string;
  description?: string;
  type: 'project' | 'template' | 'configuration';
  created_at: string;
  updated_at: string;
  is_favorite: boolean;
  tags: string[];
  data: {
    code?: string;
    prompt?: string;
    settings?: Record<string, any>;
    transformations?: any[];
  };
  metadata: {
    language?: string;
    framework?: string;
    size?: number;
    author?: string;
  };
}

interface WorkspaceManagerProps {
  className?: string;
  onLoad?: (item: WorkspaceItem) => void;
  currentData?: {
    code: string;
    prompt: string;
    settings: Record<string, any>;
  };
}

export const WorkspaceManager: React.FC<WorkspaceManagerProps> = ({
  className,
  onLoad,
  currentData
}) => {
  const [items, setItems] = useState<WorkspaceItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<WorkspaceItem[]>([]);
  const [selectedType, setSelectedType] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [saveForm, setSaveForm] = useState({
    name: '',
    description: '',
    type: 'project' as 'project' | 'template' | 'configuration',
    tags: ''
  });

  // Fetch workspace items
  const fetchItems = async () => {
    try {
      const response = await fetch('/api/workspace');
      if (response.ok) {
        const data = await response.json();
        setItems(data.items || []);
      }
    } catch (error) {
      console.error('Failed to fetch workspace items:', error);
      // Mock data for demonstration
      const mockItems: WorkspaceItem[] = [
        {
          id: '1',
          name: 'React Component Refactor',
          description: 'Convert class components to functional components with hooks',
          type: 'template',
          created_at: new Date(Date.now() - 86400000).toISOString(),
          updated_at: new Date(Date.now() - 3600000).toISOString(),
          is_favorite: true,
          tags: ['react', 'hooks', 'refactor'],
          data: {
            prompt: 'Convert this React class component to a functional component using hooks',
            settings: { model: 'gpt-4-turbo', temperature: 0.3 }
          },
          metadata: {
            language: 'javascript',
            framework: 'react',
            author: '<EMAIL>'
          }
        },
        {
          id: '2',
          name: 'API Integration Project',
          description: 'Complete project with REST API integration',
          type: 'project',
          created_at: new Date(Date.now() - 172800000).toISOString(),
          updated_at: new Date(Date.now() - 7200000).toISOString(),
          is_favorite: false,
          tags: ['api', 'integration', 'typescript'],
          data: {
            code: 'const api = {\n  baseURL: "https://api.example.com",\n  // ...\n};',
            prompt: 'Add error handling and retry logic to this API client',
            settings: { model: 'claude-3-sonnet', maxTokens: 4000 }
          },
          metadata: {
            language: 'typescript',
            size: 1024,
            author: '<EMAIL>'
          }
        }
      ];
      setItems(mockItems);
    }
  };

  useEffect(() => {
    fetchItems();
  }, []);

  // Filter items
  useEffect(() => {
    let filtered = items;

    if (selectedType !== 'all') {
      filtered = filtered.filter(item => item.type === selectedType);
    }

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(query) ||
        item.description?.toLowerCase().includes(query) ||
        item.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Sort by favorites first, then by updated date
    filtered.sort((a, b) => {
      if (a.is_favorite && !b.is_favorite) return -1;
      if (!a.is_favorite && b.is_favorite) return 1;
      return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
    });

    setFilteredItems(filtered);
  }, [items, selectedType, searchQuery]);

  const handleSave = async () => {
    if (!currentData || !saveForm.name) return;

    try {
      const newItem: Omit<WorkspaceItem, 'id' | 'created_at' | 'updated_at'> = {
        name: saveForm.name,
        description: saveForm.description,
        type: saveForm.type,
        is_favorite: false,
        tags: saveForm.tags.split(',').map(tag => tag.trim()).filter(Boolean),
        data: {
          code: currentData.code,
          prompt: currentData.prompt,
          settings: currentData.settings
        },
        metadata: {
          size: currentData.code.length,
          author: '<EMAIL>'
        }
      };

      const response = await fetch('/api/workspace', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newItem)
      });

      if (response.ok) {
        setShowSaveDialog(false);
        setSaveForm({ name: '', description: '', type: 'project', tags: '' });
        fetchItems();
      }
    } catch (error) {
      console.error('Failed to save workspace item:', error);
    }
  };

  const handleLoad = (item: WorkspaceItem) => {
    onLoad?.(item);
  };

  const handleDelete = async (itemId: string) => {
    if (!confirm('Are you sure you want to delete this item?')) return;

    try {
      const response = await fetch(`/api/workspace/${itemId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        setItems(prev => prev.filter(item => item.id !== itemId));
      }
    } catch (error) {
      console.error('Failed to delete workspace item:', error);
    }
  };

  const handleToggleFavorite = async (itemId: string) => {
    try {
      const item = items.find(i => i.id === itemId);
      if (!item) return;

      const response = await fetch(`/api/workspace/${itemId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_favorite: !item.is_favorite })
      });

      if (response.ok) {
        setItems(prev => prev.map(i => 
          i.id === itemId ? { ...i, is_favorite: !i.is_favorite } : i
        ));
      }
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
    }
  };

  const handleExport = (item: WorkspaceItem) => {
    const exportData = {
      name: item.name,
      description: item.description,
      type: item.type,
      tags: item.tags,
      data: item.data,
      metadata: item.metadata,
      exported_at: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${item.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const text = await file.text();
      const importData = JSON.parse(text);

      const response = await fetch('/api/workspace/import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(importData)
      });

      if (response.ok) {
        fetchItems();
        setShowImportDialog(false);
      }
    } catch (error) {
      console.error('Failed to import workspace item:', error);
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'project': return <FolderOpen className="w-4 h-4" />;
      case 'template': return <FileText className="w-4 h-4" />;
      case 'configuration': return <Settings className="w-4 h-4" />;
      default: return <Code className="w-4 h-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'project': return 'text-blue-500';
      case 'template': return 'text-green-500';
      case 'configuration': return 'text-purple-500';
      default: return 'text-gray-500';
    }
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <FolderOpen className="w-6 h-6 text-primary" />
            <div>
              <CardTitle className="text-xl">Workspace</CardTitle>
              <CardDescription>
                Save, organize, and reuse your projects, templates, and configurations
              </CardDescription>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
              <DialogTrigger asChild>
                <Button disabled={!currentData}>
                  <Save className="w-4 h-4 mr-2" />
                  Save Current
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Save to Workspace</DialogTitle>
                  <DialogDescription>
                    Save your current work to reuse later
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Name *</Label>
                    <Input
                      value={saveForm.name}
                      onChange={(e) => setSaveForm(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="My Project"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Description</Label>
                    <Textarea
                      value={saveForm.description}
                      onChange={(e) => setSaveForm(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Brief description of this item"
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Type</Label>
                      <select
                        value={saveForm.type}
                        onChange={(e) => setSaveForm(prev => ({ ...prev, type: e.target.value as any }))}
                        className="w-full px-3 py-2 border border-border rounded-md bg-background"
                      >
                        <option value="project">Project</option>
                        <option value="template">Template</option>
                        <option value="configuration">Configuration</option>
                      </select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label>Tags</Label>
                      <Input
                        value={saveForm.tags}
                        onChange={(e) => setSaveForm(prev => ({ ...prev, tags: e.target.value }))}
                        placeholder="react, api, typescript"
                      />
                    </div>
                  </div>
                  
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setShowSaveDialog(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleSave} disabled={!saveForm.name}>
                      Save
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>

            <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Upload className="w-4 h-4 mr-2" />
                  Import
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Import Workspace Item</DialogTitle>
                  <DialogDescription>
                    Import a previously exported workspace item
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Select File</Label>
                    <Input
                      type="file"
                      accept=".json"
                      onChange={handleImport}
                    />
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <Input
              placeholder="Search workspace..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="px-3 py-2 border border-border rounded-md bg-background"
          >
            <option value="all">All Types</option>
            <option value="project">Projects</option>
            <option value="template">Templates</option>
            <option value="configuration">Configurations</option>
          </select>
        </div>

        {/* Items List */}
        <ScrollArea className="h-96">
          <div className="space-y-3">
            {filteredItems.length === 0 ? (
              <div className="text-center py-8">
                <FolderOpen className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                <Typography variant="body-sm" className="text-muted-foreground">
                  {items.length === 0 ? 'No workspace items yet' : 'No items match your search'}
                </Typography>
              </div>
            ) : (
              filteredItems.map((item) => (
                <Card key={item.id} className="p-4 hover:bg-card/80 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-2">
                        <div className={cn("flex items-center space-x-1", getTypeColor(item.type))}>
                          {getTypeIcon(item.type)}
                          <Typography variant="body-sm" className="font-medium">
                            {item.name}
                          </Typography>
                        </div>
                        {item.is_favorite && (
                          <Star className="w-4 h-4 text-yellow-500 fill-current" />
                        )}
                        <Badge variant="outline" className="text-xs">
                          {item.type}
                        </Badge>
                      </div>
                      
                      {item.description && (
                        <Typography variant="caption" className="text-muted-foreground mb-2">
                          {item.description}
                        </Typography>
                      )}
                      
                      <div className="flex flex-wrap gap-1 mb-2">
                        {item.tags.map((tag) => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      
                      <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                        <div className="flex items-center space-x-1">
                          <Clock className="w-3 h-3" />
                          <span>{formatDistanceToNow(new Date(item.updated_at), { addSuffix: true })}</span>
                        </div>
                        {item.metadata.language && (
                          <span>{item.metadata.language}</span>
                        )}
                        {item.metadata.size && (
                          <span>{(item.metadata.size / 1024).toFixed(1)}KB</span>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleToggleFavorite(item.id)}
                      >
                        {item.is_favorite ? (
                          <Star className="w-4 h-4 text-yellow-500 fill-current" />
                        ) : (
                          <StarOff className="w-4 h-4" />
                        )}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleLoad(item)}
                      >
                        <FolderOpen className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleExport(item)}
                      >
                        <Download className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(item.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </Card>
              ))
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};

export default WorkspaceManager;
