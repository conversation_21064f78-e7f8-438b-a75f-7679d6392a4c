import { EventEmitter } from 'events';
import { loggingService } from './loggingService.js';
import { supabaseClient } from './supabase.js';
import { autonomousProcessor } from './autonomousProcessor.js';
import { repositoryMonitor } from './repositoryMonitor.js';

export interface CostLimit {
  repositoryId: string;
  dailyLimit: number; // USD
  weeklyLimit: number; // USD
  monthlyLimit: number; // USD
  perOperationLimit: number; // USD
  alertThreshold: number; // 0-1 (percentage of limit)
  enabled: boolean;
}

export interface CostAlert {
  id: string;
  repositoryId: string;
  alertType: 'approaching_limit' | 'limit_exceeded' | 'budget_exhausted' | 'unusual_spending';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  currentCost: number;
  limit: number;
  percentage: number;
  timestamp: Date;
  acknowledged: boolean;
}

export interface CostPrediction {
  repositoryId: string;
  timeframe: 'daily' | 'weekly' | 'monthly';
  currentCost: number;
  predictedCost: number;
  confidence: number; // 0-1
  trend: 'increasing' | 'stable' | 'decreasing';
  projectedLimitReach?: Date;
  recommendations: string[];
}

export interface CostBreakdown {
  repositoryId: string;
  timeframe: 'daily' | 'weekly' | 'monthly';
  totalCost: number;
  breakdown: {
    scanning: number;
    planning: number;
    criticism: number;
    testing: number;
    prCreation: number;
  };
  operationCounts: {
    scans: number;
    improvements: number;
    prsCreated: number;
  };
  averageCostPerOperation: number;
}

class CostGuardService extends EventEmitter {
  private costLimits: Map<string, CostLimit> = new Map();
  private activeAlerts: Map<string, CostAlert[]> = new Map();
  private costHistory: Map<string, any[]> = new Map();
  private emergencyShutdown: Set<string> = new Set();
  
  // Cost tracking intervals
  private dailyCosts: Map<string, number> = new Map();
  private weeklyCosts: Map<string, number> = new Map();
  private monthlyCosts: Map<string, number> = new Map();
  
  // Time tracking for resets
  private lastDailyReset = new Date().toDateString();
  private lastWeeklyReset = this.getWeekString(new Date());
  private lastMonthlyReset = this.getMonthString(new Date());

  constructor() {
    super();
    this.loadCostLimits();
    this.startCostTracking();
    this.setupEventListeners();
    
    // Reset costs at appropriate intervals
    setInterval(() => this.resetCostsIfNeeded(), 60 * 60 * 1000); // Check every hour
    
    // Generate predictions every 6 hours
    setInterval(() => this.generatePredictions(), 6 * 60 * 60 * 1000);
    
    // Clean up old alerts daily
    setInterval(() => this.cleanupOldAlerts(), 24 * 60 * 60 * 1000);
  }

  /**
   * Check if an operation is allowed based on cost limits
   */
  async checkOperationAllowed(
    repositoryId: string,
    operationType: 'scan' | 'improve' | 'test' | 'pr',
    estimatedCost: number
  ): Promise<{ allowed: boolean; reason?: string; alert?: CostAlert }> {
    // Check if repository is in emergency shutdown
    if (this.emergencyShutdown.has(repositoryId)) {
      return {
        allowed: false,
        reason: 'Repository in emergency shutdown due to cost limits'
      };
    }

    const limits = this.costLimits.get(repositoryId);
    if (!limits || !limits.enabled) {
      return { allowed: true }; // No limits configured
    }

    // Check per-operation limit
    if (estimatedCost > limits.perOperationLimit) {
      const alert = await this.createAlert(repositoryId, {
        alertType: 'limit_exceeded',
        severity: 'high',
        message: `Operation cost $${estimatedCost.toFixed(4)} exceeds per-operation limit $${limits.perOperationLimit.toFixed(4)}`,
        currentCost: estimatedCost,
        limit: limits.perOperationLimit,
        percentage: (estimatedCost / limits.perOperationLimit) * 100
      });

      return {
        allowed: false,
        reason: `Operation cost exceeds per-operation limit`,
        alert
      };
    }

    // Check daily limit
    const dailyCost = this.getDailyCost(repositoryId);
    const projectedDailyCost = dailyCost + estimatedCost;
    
    if (projectedDailyCost > limits.dailyLimit) {
      const alert = await this.createAlert(repositoryId, {
        alertType: 'limit_exceeded',
        severity: 'critical',
        message: `Projected daily cost $${projectedDailyCost.toFixed(4)} would exceed limit $${limits.dailyLimit.toFixed(4)}`,
        currentCost: projectedDailyCost,
        limit: limits.dailyLimit,
        percentage: (projectedDailyCost / limits.dailyLimit) * 100
      });

      return {
        allowed: false,
        reason: 'Would exceed daily cost limit',
        alert
      };
    }

    // Check if approaching limits (alert threshold)
    const dailyPercentage = projectedDailyCost / limits.dailyLimit;
    if (dailyPercentage >= limits.alertThreshold) {
      const alert = await this.createAlert(repositoryId, {
        alertType: 'approaching_limit',
        severity: dailyPercentage >= 0.9 ? 'high' : 'medium',
        message: `Approaching daily cost limit: ${(dailyPercentage * 100).toFixed(1)}% of $${limits.dailyLimit.toFixed(4)}`,
        currentCost: projectedDailyCost,
        limit: limits.dailyLimit,
        percentage: dailyPercentage * 100
      });

      // Still allow operation but with warning
      return {
        allowed: true,
        alert
      };
    }

    return { allowed: true };
  }

  /**
   * Record a cost for an operation
   */
  async recordCost(
    repositoryId: string,
    operationType: 'scan' | 'improve' | 'test' | 'pr',
    actualCost: number,
    metadata?: any
  ): Promise<void> {
    this.resetCostsIfNeeded();

    // Update cost tracking
    this.addToDailyCost(repositoryId, actualCost);
    this.addToWeeklyCost(repositoryId, actualCost);
    this.addToMonthlyCost(repositoryId, actualCost);

    // Store in database for analytics
    await this.storeCostRecord(repositoryId, operationType, actualCost, metadata);

    // Check for unusual spending patterns
    await this.checkUnusualSpending(repositoryId, operationType, actualCost);

    // Emit cost recorded event
    this.emit('cost-recorded', {
      repositoryId,
      operationType,
      actualCost,
      dailyTotal: this.getDailyCost(repositoryId),
      weeklyTotal: this.getWeeklyCost(repositoryId),
      monthlyTotal: this.getMonthlyCost(repositoryId)
    });

    await loggingService.log({
      level: 'info',
      message: 'Cost recorded',
      service: 'cost-guard',
      metadata: {
        repositoryId,
        operationType,
        actualCost,
        dailyTotal: this.getDailyCost(repositoryId),
        metadata
      }
    });
  }

  /**
   * Set cost limits for a repository
   */
  async setCostLimits(repositoryId: string, limits: Omit<CostLimit, 'repositoryId'>): Promise<void> {
    const costLimit: CostLimit = {
      repositoryId,
      ...limits
    };

    this.costLimits.set(repositoryId, costLimit);

    // Store in database
    await supabaseClient
      .from('cost_limits')
      .upsert({
        repository_id: repositoryId,
        daily_limit: limits.dailyLimit,
        weekly_limit: limits.weeklyLimit,
        monthly_limit: limits.monthlyLimit,
        per_operation_limit: limits.perOperationLimit,
        alert_threshold: limits.alertThreshold,
        enabled: limits.enabled,
        updated_at: new Date().toISOString()
      }, { onConflict: 'repository_id' });

    // Remove from emergency shutdown if limits are increased
    if (limits.enabled) {
      this.emergencyShutdown.delete(repositoryId);
    }

    await loggingService.log({
      level: 'info',
      message: 'Cost limits updated',
      service: 'cost-guard',
      metadata: {
        repositoryId,
        limits
      }
    });

    this.emit('limits-updated', { repositoryId, limits: costLimit });
  }

  /**
   * Get cost breakdown for a repository
   */
  async getCostBreakdown(
    repositoryId: string,
    timeframe: 'daily' | 'weekly' | 'monthly' = 'daily'
  ): Promise<CostBreakdown> {
    let startDate: Date;
    const now = new Date();

    switch (timeframe) {
      case 'daily':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'weekly':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'monthly':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
    }

    const { data: costs, error } = await supabaseClient
      .from('autonomous_costs')
      .select('*')
      .eq('repository_id', repositoryId)
      .gte('timestamp', startDate.toISOString());

    if (error) {
      throw error;
    }

    const breakdown = {
      scanning: 0,
      planning: 0,
      criticism: 0,
      testing: 0,
      prCreation: 0
    };

    const operationCounts = {
      scans: 0,
      improvements: 0,
      prsCreated: 0
    };

    let totalCost = 0;

    if (costs) {
      for (const cost of costs) {
        totalCost += parseFloat(cost.cost_usd);

        switch (cost.operation_type) {
          case 'scan':
            breakdown.scanning += parseFloat(cost.cost_usd);
            operationCounts.scans++;
            break;
          case 'improve':
            if (cost.metadata?.phase === 'planning') {
              breakdown.planning += parseFloat(cost.cost_usd);
            } else if (cost.metadata?.phase === 'criticism') {
              breakdown.criticism += parseFloat(cost.cost_usd);
            }
            operationCounts.improvements++;
            break;
          case 'test':
            breakdown.testing += parseFloat(cost.cost_usd);
            break;
          case 'pr':
            breakdown.prCreation += parseFloat(cost.cost_usd);
            operationCounts.prsCreated++;
            break;
        }
      }
    }

    const totalOperations = operationCounts.scans + operationCounts.improvements + operationCounts.prsCreated;
    const averageCostPerOperation = totalOperations > 0 ? totalCost / totalOperations : 0;

    return {
      repositoryId,
      timeframe,
      totalCost,
      breakdown,
      operationCounts,
      averageCostPerOperation
    };
  }

  /**
   * Generate cost predictions
   */
  async generateCostPrediction(
    repositoryId: string,
    timeframe: 'daily' | 'weekly' | 'monthly' = 'daily'
  ): Promise<CostPrediction> {
    const breakdown = await this.getCostBreakdown(repositoryId, timeframe);
    const limits = this.costLimits.get(repositoryId);

    // Get historical data for trend analysis
    const historicalData = await this.getHistoricalCosts(repositoryId, timeframe, 7); // Last 7 periods

    // Calculate trend
    const trend = this.calculateTrend(historicalData);
    const trendMultiplier = trend === 'increasing' ? 1.2 : trend === 'decreasing' ? 0.8 : 1.0;

    // Predict based on current usage patterns and trend
    let predictedCost = breakdown.totalCost;
    let confidence = 0.7;

    if (breakdown.operationCounts.scans > 0) {
      // Predict based on operation frequency
      const avgCostPerScan = breakdown.breakdown.scanning / breakdown.operationCounts.scans;
      const expectedScans = this.predictOperationFrequency(repositoryId, 'scan', timeframe);
      predictedCost = expectedScans * avgCostPerScan * trendMultiplier;
      confidence = 0.8;
    }

    // Adjust for time remaining in period
    const timeRemaining = this.getTimeRemainingInPeriod(timeframe);
    if (timeRemaining > 0 && timeRemaining < 1) {
      predictedCost = breakdown.totalCost + (predictedCost * timeRemaining);
      confidence *= 0.9;
    }

    // Generate recommendations
    const recommendations: string[] = [];
    
    if (limits) {
      const limit = timeframe === 'daily' ? limits.dailyLimit : 
                   timeframe === 'weekly' ? limits.weeklyLimit : 
                   limits.monthlyLimit;

      if (predictedCost > limit * 0.8) {
        recommendations.push('Consider reducing scan frequency to stay within budget');
      }
      
      if (breakdown.breakdown.planning > breakdown.breakdown.criticism) {
        recommendations.push('Optimize planner prompts to reduce token usage');
      }

      if (breakdown.operationCounts.improvements > breakdown.operationCounts.scans * 0.5) {
        recommendations.push('Review improvement criteria to be more selective');
      }
    }

    // Calculate when limit might be reached
    let projectedLimitReach: Date | undefined;
    if (limits && predictedCost > 0) {
      const limit = timeframe === 'daily' ? limits.dailyLimit : 
                   timeframe === 'weekly' ? limits.weeklyLimit : 
                   limits.monthlyLimit;
      
      if (predictedCost > limit) {
        const daysToLimit = (limit / predictedCost) * this.getDaysInPeriod(timeframe);
        projectedLimitReach = new Date(Date.now() + daysToLimit * 24 * 60 * 60 * 1000);
      }
    }

    return {
      repositoryId,
      timeframe,
      currentCost: breakdown.totalCost,
      predictedCost,
      confidence,
      trend,
      projectedLimitReach,
      recommendations
    };
  }

  /**
   * Load cost limits from database
   */
  private async loadCostLimits(): Promise<void> {
    try {
      const { data: limits, error } = await supabaseClient
        .from('cost_limits')
        .select('*')
        .eq('enabled', true);

      if (error) {
        throw error;
      }

      if (limits) {
        for (const limit of limits) {
          const costLimit: CostLimit = {
            repositoryId: limit.repository_id,
            dailyLimit: parseFloat(limit.daily_limit),
            weeklyLimit: parseFloat(limit.weekly_limit),
            monthlyLimit: parseFloat(limit.monthly_limit),
            perOperationLimit: parseFloat(limit.per_operation_limit),
            alertThreshold: parseFloat(limit.alert_threshold),
            enabled: limit.enabled
          };

          this.costLimits.set(limit.repository_id, costLimit);
        }

        await loggingService.log({
          level: 'info',
          message: 'Cost limits loaded from database',
          service: 'cost-guard',
          metadata: {
            limitsCount: limits.length
          }
        });
      }

    } catch (error) {
      await loggingService.log({
        level: 'error',
        message: 'Failed to load cost limits from database',
        service: 'cost-guard',
        metadata: {
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      });
    }
  }

  /**
   * Start cost tracking and monitoring
   */
  private startCostTracking(): void {
    // Load current costs from database
    this.loadCurrentCosts();

    // Monitor for cost threshold breaches every 5 minutes
    setInterval(async () => {
      for (const [repositoryId, limits] of this.costLimits) {
        if (!limits.enabled) continue;

        const dailyCost = this.getDailyCost(repositoryId);
        const dailyPercentage = dailyCost / limits.dailyLimit;

        if (dailyPercentage >= limits.alertThreshold && dailyPercentage < 1.0) {
          await this.createAlert(repositoryId, {
            alertType: 'approaching_limit',
            severity: dailyPercentage >= 0.9 ? 'high' : 'medium',
            message: `Daily cost approaching limit: ${(dailyPercentage * 100).toFixed(1)}% of $${limits.dailyLimit.toFixed(4)}`,
            currentCost: dailyCost,
            limit: limits.dailyLimit,
            percentage: dailyPercentage * 100
          });
        } else if (dailyPercentage >= 1.0) {
          // Emergency shutdown
          this.emergencyShutdown.add(repositoryId);

          await this.createAlert(repositoryId, {
            alertType: 'budget_exhausted',
            severity: 'critical',
            message: `Daily budget exhausted! Repository operations suspended. Cost: $${dailyCost.toFixed(4)} / $${limits.dailyLimit.toFixed(4)}`,
            currentCost: dailyCost,
            limit: limits.dailyLimit,
            percentage: dailyPercentage * 100
          });

          // Cancel all queued jobs for this repository
          await this.cancelRepositoryJobs(repositoryId);
        }
      }
    }, 5 * 60 * 1000);
  }

  /**
   * Setup event listeners for cost tracking
   */
  private setupEventListeners(): void {
    // Listen for autonomous processor cost events
    autonomousProcessor.on('job-completed', async ({ job, result }) => {
      if (result.cost > 0) {
        await this.recordCost(
          job.repositoryId,
          job.type,
          result.cost,
          {
            jobId: job.id,
            duration: result.duration,
            success: result.success
          }
        );
      }
    });

    // Listen for repository monitor scan costs
    repositoryMonitor.on('scan-completed', async (scanResult) => {
      if (scanResult.totalEstimatedCost > 0) {
        await this.recordCost(
          scanResult.repositoryId,
          'scan',
          scanResult.totalEstimatedCost,
          {
            scanId: scanResult.scanId,
            filesScanned: scanResult.filesScanned,
            opportunitiesFound: scanResult.opportunitiesFound
          }
        );
      }
    });
  }

  /**
   * Reset costs if time periods have changed
   */
  private resetCostsIfNeeded(): void {
    const today = new Date().toDateString();
    const thisWeek = this.getWeekString(new Date());
    const thisMonth = this.getMonthString(new Date());

    // Reset daily costs
    if (this.lastDailyReset !== today) {
      this.dailyCosts.clear();
      this.lastDailyReset = today;

      // Remove emergency shutdowns on new day
      this.emergencyShutdown.clear();
    }

    // Reset weekly costs
    if (this.lastWeeklyReset !== thisWeek) {
      this.weeklyCosts.clear();
      this.lastWeeklyReset = thisWeek;
    }

    // Reset monthly costs
    if (this.lastMonthlyReset !== thisMonth) {
      this.monthlyCosts.clear();
      this.lastMonthlyReset = thisMonth;
    }
  }

  private getDailyCost(repositoryId: string): number {
    this.resetCostsIfNeeded();
    return this.dailyCosts.get(repositoryId) || 0;
  }

  private addToDailyCost(repositoryId: string, cost: number): void {
    this.resetCostsIfNeeded();
    const current = this.dailyCosts.get(repositoryId) || 0;
    this.dailyCosts.set(repositoryId, current + cost);
  }

  private getWeeklyCost(repositoryId: string): number {
    this.resetCostsIfNeeded();
    return this.weeklyCosts.get(repositoryId) || 0;
  }

  private addToWeeklyCost(repositoryId: string, cost: number): void {
    this.resetCostsIfNeeded();
    const current = this.weeklyCosts.get(repositoryId) || 0;
    this.weeklyCosts.set(repositoryId, current + cost);
  }

  private getMonthlyCost(repositoryId: string): number {
    this.resetCostsIfNeeded();
    return this.monthlyCosts.get(repositoryId) || 0;
  }

  private addToMonthlyCost(repositoryId: string, cost: number): void {
    this.resetCostsIfNeeded();
    const current = this.monthlyCosts.get(repositoryId) || 0;
    this.monthlyCosts.set(repositoryId, current + cost);
  }

  private getWeekString(date: Date): string {
    const year = date.getFullYear();
    const week = Math.ceil((date.getTime() - new Date(year, 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000));
    return `${year}-W${week}`;
  }

  private getMonthString(date: Date): string {
    return `${date.getFullYear()}-${date.getMonth() + 1}`;
  }

  /**
   * Create a cost alert
   */
  private async createAlert(repositoryId: string, alertData: Omit<CostAlert, 'id' | 'repositoryId' | 'timestamp' | 'acknowledged'>): Promise<CostAlert> {
    const alertId = `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const alert: CostAlert = {
      id: alertId,
      repositoryId,
      timestamp: new Date(),
      acknowledged: false,
      ...alertData
    };

    // Store alert in memory
    if (!this.activeAlerts.has(repositoryId)) {
      this.activeAlerts.set(repositoryId, []);
    }
    this.activeAlerts.get(repositoryId)!.push(alert);

    // Store in database
    await supabaseClient
      .from('cost_alerts')
      .insert({
        id: alert.id,
        repository_id: repositoryId,
        alert_type: alert.alertType,
        severity: alert.severity,
        message: alert.message,
        current_cost: alert.currentCost,
        limit_value: alert.limit,
        percentage: alert.percentage,
        acknowledged: false,
        created_at: alert.timestamp.toISOString()
      });

    await loggingService.log({
      level: alert.severity === 'critical' ? 'error' : 'warn',
      message: 'Cost alert created',
      service: 'cost-guard',
      metadata: {
        alertId: alert.id,
        repositoryId,
        alertType: alert.alertType,
        severity: alert.severity,
        message: alert.message,
        currentCost: alert.currentCost,
        limit: alert.limit
      }
    });

    this.emit('alert-created', alert);
    return alert;
  }

  /**
   * Store cost record in database
   */
  private async storeCostRecord(repositoryId: string, operationType: string, cost: number, metadata?: any): Promise<void> {
    try {
      await supabaseClient
        .from('autonomous_costs')
        .insert({
          repository_id: repositoryId,
          operation_type: operationType,
          provider: metadata?.provider || 'system',
          model: metadata?.model || 'unknown',
          tokens_used: metadata?.tokensUsed || 0,
          cost_usd: cost,
          timestamp: new Date().toISOString(),
          metadata: metadata || {}
        });

    } catch (error) {
      await loggingService.log({
        level: 'error',
        message: 'Failed to store cost record',
        service: 'cost-guard',
        metadata: {
          repositoryId,
          operationType,
          cost,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      });
    }
  }

  /**
   * Check for unusual spending patterns
   */
  private async checkUnusualSpending(repositoryId: string, operationType: string, cost: number): Promise<void> {
    // Get recent costs for this operation type
    const recentCosts = await this.getRecentOperationCosts(repositoryId, operationType, 10);

    if (recentCosts.length < 3) return; // Need at least 3 data points

    const average = recentCosts.reduce((sum, c) => sum + c, 0) / recentCosts.length;
    const threshold = average * 3; // 3x average is considered unusual

    if (cost > threshold) {
      await this.createAlert(repositoryId, {
        alertType: 'unusual_spending',
        severity: 'medium',
        message: `Unusual spending detected: $${cost.toFixed(4)} for ${operationType} (avg: $${average.toFixed(4)})`,
        currentCost: cost,
        limit: threshold,
        percentage: (cost / threshold) * 100
      });
    }
  }

  /**
   * Get historical costs for trend analysis
   */
  private async getHistoricalCosts(repositoryId: string, timeframe: string, periods: number): Promise<number[]> {
    const costs: number[] = [];
    const now = new Date();

    for (let i = periods - 1; i >= 0; i--) {
      let startDate: Date;
      let endDate: Date;

      switch (timeframe) {
        case 'daily':
          startDate = new Date(now.getTime() - (i + 1) * 24 * 60 * 60 * 1000);
          endDate = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
          break;
        case 'weekly':
          startDate = new Date(now.getTime() - (i + 1) * 7 * 24 * 60 * 60 * 1000);
          endDate = new Date(now.getTime() - i * 7 * 24 * 60 * 60 * 1000);
          break;
        case 'monthly':
          startDate = new Date(now.getFullYear(), now.getMonth() - (i + 1), 1);
          endDate = new Date(now.getFullYear(), now.getMonth() - i, 1);
          break;
        default:
          continue;
      }

      const { data: periodCosts } = await supabaseClient
        .from('autonomous_costs')
        .select('cost_usd')
        .eq('repository_id', repositoryId)
        .gte('timestamp', startDate.toISOString())
        .lt('timestamp', endDate.toISOString());

      const totalCost = periodCosts?.reduce((sum, record) => sum + parseFloat(record.cost_usd), 0) || 0;
      costs.push(totalCost);
    }

    return costs;
  }

  /**
   * Calculate trend from historical data
   */
  private calculateTrend(data: number[]): 'increasing' | 'stable' | 'decreasing' {
    if (data.length < 2) return 'stable';

    const recent = data.slice(-3); // Last 3 periods
    const older = data.slice(0, -3); // Earlier periods

    if (recent.length === 0 || older.length === 0) return 'stable';

    const recentAvg = recent.reduce((sum, val) => sum + val, 0) / recent.length;
    const olderAvg = older.reduce((sum, val) => sum + val, 0) / older.length;

    const changePercent = olderAvg > 0 ? (recentAvg - olderAvg) / olderAvg : 0;

    if (changePercent > 0.2) return 'increasing';
    if (changePercent < -0.2) return 'decreasing';
    return 'stable';
  }

  /**
   * Predict operation frequency based on historical patterns
   */
  private predictOperationFrequency(repositoryId: string, operationType: string, timeframe: string): number {
    // This is a simplified prediction - in production, this would use more sophisticated algorithms
    const baseFrequency = {
      scan: timeframe === 'daily' ? 24 : timeframe === 'weekly' ? 168 : 720, // hourly scans
      improve: timeframe === 'daily' ? 5 : timeframe === 'weekly' ? 35 : 150,
      test: timeframe === 'daily' ? 3 : timeframe === 'weekly' ? 21 : 90,
      pr: timeframe === 'daily' ? 2 : timeframe === 'weekly' ? 14 : 60
    };

    return baseFrequency[operationType] || 1;
  }

  /**
   * Get time remaining in current period
   */
  private getTimeRemainingInPeriod(timeframe: string): number {
    const now = new Date();

    switch (timeframe) {
      case 'daily':
        const hoursRemaining = 24 - now.getHours();
        return hoursRemaining / 24;
      case 'weekly':
        const daysRemainingInWeek = 7 - now.getDay();
        return daysRemainingInWeek / 7;
      case 'monthly':
        const daysInMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate();
        const daysRemainingInMonth = daysInMonth - now.getDate();
        return daysRemainingInMonth / daysInMonth;
      default:
        return 0.5;
    }
  }

  private getDaysInPeriod(timeframe: string): number {
    switch (timeframe) {
      case 'daily': return 1;
      case 'weekly': return 7;
      case 'monthly': return 30;
      default: return 1;
    }
  }

  /**
   * Generate predictions for all repositories
   */
  private async generatePredictions(): Promise<void> {
    for (const repositoryId of this.costLimits.keys()) {
      try {
        const prediction = await this.generateCostPrediction(repositoryId, 'daily');

        // Store prediction in database for analytics
        await supabaseClient
          .from('cost_predictions')
          .upsert({
            repository_id: repositoryId,
            timeframe: prediction.timeframe,
            current_cost: prediction.currentCost,
            predicted_cost: prediction.predictedCost,
            confidence: prediction.confidence,
            trend: prediction.trend,
            projected_limit_reach: prediction.projectedLimitReach?.toISOString(),
            recommendations: prediction.recommendations,
            created_at: new Date().toISOString()
          }, { onConflict: 'repository_id,timeframe' });

        this.emit('prediction-generated', prediction);

      } catch (error) {
        await loggingService.log({
          level: 'error',
          message: 'Failed to generate cost prediction',
          service: 'cost-guard',
          metadata: {
            repositoryId,
            error: error instanceof Error ? error.message : 'Unknown error'
          }
        });
      }
    }
  }

  /**
   * Clean up old alerts
   */
  private cleanupOldAlerts(): void {
    const cutoffTime = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 days ago

    for (const [repositoryId, alerts] of this.activeAlerts.entries()) {
      const filteredAlerts = alerts.filter(alert => alert.timestamp > cutoffTime);

      if (filteredAlerts.length === 0) {
        this.activeAlerts.delete(repositoryId);
      } else {
        this.activeAlerts.set(repositoryId, filteredAlerts);
      }
    }
  }

  /**
   * Load current costs from database
   */
  private async loadCurrentCosts(): Promise<void> {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const startOfWeek = new Date(today.getTime() - today.getDay() * 24 * 60 * 60 * 1000);
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    try {
      // Load daily costs
      const { data: dailyCosts } = await supabaseClient
        .from('autonomous_costs')
        .select('repository_id, cost_usd')
        .gte('timestamp', startOfDay.toISOString());

      if (dailyCosts) {
        const costsByRepo = new Map<string, number>();
        for (const cost of dailyCosts) {
          const current = costsByRepo.get(cost.repository_id) || 0;
          costsByRepo.set(cost.repository_id, current + parseFloat(cost.cost_usd));
        }
        this.dailyCosts = costsByRepo;
      }

      // Load weekly costs
      const { data: weeklyCosts } = await supabaseClient
        .from('autonomous_costs')
        .select('repository_id, cost_usd')
        .gte('timestamp', startOfWeek.toISOString());

      if (weeklyCosts) {
        const costsByRepo = new Map<string, number>();
        for (const cost of weeklyCosts) {
          const current = costsByRepo.get(cost.repository_id) || 0;
          costsByRepo.set(cost.repository_id, current + parseFloat(cost.cost_usd));
        }
        this.weeklyCosts = costsByRepo;
      }

      // Load monthly costs
      const { data: monthlyCosts } = await supabaseClient
        .from('autonomous_costs')
        .select('repository_id, cost_usd')
        .gte('timestamp', startOfMonth.toISOString());

      if (monthlyCosts) {
        const costsByRepo = new Map<string, number>();
        for (const cost of monthlyCosts) {
          const current = costsByRepo.get(cost.repository_id) || 0;
          costsByRepo.set(cost.repository_id, current + parseFloat(cost.cost_usd));
        }
        this.monthlyCosts = costsByRepo;
      }

    } catch (error) {
      await loggingService.log({
        level: 'error',
        message: 'Failed to load current costs from database',
        service: 'cost-guard',
        metadata: {
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      });
    }
  }

  /**
   * Get recent operation costs for analysis
   */
  private async getRecentOperationCosts(repositoryId: string, operationType: string, limit: number): Promise<number[]> {
    const { data: costs } = await supabaseClient
      .from('autonomous_costs')
      .select('cost_usd')
      .eq('repository_id', repositoryId)
      .eq('operation_type', operationType)
      .order('timestamp', { ascending: false })
      .limit(limit);

    return costs?.map(c => parseFloat(c.cost_usd)) || [];
  }

  /**
   * Cancel all queued jobs for a repository (emergency shutdown)
   */
  private async cancelRepositoryJobs(repositoryId: string): Promise<void> {
    try {
      // Get all queued jobs for this repository
      const jobs = autonomousProcessor.getJobsForRepository(repositoryId)
        .filter(job => job.status === 'queued');

      // Cancel each job
      for (const job of jobs) {
        await autonomousProcessor.cancelJob(job.id);
      }

      await loggingService.log({
        level: 'warn',
        message: 'Repository jobs cancelled due to cost limit breach',
        service: 'cost-guard',
        metadata: {
          repositoryId,
          cancelledJobs: jobs.length
        }
      });

    } catch (error) {
      await loggingService.log({
        level: 'error',
        message: 'Failed to cancel repository jobs',
        service: 'cost-guard',
        metadata: {
          repositoryId,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      });
    }
  }

  /**
   * Get cost limits for a repository
   */
  getCostLimits(repositoryId: string): CostLimit | undefined {
    return this.costLimits.get(repositoryId);
  }

  /**
   * Get active alerts for a repository
   */
  getActiveAlerts(repositoryId: string): CostAlert[] {
    return this.activeAlerts.get(repositoryId) || [];
  }

  /**
   * Acknowledge an alert
   */
  async acknowledgeAlert(alertId: string): Promise<boolean> {
    for (const alerts of this.activeAlerts.values()) {
      const alert = alerts.find(a => a.id === alertId);
      if (alert) {
        alert.acknowledged = true;

        // Update in database
        await supabaseClient
          .from('cost_alerts')
          .update({ acknowledged: true })
          .eq('id', alertId);

        this.emit('alert-acknowledged', alert);
        return true;
      }
    }
    return false;
  }

  /**
   * Check if repository is in emergency shutdown
   */
  isRepositoryShutdown(repositoryId: string): boolean {
    return this.emergencyShutdown.has(repositoryId);
  }

  /**
   * Manually override emergency shutdown (admin only)
   */
  async overrideEmergencyShutdown(repositoryId: string, reason: string): Promise<void> {
    this.emergencyShutdown.delete(repositoryId);

    await loggingService.log({
      level: 'warn',
      message: 'Emergency shutdown overridden',
      service: 'cost-guard',
      metadata: {
        repositoryId,
        reason
      }
    });

    this.emit('shutdown-overridden', { repositoryId, reason });
  }
}

export const costGuard = new CostGuardService();
