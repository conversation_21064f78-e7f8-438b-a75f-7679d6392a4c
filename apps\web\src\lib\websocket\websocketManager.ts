import { z } from 'zod';

// WebSocket event types
export enum WebSocketEventType {
  // Connection events
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  RECONNECT = 'reconnect',
  ERROR = 'error',
  
  // Transformation events
  TRANSFORMATION_STARTED = 'transformation_started',
  TRANSFORMATION_PROGRESS = 'transformation_progress',
  TRANSFORMATION_COMPLETED = 'transformation_completed',
  TRANSFORMATION_FAILED = 'transformation_failed',
  
  // Agent events
  PLANNER_THINKING = 'planner_thinking',
  PLANNER_RESPONSE = 'planner_response',
  CRITIC_EVALUATING = 'critic_evaluating',
  CRITIC_RESPONSE = 'critic_response',
  
  // System events
  SYSTEM_STATUS = 'system_status',
  COST_UPDATE = 'cost_update',
  QUEUE_UPDATE = 'queue_update',
  
  // User events
  USER_JOINED = 'user_joined',
  USER_LEFT = 'user_left',
}

// WebSocket message schema
export const websocketMessageSchema = z.object({
  type: z.nativeEnum(WebSocketEventType),
  id: z.string(),
  timestamp: z.string().transform(str => new Date(str)),
  data: z.any(),
  userId: z.string().optional(),
  sessionId: z.string().optional(),
});

export type WebSocketMessage = z.infer<typeof websocketMessageSchema>;

// Event listener type
export type WebSocketEventListener = (message: WebSocketMessage) => void;

// Connection status
export enum ConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
}

// WebSocket Manager Configuration
export interface WebSocketConfig {
  url: string;
  reconnectInterval: number;
  maxReconnectAttempts: number;
  heartbeatInterval: number;
  enableHeartbeat: boolean;
  enableLogging: boolean;
}

// WebSocket Manager Class
export class WebSocketManager {
  private ws: WebSocket | null = null;
  private config: WebSocketConfig;
  private status: ConnectionStatus = ConnectionStatus.DISCONNECTED;
  private listeners: Map<WebSocketEventType, WebSocketEventListener[]> = new Map();
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private messageQueue: WebSocketMessage[] = [];
  private sessionId: string;

  constructor(config: Partial<WebSocketConfig> = {}) {
    this.config = {
      url: config.url || this.getWebSocketUrl(),
      reconnectInterval: config.reconnectInterval || 5000,
      maxReconnectAttempts: config.maxReconnectAttempts || 10,
      heartbeatInterval: config.heartbeatInterval || 30000,
      enableHeartbeat: config.enableHeartbeat ?? true,
      enableLogging: config.enableLogging ?? process.env.NODE_ENV === 'development',
    };
    
    this.sessionId = this.generateSessionId();
  }

  // Connection management
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.status === ConnectionStatus.CONNECTED) {
        resolve();
        return;
      }

      this.setStatus(ConnectionStatus.CONNECTING);
      this.log('Connecting to WebSocket server...');

      try {
        this.ws = new WebSocket(this.config.url);
        
        this.ws.onopen = () => {
          this.setStatus(ConnectionStatus.CONNECTED);
          this.reconnectAttempts = 0;
          this.log('WebSocket connected');
          
          // Send queued messages
          this.flushMessageQueue();
          
          // Start heartbeat
          if (this.config.enableHeartbeat) {
            this.startHeartbeat();
          }
          
          this.emit(WebSocketEventType.CONNECT, {});
          resolve();
        };

        this.ws.onmessage = (event) => {
          this.handleMessage(event.data);
        };

        this.ws.onclose = (event) => {
          this.setStatus(ConnectionStatus.DISCONNECTED);
          this.log(`WebSocket closed: ${event.code} ${event.reason}`);
          
          this.stopHeartbeat();
          this.emit(WebSocketEventType.DISCONNECT, { code: event.code, reason: event.reason });
          
          // Auto-reconnect if not intentionally closed
          if (event.code !== 1000 && this.reconnectAttempts < this.config.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          this.setStatus(ConnectionStatus.ERROR);
          this.log('WebSocket error:', error);
          this.emit(WebSocketEventType.ERROR, { error });
          reject(error);
        };

      } catch (error) {
        this.setStatus(ConnectionStatus.ERROR);
        this.log('Failed to create WebSocket connection:', error);
        reject(error);
      }
    });
  }

  disconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    this.stopHeartbeat();
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    
    this.setStatus(ConnectionStatus.DISCONNECTED);
  }

  // Message handling
  send(type: WebSocketEventType, data: any): void {
    const message: WebSocketMessage = {
      type,
      id: this.generateMessageId(),
      timestamp: new Date(),
      data,
      sessionId: this.sessionId,
    };

    if (this.status === ConnectionStatus.CONNECTED && this.ws) {
      try {
        this.ws.send(JSON.stringify(message));
        this.log('Sent message:', message);
      } catch (error) {
        this.log('Failed to send message:', error);
        this.queueMessage(message);
      }
    } else {
      this.queueMessage(message);
    }
  }

  private handleMessage(data: string): void {
    try {
      const parsed = JSON.parse(data);
      const result = websocketMessageSchema.safeParse(parsed);
      
      if (result.success) {
        const message = result.data;
        this.log('Received message:', message);
        this.emit(message.type, message.data);
      } else {
        this.log('Invalid message format:', result.error);
      }
    } catch (error) {
      this.log('Failed to parse message:', error);
    }
  }

  private queueMessage(message: WebSocketMessage): void {
    this.messageQueue.push(message);
    
    // Limit queue size
    if (this.messageQueue.length > 100) {
      this.messageQueue = this.messageQueue.slice(-100);
    }
  }

  private flushMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.status === ConnectionStatus.CONNECTED) {
      const message = this.messageQueue.shift();
      if (message && this.ws) {
        try {
          this.ws.send(JSON.stringify(message));
        } catch (error) {
          this.log('Failed to send queued message:', error);
          break;
        }
      }
    }
  }

  // Event handling
  on(eventType: WebSocketEventType, listener: WebSocketEventListener): void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }
    this.listeners.get(eventType)!.push(listener);
  }

  off(eventType: WebSocketEventType, listener: WebSocketEventListener): void {
    const listeners = this.listeners.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(eventType: WebSocketEventType, data: any): void {
    const listeners = this.listeners.get(eventType) || [];
    const message: WebSocketMessage = {
      type: eventType,
      id: this.generateMessageId(),
      timestamp: new Date(),
      data,
      sessionId: this.sessionId,
    };

    listeners.forEach(listener => {
      try {
        listener(message);
      } catch (error) {
        this.log('Error in event listener:', error);
      }
    });
  }

  // Reconnection logic
  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    this.reconnectAttempts++;
    this.setStatus(ConnectionStatus.RECONNECTING);
    
    const delay = Math.min(
      this.config.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1),
      30000 // Max 30 seconds
    );

    this.log(`Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`);

    this.reconnectTimer = setTimeout(() => {
      this.connect().catch(error => {
        this.log('Reconnection failed:', error);
        
        if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
          this.log('Max reconnection attempts reached');
          this.setStatus(ConnectionStatus.ERROR);
        } else {
          this.scheduleReconnect();
        }
      });
    }, delay);
  }

  // Heartbeat
  private startHeartbeat(): void {
    this.stopHeartbeat();
    
    this.heartbeatTimer = setInterval(() => {
      if (this.status === ConnectionStatus.CONNECTED) {
        this.send(WebSocketEventType.SYSTEM_STATUS, { type: 'heartbeat' });
      }
    }, this.config.heartbeatInterval);
  }

  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  // Status management
  private setStatus(status: ConnectionStatus): void {
    if (this.status !== status) {
      this.status = status;
      this.log(`Status changed to: ${status}`);
    }
  }

  getStatus(): ConnectionStatus {
    return this.status;
  }

  isConnected(): boolean {
    return this.status === ConnectionStatus.CONNECTED;
  }

  // Utility methods
  private getWebSocketUrl(): string {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    return `${protocol}//${host}/ws`;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private log(...args: any[]): void {
    if (this.config.enableLogging) {
      console.log('[WebSocket]', ...args);
    }
  }

  // Cleanup
  destroy(): void {
    this.disconnect();
    this.listeners.clear();
    this.messageQueue = [];
  }
}

// Global WebSocket manager instance
let globalWebSocketManager: WebSocketManager | null = null;

export const getWebSocketManager = (): WebSocketManager => {
  if (!globalWebSocketManager) {
    globalWebSocketManager = new WebSocketManager();
  }
  return globalWebSocketManager;
};

export const initializeWebSocket = async (config?: Partial<WebSocketConfig>): Promise<WebSocketManager> => {
  if (globalWebSocketManager) {
    globalWebSocketManager.destroy();
  }
  
  globalWebSocketManager = new WebSocketManager(config);
  await globalWebSocketManager.connect();
  return globalWebSocketManager;
};
