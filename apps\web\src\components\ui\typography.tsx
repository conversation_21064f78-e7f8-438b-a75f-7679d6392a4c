import * as React from "react"
import { cn } from "@/lib/utils"

interface TypographyProps extends React.HTMLAttributes<HTMLElement> {
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body-lg' | 'body' | 'body-sm' | 'caption' | 'code'
  as?: React.ElementType
  children: React.ReactNode
}

const Typography = React.forwardRef<HTMLElement, TypographyProps>(
  ({ className, variant = 'body', as, children, ...props }, ref) => {
    const Component = as || getDefaultElement(variant)
    
    return (
      <Component
        className={cn(getVariantClasses(variant), className)}
        ref={ref}
        {...props}
      >
        {children}
      </Component>
    )
  }
)
Typography.displayName = "Typography"

function getDefaultElement(variant: string): React.ElementType {
  switch (variant) {
    case 'h1': return 'h1'
    case 'h2': return 'h2'
    case 'h3': return 'h3'
    case 'h4': return 'h4'
    case 'h5': return 'h5'
    case 'h6': return 'h6'
    case 'code': return 'code'
    default: return 'p'
  }
}

function getVariantClasses(variant: string): string {
  switch (variant) {
    case 'h1':
      return 'text-h1 text-foreground'
    case 'h2':
      return 'text-h2 text-foreground'
    case 'h3':
      return 'text-h3 text-foreground'
    case 'h4':
      return 'text-h4 text-foreground'
    case 'h5':
      return 'text-h5 text-foreground'
    case 'h6':
      return 'text-h6 text-foreground'
    case 'body-lg':
      return 'text-body-lg text-foreground'
    case 'body':
      return 'text-body text-foreground'
    case 'body-sm':
      return 'text-body-sm text-muted-foreground'
    case 'caption':
      return 'text-caption text-muted-foreground'
    case 'code':
      return 'text-code'
    default:
      return 'text-body text-foreground'
  }
}

// Specific typography components for common use cases
const TypographyH1 = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h1
    ref={ref}
    className={cn("text-h1 text-foreground", className)}
    {...props}
  />
))
TypographyH1.displayName = "TypographyH1"

const TypographyH2 = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h2
    ref={ref}
    className={cn("text-h2 text-foreground", className)}
    {...props}
  />
))
TypographyH2.displayName = "TypographyH2"

const TypographyH3 = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn("text-h3 text-foreground", className)}
    {...props}
  />
))
TypographyH3.displayName = "TypographyH3"

const TypographyP = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-body text-foreground", className)}
    {...props}
  />
))
TypographyP.displayName = "TypographyP"

const TypographySmall = React.forwardRef<
  HTMLElement,
  React.HTMLAttributes<HTMLElement>
>(({ className, ...props }, ref) => (
  <small
    ref={ref}
    className={cn("text-body-sm text-muted-foreground", className)}
    {...props}
  />
))
TypographySmall.displayName = "TypographySmall"

const TypographyCode = React.forwardRef<
  HTMLElement,
  React.HTMLAttributes<HTMLElement>
>(({ className, ...props }, ref) => (
  <code
    ref={ref}
    className={cn("text-code", className)}
    {...props}
  />
))
TypographyCode.displayName = "TypographyCode"

const TypographyLead = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-body-lg text-muted-foreground", className)}
    {...props}
  />
))
TypographyLead.displayName = "TypographyLead"

const TypographyMuted = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-body-sm text-muted-foreground", className)}
    {...props}
  />
))
TypographyMuted.displayName = "TypographyMuted"

export {
  Typography,
  TypographyH1,
  TypographyH2,
  TypographyH3,
  TypographyP,
  TypographySmall,
  TypographyCode,
  TypographyLead,
  TypographyMuted,
}
