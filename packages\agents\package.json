{"name": "@metamorphic-reactor/agents", "version": "1.0.0", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "test:coverage": "jest --coverage --coverageReporters=text --coverageReporters=lcov --coverageReporters=html", "test:coverage:ci": "jest --coverage --coverageReporters=text --coverageReporters=lcov --coverageThreshold='{\"global\":{\"branches\":90,\"functions\":90,\"lines\":90,\"statements\":90}}'", "test:watch": "jest --watch"}, "dependencies": {"fast-json-patch": "^3.1.1", "zod": "^3.23.8", "openai": "^4.67.3", "@google-cloud/vertexai": "^1.9.0", "@anthropic-ai/sdk": "^0.30.1"}, "devDependencies": {"typescript": "^5.5.3", "@types/jest": "^29.5.12", "jest": "^29.7.0", "ts-jest": "^29.1.1"}}