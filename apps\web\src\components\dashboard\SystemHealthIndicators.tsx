import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Activity, 
  CheckCircle, 
  XCircle,
  AlertTriangle,
  Clock,
  Wifi,
  Database,
  Cloud,
  RefreshCw,
  Zap,
  Globe,
  Server,
  Shield
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { HStack, VStack } from '@/components/ui/layout';
import { Typography } from '@/components/ui/typography';

interface ServiceStatus {
  name: string;
  status: 'healthy' | 'degraded' | 'down' | 'unknown';
  responseTime?: number;
  uptime?: number;
  lastCheck: string;
  errorRate?: number;
  icon: React.ReactNode;
}

interface SystemHealth {
  overall: 'healthy' | 'degraded' | 'down';
  uptime: number;
  services: ServiceStatus[];
  lastIncident?: {
    title: string;
    status: string;
    timestamp: string;
  };
}

interface SystemHealthIndicatorsProps {
  className?: string;
  refreshInterval?: number;
}

export const SystemHealthIndicators: React.FC<SystemHealthIndicatorsProps> = ({
  className,
  refreshInterval = 30000 // 30 seconds
}) => {
  const [systemHealth, setSystemHealth] = useState<SystemHealth>({
    overall: 'healthy',
    uptime: 99.9,
    services: [],
    lastIncident: undefined
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [error, setError] = useState<string | null>(null);

  // Fetch system health data
  const fetchSystemHealth = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Fetch main health endpoint
      const healthResponse = await fetch('/api/health');
      let healthData = null;
      
      if (healthResponse.ok) {
        healthData = await healthResponse.json();
      }

      // Fetch individual service health
      const services: ServiceStatus[] = [];
      
      // Check API status
      try {
        const apiResponse = await fetch('/api/status');
        const apiData = apiResponse.ok ? await apiResponse.json() : null;
        services.push({
          name: 'API Server',
          status: apiResponse.ok ? 'healthy' : 'down',
          responseTime: apiData?.responseTime || 0,
          uptime: apiData?.uptime || 0,
          lastCheck: new Date().toISOString(),
          icon: <Server className="w-4 h-4" />
        });
      } catch {
        services.push({
          name: 'API Server',
          status: 'down',
          lastCheck: new Date().toISOString(),
          icon: <Server className="w-4 h-4" />
        });
      }

      // Check billing service
      try {
        const billingResponse = await fetch('/api/billing/health');
        const billingData = billingResponse.ok ? await billingResponse.json() : null;
        services.push({
          name: 'Billing Service',
          status: billingData?.status === 'healthy' ? 'healthy' : 'degraded',
          lastCheck: new Date().toISOString(),
          icon: <Shield className="w-4 h-4" />
        });
      } catch {
        services.push({
          name: 'Billing Service',
          status: 'unknown',
          lastCheck: new Date().toISOString(),
          icon: <Shield className="w-4 h-4" />
        });
      }

      // Check logs service
      try {
        const logsResponse = await fetch('/api/logs/health');
        const logsData = logsResponse.ok ? await logsResponse.json() : null;
        services.push({
          name: 'Logging Service',
          status: logsData?.status === 'healthy' ? 'healthy' : 'degraded',
          lastCheck: new Date().toISOString(),
          icon: <Database className="w-4 h-4" />
        });
      } catch {
        services.push({
          name: 'Logging Service',
          status: 'unknown',
          lastCheck: new Date().toISOString(),
          icon: <Database className="w-4 h-4" />
        });
      }

      // Check external services from health data
      if (healthData?.services) {
        Object.entries(healthData.services).forEach(([serviceName, status]) => {
          const serviceStatus = status === 'healthy' ? 'healthy' : 'degraded';
          let icon = <Cloud className="w-4 h-4" />;
          
          if (serviceName.toLowerCase().includes('database')) {
            icon = <Database className="w-4 h-4" />;
          } else if (serviceName.toLowerCase().includes('openai')) {
            icon = <Zap className="w-4 h-4" />;
          } else if (serviceName.toLowerCase().includes('github')) {
            icon = <Globe className="w-4 h-4" />;
          }
          
          services.push({
            name: serviceName.charAt(0).toUpperCase() + serviceName.slice(1),
            status: serviceStatus,
            lastCheck: new Date().toISOString(),
            icon
          });
        });
      }

      // Determine overall health
      const healthyServices = services.filter(s => s.status === 'healthy').length;
      const totalServices = services.length;
      let overall: 'healthy' | 'degraded' | 'down' = 'healthy';
      
      if (healthyServices === 0) {
        overall = 'down';
      } else if (healthyServices < totalServices) {
        overall = 'degraded';
      }

      setSystemHealth({
        overall,
        uptime: healthData?.uptime || 99.9,
        services,
        lastIncident: healthData?.lastIncident
      });

    } catch (error) {
      console.error('Failed to fetch system health:', error);
      setError('Failed to load system health data');
      
      // Set all services as unknown on error
      setSystemHealth(prev => ({
        ...prev,
        overall: 'down',
        services: prev.services.map(service => ({
          ...service,
          status: 'unknown'
        }))
      }));
    } finally {
      setIsLoading(false);
      setLastUpdated(new Date());
    }
  };

  // Auto-refresh system health
  useEffect(() => {
    fetchSystemHealth();
    const interval = setInterval(fetchSystemHealth, refreshInterval);
    return () => clearInterval(interval);
  }, [refreshInterval]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-500';
      case 'degraded': return 'text-yellow-500';
      case 'down': return 'text-red-500';
      case 'unknown': return 'text-gray-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'degraded': return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'down': return <XCircle className="w-4 h-4 text-red-500" />;
      case 'unknown': return <AlertTriangle className="w-4 h-4 text-gray-500" />;
      default: return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'healthy': return 'default';
      case 'degraded': return 'secondary';
      case 'down': return 'destructive';
      case 'unknown': return 'outline';
      default: return 'outline';
    }
  };

  const formatUptime = (uptime: number): string => {
    return `${uptime.toFixed(2)}%`;
  };

  const formatResponseTime = (ms: number): string => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const hasIssues = systemHealth.overall !== 'healthy';

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Activity className="w-5 h-5 text-primary" />
            <CardTitle className="text-lg">System Health</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            {getStatusIcon(systemHealth.overall)}
            <Badge variant={getStatusBadgeVariant(systemHealth.overall)}>
              {systemHealth.overall.charAt(0).toUpperCase() + systemHealth.overall.slice(1)}
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={fetchSystemHealth}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={cn("w-4 h-4", isLoading && "animate-spin")} />
            </Button>
          </div>
        </div>
        <CardDescription>
          Real-time monitoring of all system components and services
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {error && (
          <Alert className="border-red-500/30 bg-red-900/10">
            <AlertTriangle className="h-4 w-4 text-red-500" />
            <AlertDescription className="text-red-200">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Overall Status */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <div className={cn("w-2 h-2 rounded-full", 
                systemHealth.overall === 'healthy' ? 'bg-green-500' :
                systemHealth.overall === 'degraded' ? 'bg-yellow-500' : 'bg-red-500'
              )} />
              <Typography variant="caption" className="text-muted-foreground">
                Overall Status
              </Typography>
            </div>
            <Typography variant="h6" className={getStatusColor(systemHealth.overall)}>
              {systemHealth.overall.charAt(0).toUpperCase() + systemHealth.overall.slice(1)}
            </Typography>
          </div>

          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Clock className="w-4 h-4 text-blue-500" />
              <Typography variant="caption" className="text-muted-foreground">
                System Uptime
              </Typography>
            </div>
            <Typography variant="h6" className="font-mono">
              {formatUptime(systemHealth.uptime)}
            </Typography>
          </div>

          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <Typography variant="caption" className="text-muted-foreground">
                Services Online
              </Typography>
            </div>
            <Typography variant="h6" className="font-mono">
              {systemHealth.services.filter(s => s.status === 'healthy').length} / {systemHealth.services.length}
            </Typography>
          </div>
        </div>

        {/* Service Status */}
        <div className="space-y-3">
          <Typography variant="body-sm" className="font-medium">
            Service Status
          </Typography>
          <div className="space-y-3">
            {systemHealth.services.map((service, index) => (
              <div key={index} className="flex items-center justify-between p-3 rounded-lg border border-border bg-card/50">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    {service.icon}
                    <Typography variant="body-sm" className="font-medium">
                      {service.name}
                    </Typography>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  {service.responseTime && (
                    <Typography variant="caption" className="text-muted-foreground font-mono">
                      {formatResponseTime(service.responseTime)}
                    </Typography>
                  )}
                  
                  {service.uptime && (
                    <Typography variant="caption" className="text-muted-foreground font-mono">
                      {formatUptime(service.uptime)}
                    </Typography>
                  )}
                  
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(service.status)}
                    <Badge variant={getStatusBadgeVariant(service.status)} className="text-xs">
                      {service.status.charAt(0).toUpperCase() + service.status.slice(1)}
                    </Badge>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Last Incident */}
        {systemHealth.lastIncident && (
          <div className="space-y-3">
            <Typography variant="body-sm" className="font-medium">
              Recent Incident
            </Typography>
            <Alert className="border-yellow-500/30 bg-yellow-900/10">
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
              <AlertDescription className="text-yellow-200">
                <VStack spacing="xs">
                  <span className="font-medium">{systemHealth.lastIncident.title}</span>
                  <span className="text-xs">
                    Status: {systemHealth.lastIncident.status} • {new Date(systemHealth.lastIncident.timestamp).toLocaleString()}
                  </span>
                </VStack>
              </AlertDescription>
            </Alert>
          </div>
        )}

        {/* System Issues Alert */}
        {hasIssues && !systemHealth.lastIncident && (
          <Alert className="border-yellow-500/30 bg-yellow-900/10">
            <AlertTriangle className="h-4 w-4 text-yellow-500" />
            <AlertDescription className="text-yellow-200">
              Some services are experiencing issues. Functionality may be limited.
            </AlertDescription>
          </Alert>
        )}

        {/* Last Updated */}
        <div className="pt-2 border-t border-border">
          <Typography variant="caption" className="text-muted-foreground">
            Last checked: {lastUpdated.toLocaleTimeString()}
          </Typography>
        </div>
      </CardContent>
    </Card>
  );
};

export default SystemHealthIndicators;
