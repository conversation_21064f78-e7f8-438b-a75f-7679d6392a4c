
import { useEffect, useRef, useState } from 'react';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Bo<PERSON>, Eye, Cog, Wifi, WifiOff, Pause, Play, RotateCcw } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { cn } from "@/lib/utils";

interface LogEntry {
  id: number;
  agent: 'planner' | 'critic' | 'system';
  message: string;
  timestamp: Date;
  level?: 'info' | 'warning' | 'error' | 'success';
  metadata?: Record<string, any>;
}

interface AgentLogProps {
  logs: LogEntry[];
  enableRealTime?: boolean;
  maxLogs?: number;
  onClear?: () => void;
}

// Connection status for real-time streaming
enum ConnectionStatus {
  CONNECTED = 'connected',
  CONNECTING = 'connecting',
  DISCONNECTED = 'disconnected',
  ERROR = 'error'
}

export const AgentLog = ({
  logs,
  enableRealTime = true,
  maxLogs = 100,
  onClear
}: AgentLogProps) => {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>(ConnectionStatus.DISCONNECTED);
  const [isPaused, setIsPaused] = useState(false);
  const [realTimeLogs, setRealTimeLogs] = useState<LogEntry[]>([]);
  const [autoScroll, setAutoScroll] = useState(true);

  // Combine props logs with real-time logs
  const allLogs = [...logs, ...realTimeLogs].slice(-maxLogs);

  useEffect(() => {
    if (scrollRef.current && autoScroll) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [allLogs, autoScroll]);

  // Simulate real-time connection (replace with actual WebSocket/SSE implementation)
  useEffect(() => {
    if (!enableRealTime) return;

    let interval: NodeJS.Timeout;

    // Simulate connection status changes
    setConnectionStatus(ConnectionStatus.CONNECTING);

    setTimeout(() => {
      setConnectionStatus(ConnectionStatus.CONNECTED);

      // Simulate real-time log updates
      if (!isPaused) {
        interval = setInterval(() => {
          const newLog: LogEntry = {
            id: Date.now(),
            agent: ['planner', 'critic', 'system'][Math.floor(Math.random() * 3)] as any,
            message: `Real-time update: ${new Date().toLocaleTimeString()}`,
            timestamp: new Date(),
            level: ['info', 'success', 'warning'][Math.floor(Math.random() * 3)] as any
          };

          setRealTimeLogs(prev => [...prev, newLog].slice(-maxLogs));
        }, 5000);
      }
    }, 1000);

    return () => {
      if (interval) clearInterval(interval);
      setConnectionStatus(ConnectionStatus.DISCONNECTED);
    };
  }, [enableRealTime, isPaused, maxLogs]);

  // Handle scroll to detect manual scrolling
  const handleScroll = () => {
    if (scrollRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = scrollRef.current;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;
      setAutoScroll(isAtBottom);
    }
  };

  const getAgentIcon = (agent: string) => {
    switch (agent) {
      case 'planner': return <Bot className="w-4 h-4" />;
      case 'critic': return <Eye className="w-4 h-4" />;
      case 'system': return <Cog className="w-4 h-4" />;
      default: return <Bot className="w-4 h-4" />;
    }
  };

  const getAgentColor = (agent: string) => {
    switch (agent) {
      case 'planner': return "bg-agent-planner/20 text-agent-planner-foreground border-agent-planner/30";
      case 'critic': return "bg-agent-critic/20 text-agent-critic-foreground border-agent-critic/30";
      case 'system': return "bg-agent-system/20 text-agent-system-foreground border-agent-system/30";
      default: return "bg-agent-system/20 text-agent-system-foreground border-agent-system/30";
    }
  };

  const getLevelColor = (level?: string) => {
    switch (level) {
      case 'error': return "text-destructive";
      case 'warning': return "text-warning";
      case 'success': return "text-success";
      case 'info':
      default: return "text-muted-foreground";
    }
  };

  const getConnectionStatusIcon = () => {
    switch (connectionStatus) {
      case ConnectionStatus.CONNECTED:
        return <Wifi className="w-4 h-4 text-success" />;
      case ConnectionStatus.CONNECTING:
        return <Wifi className="w-4 h-4 text-warning animate-pulse" />;
      case ConnectionStatus.ERROR:
        return <WifiOff className="w-4 h-4 text-destructive" />;
      case ConnectionStatus.DISCONNECTED:
      default:
        return <WifiOff className="w-4 h-4 text-muted-foreground" />;
    }
  };

  const handleClearLogs = () => {
    setRealTimeLogs([]);
    if (onClear) onClear();
  };

  const togglePause = () => {
    setIsPaused(!isPaused);
  };

  const scrollToBottom = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
      setAutoScroll(true);
    }
  };

  return (
    <div className="h-full bg-background flex flex-col">
      {/* Header with controls */}
      <div className="flex items-center justify-between p-3 border-b border-border bg-muted/50">
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1">
            {getConnectionStatusIcon()}
            <span className="text-xs text-muted-foreground">
              {connectionStatus === ConnectionStatus.CONNECTED ? 'Live' :
               connectionStatus === ConnectionStatus.CONNECTING ? 'Connecting...' :
               'Offline'}
            </span>
          </div>
          {enableRealTime && (
            <Badge variant="outline" className="text-xs">
              {allLogs.length} logs
            </Badge>
          )}
        </div>

        <div className="flex items-center space-x-1">
          {enableRealTime && (
            <Button
              variant="ghost"
              size="sm"
              onClick={togglePause}
              className="h-7 w-7 p-0"
              aria-label={isPaused ? "Resume real-time updates" : "Pause real-time updates"}
            >
              {isPaused ? <Play className="w-3 h-3" /> : <Pause className="w-3 h-3" />}
            </Button>
          )}

          <Button
            variant="ghost"
            size="sm"
            onClick={scrollToBottom}
            className="h-7 w-7 p-0"
            aria-label="Scroll to bottom"
          >
            <RotateCcw className="w-3 h-3" />
          </Button>

          {onClear && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClearLogs}
              className="h-7 px-2 text-xs"
              aria-label="Clear logs"
            >
              Clear
            </Button>
          )}
        </div>
      </div>

      {/* Log content */}
      <div
        ref={scrollRef}
        className="flex-1 overflow-auto p-3 space-y-3"
        onScroll={handleScroll}
        role="log"
        aria-label="Agent activity log"
        aria-live="polite"
        aria-atomic="false"
      >
        {allLogs.map((log) => (
          <div key={log.id} className="flex items-start space-x-3 group">
            <div className="flex-shrink-0 mt-1">
              <Badge className={`${getAgentColor(log.agent)} px-2 py-1`}>
                <div className="flex items-center space-x-1">
                  {getAgentIcon(log.agent)}
                  <span className="text-xs font-medium capitalize">{log.agent}</span>
                </div>
              </Badge>
            </div>
            <div className="flex-1 min-w-0">
              <p className={cn("text-sm leading-relaxed", getLevelColor(log.level))}>
                {log.message}
              </p>
              <div className="flex items-center space-x-2 mt-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <p className="text-xs text-muted-foreground">
                  {formatDistanceToNow(log.timestamp, { addSuffix: true })}
                </p>
                {log.level && log.level !== 'info' && (
                  <Badge variant="outline" className="text-xs px-1 py-0">
                    {log.level}
                  </Badge>
                )}
              </div>
            </div>
          </div>
        ))}

        {allLogs.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-muted flex items-center justify-center">
              <Bot className="w-6 h-6" />
            </div>
            <p className="text-sm">No logs yet</p>
            <p className="text-xs">Agent logs will appear here</p>
            {enableRealTime && connectionStatus === ConnectionStatus.DISCONNECTED && (
              <p className="text-xs mt-2 text-warning">
                Real-time updates are offline
              </p>
            )}
          </div>
        )}

        {/* Auto-scroll indicator */}
        {!autoScroll && allLogs.length > 0 && (
          <div className="sticky bottom-0 left-0 right-0 flex justify-center">
            <Button
              variant="secondary"
              size="sm"
              onClick={scrollToBottom}
              className="shadow-lg"
            >
              <RotateCcw className="w-3 h-3 mr-1" />
              Scroll to bottom
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};
