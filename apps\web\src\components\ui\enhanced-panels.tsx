import * as React from "react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { VStack, HStack } from "@/components/ui/layout"
import { Typography } from "@/components/ui/typography"
import { 
  ResizableHandle, 
  ResizablePanel, 
  ResizablePanelGroup 
} from "@/components/ui/resizable"

// Enhanced panel with persistence and state management
interface EnhancedPanelProps {
  id: string
  title: string
  children: React.ReactNode
  defaultSize?: number
  minSize?: number
  maxSize?: number
  collapsible?: boolean
  collapsed?: boolean
  onCollapse?: (collapsed: boolean) => void
  actions?: React.ReactNode
  className?: string
}

export function EnhancedPanel({
  id,
  title,
  children,
  defaultSize = 50,
  minSize = 20,
  maxSize = 80,
  collapsible = false,
  collapsed = false,
  onCollapse,
  actions,
  className
}: EnhancedPanelProps) {
  const [isCollapsed, setIsCollapsed] = React.useState(collapsed)
  
  React.useEffect(() => {
    setIsCollapsed(collapsed)
  }, [collapsed])

  const handleCollapse = () => {
    const newCollapsed = !isCollapsed
    setIsCollapsed(newCollapsed)
    onCollapse?.(newCollapsed)
  }

  return (
    <ResizablePanel
      id={id}
      defaultSize={isCollapsed ? 5 : defaultSize}
      minSize={isCollapsed ? 5 : minSize}
      maxSize={isCollapsed ? 5 : maxSize}
      className={cn("flex flex-col", className)}
    >
      {/* Panel Header */}
      <div className="flex items-center justify-between p-4 border-b border-border bg-card">
        <HStack spacing="sm" align="center">
          {collapsible && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCollapse}
              className="h-6 w-6 p-0"
            >
              {isCollapsed ? '▶' : '▼'}
            </Button>
          )}
          <Typography variant="h6" className="text-foreground">
            {title}
          </Typography>
        </HStack>
        
        {actions && !isCollapsed && (
          <div className="flex items-center space-x-2">
            {actions}
          </div>
        )}
      </div>

      {/* Panel Content */}
      {!isCollapsed && (
        <div className="flex-1 overflow-hidden">
          {children}
        </div>
      )}
    </ResizablePanel>
  )
}

// Panel layout manager with persistence
interface PanelLayoutProps {
  children: React.ReactNode
  direction?: 'horizontal' | 'vertical'
  storageKey?: string
  className?: string
}

export function PanelLayout({
  children,
  direction = 'horizontal',
  storageKey,
  className
}: PanelLayoutProps) {
  const [layout, setLayout] = React.useState<number[]>([])

  // Load layout from localStorage
  React.useEffect(() => {
    if (storageKey) {
      const savedLayout = localStorage.getItem(`panel-layout-${storageKey}`)
      if (savedLayout) {
        try {
          setLayout(JSON.parse(savedLayout))
        } catch (error) {
          console.warn('Failed to load panel layout:', error)
        }
      }
    }
  }, [storageKey])

  // Save layout to localStorage
  const handleLayoutChange = (sizes: number[]) => {
    setLayout(sizes)
    if (storageKey) {
      localStorage.setItem(`panel-layout-${storageKey}`, JSON.stringify(sizes))
    }
  }

  return (
    <ResizablePanelGroup
      direction={direction}
      onLayout={handleLayoutChange}
      className={className}
    >
      {children}
    </ResizablePanelGroup>
  )
}

// Tabbed panel system
interface TabPanelItem {
  id: string
  label: string
  content: React.ReactNode
  badge?: string | number
  disabled?: boolean
}

interface TabbedPanelProps {
  items: TabPanelItem[]
  defaultActiveId?: string
  onTabChange?: (id: string) => void
  className?: string
}

export function TabbedPanel({
  items,
  defaultActiveId,
  onTabChange,
  className
}: TabbedPanelProps) {
  const [activeId, setActiveId] = React.useState(
    defaultActiveId || items[0]?.id || ''
  )

  const handleTabChange = (id: string) => {
    setActiveId(id)
    onTabChange?.(id)
  }

  const activeItem = items.find(item => item.id === activeId)

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Tab Headers */}
      <div className="flex border-b border-border bg-card">
        {items.map((item) => (
          <button
            key={item.id}
            onClick={() => !item.disabled && handleTabChange(item.id)}
            disabled={item.disabled}
            className={cn(
              "flex items-center space-x-2 px-4 py-2 text-sm font-medium transition-colors",
              "border-b-2 border-transparent hover:text-foreground",
              {
                "text-primary border-primary": activeId === item.id,
                "text-muted-foreground": activeId !== item.id && !item.disabled,
                "text-muted-foreground/50 cursor-not-allowed": item.disabled
              }
            )}
          >
            <span>{item.label}</span>
            {item.badge && (
              <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-primary-foreground bg-primary rounded-full">
                {item.badge}
              </span>
            )}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden">
        {activeItem?.content}
      </div>
    </div>
  )
}

// Floating panel system
interface FloatingPanelProps {
  isOpen: boolean
  onClose: () => void
  title: string
  children: React.ReactNode
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  size?: 'sm' | 'md' | 'lg'
  resizable?: boolean
  className?: string
}

export function FloatingPanel({
  isOpen,
  onClose,
  title,
  children,
  position = 'top-right',
  size = 'md',
  resizable = false,
  className
}: FloatingPanelProps) {
  const [isDragging, setIsDragging] = React.useState(false)
  const [dragOffset, setDragOffset] = React.useState({ x: 0, y: 0 })
  const panelRef = React.useRef<HTMLDivElement>(null)

  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4'
  }

  const sizeClasses = {
    sm: 'w-80 h-60',
    md: 'w-96 h-80',
    lg: 'w-[32rem] h-96'
  }

  if (!isOpen) return null

  return (
    <div
      ref={panelRef}
      className={cn(
        "fixed z-50 bg-card border border-border rounded-lg shadow-lg",
        "animate-in fade-in-0 zoom-in-95 duration-200",
        positionClasses[position],
        sizeClasses[size],
        resizable && "resize overflow-auto",
        className
      )}
    >
      {/* Panel Header */}
      <div
        className="flex items-center justify-between p-3 border-b border-border cursor-move"
        onMouseDown={(e) => {
          setIsDragging(true)
          const rect = panelRef.current?.getBoundingClientRect()
          if (rect) {
            setDragOffset({
              x: e.clientX - rect.left,
              y: e.clientY - rect.top
            })
          }
        }}
      >
        <Typography variant="h6" className="text-foreground">
          {title}
        </Typography>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-6 w-6 p-0"
        >
          ×
        </Button>
      </div>

      {/* Panel Content */}
      <div className="p-4 overflow-auto h-[calc(100%-3rem)]">
        {children}
      </div>
    </div>
  )
}

// Panel state management hook
export function usePanelState(storageKey?: string) {
  const [panels, setPanels] = React.useState<Record<string, any>>({})

  // Load panel states from localStorage
  React.useEffect(() => {
    if (storageKey) {
      const savedStates = localStorage.getItem(`panel-states-${storageKey}`)
      if (savedStates) {
        try {
          setPanels(JSON.parse(savedStates))
        } catch (error) {
          console.warn('Failed to load panel states:', error)
        }
      }
    }
  }, [storageKey])

  // Save panel states to localStorage
  React.useEffect(() => {
    if (storageKey && Object.keys(panels).length > 0) {
      localStorage.setItem(`panel-states-${storageKey}`, JSON.stringify(panels))
    }
  }, [panels, storageKey])

  const updatePanelState = React.useCallback((panelId: string, state: any) => {
    setPanels(prev => ({
      ...prev,
      [panelId]: { ...prev[panelId], ...state }
    }))
  }, [])

  const getPanelState = React.useCallback((panelId: string, defaultState: any = {}) => {
    return panels[panelId] || defaultState
  }, [panels])

  const resetPanelStates = React.useCallback(() => {
    setPanels({})
    if (storageKey) {
      localStorage.removeItem(`panel-states-${storageKey}`)
    }
  }, [storageKey])

  return {
    updatePanelState,
    getPanelState,
    resetPanelStates
  }
}
