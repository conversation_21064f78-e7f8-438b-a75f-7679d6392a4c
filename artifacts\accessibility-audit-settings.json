{"page": "settings", "url": "http://localhost:8080/settings", "timestamp": "2025-06-19T19:31:54.083Z", "score": 0, "status": "FAILED", "target_score": 97, "summary": {"violations": 6, "incomplete": 1, "passes": 34, "total": 41}, "violations": [{"id": "aria-input-field-name", "impact": "serious", "description": "Ensures every ARIA input field has an accessible name", "help": "ARIA input fields must have an accessible name", "helpUrl": "https://dequeuniversity.com/rules/axe/4.8/aria-input-field-name?application=axeAPI", "nodes": 2, "tags": ["cat.aria", "wcag2a", "wcag412", "TTv5", "TT5.c", "EN-301-549", "EN-*******", "ACT"], "nodeDetails": [{"target": [".text-card-foreground.shadow-sm[data-lov-name=\"Card\"]:nth-child(1) > .pt-0[data-lov-name=\"CardContent\"][data-component-line=\"158\"] > .grid-cols-2.gap-4[data-component-line=\"265\"] > .space-y-2[data-component-line=\"266\"][data-lov-name=\"div\"] > .px-2[data-component-line=\"268\"][data-lov-name=\"div\"] > .touch-none.select-none[data-lov-name=\"Slider\"] > span:nth-child(2) > .border-primary[role=\"slider\"][aria-valuemin=\"0\"]"], "html": "<span role=\"slider\" aria-valuemin=\"0\" aria-valuemax=\"2\" aria-orientation=\"horizontal\" data-orientation=\"horizontal\" tabindex=\"0\" data-lov-id=\"src\\components\\ui\\slider.tsx:21:4\" data-lov-name=\"SliderPr"}, {"target": [".text-card-foreground.shadow-sm[data-lov-name=\"Card\"]:nth-child(2) > .pt-0[data-lov-name=\"CardContent\"][data-component-line=\"158\"] > .grid-cols-2.gap-4[data-component-line=\"265\"] > .space-y-2[data-component-line=\"266\"][data-lov-name=\"div\"] > .px-2[data-component-line=\"268\"][data-lov-name=\"div\"] > .touch-none.select-none[data-lov-name=\"Slider\"] > span:nth-child(2) > .border-primary[role=\"slider\"][aria-valuemin=\"0\"]"], "html": "<span role=\"slider\" aria-valuemin=\"0\" aria-valuemax=\"2\" aria-orientation=\"horizontal\" data-orientation=\"horizontal\" tabindex=\"0\" data-lov-id=\"src\\components\\ui\\slider.tsx:21:4\" data-lov-name=\"SliderPr"}]}, {"id": "button-name", "impact": "critical", "description": "Ensures buttons have discernible text", "help": "Buttons must have discernible text", "helpUrl": "https://dequeuniversity.com/rules/axe/4.8/button-name?application=axeAPI", "nodes": 8, "tags": ["cat.name-role-value", "wcag2a", "wcag412", "section508", "section508.22.a", "TTv5", "TT6.a", "EN-301-549", "EN-*******", "ACT"], "nodeDetails": [{"target": ["button[aria-controls=\"radix-:r5:\"]"], "html": "<button type=\"button\" role=\"combobox\" aria-controls=\"radix-:r5:\" aria-expanded=\"false\" aria-autocomplete=\"none\" dir=\"ltr\" data-state=\"closed\" data-lov-id=\"src\\components\\settings\\ProviderConfigForm.ts"}, {"target": ["button[aria-controls=\"radix-:r6:\"]"], "html": "<button type=\"button\" role=\"combobox\" aria-controls=\"radix-:r6:\" aria-expanded=\"false\" aria-autocomplete=\"none\" dir=\"ltr\" data-state=\"closed\" data-lov-id=\"src\\components\\settings\\ProviderConfigForm.ts"}, {"target": [".text-card-foreground.shadow-sm[data-lov-name=\"Card\"]:nth-child(1) > .pt-0[data-lov-name=\"CardContent\"][data-component-line=\"158\"] > .space-y-2[data-component-line=\"213\"][data-lov-name=\"div\"] > .gap-2.flex[data-component-line=\"219\"] > .flex-1.relative[data-component-line=\"220\"] > .right-1.top-1.h-8"], "html": "<button data-lov-id=\"src\\components\\settings\\ProviderConfigForm.tsx:228:16\" data-lov-name=\"Button\" data-component-path=\"src\\components\\settings\\ProviderConfigForm.tsx\" data-component-line=\"228\" data-c"}]}, {"id": "heading-order", "impact": "moderate", "description": "Ensures the order of headings is semantically correct", "help": "Heading levels should only increase by one", "helpUrl": "https://dequeuniversity.com/rules/axe/4.8/heading-order?application=axeAPI", "nodes": 1, "tags": ["cat.semantics", "best-practice"], "nodeDetails": [{"target": [".text-card-foreground.shadow-sm[data-lov-name=\"Card\"]:nth-child(1) > .flex-col.space-y-1\\.5[data-lov-name=\"<PERSON>Head<PERSON>\"] > h3"], "html": "<h3 data-lov-id=\"src\\components\\settings\\ProviderConfigForm.tsx:145:8\" data-lov-name=\"CardTitle\" data-component-path=\"src\\components\\settings\\ProviderConfigForm.tsx\" data-component-line=\"145\" data-com"}]}, {"id": "label", "impact": "critical", "description": "Ensures every form element has a label", "help": "Form elements must have labels", "helpUrl": "https://dequeuniversity.com/rules/axe/4.8/label?application=axeAPI", "nodes": 3, "tags": ["cat.forms", "wcag2a", "wcag412", "section508", "section508.22.n", "TTv5", "TT5.c", "EN-301-549", "EN-*******", "ACT"], "nodeDetails": [{"target": ["input[value=\"2000\"]"], "html": "<input data-lov-id=\"src\\components\\settings\\ProviderConfigForm.tsx:286:12\" data-lov-name=\"Input\" data-component-path=\"src\\components\\settings\\ProviderConfigForm.tsx\" data-component-line=\"286\" data-com"}, {"target": ["input[value=\"1500\"]"], "html": "<input data-lov-id=\"src\\components\\settings\\ProviderConfigForm.tsx:286:12\" data-lov-name=\"Input\" data-component-path=\"src\\components\\settings\\ProviderConfigForm.tsx\" data-component-line=\"286\" data-com"}, {"target": [".inset-0"], "html": "<input data-lov-id=\"src\\components\\settings\\EnhancedSettingsPage.tsx:366:14\" data-lov-name=\"input\" data-component-path=\"src\\components\\settings\\EnhancedSettingsPage.tsx\" data-component-line=\"366\" data"}]}, {"id": "landmark-one-main", "impact": "moderate", "description": "Ensures the document has a main landmark", "help": "Document should have one main landmark", "helpUrl": "https://dequeuniversity.com/rules/axe/4.8/landmark-one-main?application=axeAPI", "nodes": 1, "tags": ["cat.semantics", "best-practice"], "nodeDetails": [{"target": ["html"], "html": "<html lang=\"en\" class=\"dark\">"}]}, {"id": "region", "impact": "moderate", "description": "Ensures all page content is contained by landmarks", "help": "All page content should be contained by landmarks", "helpUrl": "https://dequeuniversity.com/rules/axe/4.8/region?application=axeAPI", "nodes": 7, "tags": ["cat.keyboard", "best-practice"], "nodeDetails": [{"target": ["h1[data-lov-id=\"src\\\\pages\\\\Settings.tsx:322:12\"]"], "html": "<h1 data-lov-id=\"src\\pages\\Settings.tsx:322:12\" data-lov-name=\"h1\" data-component-path=\"src\\pages\\Settings.tsx\" data-component-line=\"322\" data-component-file=\"Settings.tsx\" data-component-name=\"h1\" da"}, {"target": [".hover\\:bg-secondary\\/80"], "html": "<div data-lov-id=\"src\\pages\\Settings.tsx:323:12\" data-lov-name=\"Badge\" data-component-path=\"src\\pages\\Settings.tsx\" data-component-line=\"323\" data-component-file=\"Settings.tsx\" data-component-name=\"Ba"}, {"target": ["div[data-component-line=\"205\"]"], "html": "<div data-lov-id=\"src\\components\\settings\\EnhancedSettingsPage.tsx:205:10\" data-lov-name=\"div\" data-component-path=\"src\\components\\settings\\EnhancedSettingsPage.tsx\" data-component-line=\"205\" data-com"}]}], "incomplete": [{"id": "color-contrast", "impact": "serious", "description": "Ensures the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds", "help": "Elements must meet minimum color contrast ratio thresholds", "nodes": 41}], "recommendations": ["Add aria-label or aria-labelledby to slider components", "Add discernible text or aria-label to 8 buttons", "Fix heading hierarchy - ensure h3 follows h1 or h2", "Add proper labels to 3 form inputs", "Add main landmark to page structure", "Wrap page content in proper landmark regions", "Test color contrast for 41 elements"]}