import { jest } from '@jest/globals';
import { ProviderFailover } from '../../utils/ProviderFailover.js';
import { AgentProvider, ProviderError, RateLimitError } from '../../providers/AgentProvider.js';
import { ProviderConfig } from '../../types.js';

// Mock provider factory
jest.mock('../../providers/ProviderFactory.js', () => ({
  ProviderFactory: {
    createProvider: jest.fn(),
  },
}));

describe('ProviderFailover', () => {
  let mockPrimaryProvider: jest.Mocked<AgentProvider>;
  let mockFallbackProvider1: jest.Mocked<AgentProvider>;
  let mockFallbackProvider2: jest.Mocked<AgentProvider>;
  let failover: ProviderFailover;

  const primaryConfig: ProviderConfig = {
    type: 'openai',
    model: 'gpt-4o',
    temperature: 0.7,
    maxTokens: 2000,
    apiKey: 'primary-key',
  };

  const fallbackConfigs: ProviderConfig[] = [
    {
      type: 'vertex-ai',
      model: 'gemini-2.5-flash',
      temperature: 0.7,
      maxTokens: 2000,
      vertexAI: { projectId: 'test-project', location: 'us-central1' },
    },
    {
      type: 'anthropic',
      model: 'claude-opus-4-20250514',
      temperature: 0.7,
      maxTokens: 2000,
      apiKey: 'fallback-key',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();

    // Create mock providers
    mockPrimaryProvider = {
      getType: jest.fn().mockReturnValue('openai'),
      getModel: jest.fn().mockReturnValue('gpt-4o'),
      getCost: jest.fn().mockReturnValue(0),
      supportsStreaming: jest.fn().mockReturnValue(true),
      validateConfig: jest.fn().mockResolvedValue(true),
      generatePatch: jest.fn(),
      scorePatch: jest.fn(),
      streamGeneratePatch: jest.fn(),
      streamScorePatch: jest.fn(),
      getTokenUsage: jest.fn().mockReturnValue({
        inputTokens: 0,
        outputTokens: 0,
        totalTokens: 0,
        cost: 0,
        provider: 'openai',
        model: 'gpt-4o',
        timestamp: new Date(),
      }),
      resetMetrics: jest.fn(),
      updateConfig: jest.fn(),
    } as any;

    mockFallbackProvider1 = {
      getType: jest.fn().mockReturnValue('vertex-ai'),
      getModel: jest.fn().mockReturnValue('gemini-2.5-flash'),
      getCost: jest.fn().mockReturnValue(0),
      supportsStreaming: jest.fn().mockReturnValue(true),
      validateConfig: jest.fn().mockResolvedValue(true),
      generatePatch: jest.fn(),
      scorePatch: jest.fn(),
      streamGeneratePatch: jest.fn(),
      streamScorePatch: jest.fn(),
      getTokenUsage: jest.fn().mockReturnValue({
        inputTokens: 0,
        outputTokens: 0,
        totalTokens: 0,
        cost: 0,
        provider: 'vertex-ai',
        model: 'gemini-2.5-flash',
        timestamp: new Date(),
      }),
      resetMetrics: jest.fn(),
      updateConfig: jest.fn(),
    } as any;

    mockFallbackProvider2 = {
      getType: jest.fn().mockReturnValue('anthropic'),
      getModel: jest.fn().mockReturnValue('claude-opus-4-20250514'),
      getCost: jest.fn().mockReturnValue(0),
      supportsStreaming: jest.fn().mockReturnValue(true),
      validateConfig: jest.fn().mockResolvedValue(true),
      generatePatch: jest.fn(),
      scorePatch: jest.fn(),
      streamGeneratePatch: jest.fn(),
      streamScorePatch: jest.fn(),
      getTokenUsage: jest.fn().mockReturnValue({
        inputTokens: 0,
        outputTokens: 0,
        totalTokens: 0,
        cost: 0,
        provider: 'anthropic',
        model: 'claude-opus-4-20250514',
        timestamp: new Date(),
      }),
      resetMetrics: jest.fn(),
      updateConfig: jest.fn(),
    } as any;

    // Mock ProviderFactory
    const { ProviderFactory } = require('../../providers/ProviderFactory.js');
    ProviderFactory.createProvider
      .mockReturnValueOnce(mockPrimaryProvider)
      .mockReturnValueOnce(mockFallbackProvider1)
      .mockReturnValueOnce(mockFallbackProvider2);

    failover = new ProviderFailover(primaryConfig, fallbackConfigs);
  });

  describe('constructor', () => {
    it('should initialize with primary and fallback providers', () => {
      expect(failover.getCurrentProvider()).toBe(mockPrimaryProvider);
    });

    it('should initialize health status for all providers', () => {
      const healthStatus = failover.getHealthStatus();
      
      expect(healthStatus.size).toBe(3);
      expect(healthStatus.has('openai')).toBe(true);
      expect(healthStatus.has('vertex-ai')).toBe(true);
      expect(healthStatus.has('anthropic')).toBe(true);
    });
  });

  describe('executeWithFailover', () => {
    const mockOperation = jest.fn();
    const mockResponse = {
      data: { test: 'result' },
      usage: {
        inputTokens: 100,
        outputTokens: 200,
        totalTokens: 300,
        cost: 0.01,
        provider: 'openai' as const,
        model: 'gpt-4o',
        timestamp: new Date(),
      },
      requestId: 'test-id',
      latency: 1000,
    };

    beforeEach(() => {
      mockOperation.mockClear();
    });

    it('should succeed with primary provider', async () => {
      mockOperation.mockResolvedValue(mockResponse);

      const result = await failover.executeWithFailover(mockOperation, 'test-operation');

      expect(result).toBe(mockResponse);
      expect(mockOperation).toHaveBeenCalledTimes(1);
      expect(mockOperation).toHaveBeenCalledWith(mockPrimaryProvider);
      expect(failover.getCurrentProvider()).toBe(mockPrimaryProvider);
    });

    it('should failover on rate limit error', async () => {
      mockOperation
        .mockRejectedValueOnce(new RateLimitError('openai', 1000))
        .mockResolvedValue(mockResponse);

      const result = await failover.executeWithFailover(mockOperation, 'test-operation');

      expect(result).toBe(mockResponse);
      expect(mockOperation).toHaveBeenCalledTimes(2);
      expect(mockOperation).toHaveBeenNthCalledWith(1, mockPrimaryProvider);
      expect(mockOperation).toHaveBeenNthCalledWith(2, mockFallbackProvider1);
      expect(failover.getCurrentProvider()).toBe(mockFallbackProvider1);
    });

    it('should failover on 5xx server errors', async () => {
      mockOperation
        .mockRejectedValueOnce(new ProviderError('Server error', 'openai', 'SERVER_ERROR', 500))
        .mockResolvedValue(mockResponse);

      const result = await failover.executeWithFailover(mockOperation, 'test-operation');

      expect(result).toBe(mockResponse);
      expect(mockOperation).toHaveBeenCalledTimes(2);
      expect(failover.getCurrentProvider()).toBe(mockFallbackProvider1);
    });

    it('should not failover on authentication errors', async () => {
      mockOperation
        .mockRejectedValue(new ProviderError('Auth failed', 'openai', 'AUTH_ERROR', 401));

      await expect(failover.executeWithFailover(mockOperation, 'test-operation'))
        .rejects.toThrow('Auth failed');

      expect(mockOperation).toHaveBeenCalledTimes(1);
      expect(failover.getCurrentProvider()).toBe(mockPrimaryProvider);
    });

    it('should try all providers before failing', async () => {
      mockOperation
        .mockRejectedValueOnce(new RateLimitError('openai'))
        .mockRejectedValueOnce(new RateLimitError('vertex-ai'))
        .mockRejectedValueOnce(new RateLimitError('anthropic'));

      await expect(failover.executeWithFailover(mockOperation, 'test-operation'))
        .rejects.toThrow('All providers failed');

      expect(mockOperation).toHaveBeenCalledTimes(3);
    });

    it('should apply failover delay', async () => {
      const startTime = Date.now();
      
      mockOperation
        .mockRejectedValueOnce(new RateLimitError('openai'))
        .mockResolvedValue(mockResponse);

      await failover.executeWithFailover(mockOperation, 'test-operation');

      const elapsed = Date.now() - startTime;
      expect(elapsed).toBeGreaterThan(900); // Should have some delay
    });

    it('should record failover history', async () => {
      mockOperation
        .mockRejectedValueOnce(new RateLimitError('openai'))
        .mockResolvedValue(mockResponse);

      await failover.executeWithFailover(mockOperation, 'test-operation');

      const history = failover.getFailoverHistory();
      expect(history).toHaveLength(1);
      expect(history[0].fromProvider).toBe('openai');
      expect(history[0].toProvider).toBe('vertex-ai');
      expect(history[0].success).toBe(true);
    });
  });

  describe('forceFailover', () => {
    it('should manually switch to specified provider', async () => {
      const success = await failover.forceFailover('anthropic');

      expect(success).toBe(true);
      expect(failover.getCurrentProvider()).toBe(mockFallbackProvider2);
    });

    it('should fail for unknown provider', async () => {
      await expect(failover.forceFailover('unknown' as any))
        .rejects.toThrow('Provider unknown not found');
    });

    it('should fail if target provider is unhealthy', async () => {
      mockFallbackProvider2.validateConfig.mockResolvedValue(false);

      const success = await failover.forceFailover('anthropic');

      expect(success).toBe(false);
      expect(failover.getCurrentProvider()).toBe(mockPrimaryProvider);
    });
  });

  describe('resetToPrimary', () => {
    it('should reset to primary provider when healthy', async () => {
      // First failover to secondary
      await failover.forceFailover('vertex-ai');
      expect(failover.getCurrentProvider()).toBe(mockFallbackProvider1);

      // Then reset to primary
      const success = await failover.resetToPrimary();

      expect(success).toBe(true);
      expect(failover.getCurrentProvider()).toBe(mockPrimaryProvider);
    });

    it('should fail to reset if primary is unhealthy', async () => {
      await failover.forceFailover('vertex-ai');
      mockPrimaryProvider.validateConfig.mockResolvedValue(false);

      const success = await failover.resetToPrimary();

      expect(success).toBe(false);
      expect(failover.getCurrentProvider()).toBe(mockFallbackProvider1);
    });

    it('should return true if already on primary', async () => {
      const success = await failover.resetToPrimary();

      expect(success).toBe(true);
      expect(failover.getCurrentProvider()).toBe(mockPrimaryProvider);
    });
  });

  describe('health monitoring', () => {
    it('should update health status on success', async () => {
      const mockOperation = jest.fn().mockResolvedValue({ data: 'success' });

      await failover.executeWithFailover(mockOperation, 'test');

      const healthStatus = failover.getHealthStatus();
      const primaryHealth = healthStatus.get('openai');

      expect(primaryHealth?.isHealthy).toBe(true);
      expect(primaryHealth?.consecutiveFailures).toBe(0);
      expect(primaryHealth?.totalRequests).toBe(1);
      expect(primaryHealth?.successfulRequests).toBe(1);
    });

    it('should update health status on failure', async () => {
      const mockOperation = jest.fn()
        .mockRejectedValue(new ProviderError('Auth failed', 'openai', 'AUTH_ERROR', 401));

      await expect(failover.executeWithFailover(mockOperation, 'test'))
        .rejects.toThrow();

      const healthStatus = failover.getHealthStatus();
      const primaryHealth = healthStatus.get('openai');

      expect(primaryHealth?.consecutiveFailures).toBe(1);
      expect(primaryHealth?.totalRequests).toBe(1);
      expect(primaryHealth?.successfulRequests).toBe(0);
      expect(primaryHealth?.lastError).toBeInstanceOf(ProviderError);
    });

    it('should mark provider unhealthy after consecutive failures', async () => {
      const mockOperation = jest.fn()
        .mockRejectedValue(new ProviderError('Server error', 'openai', 'SERVER_ERROR', 500));

      // Simulate multiple failures
      for (let i = 0; i < 3; i++) {
        try {
          await failover.executeWithFailover(mockOperation, 'test');
        } catch (error) {
          // Expected to fail
        }
      }

      const healthStatus = failover.getHealthStatus();
      const primaryHealth = healthStatus.get('openai');

      expect(primaryHealth?.isHealthy).toBe(false);
      expect(primaryHealth?.consecutiveFailures).toBe(3);
    });
  });

  describe('getFailoverStats', () => {
    it('should provide comprehensive statistics', async () => {
      const mockOperation = jest.fn()
        .mockRejectedValueOnce(new RateLimitError('openai'))
        .mockResolvedValue({ data: 'success' });

      await failover.executeWithFailover(mockOperation, 'test');

      const stats = failover.getFailoverStats();

      expect(stats.totalFailovers).toBe(1);
      expect(stats.successfulFailovers).toBe(1);
      expect(stats.currentProvider).toBe('vertex-ai');
      expect(stats.primaryProviderHealth).toBeDefined();
      expect(stats.overallHealthScore).toBeGreaterThanOrEqual(0);
    });
  });

  describe('cleanup', () => {
    it('should cleanup resources', () => {
      failover.cleanup();
      // Should not throw any errors
    });
  });

  describe('configuration', () => {
    it('should respect failover configuration', () => {
      const customConfig = {
        enabled: false,
        maxFailovers: 1,
        failoverDelay: 2000,
      };

      const customFailover = new ProviderFailover(
        primaryConfig,
        fallbackConfigs,
        customConfig
      );

      expect(customFailover).toBeDefined();
    });

    it('should disable failover when configured', async () => {
      const disabledFailover = new ProviderFailover(
        primaryConfig,
        fallbackConfigs,
        { enabled: false }
      );

      const mockOperation = jest.fn()
        .mockRejectedValue(new RateLimitError('openai'));

      await expect(disabledFailover.executeWithFailover(mockOperation, 'test'))
        .rejects.toThrow();

      expect(mockOperation).toHaveBeenCalledTimes(1);
    });
  });
});
