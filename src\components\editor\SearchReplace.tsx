import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Search, 
  Replace, 
  ChevronDown, 
  ChevronUp, 
  RotateCcw, 
  FileText, 
  CheckCircle, 
  XCircle,
  Eye,
  EyeOff,
  Settings,
  History,
  Bookmark
} from 'lucide-react';

interface SearchResult {
  fileId: string;
  fileName: string;
  lineNumber: number;
  columnStart: number;
  columnEnd: number;
  lineContent: string;
  matchText: string;
  context: {
    before: string[];
    after: string[];
  };
}

interface SearchOptions {
  caseSensitive: boolean;
  wholeWord: boolean;
  useRegex: boolean;
  includeFiles: string[];
  excludeFiles: string[];
  searchInComments: boolean;
  searchInStrings: boolean;
}

interface SearchReplaceProps {
  files: Array<{
    id: string;
    name: string;
    content: string;
    language: string;
  }>;
  onReplace: (fileId: string, replacements: Array<{
    start: number;
    end: number;
    replacement: string;
  }>) => void;
  onNavigateToResult: (fileId: string, lineNumber: number, columnStart: number) => void;
  isVisible: boolean;
  onClose: () => void;
}

export const SearchReplace = ({
  files,
  onReplace,
  onNavigateToResult,
  isVisible,
  onClose
}: SearchReplaceProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [replaceTerm, setReplaceTerm] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [currentResultIndex, setCurrentResultIndex] = useState(0);
  const [isReplaceMode, setIsReplaceMode] = useState(false);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [replaceHistory, setReplaceHistory] = useState<string[]>([]);
  const [selectedResults, setSelectedResults] = useState<Set<string>>(new Set());
  
  const [options, setOptions] = useState<SearchOptions>({
    caseSensitive: false,
    wholeWord: false,
    useRegex: false,
    includeFiles: [],
    excludeFiles: [],
    searchInComments: true,
    searchInStrings: true
  });

  const searchInputRef = useRef<HTMLInputElement>(null);
  const replaceInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isVisible && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isVisible]);

  const escapeRegex = (string: string) => {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  };

  const createSearchRegex = (term: string) => {
    let pattern = options.useRegex ? term : escapeRegex(term);
    
    if (options.wholeWord) {
      pattern = `\\b${pattern}\\b`;
    }
    
    const flags = options.caseSensitive ? 'g' : 'gi';
    
    try {
      return new RegExp(pattern, flags);
    } catch (error) {
      console.error('Invalid regex pattern:', error);
      return null;
    }
  };

  const shouldSearchInLine = (line: string, lineNumber: number, fileContent: string) => {
    // Simple heuristic to detect comments and strings
    const trimmedLine = line.trim();
    
    if (!options.searchInComments) {
      if (trimmedLine.startsWith('//') || 
          trimmedLine.startsWith('/*') || 
          trimmedLine.startsWith('*') ||
          trimmedLine.startsWith('#')) {
        return false;
      }
    }
    
    if (!options.searchInStrings) {
      // This is a simplified check - in a real implementation,
      // you'd want to use a proper parser
      const stringRegex = /(['"`])(?:(?!\1)[^\\]|\\.)*/g;
      if (stringRegex.test(line)) {
        return false;
      }
    }
    
    return true;
  };

  const performSearch = () => {
    if (!searchTerm.trim()) {
      setSearchResults([]);
      return;
    }

    const regex = createSearchRegex(searchTerm);
    if (!regex) return;

    const results: SearchResult[] = [];
    
    files.forEach(file => {
      // Check file inclusion/exclusion
      if (options.includeFiles.length > 0) {
        const included = options.includeFiles.some(pattern => 
          file.name.includes(pattern) || new RegExp(pattern).test(file.name)
        );
        if (!included) return;
      }
      
      if (options.excludeFiles.length > 0) {
        const excluded = options.excludeFiles.some(pattern => 
          file.name.includes(pattern) || new RegExp(pattern).test(file.name)
        );
        if (excluded) return;
      }

      const lines = file.content.split('\n');
      
      lines.forEach((line, lineIndex) => {
        if (!shouldSearchInLine(line, lineIndex, file.content)) return;
        
        let match;
        while ((match = regex.exec(line)) !== null) {
          const contextBefore = lines.slice(Math.max(0, lineIndex - 2), lineIndex);
          const contextAfter = lines.slice(lineIndex + 1, Math.min(lines.length, lineIndex + 3));
          
          results.push({
            fileId: file.id,
            fileName: file.name,
            lineNumber: lineIndex + 1,
            columnStart: match.index,
            columnEnd: match.index + match[0].length,
            lineContent: line,
            matchText: match[0],
            context: {
              before: contextBefore,
              after: contextAfter
            }
          });
          
          // Prevent infinite loop with zero-width matches
          if (match[0].length === 0) {
            regex.lastIndex++;
          }
        }
        
        // Reset regex lastIndex for next line
        regex.lastIndex = 0;
      });
    });

    setSearchResults(results);
    setCurrentResultIndex(0);
    
    // Add to search history
    if (searchTerm && !searchHistory.includes(searchTerm)) {
      setSearchHistory(prev => [searchTerm, ...prev.slice(0, 9)]);
    }
  };

  const performReplace = (replaceAll = false) => {
    if (!replaceTerm && replaceTerm !== '') return;
    
    const resultsToReplace = replaceAll 
      ? searchResults 
      : searchResults.filter((_, index) => selectedResults.has(`${index}`));
    
    // Group replacements by file
    const replacementsByFile = new Map<string, Array<{
      start: number;
      end: number;
      replacement: string;
    }>>();
    
    resultsToReplace.forEach(result => {
      if (!replacementsByFile.has(result.fileId)) {
        replacementsByFile.set(result.fileId, []);
      }
      
      const file = files.find(f => f.id === result.fileId);
      if (!file) return;
      
      const lines = file.content.split('\n');
      const lineStartOffset = lines.slice(0, result.lineNumber - 1)
        .reduce((acc, line) => acc + line.length + 1, 0);
      
      const startOffset = lineStartOffset + result.columnStart;
      const endOffset = lineStartOffset + result.columnEnd;
      
      replacementsByFile.get(result.fileId)!.push({
        start: startOffset,
        end: endOffset,
        replacement: replaceTerm
      });
    });
    
    // Apply replacements
    replacementsByFile.forEach((replacements, fileId) => {
      // Sort by start position in descending order to avoid offset issues
      replacements.sort((a, b) => b.start - a.start);
      onReplace(fileId, replacements);
    });
    
    // Add to replace history
    if (replaceTerm && !replaceHistory.includes(replaceTerm)) {
      setReplaceHistory(prev => [replaceTerm, ...prev.slice(0, 9)]);
    }
    
    // Clear selections and re-search
    setSelectedResults(new Set());
    setTimeout(performSearch, 100);
  };

  const navigateToResult = (index: number) => {
    if (index >= 0 && index < searchResults.length) {
      const result = searchResults[index];
      setCurrentResultIndex(index);
      onNavigateToResult(result.fileId, result.lineNumber, result.columnStart);
    }
  };

  const toggleResultSelection = (index: number) => {
    const key = `${index}`;
    setSelectedResults(prev => {
      const newSet = new Set(prev);
      if (newSet.has(key)) {
        newSet.delete(key);
      } else {
        newSet.add(key);
      }
      return newSet;
    });
  };

  const selectAllResults = () => {
    setSelectedResults(new Set(searchResults.map((_, index) => `${index}`)));
  };

  const clearAllSelections = () => {
    setSelectedResults(new Set());
  };

  if (!isVisible) return null;

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Search className="w-5 h-5" />
            <span>Search & Replace</span>
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <XCircle className="w-4 h-4" />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <Tabs defaultValue="search" className="w-full">
          <TabsList>
            <TabsTrigger value="search">Search</TabsTrigger>
            <TabsTrigger value="replace">Replace</TabsTrigger>
            <TabsTrigger value="options">Options</TabsTrigger>
          </TabsList>

          <TabsContent value="search" className="space-y-4">
            <div className="space-y-3">
              <div className="flex space-x-2">
                <div className="flex-1">
                  <Input
                    ref={searchInputRef}
                    placeholder="Search..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && performSearch()}
                  />
                </div>
                <Button onClick={performSearch}>
                  <Search className="w-4 h-4 mr-1" />
                  Search
                </Button>
              </div>

              {searchResults.length > 0 && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary">
                      {searchResults.length} results
                    </Badge>
                    <div className="flex items-center space-x-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigateToResult(currentResultIndex - 1)}
                        disabled={currentResultIndex === 0}
                      >
                        <ChevronUp className="w-4 h-4" />
                      </Button>
                      <span className="text-sm text-muted-foreground">
                        {currentResultIndex + 1} of {searchResults.length}
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigateToResult(currentResultIndex + 1)}
                        disabled={currentResultIndex === searchResults.length - 1}
                      >
                        <ChevronDown className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm" onClick={selectAllResults}>
                      Select All
                    </Button>
                    <Button variant="outline" size="sm" onClick={clearAllSelections}>
                      Clear
                    </Button>
                  </div>
                </div>
              )}
            </div>

            <ScrollArea className="h-64 border rounded">
              <div className="p-2 space-y-2">
                {searchResults.map((result, index) => (
                  <div
                    key={`${result.fileId}-${result.lineNumber}-${result.columnStart}`}
                    className={`p-2 border rounded cursor-pointer hover:bg-muted ${
                      index === currentResultIndex ? 'bg-muted border-primary' : ''
                    }`}
                    onClick={() => navigateToResult(index)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={selectedResults.has(`${index}`)}
                          onChange={() => toggleResultSelection(index)}
                          onClick={(e) => e.stopPropagation()}
                        />
                        <FileText className="w-4 h-4" />
                        <span className="font-medium text-sm">{result.fileName}</span>
                        <Badge variant="outline" className="text-xs">
                          Line {result.lineNumber}
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="mt-1 text-sm font-mono">
                      <span className="text-muted-foreground">
                        {result.lineContent.substring(0, result.columnStart)}
                      </span>
                      <span className="bg-yellow-200 dark:bg-yellow-800">
                        {result.matchText}
                      </span>
                      <span className="text-muted-foreground">
                        {result.lineContent.substring(result.columnEnd)}
                      </span>
                    </div>
                  </div>
                ))}
                
                {searchResults.length === 0 && searchTerm && (
                  <div className="text-center py-8 text-muted-foreground">
                    <Search className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p>No results found</p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="replace" className="space-y-4">
            <div className="space-y-3">
              <div className="flex space-x-2">
                <div className="flex-1">
                  <Input
                    placeholder="Replace with..."
                    value={replaceTerm}
                    onChange={(e) => setReplaceTerm(e.target.value)}
                  />
                </div>
                <Button 
                  onClick={() => performReplace(false)}
                  disabled={selectedResults.size === 0}
                >
                  <Replace className="w-4 h-4 mr-1" />
                  Replace Selected
                </Button>
                <Button 
                  onClick={() => performReplace(true)}
                  disabled={searchResults.length === 0}
                  variant="outline"
                >
                  Replace All
                </Button>
              </div>

              {selectedResults.size > 0 && (
                <Badge variant="secondary">
                  {selectedResults.size} selected for replacement
                </Badge>
              )}
            </div>
          </TabsContent>

          <TabsContent value="options" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label htmlFor="caseSensitive">Case Sensitive</Label>
                  <Switch
                    id="caseSensitive"
                    checked={options.caseSensitive}
                    onCheckedChange={(checked) => 
                      setOptions(prev => ({ ...prev, caseSensitive: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="wholeWord">Whole Word</Label>
                  <Switch
                    id="wholeWord"
                    checked={options.wholeWord}
                    onCheckedChange={(checked) => 
                      setOptions(prev => ({ ...prev, wholeWord: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="useRegex">Use Regex</Label>
                  <Switch
                    id="useRegex"
                    checked={options.useRegex}
                    onCheckedChange={(checked) => 
                      setOptions(prev => ({ ...prev, useRegex: checked }))
                    }
                  />
                </div>
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label htmlFor="searchInComments">Search in Comments</Label>
                  <Switch
                    id="searchInComments"
                    checked={options.searchInComments}
                    onCheckedChange={(checked) => 
                      setOptions(prev => ({ ...prev, searchInComments: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="searchInStrings">Search in Strings</Label>
                  <Switch
                    id="searchInStrings"
                    checked={options.searchInStrings}
                    onCheckedChange={(checked) => 
                      setOptions(prev => ({ ...prev, searchInStrings: checked }))
                    }
                  />
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
