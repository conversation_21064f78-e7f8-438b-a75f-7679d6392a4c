import { jest } from '@jest/globals';
import { AnthropicProvider } from '../../providers/AnthropicProvider.js';
import { ProviderConfig, PlanRequest, CritiqueRequest } from '../../types.js';

// Mock Anthropic SDK
jest.mock('@anthropic-ai/sdk', () => {
  return {
    default: jest.fn().mockImplementation(() => ({
      messages: {
        create: jest.fn(),
      },
    })),
    APIError: class APIError extends Error {
      constructor(message: string, public status?: number, public type?: string, public headers?: Record<string, string>) {
        super(message);
        this.name = 'APIError';
      }
    },
  };
});

describe('AnthropicProvider', () => {
  let provider: AnthropicProvider;
  let mockAnthropic: any;
  let config: ProviderConfig;

  beforeEach(() => {
    config = {
      type: 'anthropic',
      model: 'claude-opus-4-20250514',
      temperature: 0.7,
      maxTokens: 2000,
      apiKey: 'test-api-key',
    };

    // Reset mocks
    jest.clearAllMocks();
    
    // Setup mocks
    mockAnthropic = {
      messages: {
        create: jest.fn(),
      },
    };
    
    const Anthropic = require('@anthropic-ai/sdk').default;
    Anthropic.mockReturnValue(mockAnthropic);
    
    provider = new AnthropicProvider(config);
  });

  describe('constructor', () => {
    it('should create provider with valid config', () => {
      expect(provider).toBeInstanceOf(AnthropicProvider);
      expect(provider.getType()).toBe('anthropic');
      expect(provider.getModel()).toBe('claude-opus-4-20250514');
    });

    it('should throw error without API key', () => {
      const invalidConfig = { ...config };
      delete invalidConfig.apiKey;
      delete process.env.ANTHROPIC_API_KEY;

      expect(() => new AnthropicProvider(invalidConfig)).toThrow('Anthropic API key is required');
    });

    it('should use environment variable for API key', () => {
      process.env.ANTHROPIC_API_KEY = 'env-api-key';
      const configWithoutKey = { ...config };
      delete configWithoutKey.apiKey;

      expect(() => new AnthropicProvider(configWithoutKey)).not.toThrow();
      delete process.env.ANTHROPIC_API_KEY;
    });
  });

  describe('validateConfig', () => {
    it('should validate config successfully', async () => {
      mockAnthropic.messages.create.mockResolvedValue({
        id: 'test-id',
        content: [{ type: 'text', text: 'Hello' }],
        usage: { input_tokens: 1, output_tokens: 1 },
      });
      
      const isValid = await provider.validateConfig();
      expect(isValid).toBe(true);
      expect(mockAnthropic.messages.create).toHaveBeenCalled();
    });

    it('should return false on API error', async () => {
      mockAnthropic.messages.create.mockRejectedValue(new Error('API Error'));
      
      const isValid = await provider.validateConfig();
      expect(isValid).toBe(false);
    });
  });

  describe('generatePatch', () => {
    const mockRequest: PlanRequest = {
      prompt: 'Add a new user registration feature',
      context: { framework: 'React' },
    };

    const mockToolUseResponse = {
      id: 'test-response-id',
      content: [{
        type: 'tool_use',
        name: 'generate_json_patch',
        input: {
          operations: [
            { op: 'add', path: '/components/UserRegistration', value: { component: 'UserRegistration' } }
          ],
          description: 'Added user registration component',
          confidence: 0.9,
        },
      }],
      usage: {
        input_tokens: 100,
        output_tokens: 200,
      },
    };

    const mockTextResponse = {
      id: 'test-response-id',
      content: [{
        type: 'text',
        text: JSON.stringify({
          operations: [
            { op: 'add', path: '/components/UserRegistration', value: { component: 'UserRegistration' } }
          ],
          description: 'Added user registration component',
          confidence: 0.9,
        }),
      }],
      usage: {
        input_tokens: 100,
        output_tokens: 200,
      },
    };

    it('should generate patch successfully with tool use', async () => {
      mockAnthropic.messages.create.mockResolvedValue(mockToolUseResponse);

      const result = await provider.generatePatch(mockRequest);

      expect(result.data).toEqual({
        operations: [
          { op: 'add', path: '/components/UserRegistration', value: { component: 'UserRegistration' } }
        ],
        description: 'Added user registration component',
        confidence: 0.9,
      });
      expect(result.usage.totalTokens).toBe(300);
      expect(result.requestId).toBe('test-response-id');
    });

    it('should generate patch successfully with text fallback', async () => {
      mockAnthropic.messages.create.mockResolvedValue(mockTextResponse);

      const result = await provider.generatePatch(mockRequest);

      expect(result.data).toEqual({
        operations: [
          { op: 'add', path: '/components/UserRegistration', value: { component: 'UserRegistration' } }
        ],
        description: 'Added user registration component',
        confidence: 0.9,
      });
    });

    it('should handle API errors', async () => {
      const Anthropic = require('@anthropic-ai/sdk');
      const apiError = new Anthropic.APIError('Rate limit exceeded', 429);
      mockAnthropic.messages.create.mockRejectedValue(apiError);

      await expect(provider.generatePatch(mockRequest)).rejects.toThrow('Rate limit exceeded');
    });

    it('should handle invalid JSON response', async () => {
      const invalidResponse = {
        ...mockTextResponse,
        content: [{ type: 'text', text: 'invalid json' }],
      };
      mockAnthropic.messages.create.mockResolvedValue(invalidResponse);

      await expect(provider.generatePatch(mockRequest)).rejects.toThrow('Failed to parse Anthropic response as JSON');
    });

    it('should handle empty response', async () => {
      const emptyResponse = {
        ...mockTextResponse,
        content: [],
      };
      mockAnthropic.messages.create.mockResolvedValue(emptyResponse);

      await expect(provider.generatePatch(mockRequest)).rejects.toThrow('No content in Anthropic response');
    });
  });

  describe('scorePatch', () => {
    const mockRequest: CritiqueRequest = {
      patch: {
        operations: [{ op: 'add', path: '/test', value: 'test' }],
        description: 'Test patch',
        confidence: 0.8,
      },
      originalPrompt: 'Add test feature',
    };

    const mockToolUseResponse = {
      id: 'test-critique-id',
      content: [{
        type: 'tool_use',
        name: 'critique_json_patch',
        input: {
          score: 0.85,
          feedback: 'Good implementation with minor improvements needed',
          suggestions: ['Add error handling', 'Include tests'],
          isAcceptable: false,
        },
      }],
      usage: {
        input_tokens: 150,
        output_tokens: 100,
      },
    };

    it('should score patch successfully with tool use', async () => {
      mockAnthropic.messages.create.mockResolvedValue(mockToolUseResponse);

      const result = await provider.scorePatch(mockRequest);

      expect(result.data).toEqual({
        score: 0.85,
        feedback: 'Good implementation with minor improvements needed',
        suggestions: ['Add error handling', 'Include tests'],
        isAcceptable: false,
      });
      expect(result.usage.totalTokens).toBe(250);
    });
  });

  describe('supportsStreaming', () => {
    it('should return true for streaming support', () => {
      expect(provider.supportsStreaming()).toBe(true);
    });
  });

  describe('cost calculation', () => {
    it('should calculate cost correctly for known model', () => {
      const usage = { inputTokens: 1000, outputTokens: 500 };
      // claude-opus-4: input $0.015, output $0.075 per 1k tokens
      const expectedCost = (1000 * 0.015 + 500 * 0.075) / 1000;
      
      const cost = (provider as any).calculateCost(usage);
      expect(cost).toBeCloseTo(expectedCost, 6);
    });

    it('should use default rates for unknown model', () => {
      const unknownConfig = { ...config, model: 'unknown-model' };
      const unknownProvider = new AnthropicProvider(unknownConfig);
      
      const usage = { inputTokens: 1000, outputTokens: 500 };
      const cost = (unknownProvider as any).calculateCost(usage);
      
      expect(cost).toBeGreaterThan(0);
    });
  });

  describe('tool building', () => {
    it('should build planner tools correctly', () => {
      const plannerTools = (provider as any).buildAnthropicTools('planner');
      
      expect(plannerTools).toHaveLength(2);
      expect(plannerTools[0].name).toBe('generate_json_patch');
      expect(plannerTools[1].name).toBe('web_search_20250305');
    });

    it('should build critic tools correctly', () => {
      const criticTools = (provider as any).buildAnthropicTools('critic');
      
      expect(criticTools).toHaveLength(1);
      expect(criticTools[0].name).toBe('critique_json_patch');
    });
  });

  describe('validation', () => {
    it('should validate patch format correctly', () => {
      const validPatch = {
        operations: [{ op: 'add', path: '/test', value: 'test' }],
        description: 'Test patch',
        confidence: 0.8,
      };

      const result = (provider as any).validateAndFormatPatch(validPatch);
      expect(result).toEqual(validPatch);
    });

    it('should throw error for invalid patch', () => {
      const invalidPatch = {
        operations: 'not an array',
        description: 'Test patch',
        confidence: 0.8,
      };

      expect(() => (provider as any).validateAndFormatPatch(invalidPatch))
        .toThrow('Missing or invalid operations array');
    });

    it('should validate critique format correctly', () => {
      const validCritique = {
        score: 0.85,
        feedback: 'Good implementation',
        suggestions: ['Add tests'],
        isAcceptable: false,
      };

      const result = (provider as any).validateAndFormatCritique(validCritique);
      expect(result).toEqual(validCritique);
    });

    it('should throw error for invalid critique', () => {
      const invalidCritique = {
        score: 'not a number',
        feedback: 'Good implementation',
        suggestions: ['Add tests'],
        isAcceptable: false,
      };

      expect(() => (provider as any).validateAndFormatCritique(invalidCritique))
        .toThrow('Missing or invalid score');
    });
  });

  describe('retry after parsing', () => {
    it('should parse retry-after header correctly', () => {
      const headers = { 'retry-after': '60' };
      const retryAfter = (provider as any).parseRetryAfter(headers);
      expect(retryAfter).toBe(60000); // 60 seconds in milliseconds
    });

    it('should return undefined for missing retry-after header', () => {
      const headers = {};
      const retryAfter = (provider as any).parseRetryAfter(headers);
      expect(retryAfter).toBeUndefined();
    });
  });
});
