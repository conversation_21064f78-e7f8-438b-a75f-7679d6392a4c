import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AgentLog } from '../components/AgentLog';

// Mock date-fns
jest.mock('date-fns', () => ({
  formatDistanceToNow: jest.fn(() => '2 minutes ago'),
}));

describe('Real-time Streaming Tests', () => {
  const mockLogs = [
    {
      id: 1,
      agent: 'planner' as const,
      message: 'Starting code analysis...',
      timestamp: new Date('2024-01-01T10:00:00Z'),
      level: 'info' as const,
    },
    {
      id: 2,
      agent: 'critic' as const,
      message: 'Evaluating generated patch...',
      timestamp: new Date('2024-01-01T10:01:00Z'),
      level: 'success' as const,
    },
    {
      id: 3,
      agent: 'system' as const,
      message: 'Error occurred during processing',
      timestamp: new Date('2024-01-01T10:02:00Z'),
      level: 'error' as const,
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should render logs with proper structure', () => {
    render(<AgentLog logs={mockLogs} />);
    
    // Should have log container
    expect(screen.getByRole('log')).toBeInTheDocument();
    
    // Should display all logs
    expect(screen.getByText('Starting code analysis...')).toBeInTheDocument();
    expect(screen.getByText('Evaluating generated patch...')).toBeInTheDocument();
    expect(screen.getByText('Error occurred during processing')).toBeInTheDocument();
  });

  test('should show connection status indicator', () => {
    render(<AgentLog logs={mockLogs} enableRealTime={true} />);
    
    // Should show connection status
    expect(screen.getByText(/Live|Connecting|Offline/)).toBeInTheDocument();
  });

  test('should display agent badges with correct icons', () => {
    render(<AgentLog logs={mockLogs} />);
    
    // Should have agent badges
    expect(screen.getByText('planner')).toBeInTheDocument();
    expect(screen.getByText('critic')).toBeInTheDocument();
    expect(screen.getByText('system')).toBeInTheDocument();
  });

  test('should show log count badge when real-time is enabled', () => {
    render(<AgentLog logs={mockLogs} enableRealTime={true} />);
    
    // Should show log count
    expect(screen.getByText(/\d+ logs/)).toBeInTheDocument();
  });

  test('should have pause/resume functionality', async () => {
    render(<AgentLog logs={mockLogs} enableRealTime={true} />);
    
    // Should have pause button
    const pauseButton = screen.getByLabelText(/Pause real-time updates/);
    expect(pauseButton).toBeInTheDocument();
    
    // Click pause
    fireEvent.click(pauseButton);
    
    // Should change to resume button
    await waitFor(() => {
      expect(screen.getByLabelText(/Resume real-time updates/)).toBeInTheDocument();
    });
  });

  test('should have scroll to bottom functionality', () => {
    render(<AgentLog logs={mockLogs} />);
    
    // Should have scroll to bottom button
    const scrollButton = screen.getByLabelText('Scroll to bottom');
    expect(scrollButton).toBeInTheDocument();
    
    // Should be clickable
    fireEvent.click(scrollButton);
  });

  test('should have clear logs functionality when onClear is provided', () => {
    const mockOnClear = jest.fn();
    render(<AgentLog logs={mockLogs} onClear={mockOnClear} />);
    
    // Should have clear button
    const clearButton = screen.getByLabelText('Clear logs');
    expect(clearButton).toBeInTheDocument();
    
    // Click clear
    fireEvent.click(clearButton);
    
    // Should call onClear
    expect(mockOnClear).toHaveBeenCalled();
  });

  test('should show empty state when no logs', () => {
    render(<AgentLog logs={[]} />);
    
    // Should show empty state
    expect(screen.getByText('No logs yet')).toBeInTheDocument();
    expect(screen.getByText('Agent logs will appear here')).toBeInTheDocument();
  });

  test('should show offline message when real-time is enabled but disconnected', () => {
    render(<AgentLog logs={[]} enableRealTime={true} />);
    
    // Should show offline status
    expect(screen.getByText('Offline')).toBeInTheDocument();
  });

  test('should display log levels with appropriate styling', () => {
    render(<AgentLog logs={mockLogs} />);
    
    // Should have level badges for non-info levels
    expect(screen.getByText('success')).toBeInTheDocument();
    expect(screen.getByText('error')).toBeInTheDocument();
  });

  test('should be accessible with proper ARIA attributes', () => {
    render(<AgentLog logs={mockLogs} />);
    
    const logContainer = screen.getByRole('log');
    
    // Should have proper ARIA attributes
    expect(logContainer).toHaveAttribute('aria-label', 'Agent activity log');
    expect(logContainer).toHaveAttribute('aria-live', 'polite');
    expect(logContainer).toHaveAttribute('aria-atomic', 'false');
  });

  test('should handle real-time updates simulation', async () => {
    jest.useFakeTimers();
    
    render(<AgentLog logs={[]} enableRealTime={true} />);
    
    // Fast-forward time to trigger simulated updates
    jest.advanceTimersByTime(6000); // 6 seconds
    
    await waitFor(() => {
      // Should show connection status change
      expect(screen.getByText(/Live|Connecting/)).toBeInTheDocument();
    });
    
    jest.useRealTimers();
  });

  test('should limit logs to maxLogs parameter', () => {
    const manyLogs = Array.from({ length: 150 }, (_, i) => ({
      id: i,
      agent: 'system' as const,
      message: `Log message ${i}`,
      timestamp: new Date(),
      level: 'info' as const,
    }));
    
    render(<AgentLog logs={manyLogs} maxLogs={50} />);
    
    // Should only show maxLogs count
    const logContainer = screen.getByRole('log');
    const logEntries = logContainer.querySelectorAll('.group');
    expect(logEntries.length).toBeLessThanOrEqual(50);
  });

  test('should handle scroll events for auto-scroll detection', () => {
    render(<AgentLog logs={mockLogs} />);
    
    const logContainer = screen.getByRole('log');
    
    // Simulate scroll event
    fireEvent.scroll(logContainer, { target: { scrollTop: 100 } });
    
    // Should handle scroll without errors
    expect(logContainer).toBeInTheDocument();
  });

  test('should show timestamps on hover', async () => {
    render(<AgentLog logs={mockLogs} />);
    
    const logEntry = screen.getByText('Starting code analysis...').closest('.group');
    expect(logEntry).toBeInTheDocument();
    
    // Hover should reveal timestamp
    if (logEntry) {
      fireEvent.mouseEnter(logEntry);
      
      await waitFor(() => {
        expect(screen.getByText('2 minutes ago')).toBeInTheDocument();
      });
    }
  });

  test('should handle different agent types correctly', () => {
    const agentLogs = [
      { id: 1, agent: 'planner' as const, message: 'Planner message', timestamp: new Date() },
      { id: 2, agent: 'critic' as const, message: 'Critic message', timestamp: new Date() },
      { id: 3, agent: 'system' as const, message: 'System message', timestamp: new Date() },
    ];
    
    render(<AgentLog logs={agentLogs} />);
    
    // Should display all agent types
    expect(screen.getByText('planner')).toBeInTheDocument();
    expect(screen.getByText('critic')).toBeInTheDocument();
    expect(screen.getByText('system')).toBeInTheDocument();
  });

  test('should maintain accessibility during real-time updates', async () => {
    render(<AgentLog logs={[]} enableRealTime={true} />);
    
    const logContainer = screen.getByRole('log');
    
    // Should maintain ARIA attributes
    expect(logContainer).toHaveAttribute('aria-live', 'polite');
    
    // Simulate real-time update
    jest.useFakeTimers();
    jest.advanceTimersByTime(6000);
    
    await waitFor(() => {
      // Should still have proper ARIA attributes
      expect(logContainer).toHaveAttribute('aria-live', 'polite');
    });
    
    jest.useRealTimers();
  });
});
