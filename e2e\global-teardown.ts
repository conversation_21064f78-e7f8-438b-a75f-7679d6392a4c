import { FullConfig } from '@playwright/test';

/**
 * Global teardown for Playwright tests
 * Cleans up test environment and generates final reports
 */

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global teardown...');
  
  try {
    // Generate test summary
    const fs = require('fs');
    const path = require('path');
    
    const artifactsDir = path.join(process.cwd(), 'artifacts');
    const summaryPath = path.join(artifactsDir, 'test-summary.json');
    
    const summary = {
      timestamp: new Date().toISOString(),
      testSuite: 'Dashboard UI Controls',
      environment: {
        baseURL: config.projects[0].use.baseURL,
        browsers: config.projects.map(p => p.name),
        nodeVersion: process.version,
        playwrightVersion: require('@playwright/test/package.json').version
      },
      configuration: {
        fullyParallel: config.fullyParallel,
        retries: config.retries,
        timeout: config.timeout,
        workers: config.workers
      },
      artifacts: {
        htmlReport: 'artifacts/playwright-report/index.html',
        jsonResults: 'artifacts/test-results.json',
        junitResults: 'artifacts/junit-results.xml',
        screenshots: 'artifacts/test-results/',
        videos: 'artifacts/test-results/',
        traces: 'artifacts/test-results/'
      }
    };
    
    fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
    console.log('📊 Test summary generated:', summaryPath);
    
    // Log completion
    console.log('✅ Global teardown completed successfully!');
    console.log('📁 Test artifacts available in ./artifacts/');
    console.log('🌐 HTML report: ./artifacts/playwright-report/index.html');
    
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw error in teardown to avoid masking test failures
  }
}

export default globalTeardown;
