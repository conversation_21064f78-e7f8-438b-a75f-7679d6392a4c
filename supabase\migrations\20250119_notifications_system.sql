-- Migration: Notifications System
-- Description: Tables for user notifications and alerts
-- Date: 2025-01-19

BEGIN;

-- Notifications table
CREATE TABLE IF NOT EXISTS public.notifications (
    id TEXT PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('success', 'error', 'warning', 'info', 'system')),
    priority TEXT NOT NULL CHECK (priority IN ('low', 'medium', 'high', 'critical')),
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    read BOOLEAN DEFAULT false,
    dismissible BOOLEAN DEFAULT true,
    action_url TEXT,
    action_label TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON public.notifications(read);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON public.notifications(type);
CREATE INDEX IF NOT EXISTS idx_notifications_priority ON public.notifications(priority);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON public.notifications(created_at DESC);

-- Composite index for common queries
CREATE INDEX IF NOT EXISTS idx_notifications_user_read_created ON public.notifications(user_id, read, created_at DESC);

-- RLS policies for notifications
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Users can only see their own notifications
CREATE POLICY "Users can view own notifications" ON public.notifications
    FOR SELECT USING (auth.uid() = user_id);

-- Users can update their own notifications (mark as read, etc.)
CREATE POLICY "Users can update own notifications" ON public.notifications
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own dismissible notifications
CREATE POLICY "Users can delete own dismissible notifications" ON public.notifications
    FOR DELETE USING (auth.uid() = user_id AND dismissible = true);

-- Only authenticated users can insert notifications (for system notifications)
CREATE POLICY "Authenticated users can create notifications" ON public.notifications
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_notifications_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update updated_at on notifications
DROP TRIGGER IF EXISTS trigger_update_notifications_updated_at ON public.notifications;
CREATE TRIGGER trigger_update_notifications_updated_at
    BEFORE UPDATE ON public.notifications
    FOR EACH ROW
    EXECUTE FUNCTION update_notifications_updated_at();

-- Function to clean up old notifications (optional)
CREATE OR REPLACE FUNCTION cleanup_old_notifications()
RETURNS void AS $$
BEGIN
    -- Delete read notifications older than 30 days
    DELETE FROM public.notifications 
    WHERE read = true 
    AND dismissible = true 
    AND created_at < NOW() - INTERVAL '30 days';
    
    -- Delete unread low priority notifications older than 7 days
    DELETE FROM public.notifications 
    WHERE read = false 
    AND priority = 'low' 
    AND dismissible = true 
    AND created_at < NOW() - INTERVAL '7 days';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Insert some sample notifications for testing
INSERT INTO public.notifications (id, user_id, type, priority, title, message, metadata) VALUES
('notif_sample_1', (SELECT id FROM auth.users LIMIT 1), 'success', 'medium', 'Transformation Completed', 'Your code transformation finished successfully with a score of 0.94', '{"source": "reactor", "category": "transformation"}'),
('notif_sample_2', (SELECT id FROM auth.users LIMIT 1), 'warning', 'high', 'Approaching Cost Limit', 'You have used 85% of your daily budget. Consider reviewing your settings.', '{"source": "billing", "category": "budget"}'),
('notif_sample_3', (SELECT id FROM auth.users LIMIT 1), 'error', 'critical', 'API Rate Limit Exceeded', 'OpenAI API rate limit exceeded. Requests will be throttled for the next 15 minutes.', '{"source": "api", "category": "rate_limit"}'),
('notif_sample_4', (SELECT id FROM auth.users LIMIT 1), 'info', 'low', 'System Maintenance Scheduled', 'Scheduled maintenance will occur tonight from 2:00 AM to 4:00 AM UTC.', '{"source": "system", "category": "maintenance"}'),
('notif_sample_5', (SELECT id FROM auth.users LIMIT 1), 'system', 'medium', 'New Feature Available', 'GitHub PR creation is now available! Automatically create pull requests from your transformations.', '{"source": "product", "category": "feature"}')
ON CONFLICT (id) DO NOTHING;

COMMIT;
