-- Migration: Autonomous Repository Monitoring System
-- Description: Tables for autonomous repository monitoring and improvement tracking

-- Enable RLS
ALTER DATABASE postgres SET row_security = on;

-- Autonomous repositories table
CREATE TABLE IF NOT EXISTS autonomous_repositories (
    id TEXT PRIMARY KEY,
    owner TEXT NOT NULL,
    name TEXT NOT NULL,
    branch TEXT DEFAULT 'main',
    enabled BOOLEAN DEFAULT true,
    scan_interval INTEGER DEFAULT 60, -- minutes
    quality_threshold DECIMAL(3,2) DEFAULT 0.80,
    cost_limit DECIMAL(10,2) DEFAULT 3.00, -- USD
    last_scan TIMESTAMPTZ,
    last_commit TEXT,
    improvement_targets TEXT[] DEFAULT '{}',
    exclude_patterns TEXT[] DEFAULT ARRAY['node_modules/**', '.git/**', 'dist/**'],
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    UNIQUE(owner, name, user_id)
);

-- Repository scans table
CREATE TABLE IF NOT EXISTS repository_scans (
    id TEXT PRIMARY KEY,
    repository_id TEXT NOT NULL REFERENCES autonomous_repositories(id) ON DELETE CASCADE,
    started_at TIMESTAMPTZ NOT NULL,
    completed_at TIMESTAMPTZ,
    status TEXT NOT NULL CHECK (status IN ('scanning', 'completed', 'failed')),
    files_scanned INTEGER DEFAULT 0,
    opportunities_found INTEGER DEFAULT 0,
    total_estimated_cost DECIMAL(10,4) DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Improvement opportunities table
CREATE TABLE IF NOT EXISTS improvement_opportunities (
    id TEXT PRIMARY KEY,
    repository_id TEXT NOT NULL REFERENCES autonomous_repositories(id) ON DELETE CASCADE,
    scan_id TEXT NOT NULL REFERENCES repository_scans(id) ON DELETE CASCADE,
    file_path TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('performance', 'security', 'maintainability', 'bugs', 'style')),
    priority INTEGER NOT NULL CHECK (priority >= 1 AND priority <= 10),
    description TEXT NOT NULL,
    estimated_cost DECIMAL(10,4) NOT NULL,
    confidence DECIMAL(3,2) NOT NULL CHECK (confidence >= 0 AND confidence <= 1),
    code_snippet TEXT,
    suggested_improvement TEXT,
    detected_at TIMESTAMPTZ NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'queued', 'processing', 'completed', 'failed', 'skipped')),
    processed_at TIMESTAMPTZ,
    result_patch JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Autonomous processing jobs table
CREATE TABLE IF NOT EXISTS autonomous_jobs (
    id TEXT PRIMARY KEY,
    repository_id TEXT NOT NULL REFERENCES autonomous_repositories(id) ON DELETE CASCADE,
    opportunity_id TEXT REFERENCES improvement_opportunities(id) ON DELETE CASCADE,
    job_type TEXT NOT NULL CHECK (job_type IN ('scan', 'improve', 'test', 'pr')),
    status TEXT NOT NULL CHECK (status IN ('queued', 'processing', 'completed', 'failed', 'cancelled')),
    priority INTEGER DEFAULT 5,
    estimated_cost DECIMAL(10,4) DEFAULT 0,
    actual_cost DECIMAL(10,4) DEFAULT 0,
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    error_message TEXT,
    result_data JSONB,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Cost tracking for autonomous operations
CREATE TABLE IF NOT EXISTS autonomous_costs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    repository_id TEXT NOT NULL REFERENCES autonomous_repositories(id) ON DELETE CASCADE,
    job_id TEXT REFERENCES autonomous_jobs(id) ON DELETE CASCADE,
    operation_type TEXT NOT NULL,
    provider TEXT NOT NULL, -- 'openai', 'anthropic', 'google'
    model TEXT NOT NULL,
    tokens_used INTEGER,
    cost_usd DECIMAL(10,6) NOT NULL,
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    metadata JSONB
);

-- Repository statistics view
CREATE OR REPLACE VIEW repository_stats AS
SELECT 
    ar.id,
    ar.owner,
    ar.name,
    ar.enabled,
    ar.last_scan,
    COUNT(DISTINCT rs.id) as total_scans,
    COUNT(DISTINCT CASE WHEN rs.status = 'completed' THEN rs.id END) as successful_scans,
    COUNT(DISTINCT io.id) as total_opportunities,
    COUNT(DISTINCT CASE WHEN io.status = 'completed' THEN io.id END) as completed_improvements,
    COALESCE(SUM(ac.cost_usd), 0) as total_cost,
    COALESCE(AVG(rs.opportunities_found), 0) as avg_opportunities_per_scan,
    MAX(rs.completed_at) as last_successful_scan
FROM autonomous_repositories ar
LEFT JOIN repository_scans rs ON ar.id = rs.repository_id
LEFT JOIN improvement_opportunities io ON ar.id = io.repository_id
LEFT JOIN autonomous_costs ac ON ar.id = ac.repository_id
GROUP BY ar.id, ar.owner, ar.name, ar.enabled, ar.last_scan;

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_autonomous_repositories_user_id ON autonomous_repositories(user_id);
CREATE INDEX IF NOT EXISTS idx_autonomous_repositories_enabled ON autonomous_repositories(enabled);
CREATE INDEX IF NOT EXISTS idx_repository_scans_repository_id ON repository_scans(repository_id);
CREATE INDEX IF NOT EXISTS idx_repository_scans_status ON repository_scans(status);
CREATE INDEX IF NOT EXISTS idx_improvement_opportunities_repository_id ON improvement_opportunities(repository_id);
CREATE INDEX IF NOT EXISTS idx_improvement_opportunities_status ON improvement_opportunities(status);
CREATE INDEX IF NOT EXISTS idx_improvement_opportunities_priority ON improvement_opportunities(priority);
CREATE INDEX IF NOT EXISTS idx_autonomous_jobs_repository_id ON autonomous_jobs(repository_id);
CREATE INDEX IF NOT EXISTS idx_autonomous_jobs_status ON autonomous_jobs(status);
CREATE INDEX IF NOT EXISTS idx_autonomous_costs_repository_id ON autonomous_costs(repository_id);
CREATE INDEX IF NOT EXISTS idx_autonomous_costs_timestamp ON autonomous_costs(timestamp);

-- Row Level Security (RLS) policies
ALTER TABLE autonomous_repositories ENABLE ROW LEVEL SECURITY;
ALTER TABLE repository_scans ENABLE ROW LEVEL SECURITY;
ALTER TABLE improvement_opportunities ENABLE ROW LEVEL SECURITY;
ALTER TABLE autonomous_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE autonomous_costs ENABLE ROW LEVEL SECURITY;

-- RLS Policies for autonomous_repositories
CREATE POLICY "Users can view their own repositories" ON autonomous_repositories
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own repositories" ON autonomous_repositories
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own repositories" ON autonomous_repositories
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own repositories" ON autonomous_repositories
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for repository_scans
CREATE POLICY "Users can view scans for their repositories" ON repository_scans
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM autonomous_repositories ar 
            WHERE ar.id = repository_scans.repository_id 
            AND ar.user_id = auth.uid()
        )
    );

CREATE POLICY "System can insert scan records" ON repository_scans
    FOR INSERT WITH CHECK (true); -- Allow system to insert

-- RLS Policies for improvement_opportunities
CREATE POLICY "Users can view opportunities for their repositories" ON improvement_opportunities
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM autonomous_repositories ar 
            WHERE ar.id = improvement_opportunities.repository_id 
            AND ar.user_id = auth.uid()
        )
    );

CREATE POLICY "System can insert opportunities" ON improvement_opportunities
    FOR INSERT WITH CHECK (true); -- Allow system to insert

CREATE POLICY "System can update opportunities" ON improvement_opportunities
    FOR UPDATE USING (true); -- Allow system to update

-- RLS Policies for autonomous_jobs
CREATE POLICY "Users can view jobs for their repositories" ON autonomous_jobs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM autonomous_repositories ar 
            WHERE ar.id = autonomous_jobs.repository_id 
            AND ar.user_id = auth.uid()
        )
    );

CREATE POLICY "System can manage jobs" ON autonomous_jobs
    FOR ALL USING (true); -- Allow system full access

-- RLS Policies for autonomous_costs
CREATE POLICY "Users can view costs for their repositories" ON autonomous_costs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM autonomous_repositories ar 
            WHERE ar.id = autonomous_costs.repository_id 
            AND ar.user_id = auth.uid()
        )
    );

CREATE POLICY "System can insert costs" ON autonomous_costs
    FOR INSERT WITH CHECK (true); -- Allow system to insert

-- Functions for cost management
CREATE OR REPLACE FUNCTION get_repository_daily_cost(repo_id TEXT, target_date DATE DEFAULT CURRENT_DATE)
RETURNS DECIMAL(10,6) AS $$
BEGIN
    RETURN COALESCE(
        (SELECT SUM(cost_usd) 
         FROM autonomous_costs 
         WHERE repository_id = repo_id 
         AND DATE(timestamp) = target_date),
        0
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION get_repository_monthly_cost(repo_id TEXT, target_month DATE DEFAULT DATE_TRUNC('month', CURRENT_DATE))
RETURNS DECIMAL(10,6) AS $$
BEGIN
    RETURN COALESCE(
        (SELECT SUM(cost_usd) 
         FROM autonomous_costs 
         WHERE repository_id = repo_id 
         AND DATE_TRUNC('month', timestamp) = target_month),
        0
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_autonomous_repositories_updated_at
    BEFORE UPDATE ON autonomous_repositories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Improvement plans table
CREATE TABLE IF NOT EXISTS improvement_plans (
    id TEXT PRIMARY KEY,
    repository_id TEXT NOT NULL REFERENCES autonomous_repositories(id) ON DELETE CASCADE,
    opportunity_id TEXT NOT NULL REFERENCES improvement_opportunities(id) ON DELETE CASCADE,
    planner_prompt TEXT NOT NULL,
    critic_prompt TEXT NOT NULL,
    expected_outcome TEXT NOT NULL,
    risk_assessment TEXT NOT NULL,
    rollback_plan TEXT NOT NULL,
    testing_strategy TEXT NOT NULL,
    estimated_duration INTEGER NOT NULL, -- minutes
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'executing', 'completed', 'failed')),
    planner_response JSONB,
    critic_response JSONB,
    execution_result JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for improvement plans
CREATE INDEX IF NOT EXISTS idx_improvement_plans_repository_id ON improvement_plans(repository_id);
CREATE INDEX IF NOT EXISTS idx_improvement_plans_opportunity_id ON improvement_plans(opportunity_id);
CREATE INDEX IF NOT EXISTS idx_improvement_plans_status ON improvement_plans(status);
CREATE INDEX IF NOT EXISTS idx_improvement_plans_created_at ON improvement_plans(created_at);

-- RLS for improvement plans
ALTER TABLE improvement_plans ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view plans for their repositories" ON improvement_plans
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM autonomous_repositories ar
            WHERE ar.id = improvement_plans.repository_id
            AND ar.user_id = auth.uid()
        )
    );

CREATE POLICY "System can manage improvement plans" ON improvement_plans
    FOR ALL USING (true); -- Allow system full access

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON repository_stats TO authenticated;
GRANT EXECUTE ON FUNCTION get_repository_daily_cost(TEXT, DATE) TO authenticated;
GRANT EXECUTE ON FUNCTION get_repository_monthly_cost(TEXT, DATE) TO authenticated;
