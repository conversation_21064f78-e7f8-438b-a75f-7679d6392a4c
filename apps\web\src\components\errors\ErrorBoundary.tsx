import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  AlertTriangle, 
  RefreshCw, 
  Bug, 
  Home, 
  Copy,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { Error<PERSON><PERSON><PERSON>, ErrorCategory, ErrorSeverity } from '@/lib/errors/errorHandler';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  level?: 'page' | 'component' | 'widget';
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  showDetails: boolean;
  errorId: string | null;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error to our error handling system
    const appError = ErrorHandler.createError(
      ErrorCategory.SYSTEM,
      `React Error Boundary: ${error.message}`,
      {
        severity: ErrorSeverity.HIGH,
        details: errorInfo.componentStack,
        originalError: error,
        context: {
          componentStack: errorInfo.componentStack,
          level: this.props.level || 'component',
        },
        userMessage: 'A component error occurred. The page will try to recover.',
        recoverable: true,
        retryable: true,
      }
    );

    this.setState({
      errorInfo,
      errorId: appError.id,
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false,
      errorId: null,
    });
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleCopyError = () => {
    const errorText = `
Error ID: ${this.state.errorId}
Error: ${this.state.error?.message}
Stack: ${this.state.error?.stack}
Component Stack: ${this.state.errorInfo?.componentStack}
Timestamp: ${new Date().toISOString()}
    `.trim();

    navigator.clipboard.writeText(errorText).then(() => {
      // Could show a toast here
      console.log('Error details copied to clipboard');
    });
  };

  toggleDetails = () => {
    this.setState(prev => ({ showDetails: !prev.showDetails }));
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Different UI based on error level
      const level = this.props.level || 'component';
      
      if (level === 'widget') {
        return (
          <div className="p-4 border border-red-200 rounded-lg bg-red-50 dark:bg-red-900/20">
            <div className="flex items-center space-x-2 mb-2">
              <AlertTriangle className="w-4 h-4 text-red-500" />
              <span className="text-sm font-medium text-red-700 dark:text-red-300">
                Widget Error
              </span>
            </div>
            <p className="text-xs text-red-600 dark:text-red-400 mb-2">
              This component encountered an error and couldn't render.
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={this.handleRetry}
              className="h-6 px-2 text-xs"
            >
              <RefreshCw className="w-3 h-3 mr-1" />
              Retry
            </Button>
          </div>
        );
      }

      if (level === 'component') {
        return (
          <Alert variant="destructive" className="my-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-2">
                <p className="font-medium">Component Error</p>
                <p className="text-sm">
                  {this.state.error?.message || 'An unexpected error occurred in this component.'}
                </p>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={this.handleRetry}
                    className="h-7"
                  >
                    <RefreshCw className="w-3 h-3 mr-1" />
                    Retry
                  </Button>
                  {this.props.showDetails && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={this.toggleDetails}
                      className="h-7"
                    >
                      {this.state.showDetails ? (
                        <ChevronDown className="w-3 h-3 mr-1" />
                      ) : (
                        <ChevronRight className="w-3 h-3 mr-1" />
                      )}
                      Details
                    </Button>
                  )}
                </div>
                {this.state.showDetails && (
                  <div className="mt-2 p-2 bg-muted rounded text-xs font-mono">
                    <pre className="whitespace-pre-wrap">
                      {this.state.error?.stack}
                    </pre>
                  </div>
                )}
              </div>
            </AlertDescription>
          </Alert>
        );
      }

      // Page-level error
      return (
        <div className="min-h-screen bg-background flex items-center justify-center p-4">
          <Card className="w-full max-w-2xl">
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Bug className="w-8 h-8 text-red-500" />
              </div>
              <CardTitle className="text-xl text-red-700 dark:text-red-300">
                Oops! Something went wrong
              </CardTitle>
              <CardDescription>
                We encountered an unexpected error. Don't worry, we're working to fix it.
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-4">
              {/* Error Summary */}
              <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-red-700 dark:text-red-300">
                    Error Details
                  </span>
                  <Badge variant="outline" className="text-xs">
                    ID: {this.state.errorId?.slice(-8)}
                  </Badge>
                </div>
                <p className="text-sm text-red-600 dark:text-red-400">
                  {this.state.error?.message || 'An unexpected error occurred.'}
                </p>
              </div>

              {/* Actions */}
              <div className="flex flex-col sm:flex-row gap-2">
                <Button
                  onClick={this.handleRetry}
                  className="flex-1"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Try Again
                </Button>
                <Button
                  variant="outline"
                  onClick={this.handleGoHome}
                  className="flex-1"
                >
                  <Home className="w-4 h-4 mr-2" />
                  Go Home
                </Button>
              </div>

              {/* Technical Details */}
              <div className="space-y-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={this.toggleDetails}
                  className="w-full justify-start"
                >
                  {this.state.showDetails ? (
                    <ChevronDown className="w-4 h-4 mr-2" />
                  ) : (
                    <ChevronRight className="w-4 h-4 mr-2" />
                  )}
                  Technical Details
                </Button>

                {this.state.showDetails && (
                  <div className="space-y-2">
                    <div className="p-3 bg-muted rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-xs font-medium text-muted-foreground">
                          Error Stack
                        </span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={this.handleCopyError}
                          className="h-6 px-2"
                        >
                          <Copy className="w-3 h-3" />
                        </Button>
                      </div>
                      <pre className="text-xs font-mono whitespace-pre-wrap text-muted-foreground overflow-x-auto">
                        {this.state.error?.stack}
                      </pre>
                    </div>

                    {this.state.errorInfo?.componentStack && (
                      <div className="p-3 bg-muted rounded-lg">
                        <span className="text-xs font-medium text-muted-foreground block mb-2">
                          Component Stack
                        </span>
                        <pre className="text-xs font-mono whitespace-pre-wrap text-muted-foreground overflow-x-auto">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Help Text */}
              <div className="text-center text-sm text-muted-foreground">
                <p>
                  If this problem persists, please{' '}
                  <a href="/support" className="text-primary hover:underline">
                    contact support
                  </a>{' '}
                  with the error ID above.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for easier usage
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
};

// Specialized error boundaries
export const PageErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ErrorBoundary level="page" showDetails={process.env.NODE_ENV === 'development'}>
    {children}
  </ErrorBoundary>
);

export const ComponentErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ErrorBoundary level="component" showDetails={process.env.NODE_ENV === 'development'}>
    {children}
  </ErrorBoundary>
);

export const WidgetErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ErrorBoundary level="widget">
    {children}
  </ErrorBoundary>
);

export default ErrorBoundary;
