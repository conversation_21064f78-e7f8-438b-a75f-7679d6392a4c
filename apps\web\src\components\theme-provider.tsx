import * as React from "react"

type Theme = "dark" | "light" | "system"

type ThemeProviderProps = {
  children: React.ReactNode
  defaultTheme?: Theme
  storageKey?: string
}

type ThemeProviderState = {
  theme: Theme
  setTheme: (theme: Theme) => void
}

const initialState: ThemeProviderState = {
  theme: "system",
  setTheme: () => null,
}

const ThemeProviderContext = React.createContext<ThemeProviderState>(initialState)

export function ThemeProvider({
  children,
  defaultTheme = "dark", // Default to dark theme for Metamorphic Reactor
  storageKey = "metamorphic-reactor-theme",
  ...props
}: ThemeProviderProps) {
  const [theme, setTheme] = React.useState<Theme>(
    () => (localStorage.getItem(storageKey) as Theme) || defaultTheme
  )

  React.useEffect(() => {
    const root = window.document.documentElement

    root.classList.remove("light", "dark")

    if (theme === "system") {
      const systemTheme = window.matchMedia("(prefers-color-scheme: dark)")
        .matches
        ? "dark"
        : "light"

      root.classList.add(systemTheme)
      return
    }

    root.classList.add(theme)
  }, [theme])

  const value = {
    theme,
    setTheme: (theme: Theme) => {
      localStorage.setItem(storageKey, theme)
      setTheme(theme)
    },
  }

  return (
    <ThemeProviderContext.Provider {...props} value={value}>
      {children}
    </ThemeProviderContext.Provider>
  )
}

export const useTheme = () => {
  const context = React.useContext(ThemeProviderContext)

  if (context === undefined)
    throw new Error("useTheme must be used within a ThemeProvider")

  return context
}

// Theme toggle component
interface ThemeToggleProps {
  className?: string
}

export function ThemeToggle({ className }: ThemeToggleProps) {
  const { theme, setTheme } = useTheme()

  const toggleTheme = () => {
    if (theme === "light") {
      setTheme("dark")
    } else if (theme === "dark") {
      setTheme("system")
    } else {
      setTheme("light")
    }
  }

  const getThemeIcon = () => {
    switch (theme) {
      case "light":
        return "☀️"
      case "dark":
        return "🌙"
      case "system":
        return "💻"
      default:
        return "🌙"
    }
  }

  const getThemeLabel = () => {
    switch (theme) {
      case "light":
        return "Light"
      case "dark":
        return "Dark"
      case "system":
        return "System"
      default:
        return "Dark"
    }
  }

  return (
    <button
      onClick={toggleTheme}
      className={`inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background hover:bg-accent hover:text-accent-foreground h-10 py-2 px-4 ${className}`}
      title={`Switch to ${theme === "light" ? "dark" : theme === "dark" ? "system" : "light"} theme`}
    >
      <span className="mr-2">{getThemeIcon()}</span>
      <span className="sr-only">Toggle theme</span>
      <span className="hidden sm:inline">{getThemeLabel()}</span>
    </button>
  )
}

// Enhanced theme utilities
export const themeUtils = {
  // Get current effective theme (resolves system preference)
  getEffectiveTheme: (): "light" | "dark" => {
    const root = window.document.documentElement
    return root.classList.contains("dark") ? "dark" : "light"
  },

  // Check if dark mode is active
  isDarkMode: (): boolean => {
    return themeUtils.getEffectiveTheme() === "dark"
  },

  // Get theme-aware color value
  getThemeColor: (lightColor: string, darkColor: string): string => {
    return themeUtils.isDarkMode() ? darkColor : lightColor
  },

  // Apply theme-specific styles
  applyThemeStyles: (element: HTMLElement, styles: {
    light?: Partial<CSSStyleDeclaration>
    dark?: Partial<CSSStyleDeclaration>
  }) => {
    const effectiveTheme = themeUtils.getEffectiveTheme()
    const themeStyles = styles[effectiveTheme]
    
    if (themeStyles) {
      Object.assign(element.style, themeStyles)
    }
  }
}

// Theme-aware component wrapper
interface ThemeAwareProps {
  children: React.ReactNode
  lightClassName?: string
  darkClassName?: string
  className?: string
}

export function ThemeAware({ 
  children, 
  lightClassName = "", 
  darkClassName = "", 
  className = "" 
}: ThemeAwareProps) {
  const { theme } = useTheme()
  const [effectiveTheme, setEffectiveTheme] = React.useState<"light" | "dark">("dark")

  React.useEffect(() => {
    const updateEffectiveTheme = () => {
      setEffectiveTheme(themeUtils.getEffectiveTheme())
    }

    updateEffectiveTheme()

    // Listen for theme changes
    const observer = new MutationObserver(updateEffectiveTheme)
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    })

    return () => observer.disconnect()
  }, [theme])

  const themeSpecificClass = effectiveTheme === "dark" ? darkClassName : lightClassName

  return (
    <div className={`${className} ${themeSpecificClass}`.trim()}>
      {children}
    </div>
  )
}

// CSS-in-JS theme variables hook
export function useThemeVariables() {
  const { theme } = useTheme()
  
  return React.useMemo(() => {
    const isDark = themeUtils.isDarkMode()
    
    return {
      // Background colors
      background: isDark ? 'hsl(220.9 39.3% 11%)' : 'hsl(0 0% 100%)',
      foreground: isDark ? 'hsl(210 40% 98%)' : 'hsl(222.2 84% 4.9%)',
      
      // Card colors
      card: isDark ? 'hsl(220.9 39.3% 11%)' : 'hsl(0 0% 100%)',
      cardForeground: isDark ? 'hsl(210 40% 98%)' : 'hsl(222.2 84% 4.9%)',
      
      // Primary colors
      primary: 'hsl(238.7 83.5% 66.7%)', // indigo-500
      primaryForeground: 'hsl(0 0% 100%)',
      
      // Muted colors
      muted: isDark ? 'hsl(215 27.9% 16.9%)' : 'hsl(210 40% 96.1%)',
      mutedForeground: 'hsl(215.4 16.3% 46.9%)',
      
      // Border colors
      border: isDark ? 'hsl(215 27.9% 16.9%)' : 'hsl(214.3 31.8% 91.4%)',
      
      // Status colors
      success: isDark ? 'hsl(142.1 70.6% 45.3%)' : 'hsl(142.1 76.2% 36.3%)',
      warning: 'hsl(32.1 94.6% 43.7%)',
      error: isDark ? 'hsl(0 84.2% 60.2%)' : 'hsl(0 62.8% 30.6%)',
      info: 'hsl(221.2 83.2% 53.3%)',
      
      // Agent colors
      agentPlanner: 'hsl(238.7 83.5% 66.7%)', // indigo-500
      agentCritic: 'hsl(262.1 83.3% 57.8%)', // purple-500
      agentSystem: isDark ? 'hsl(215.4 16.3% 46.9%)' : 'hsl(220.9 39.3% 11%)',
    }
  }, [theme])
}
