import { 
  JSONPatch, 
  PlanRequest, 
  CritiqueRequest, 
  CritiqueResult, 
  ProviderConfig, 
  ProviderResponse, 
  TokenUsage,
  ProviderType 
} from '../types.js';

/**
 * Abstract base class for all AI provider implementations.
 * Provides a unified interface for different AI services (OpenAI, Vertex AI, Anthropic).
 */
export abstract class AgentProvider {
  protected config: ProviderConfig;
  protected totalCost: number = 0;
  protected totalTokens: number = 0;

  constructor(config: ProviderConfig) {
    this.config = config;
  }

  /**
   * Generate a JSON patch based on a plan request
   */
  abstract generatePatch(request: PlanRequest): Promise<ProviderResponse<JSONPatch>>;

  /**
   * Score and critique a JSON patch
   */
  abstract scorePatch(request: CritiqueRequest): Promise<ProviderResponse<CritiqueResult>>;

  /**
   * Validate the provider configuration
   */
  abstract validateConfig(): Promise<boolean>;

  /**
   * Get current token usage statistics
   */
  getTokenUsage(): TokenUsage {
    return {
      inputTokens: 0,
      outputTokens: 0,
      totalTokens: this.totalTokens,
      cost: this.totalCost,
      provider: this.config.type,
      model: this.config.model,
      timestamp: new Date()
    };
  }

  /**
   * Get current cost for this provider
   */
  getCost(): number {
    return this.totalCost;
  }

  /**
   * Reset cost and token counters
   */
  resetMetrics(): void {
    this.totalCost = 0;
    this.totalTokens = 0;
  }

  /**
   * Get provider type
   */
  getType(): ProviderType {
    return this.config.type;
  }

  /**
   * Get model name
   */
  getModel(): string {
    return this.config.model;
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<ProviderConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Check if provider supports streaming
   */
  abstract supportsStreaming(): boolean;

  /**
   * Stream generate patch (if supported)
   */
  async *streamGeneratePatch(request: PlanRequest): AsyncGenerator<Partial<JSONPatch>, ProviderResponse<JSONPatch>, unknown> {
    // Default implementation falls back to non-streaming
    const result = await this.generatePatch(request);
    yield result.data;
    return result;
  }

  /**
   * Stream score patch (if supported)
   */
  async *streamScorePatch(request: CritiqueRequest): AsyncGenerator<Partial<CritiqueResult>, ProviderResponse<CritiqueResult>, unknown> {
    // Default implementation falls back to non-streaming
    const result = await this.scorePatch(request);
    yield result.data;
    return result;
  }

  /**
   * Calculate cost based on token usage and model pricing
   */
  protected abstract calculateCost(usage: { inputTokens: number; outputTokens: number }): number;

  /**
   * Update metrics after API call
   */
  protected updateMetrics(usage: TokenUsage): void {
    this.totalTokens += usage.totalTokens;
    this.totalCost += usage.cost;
  }

  /**
   * Build system prompt for the provider
   */
  protected buildSystemPrompt(role: 'planner' | 'critic', customPrompt?: string): string {
    if (customPrompt) {
      return customPrompt;
    }

    const basePrompts = {
      planner: `You are a code planning agent. Generate precise JSON patches to implement requested changes.
Focus on being accurate, maintainable, and following best practices.
Always include a confidence score (0-1) and detailed description.`,
      critic: `You are a code critique agent. Evaluate JSON patches for correctness, completeness, quality, and safety.
Provide scores from 0.0 to 1.0, detailed feedback, and specific suggestions for improvement.
A patch is acceptable if it scores >= 0.95.`
    };

    return basePrompts[role];
  }
}

/**
 * Error types for provider operations
 */
export class ProviderError extends Error {
  constructor(
    message: string,
    public provider: ProviderType,
    public code?: string,
    public statusCode?: number
  ) {
    super(message);
    this.name = 'ProviderError';
  }
}

export class RateLimitError extends ProviderError {
  constructor(provider: ProviderType, retryAfter?: number) {
    super(`Rate limit exceeded for ${provider}`, provider, 'RATE_LIMIT', 429);
    this.retryAfter = retryAfter;
  }
  
  retryAfter?: number;
}

export class CostLimitError extends ProviderError {
  constructor(provider: ProviderType, currentCost: number, maxCost: number) {
    super(`Cost limit exceeded for ${provider}: $${currentCost} >= $${maxCost}`, provider, 'COST_LIMIT');
    this.currentCost = currentCost;
    this.maxCost = maxCost;
  }
  
  currentCost: number;
  maxCost: number;
}
