import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Package, 
  Download, 
  RefreshCw, 
  AlertTriangle,
  CheckCircle,
  Info,
  ExternalLink,
  GitBranch,
  Calendar,
  Tag
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface VersionInfo {
  current: string;
  latest: string;
  updateAvailable: boolean;
  releaseDate: string;
  changelog: Array<{
    version: string;
    date: string;
    changes: string[];
    type: 'major' | 'minor' | 'patch';
  }>;
  buildInfo: {
    commit: string;
    branch: string;
    buildDate: string;
    environment: string;
  };
}

interface VersionManagerProps {
  className?: string;
}

export const VersionManager: React.FC<VersionManagerProps> = ({ className }) => {
  const [versionInfo, setVersionInfo] = useState<VersionInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastChecked, setLastChecked] = useState<Date>(new Date());

  const fetchVersionInfo = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/version/info');
      
      if (!response.ok) {
        throw new Error(`Failed to fetch version info: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Mock data for demonstration - in real app this would come from the API
      const mockVersionInfo: VersionInfo = {
        current: '1.2.3',
        latest: '1.2.4',
        updateAvailable: true,
        releaseDate: '2024-01-15',
        changelog: [
          {
            version: '1.2.4',
            date: '2024-01-15',
            type: 'patch',
            changes: [
              'Fixed issue with GitHub PR creation',
              'Improved error handling in AI requests',
              'Updated dependencies for security patches',
              'Enhanced monitoring dashboard performance'
            ]
          },
          {
            version: '1.2.3',
            date: '2024-01-10',
            type: 'minor',
            changes: [
              'Added comprehensive monitoring dashboard',
              'Implemented billing and usage tracking',
              'Enhanced log viewer with search capabilities',
              'Added onboarding wizard for new users',
              'Improved mobile responsiveness'
            ]
          },
          {
            version: '1.2.2',
            date: '2024-01-05',
            type: 'patch',
            changes: [
              'Fixed queue management issues',
              'Improved cost tracking accuracy',
              'Enhanced error reporting',
              'Updated UI components'
            ]
          }
        ],
        buildInfo: {
          commit: 'a1b2c3d4',
          branch: 'main',
          buildDate: '2024-01-10T14:30:00Z',
          environment: 'production'
        }
      };

      setVersionInfo(data.success ? data.data : mockVersionInfo);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load version information');
    } finally {
      setIsLoading(false);
      setLastChecked(new Date());
    }
  };

  const checkForUpdates = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/version/check-updates', { method: 'POST' });
      if (response.ok) {
        await fetchVersionInfo();
      }
    } catch (err) {
      setError('Failed to check for updates');
    } finally {
      setIsLoading(false);
    }
  };

  const downloadUpdate = async () => {
    setIsUpdating(true);
    try {
      const response = await fetch('/api/version/download-update', { method: 'POST' });
      if (response.ok) {
        // In a real app, this might trigger a deployment process
        alert('Update downloaded successfully. Please restart the application to apply changes.');
      }
    } catch (err) {
      setError('Failed to download update');
    } finally {
      setIsUpdating(false);
    }
  };

  useEffect(() => {
    fetchVersionInfo();
  }, []);

  const getVersionBadgeVariant = (type: string) => {
    switch (type) {
      case 'major':
        return 'destructive';
      case 'minor':
        return 'default';
      case 'patch':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (isLoading && !versionInfo) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Package className="w-5 h-5" />
            <span>Version Management</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="w-6 h-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Package className="w-5 h-5" />
            <span>Version Management</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertTriangle className="w-4 h-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Package className="w-5 h-5 text-primary" />
            <CardTitle>Version Management</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={checkForUpdates}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={cn("w-4 h-4", isLoading && "animate-spin")} />
            </Button>
          </div>
        </div>
        <CardDescription>
          Current version and update management • Last checked: {lastChecked.toLocaleTimeString()}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Version Info */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center space-x-2">
                <Tag className="w-4 h-4" />
                <span>Current Version</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="text-2xl font-bold">{versionInfo?.current}</div>
                <div className="text-sm text-muted-foreground">
                  Released: {formatDate(versionInfo?.releaseDate || '')}
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline">
                    {versionInfo?.buildInfo.environment}
                  </Badge>
                  <Badge variant="secondary">
                    {versionInfo?.buildInfo.branch}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center space-x-2">
                <GitBranch className="w-4 h-4" />
                <span>Build Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Commit:</span>
                  <code className="text-xs bg-muted px-1 rounded">
                    {versionInfo?.buildInfo.commit}
                  </code>
                </div>
                <div className="flex justify-between">
                  <span>Branch:</span>
                  <span>{versionInfo?.buildInfo.branch}</span>
                </div>
                <div className="flex justify-between">
                  <span>Built:</span>
                  <span>{formatDate(versionInfo?.buildInfo.buildDate || '')}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Update Available */}
        {versionInfo?.updateAvailable && (
          <Alert>
            <Info className="w-4 h-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>
                Version {versionInfo.latest} is available for download.
              </span>
              <Button
                size="sm"
                onClick={downloadUpdate}
                disabled={isUpdating}
              >
                {isUpdating ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Download className="w-4 h-4 mr-2" />
                )}
                {isUpdating ? 'Downloading...' : 'Download Update'}
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Changelog */}
        <div>
          <h3 className="text-lg font-medium mb-3 flex items-center space-x-2">
            <Calendar className="w-4 h-4" />
            <span>Recent Changes</span>
          </h3>
          <ScrollArea className="h-64 border rounded-lg">
            <div className="p-4 space-y-4">
              {versionInfo?.changelog.map((release, index) => (
                <div key={release.version} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-medium">v{release.version}</h4>
                      <Badge variant={getVersionBadgeVariant(release.type) as any}>
                        {release.type}
                      </Badge>
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {formatDate(release.date)}
                    </span>
                  </div>
                  <ul className="space-y-1 text-sm text-muted-foreground">
                    {release.changes.map((change, changeIndex) => (
                      <li key={changeIndex} className="flex items-start space-x-2">
                        <span className="text-primary mt-1">•</span>
                        <span>{change}</span>
                      </li>
                    ))}
                  </ul>
                  {index < versionInfo.changelog.length - 1 && (
                    <hr className="my-3 border-border" />
                  )}
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>

        {/* Actions */}
        <div className="flex justify-between items-center pt-4 border-t">
          <div className="text-sm text-muted-foreground">
            {versionInfo?.updateAvailable ? (
              <span className="text-blue-600">Update available</span>
            ) : (
              <span className="text-green-600">You're up to date</span>
            )}
          </div>
          <div className="space-x-2">
            <Button variant="outline" size="sm">
              <ExternalLink className="w-4 h-4 mr-2" />
              View on GitHub
            </Button>
            <Button variant="outline" size="sm" onClick={checkForUpdates}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Check for Updates
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default VersionManager;
