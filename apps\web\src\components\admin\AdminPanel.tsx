import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Shield, 
  Users, 
  Settings, 
  Activity,
  Key,
  Database,
  Server,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Plus,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Copy,
  RefreshCw
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { HStack, VStack } from '@/components/ui/layout';
import { Typography } from '@/components/ui/typography';

interface AdminPanelProps {
  className?: string;
}

interface ApiKey {
  id: string;
  name: string;
  key: string;
  permissions: string[];
  created_at: string;
  last_used: string | null;
  usage_count: number;
  is_active: boolean;
}

interface SystemConfig {
  max_requests_per_minute: number;
  max_concurrent_sessions: number;
  default_timeout_seconds: number;
  enable_telemetry: boolean;
  enable_debug_logging: boolean;
  maintenance_mode: boolean;
}

interface UserInfo {
  id: string;
  email: string;
  role: 'admin' | 'user';
  created_at: string;
  last_login: string | null;
  is_active: boolean;
  usage_stats: {
    total_sessions: number;
    total_cost: number;
    last_30_days: number;
  };
}

export const AdminPanel: React.FC<AdminPanelProps> = ({ className }) => {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [systemConfig, setSystemConfig] = useState<SystemConfig>({
    max_requests_per_minute: 60,
    max_concurrent_sessions: 10,
    default_timeout_seconds: 180,
    enable_telemetry: true,
    enable_debug_logging: false,
    maintenance_mode: false
  });
  const [users, setUsers] = useState<UserInfo[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('secrets');

  // Fetch admin data
  const fetchAdminData = async () => {
    setIsLoading(true);
    try {
      // Fetch API keys
      const keysResponse = await fetch('/api/secrets');
      if (keysResponse.ok) {
        const keysData = await keysResponse.json();
        setApiKeys(keysData.data || []);
      }

      // Fetch system config
      const configResponse = await fetch('/api/admin/config');
      if (configResponse.ok) {
        const configData = await configResponse.json();
        setSystemConfig(prev => ({ ...prev, ...configData }));
      }

      // Fetch users (mock data for now)
      setUsers([
        {
          id: '1',
          email: '<EMAIL>',
          role: 'admin',
          created_at: '2024-01-01T00:00:00Z',
          last_login: new Date().toISOString(),
          is_active: true,
          usage_stats: {
            total_sessions: 150,
            total_cost: 45.67,
            last_30_days: 25
          }
        },
        {
          id: '2',
          email: '<EMAIL>',
          role: 'user',
          created_at: '2024-01-15T00:00:00Z',
          last_login: new Date(Date.now() - 86400000).toISOString(),
          is_active: true,
          usage_stats: {
            total_sessions: 75,
            total_cost: 12.34,
            last_30_days: 10
          }
        }
      ]);

    } catch (error) {
      console.error('Failed to fetch admin data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAdminData();
  }, []);

  const handleCreateApiKey = async () => {
    try {
      const response = await fetch('/api/secrets', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: `API Key ${Date.now()}`,
          permissions: ['read', 'write']
        })
      });
      
      if (response.ok) {
        fetchAdminData();
      }
    } catch (error) {
      console.error('Failed to create API key:', error);
    }
  };

  const handleDeleteApiKey = async (keyId: string) => {
    try {
      const response = await fetch(`/api/secrets/${keyId}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        setApiKeys(prev => prev.filter(key => key.id !== keyId));
      }
    } catch (error) {
      console.error('Failed to delete API key:', error);
    }
  };

  const handleUpdateConfig = async (newConfig: Partial<SystemConfig>) => {
    try {
      const response = await fetch('/api/admin/config', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newConfig)
      });
      
      if (response.ok) {
        setSystemConfig(prev => ({ ...prev, ...newConfig }));
      }
    } catch (error) {
      console.error('Failed to update config:', error);
    }
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <div className="flex items-center space-x-2">
          <Shield className="w-6 h-6 text-primary" />
          <CardTitle className="text-xl">Admin Panel</CardTitle>
        </div>
        <CardDescription>
          System administration, user management, and configuration
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="secrets">API Keys</TabsTrigger>
            <TabsTrigger value="users">Users</TabsTrigger>
            <TabsTrigger value="config">System</TabsTrigger>
            <TabsTrigger value="health">Health</TabsTrigger>
          </TabsList>

          {/* API Keys Management */}
          <TabsContent value="secrets" className="space-y-4">
            <div className="flex justify-between items-center">
              <Typography variant="h6">API Key Management</Typography>
              <Button onClick={handleCreateApiKey} size="sm">
                <Plus className="w-4 h-4 mr-2" />
                Create Key
              </Button>
            </div>

            <ScrollArea className="h-96">
              <div className="space-y-3">
                {apiKeys.map((apiKey) => (
                  <Card key={apiKey.id} className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <Typography variant="body-sm" className="font-medium">
                            {apiKey.name}
                          </Typography>
                          <Badge variant={apiKey.is_active ? 'default' : 'secondary'}>
                            {apiKey.is_active ? 'Active' : 'Inactive'}
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                          <div>Created: {new Date(apiKey.created_at).toLocaleDateString()}</div>
                          <div>Usage: {apiKey.usage_count} requests</div>
                          <div>Last used: {apiKey.last_used ? new Date(apiKey.last_used).toLocaleDateString() : 'Never'}</div>
                          <div>Permissions: {apiKey.permissions.join(', ')}</div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Button variant="ghost" size="sm">
                          <Copy className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleDeleteApiKey(apiKey.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>

          {/* User Management */}
          <TabsContent value="users" className="space-y-4">
            <Typography variant="h6">User Management</Typography>
            
            <ScrollArea className="h-96">
              <div className="space-y-3">
                {users.map((user) => (
                  <Card key={user.id} className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <Typography variant="body-sm" className="font-medium">
                            {user.email}
                          </Typography>
                          <Badge variant={user.role === 'admin' ? 'default' : 'secondary'}>
                            {user.role}
                          </Badge>
                          <Badge variant={user.is_active ? 'default' : 'destructive'}>
                            {user.is_active ? 'Active' : 'Inactive'}
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                          <div>Joined: {new Date(user.created_at).toLocaleDateString()}</div>
                          <div>Last login: {user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}</div>
                          <div>Sessions: {user.usage_stats.total_sessions}</div>
                          <div>Total cost: ${user.usage_stats.total_cost.toFixed(2)}</div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Button variant="ghost" size="sm">
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Settings className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>

          {/* System Configuration */}
          <TabsContent value="config" className="space-y-4">
            <Typography variant="h6">System Configuration</Typography>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Max Requests per Minute</Label>
                <Input
                  type="number"
                  value={systemConfig.max_requests_per_minute}
                  onChange={(e) => setSystemConfig(prev => ({
                    ...prev,
                    max_requests_per_minute: parseInt(e.target.value) || 60
                  }))}
                />
              </div>
              
              <div className="space-y-2">
                <Label>Max Concurrent Sessions</Label>
                <Input
                  type="number"
                  value={systemConfig.max_concurrent_sessions}
                  onChange={(e) => setSystemConfig(prev => ({
                    ...prev,
                    max_concurrent_sessions: parseInt(e.target.value) || 10
                  }))}
                />
              </div>
              
              <div className="space-y-2">
                <Label>Default Timeout (seconds)</Label>
                <Input
                  type="number"
                  value={systemConfig.default_timeout_seconds}
                  onChange={(e) => setSystemConfig(prev => ({
                    ...prev,
                    default_timeout_seconds: parseInt(e.target.value) || 180
                  }))}
                />
              </div>
            </div>

            <div className="space-y-4 pt-4 border-t">
              <div className="flex items-center justify-between">
                <div>
                  <Typography variant="body-sm" className="font-medium">Enable Telemetry</Typography>
                  <Typography variant="caption" className="text-muted-foreground">
                    Collect usage analytics and performance metrics
                  </Typography>
                </div>
                <Button
                  variant={systemConfig.enable_telemetry ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSystemConfig(prev => ({
                    ...prev,
                    enable_telemetry: !prev.enable_telemetry
                  }))}
                >
                  {systemConfig.enable_telemetry ? 'Enabled' : 'Disabled'}
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Typography variant="body-sm" className="font-medium">Debug Logging</Typography>
                  <Typography variant="caption" className="text-muted-foreground">
                    Enable detailed debug logs (impacts performance)
                  </Typography>
                </div>
                <Button
                  variant={systemConfig.enable_debug_logging ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSystemConfig(prev => ({
                    ...prev,
                    enable_debug_logging: !prev.enable_debug_logging
                  }))}
                >
                  {systemConfig.enable_debug_logging ? 'Enabled' : 'Disabled'}
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Typography variant="body-sm" className="font-medium">Maintenance Mode</Typography>
                  <Typography variant="caption" className="text-muted-foreground">
                    Temporarily disable new sessions
                  </Typography>
                </div>
                <Button
                  variant={systemConfig.maintenance_mode ? 'destructive' : 'outline'}
                  size="sm"
                  onClick={() => setSystemConfig(prev => ({
                    ...prev,
                    maintenance_mode: !prev.maintenance_mode
                  }))}
                >
                  {systemConfig.maintenance_mode ? 'Active' : 'Inactive'}
                </Button>
              </div>
            </div>

            <div className="pt-4 border-t">
              <Button 
                onClick={() => handleUpdateConfig(systemConfig)}
                disabled={isLoading}
              >
                <RefreshCw className={cn("w-4 h-4 mr-2", isLoading && "animate-spin")} />
                Save Configuration
              </Button>
            </div>
          </TabsContent>

          {/* System Health */}
          <TabsContent value="health" className="space-y-4">
            <Typography variant="h6">System Health Monitor</Typography>
            
            <Alert>
              <Activity className="h-4 w-4" />
              <AlertDescription>
                System health monitoring is integrated with the main dashboard. 
                Check the System Health Indicators widget for real-time status.
              </AlertDescription>
            </Alert>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="p-4">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <Typography variant="body-sm" className="font-medium">API Server</Typography>
                </div>
                <Typography variant="caption" className="text-muted-foreground">
                  Operational
                </Typography>
              </Card>

              <Card className="p-4">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <Typography variant="body-sm" className="font-medium">Database</Typography>
                </div>
                <Typography variant="caption" className="text-muted-foreground">
                  Connected
                </Typography>
              </Card>

              <Card className="p-4">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="w-5 h-5 text-yellow-500" />
                  <Typography variant="body-sm" className="font-medium">External APIs</Typography>
                </div>
                <Typography variant="caption" className="text-muted-foreground">
                  Rate limited
                </Typography>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default AdminPanel;
