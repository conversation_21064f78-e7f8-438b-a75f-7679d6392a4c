import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				},
				success: {
					DEFAULT: 'hsl(var(--success))',
					foreground: 'hsl(var(--success-foreground))'
				},
				warning: {
					DEFAULT: 'hsl(var(--warning))',
					foreground: 'hsl(var(--warning-foreground))'
				},
				info: {
					DEFAULT: 'hsl(var(--info))',
					foreground: 'hsl(var(--info-foreground))'
				},
				'status-queued': {
					DEFAULT: 'hsl(var(--status-queued))',
					foreground: 'hsl(var(--status-queued-foreground))'
				},
				'status-running': {
					DEFAULT: 'hsl(var(--status-running))',
					foreground: 'hsl(var(--status-running-foreground))'
				},
				'status-completed': {
					DEFAULT: 'hsl(var(--status-completed))',
					foreground: 'hsl(var(--status-completed-foreground))'
				},
				'status-failed': {
					DEFAULT: 'hsl(var(--status-failed))',
					foreground: 'hsl(var(--status-failed-foreground))'
				},
				'agent-planner': {
					DEFAULT: 'hsl(var(--agent-planner))',
					foreground: 'hsl(var(--agent-planner-foreground))'
				},
				'agent-critic': {
					DEFAULT: 'hsl(var(--agent-critic))',
					foreground: 'hsl(var(--agent-critic-foreground))'
				},
				'agent-system': {
					DEFAULT: 'hsl(var(--agent-system))',
					foreground: 'hsl(var(--agent-system-foreground))'
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			fontFamily: {
				sans: ['Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
				mono: ['JetBrains Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', 'monospace']
			},
			fontSize: {
				'h1': ['2.25rem', { lineHeight: '1.1', letterSpacing: '-0.025em', fontWeight: '700' }],
				'h2': ['1.875rem', { lineHeight: '1.2', letterSpacing: '-0.025em', fontWeight: '600' }],
				'h3': ['1.5rem', { lineHeight: '1.25', letterSpacing: '-0.025em', fontWeight: '600' }],
				'h4': ['1.25rem', { lineHeight: '1.3', letterSpacing: '-0.025em', fontWeight: '600' }],
				'h5': ['1.125rem', { lineHeight: '1.4', fontWeight: '500' }],
				'h6': ['1rem', { lineHeight: '1.5', fontWeight: '500' }],
				'body-lg': ['1.125rem', { lineHeight: '1.6' }],
				'body': ['1rem', { lineHeight: '1.6' }],
				'body-sm': ['0.875rem', { lineHeight: '1.5' }],
				'caption': ['0.75rem', { lineHeight: '1.4' }]
			},
			spacing: {
				'section': '2rem',
				'component': '1rem',
				'element': '0.5rem'
			},
			backdropBlur: {
				xs: '2px'
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				},
				'gradient-x': {
					'0%, 100%': {
						'background-size': '200% 200%',
						'background-position': 'left center'
					},
					'50%': {
						'background-size': '200% 200%',
						'background-position': 'right center'
					}
				},
				'gradient-y': {
					'0%, 100%': {
						'background-size': '200% 200%',
						'background-position': 'center top'
					},
					'50%': {
						'background-size': '200% 200%',
						'background-position': 'center bottom'
					}
				},
				'pulse-glow': {
					'0%, 100%': {
						'box-shadow': '0 0 0 0 hsl(var(--primary) / 0.4)'
					},
					'50%': {
						'box-shadow': '0 0 0 8px hsl(var(--primary) / 0)'
					}
				},
				'skeleton-loading': {
					'0%': {
						'background-position': '-200px 0'
					},
					'100%': {
						'background-position': 'calc(200px + 100%) 0'
					}
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'gradient-x': 'gradient-x 3s ease infinite',
				'gradient-y': 'gradient-y 3s ease infinite',
				'pulse-glow': 'pulse-glow 2s ease-in-out infinite',
				'skeleton-loading': 'skeleton-loading 1.5s infinite'
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
