
import { useEffect, useRef } from 'react';
import { Badge } from "@/components/ui/badge";
import { VStack, HStack } from "@/components/ui/layout";
import { AgentIndicator, AgentPlannerIcon } from "@/components/ui/icon";
import { formatDistanceToNow } from "date-fns";

interface LogEntry {
  id: number;
  agent: 'planner' | 'critic' | 'system';
  message: string;
  timestamp: Date;
}

interface AgentLogProps {
  logs: LogEntry[];
}

export const AgentLog = ({ logs }: AgentLogProps) => {
  const scrollRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [logs]);

  const getAgentIcon = (agent: 'planner' | 'critic' | 'system') => {
    return <AgentIndicator agent={agent} aria-hidden="true" />;
  };

  const getAgentColor = (agent: string) => {
    switch (agent) {
      case 'planner': return "bg-agent-planner/20 text-agent-planner border-agent-planner/30";
      case 'critic': return "bg-agent-critic/20 text-agent-critic border-agent-critic/30";
      case 'system': return "bg-agent-system/20 text-agent-system border-agent-system/30";
      default: return "bg-muted/20 text-muted-foreground border-muted/30";
    }
  };

  return (
    <div
      ref={scrollRef}
      className="h-full bg-background overflow-auto p-4"
      role="log"
      aria-label="Agent activity log"
    >
      <VStack spacing="sm">
        {logs.map((log) => (
          <div key={log.id} className="group">
            <HStack spacing="sm" align="start">
              <div className="flex-shrink-0 mt-1">
                <Badge className={`${getAgentColor(log.agent)} px-2 py-1`}>
                  <HStack spacing="xs" align="center">
                    {getAgentIcon(log.agent)}
                    <span className="text-caption font-medium capitalize">{log.agent}</span>
                  </HStack>
                </Badge>
              </div>
              <VStack spacing="xs" className="flex-1 min-w-0">
                <p className="text-body-sm text-foreground leading-relaxed">{log.message}</p>
                <p className="text-caption text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity">
                  {formatDistanceToNow(log.timestamp, { addSuffix: true })}
                </p>
              </VStack>
            </HStack>
          </div>
        ))}

        {logs.length === 0 && (
          <div className="text-center py-8">
            <VStack spacing="sm" align="center">
              <div className="w-12 h-12 rounded-full bg-muted flex items-center justify-center">
                <AgentPlannerIcon size="lg" className="text-muted-foreground" aria-hidden="true" />
              </div>
              <VStack spacing="xs" align="center">
                <p className="text-body-sm text-muted-foreground">No logs yet</p>
                <p className="text-caption text-muted-foreground">Agent logs will appear here</p>
              </VStack>
            </VStack>
          </div>
        )}
      </VStack>
    </div>
  );
};
