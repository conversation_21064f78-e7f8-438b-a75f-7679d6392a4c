import { z } from 'zod';
import { TransformationRequest, TransformationResult } from '../agents/dualAgentSystem';

// Job Priority Levels
export enum JobPriority {
  LOW = 1,
  NORMAL = 2,
  HIGH = 3,
  URGENT = 4,
}

// Job Status
export enum JobStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  RETRYING = 'retrying',
}

// Queue Job Schema
export const queueJobSchema = z.object({
  id: z.string(),
  priority: z.nativeEnum(JobPriority),
  status: z.nativeEnum(JobStatus),
  request: z.any(), // TransformationRequest
  result: z.any().optional(), // TransformationResult
  createdAt: z.string().transform(str => new Date(str)),
  startedAt: z.string().transform(str => new Date(str)).optional(),
  completedAt: z.string().transform(str => new Date(str)).optional(),
  retryCount: z.number().default(0),
  maxRetries: z.number().default(3),
  error: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

export type QueueJob = z.infer<typeof queueJobSchema>;

// Queue Configuration
export interface QueueConfig {
  maxConcurrentJobs: number;
  maxRetries: number;
  retryDelay: number; // milliseconds
  jobTimeout: number; // milliseconds
  enablePersistence: boolean;
}

// Queue Events
export type QueueEventType = 
  | 'job_added'
  | 'job_started'
  | 'job_completed'
  | 'job_failed'
  | 'job_cancelled'
  | 'job_retrying'
  | 'queue_empty'
  | 'queue_full';

export interface QueueEvent {
  type: QueueEventType;
  jobId: string;
  timestamp: Date;
  data?: any;
}

export type QueueEventListener = (event: QueueEvent) => void;

// Transformation Queue Manager
export class TransformationQueue {
  private jobs: Map<string, QueueJob> = new Map();
  private runningJobs: Set<string> = new Set();
  private eventListeners: Map<QueueEventType, QueueEventListener[]> = new Map();
  private config: QueueConfig;
  private isProcessing = false;

  constructor(config: Partial<QueueConfig> = {}) {
    this.config = {
      maxConcurrentJobs: 3,
      maxRetries: 3,
      retryDelay: 5000,
      jobTimeout: 300000, // 5 minutes
      enablePersistence: true,
      ...config,
    };

    // Load persisted jobs if enabled
    if (this.config.enablePersistence) {
      this.loadPersistedJobs();
    }

    // Start processing
    this.startProcessing();
  }

  // Add job to queue
  addJob(
    request: TransformationRequest,
    priority: JobPriority = JobPriority.NORMAL,
    metadata?: Record<string, any>
  ): string {
    const job: QueueJob = {
      id: this.generateJobId(),
      priority,
      status: JobStatus.PENDING,
      request,
      createdAt: new Date(),
      retryCount: 0,
      maxRetries: this.config.maxRetries,
      metadata,
    };

    this.jobs.set(job.id, job);
    this.persistJobs();
    this.emitEvent('job_added', job.id, { priority, metadata });

    return job.id;
  }

  // Get job by ID
  getJob(jobId: string): QueueJob | undefined {
    return this.jobs.get(jobId);
  }

  // Get all jobs
  getAllJobs(): QueueJob[] {
    return Array.from(this.jobs.values());
  }

  // Get jobs by status
  getJobsByStatus(status: JobStatus): QueueJob[] {
    return Array.from(this.jobs.values()).filter(job => job.status === status);
  }

  // Cancel job
  cancelJob(jobId: string): boolean {
    const job = this.jobs.get(jobId);
    if (!job) return false;

    if (job.status === JobStatus.RUNNING) {
      // Mark for cancellation - the processor will handle it
      job.status = JobStatus.CANCELLED;
      this.runningJobs.delete(jobId);
    } else if (job.status === JobStatus.PENDING || job.status === JobStatus.RETRYING) {
      job.status = JobStatus.CANCELLED;
    } else {
      return false; // Can't cancel completed/failed jobs
    }

    this.persistJobs();
    this.emitEvent('job_cancelled', jobId);
    return true;
  }

  // Remove job from queue
  removeJob(jobId: string): boolean {
    const job = this.jobs.get(jobId);
    if (!job) return false;

    // Can only remove non-running jobs
    if (job.status === JobStatus.RUNNING) {
      return false;
    }

    this.jobs.delete(jobId);
    this.persistJobs();
    return true;
  }

  // Clear completed jobs
  clearCompleted(): number {
    const completedJobs = this.getJobsByStatus(JobStatus.COMPLETED);
    completedJobs.forEach(job => this.jobs.delete(job.id));
    this.persistJobs();
    return completedJobs.length;
  }

  // Get queue statistics
  getStats(): {
    total: number;
    pending: number;
    running: number;
    completed: number;
    failed: number;
    cancelled: number;
    retrying: number;
  } {
    const jobs = Array.from(this.jobs.values());
    return {
      total: jobs.length,
      pending: jobs.filter(j => j.status === JobStatus.PENDING).length,
      running: jobs.filter(j => j.status === JobStatus.RUNNING).length,
      completed: jobs.filter(j => j.status === JobStatus.COMPLETED).length,
      failed: jobs.filter(j => j.status === JobStatus.FAILED).length,
      cancelled: jobs.filter(j => j.status === JobStatus.CANCELLED).length,
      retrying: jobs.filter(j => j.status === JobStatus.RETRYING).length,
    };
  }

  // Event handling
  on(eventType: QueueEventType, listener: QueueEventListener): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, []);
    }
    this.eventListeners.get(eventType)!.push(listener);
  }

  off(eventType: QueueEventType, listener: QueueEventListener): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emitEvent(type: QueueEventType, jobId: string, data?: any): void {
    const event: QueueEvent = {
      type,
      jobId,
      timestamp: new Date(),
      data,
    };

    const listeners = this.eventListeners.get(type) || [];
    listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Queue event listener error:', error);
      }
    });
  }

  // Start queue processing
  private startProcessing(): void {
    if (this.isProcessing) return;
    this.isProcessing = true;
    this.processQueue();
  }

  // Main queue processing loop
  private async processQueue(): Promise<void> {
    while (this.isProcessing) {
      try {
        // Check if we can start more jobs
        if (this.runningJobs.size < this.config.maxConcurrentJobs) {
          const nextJob = this.getNextJob();
          if (nextJob) {
            this.processJob(nextJob);
          }
        }

        // Check for empty queue
        const pendingJobs = this.getJobsByStatus(JobStatus.PENDING);
        const retryingJobs = this.getJobsByStatus(JobStatus.RETRYING);
        if (pendingJobs.length === 0 && retryingJobs.length === 0 && this.runningJobs.size === 0) {
          this.emitEvent('queue_empty', '', {});
        }

        // Wait before next iteration
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        console.error('Queue processing error:', error);
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
  }

  // Get next job to process (priority-based)
  private getNextJob(): QueueJob | null {
    const availableJobs = Array.from(this.jobs.values())
      .filter(job => 
        (job.status === JobStatus.PENDING || job.status === JobStatus.RETRYING) &&
        !this.runningJobs.has(job.id)
      )
      .sort((a, b) => {
        // Sort by priority (higher first), then by creation time (older first)
        if (a.priority !== b.priority) {
          return b.priority - a.priority;
        }
        return a.createdAt.getTime() - b.createdAt.getTime();
      });

    return availableJobs[0] || null;
  }

  // Process individual job
  private async processJob(job: QueueJob): Promise<void> {
    this.runningJobs.add(job.id);
    job.status = JobStatus.RUNNING;
    job.startedAt = new Date();
    this.persistJobs();
    this.emitEvent('job_started', job.id);

    try {
      // Set timeout for job
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Job timeout')), this.config.jobTimeout);
      });

      // Process the transformation (this would integrate with DualAgentSystem)
      const resultPromise = this.executeTransformation(job.request);
      
      const result = await Promise.race([resultPromise, timeoutPromise]);

      // Job completed successfully
      job.status = JobStatus.COMPLETED;
      job.result = result;
      job.completedAt = new Date();
      this.runningJobs.delete(job.id);
      this.persistJobs();
      this.emitEvent('job_completed', job.id, { result });

    } catch (error) {
      this.runningJobs.delete(job.id);
      
      // Check if job should be retried
      if (job.retryCount < job.maxRetries) {
        job.retryCount++;
        job.status = JobStatus.RETRYING;
        job.error = error instanceof Error ? error.message : 'Unknown error';
        this.persistJobs();
        this.emitEvent('job_retrying', job.id, { error: job.error, retryCount: job.retryCount });

        // Schedule retry
        setTimeout(() => {
          const currentJob = this.jobs.get(job.id);
          if (currentJob && currentJob.status === JobStatus.RETRYING) {
            currentJob.status = JobStatus.PENDING;
            this.persistJobs();
          }
        }, this.config.retryDelay);
      } else {
        // Max retries reached
        job.status = JobStatus.FAILED;
        job.error = error instanceof Error ? error.message : 'Unknown error';
        job.completedAt = new Date();
        this.persistJobs();
        this.emitEvent('job_failed', job.id, { error: job.error });
      }
    }
  }

  // Execute transformation (placeholder - would integrate with DualAgentSystem)
  private async executeTransformation(request: TransformationRequest): Promise<TransformationResult> {
    // Simulate transformation processing
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Mock result
    return {
      id: request.id,
      status: 'completed',
      originalCode: request.code,
      transformedCode: '// Transformed code here',
      iterations: [],
      finalScore: 0.85,
      totalCost: 0.05,
      duration: 5000,
    };
  }

  // Generate unique job ID
  private generateJobId(): string {
    return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Persistence methods
  private persistJobs(): void {
    if (!this.config.enablePersistence) return;
    
    try {
      const jobsData = Array.from(this.jobs.entries()).map(([id, job]) => [
        id,
        {
          ...job,
          createdAt: job.createdAt.toISOString(),
          startedAt: job.startedAt?.toISOString(),
          completedAt: job.completedAt?.toISOString(),
        }
      ]);
      
      localStorage.setItem('transformation_queue_jobs', JSON.stringify(jobsData));
    } catch (error) {
      console.error('Failed to persist queue jobs:', error);
    }
  }

  private loadPersistedJobs(): void {
    try {
      const stored = localStorage.getItem('transformation_queue_jobs');
      if (!stored) return;

      const jobsData = JSON.parse(stored);
      jobsData.forEach(([id, jobData]: [string, any]) => {
        const job: QueueJob = {
          ...jobData,
          createdAt: new Date(jobData.createdAt),
          startedAt: jobData.startedAt ? new Date(jobData.startedAt) : undefined,
          completedAt: jobData.completedAt ? new Date(jobData.completedAt) : undefined,
        };
        
        // Reset running jobs to pending on load
        if (job.status === JobStatus.RUNNING) {
          job.status = JobStatus.PENDING;
        }
        
        this.jobs.set(id, job);
      });
    } catch (error) {
      console.error('Failed to load persisted queue jobs:', error);
    }
  }

  // Cleanup
  destroy(): void {
    this.isProcessing = false;
    this.runningJobs.clear();
    this.eventListeners.clear();
  }
}
