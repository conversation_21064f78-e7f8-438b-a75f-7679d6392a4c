{"audit_summary": {"timestamp": "2025-06-19T19:32:00.000Z", "target_score": 97, "overall_status": "FAILED", "pages_tested": 2, "total_violations": 10, "total_incomplete": 4}, "page_results": {"dashboard": {"url": "http://localhost:8080/dashboard", "score": 0, "status": "FAILED", "violations": 4, "incomplete": 3, "passes": 41, "critical_issues": ["ARIA valid attributes (2 nodes)", "Color contrast (19 nodes)", "Focus order semantics (2 nodes)", "Region landmarks (4 nodes)"]}, "settings": {"url": "http://localhost:8080/settings", "score": 0, "status": "FAILED", "violations": 6, "incomplete": 1, "passes": 34, "critical_issues": ["ARIA input field names (2 nodes)", "Button names (8 nodes)", "Form labels (3 nodes)", "Main landmark missing", "Region landmarks (7 nodes)", "Heading order (1 node)"]}}, "priority_fixes": [{"priority": "P0 - Critical", "issues": ["Add discernible text/aria-label to 8 buttons on settings page", "Add proper labels to 3 form inputs on settings page", "Fix ARIA attributes on ResizableHandle components", "Add aria-label to slider components"], "impact": "Blocks screen reader users completely"}, {"priority": "P1 - High", "issues": ["Fix color contrast for 19+ elements across both pages", "Add main landmark to both pages", "Wrap content in proper landmark regions", "Fix heading hierarchy on settings page"], "impact": "Significantly impacts usability for users with disabilities"}, {"priority": "P2 - Medium", "issues": ["Fix focus order semantics for ResizableHandle components", "Test with screen readers for navigation flow"], "impact": "Affects keyboard navigation and screen reader experience"}], "estimated_fixes_needed": {"dashboard": ["Fix ResizableHandle ARIA attributes", "Improve color contrast for buttons/badges", "Add main landmark wrapper", "Add proper focus roles to interactive elements"], "settings": ["Add aria-labels to all unlabeled buttons", "Add proper labels to form inputs", "Add aria-label to slider components", "Fix heading hierarchy (h1 → h2 → h3)", "Add main landmark wrapper", "Improve color contrast"]}, "next_steps": ["Fix critical P0 issues first (buttons, labels, ARIA)", "Add semantic HTML structure with landmarks", "Improve color contrast ratios", "Re-run accessibility audit to verify fixes", "Aim for 97+ score on both pages"]}