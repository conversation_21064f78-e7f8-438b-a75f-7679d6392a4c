import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { But<PERSON> } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Activity, 
  Cpu, 
  MemoryStick, 
  Zap, 
  Clock, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  RefreshCw
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { usePerformanceMonitoring, performanceMonitor } from '@/utils/performance';
import { HStack, VStack } from '@/components/ui/layout';
import { Typography } from '@/components/ui/typography';

interface PerformanceMetrics {
  fcp: number; // First Contentful Paint
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  ttfb: number; // Time to First Byte
  domContentLoaded: number;
  loadComplete: number;
  memoryUsage?: {
    used: number;
    total: number;
    limit: number;
  };
  bundleSize?: number;
  renderTime?: number;
}

interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  uptime: number;
  responseTime: number;
  errorRate: number;
  throughput: number;
}

interface PerformanceMetricsWidgetProps {
  className?: string;
  refreshInterval?: number;
}

export const PerformanceMetricsWidget: React.FC<PerformanceMetricsWidgetProps> = ({
  className,
  refreshInterval = 5000
}) => {
  const [systemHealth, setSystemHealth] = useState<SystemHealth>({
    status: 'healthy',
    uptime: 0,
    responseTime: 0,
    errorRate: 0,
    throughput: 0
  });
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  
  const metrics = usePerformanceMonitoring('PerformanceMetricsWidget');

  // Fetch system health data
  const fetchSystemHealth = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/health');
      if (response.ok) {
        const data = await response.json();
        setSystemHealth({
          status: data.status === 'healthy' ? 'healthy' : 'warning',
          uptime: data.uptime || 0,
          responseTime: data.responseTime || 0,
          errorRate: data.errorRate || 0,
          throughput: data.throughput || 0
        });
      }
    } catch (error) {
      console.error('Failed to fetch system health:', error);
      setSystemHealth(prev => ({ ...prev, status: 'critical' }));
    } finally {
      setIsLoading(false);
      setLastUpdated(new Date());
    }
  };

  // Auto-refresh system health
  useEffect(() => {
    fetchSystemHealth();
    const interval = setInterval(fetchSystemHealth, refreshInterval);
    return () => clearInterval(interval);
  }, [refreshInterval]);

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTime = (ms: number): string => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-500';
      case 'warning': return 'text-yellow-500';
      case 'critical': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'critical': return <AlertTriangle className="w-4 h-4 text-red-500" />;
      default: return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  const getPerformanceScore = (): number => {
    if (!metrics.lcp || !metrics.fid || !metrics.cls) return 0;
    
    // Calculate performance score based on Core Web Vitals
    const lcpScore = metrics.lcp <= 2500 ? 100 : metrics.lcp <= 4000 ? 50 : 0;
    const fidScore = metrics.fid <= 100 ? 100 : metrics.fid <= 300 ? 50 : 0;
    const clsScore = metrics.cls <= 0.1 ? 100 : metrics.cls <= 0.25 ? 50 : 0;
    
    return Math.round((lcpScore + fidScore + clsScore) / 3);
  };

  const performanceScore = getPerformanceScore();

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Activity className="w-5 h-5 text-primary" />
            <CardTitle className="text-lg">Performance Metrics</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            {getStatusIcon(systemHealth.status)}
            <Button
              variant="ghost"
              size="sm"
              onClick={fetchSystemHealth}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={cn("w-4 h-4", isLoading && "animate-spin")} />
            </Button>
          </div>
        </div>
        <CardDescription>
          Real-time system performance and health indicators
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* System Status */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <div className={cn("w-2 h-2 rounded-full", 
                systemHealth.status === 'healthy' ? 'bg-green-500' :
                systemHealth.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
              )} />
              <Typography variant="caption" className="text-muted-foreground">
                System Status
              </Typography>
            </div>
            <Typography variant="body-sm" className={getStatusColor(systemHealth.status)}>
              {systemHealth.status.charAt(0).toUpperCase() + systemHealth.status.slice(1)}
            </Typography>
          </div>

          <div className="space-y-2">
            <Typography variant="caption" className="text-muted-foreground">
              Response Time
            </Typography>
            <Typography variant="body-sm" className="font-mono">
              {formatTime(systemHealth.responseTime)}
            </Typography>
          </div>

          <div className="space-y-2">
            <Typography variant="caption" className="text-muted-foreground">
              Error Rate
            </Typography>
            <Typography variant="body-sm" className="font-mono">
              {(systemHealth.errorRate * 100).toFixed(2)}%
            </Typography>
          </div>

          <div className="space-y-2">
            <Typography variant="caption" className="text-muted-foreground">
              Throughput
            </Typography>
            <Typography variant="body-sm" className="font-mono">
              {systemHealth.throughput.toFixed(1)} req/s
            </Typography>
          </div>
        </div>

        {/* Performance Score */}
        {performanceScore > 0 && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Typography variant="body-sm" className="font-medium">
                Performance Score
              </Typography>
              <Badge variant={performanceScore >= 80 ? 'default' : performanceScore >= 50 ? 'secondary' : 'destructive'}>
                {performanceScore}/100
              </Badge>
            </div>
            <Progress value={performanceScore} className="h-2" />
          </div>
        )}

        {/* Core Web Vitals */}
        {(metrics.lcp || metrics.fid || metrics.cls) && (
          <div className="space-y-3">
            <Typography variant="body-sm" className="font-medium">
              Core Web Vitals
            </Typography>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {metrics.lcp && (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Zap className="w-4 h-4 text-blue-500" />
                    <Typography variant="caption">LCP</Typography>
                  </div>
                  <Typography variant="body-sm" className="font-mono">
                    {formatTime(metrics.lcp)}
                  </Typography>
                  <div className={cn("text-xs", 
                    metrics.lcp <= 2500 ? 'text-green-500' : 
                    metrics.lcp <= 4000 ? 'text-yellow-500' : 'text-red-500'
                  )}>
                    {metrics.lcp <= 2500 ? 'Good' : metrics.lcp <= 4000 ? 'Needs Improvement' : 'Poor'}
                  </div>
                </div>
              )}

              {metrics.fid && (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-green-500" />
                    <Typography variant="caption">FID</Typography>
                  </div>
                  <Typography variant="body-sm" className="font-mono">
                    {formatTime(metrics.fid)}
                  </Typography>
                  <div className={cn("text-xs",
                    metrics.fid <= 100 ? 'text-green-500' : 
                    metrics.fid <= 300 ? 'text-yellow-500' : 'text-red-500'
                  )}>
                    {metrics.fid <= 100 ? 'Good' : metrics.fid <= 300 ? 'Needs Improvement' : 'Poor'}
                  </div>
                </div>
              )}

              {metrics.cls !== undefined && (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="w-4 h-4 text-purple-500" />
                    <Typography variant="caption">CLS</Typography>
                  </div>
                  <Typography variant="body-sm" className="font-mono">
                    {metrics.cls.toFixed(3)}
                  </Typography>
                  <div className={cn("text-xs",
                    metrics.cls <= 0.1 ? 'text-green-500' : 
                    metrics.cls <= 0.25 ? 'text-yellow-500' : 'text-red-500'
                  )}>
                    {metrics.cls <= 0.1 ? 'Good' : metrics.cls <= 0.25 ? 'Needs Improvement' : 'Poor'}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Memory Usage */}
        {metrics.memoryUsage && (
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <MemoryStick className="w-4 h-4 text-orange-500" />
              <Typography variant="body-sm" className="font-medium">
                Memory Usage
              </Typography>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>{formatBytes(metrics.memoryUsage.used)}</span>
                <span className="text-muted-foreground">
                  / {formatBytes(metrics.memoryUsage.limit)}
                </span>
              </div>
              <Progress 
                value={(metrics.memoryUsage.used / metrics.memoryUsage.limit) * 100} 
                className="h-2"
              />
            </div>
          </div>
        )}

        {/* Last Updated */}
        <div className="pt-2 border-t border-border">
          <Typography variant="caption" className="text-muted-foreground">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </Typography>
        </div>
      </CardContent>
    </Card>
  );
};

export default PerformanceMetricsWidget;
