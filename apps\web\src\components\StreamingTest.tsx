import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Activity, 
  CheckCircle, 
  XCircle, 
  Wifi, 
  WifiOff, 
  Play, 
  Square,
  RefreshCw
} from 'lucide-react';
import { getWebSocketManager, ConnectionStatus } from '@/lib/websocket/websocketManager';
import { getEventStreamingManager, StreamEventType } from '@/lib/streaming/eventStreaming';

interface TestEvent {
  id: string;
  timestamp: Date;
  type: string;
  message: string;
  status: 'success' | 'error' | 'info';
}

export const StreamingTest = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>(ConnectionStatus.DISCONNECTED);
  const [events, setEvents] = useState<TestEvent[]>([]);
  const [isTestRunning, setIsTestRunning] = useState(false);
  const [testResults, setTestResults] = useState<{
    websocket: boolean;
    sse: boolean;
    eventStreaming: boolean;
  }>({
    websocket: false,
    sse: false,
    eventStreaming: false
  });

  useEffect(() => {
    const wsManager = getWebSocketManager();
    const eventManager = getEventStreamingManager();

    // Monitor WebSocket connection status
    const checkConnection = () => {
      const status = wsManager.getStatus();
      setConnectionStatus(status);
      setIsConnected(status === ConnectionStatus.CONNECTED);
    };

    checkConnection();
    const statusInterval = setInterval(checkConnection, 1000);

    // Subscribe to streaming events
    const unsubscribe = eventManager.subscribeToMultiple(
      Object.values(StreamEventType),
      (event) => {
        addEvent({
          id: event.id,
          timestamp: event.timestamp,
          type: event.type,
          message: `Event received: ${event.type}`,
          status: 'success'
        });
      }
    );

    return () => {
      clearInterval(statusInterval);
      unsubscribe();
    };
  }, []);

  const addEvent = (event: TestEvent) => {
    setEvents(prev => [event, ...prev].slice(0, 50)); // Keep last 50 events
  };

  const testWebSocketConnection = async () => {
    try {
      const wsManager = getWebSocketManager();
      
      addEvent({
        id: Date.now().toString(),
        timestamp: new Date(),
        type: 'websocket_test',
        message: 'Testing WebSocket connection...',
        status: 'info'
      });

      if (!wsManager.isConnected()) {
        await wsManager.connect();
      }

      // Test sending a message
      wsManager.send('test_message' as any, { test: true });

      setTestResults(prev => ({ ...prev, websocket: true }));
      
      addEvent({
        id: Date.now().toString(),
        timestamp: new Date(),
        type: 'websocket_test',
        message: 'WebSocket test successful',
        status: 'success'
      });

    } catch (error) {
      setTestResults(prev => ({ ...prev, websocket: false }));
      
      addEvent({
        id: Date.now().toString(),
        timestamp: new Date(),
        type: 'websocket_test',
        message: `WebSocket test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        status: 'error'
      });
    }
  };

  const testSSEConnection = async () => {
    try {
      addEvent({
        id: Date.now().toString(),
        timestamp: new Date(),
        type: 'sse_test',
        message: 'Testing SSE connection...',
        status: 'info'
      });

      // Test SSE endpoint
      const eventSource = new EventSource('/api/loop/stream');
      
      eventSource.onopen = () => {
        setTestResults(prev => ({ ...prev, sse: true }));
        addEvent({
          id: Date.now().toString(),
          timestamp: new Date(),
          type: 'sse_test',
          message: 'SSE connection established',
          status: 'success'
        });
        eventSource.close();
      };

      eventSource.onerror = () => {
        setTestResults(prev => ({ ...prev, sse: false }));
        addEvent({
          id: Date.now().toString(),
          timestamp: new Date(),
          type: 'sse_test',
          message: 'SSE connection failed',
          status: 'error'
        });
        eventSource.close();
      };

      // Close after 5 seconds if no response
      setTimeout(() => {
        if (eventSource.readyState !== EventSource.CLOSED) {
          eventSource.close();
          setTestResults(prev => ({ ...prev, sse: false }));
          addEvent({
            id: Date.now().toString(),
            timestamp: new Date(),
            type: 'sse_test',
            message: 'SSE connection timeout',
            status: 'error'
          });
        }
      }, 5000);

    } catch (error) {
      setTestResults(prev => ({ ...prev, sse: false }));
      addEvent({
        id: Date.now().toString(),
        timestamp: new Date(),
        type: 'sse_test',
        message: `SSE test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        status: 'error'
      });
    }
  };

  const testEventStreaming = () => {
    try {
      const eventManager = getEventStreamingManager();
      
      addEvent({
        id: Date.now().toString(),
        timestamp: new Date(),
        type: 'event_streaming_test',
        message: 'Testing event streaming...',
        status: 'info'
      });

      // Emit test events
      eventManager.emit({
        type: StreamEventType.TRANSFORMATION_STARTED,
        data: { test: true },
        source: 'streaming_test'
      });

      eventManager.emit({
        type: StreamEventType.TRANSFORMATION_PROGRESS,
        data: { progress: 50, test: true },
        source: 'streaming_test'
      });

      eventManager.emit({
        type: StreamEventType.TRANSFORMATION_COMPLETED,
        data: { result: 'test_complete' },
        source: 'streaming_test'
      });

      setTestResults(prev => ({ ...prev, eventStreaming: true }));
      
      addEvent({
        id: Date.now().toString(),
        timestamp: new Date(),
        type: 'event_streaming_test',
        message: 'Event streaming test completed',
        status: 'success'
      });

    } catch (error) {
      setTestResults(prev => ({ ...prev, eventStreaming: false }));
      addEvent({
        id: Date.now().toString(),
        timestamp: new Date(),
        type: 'event_streaming_test',
        message: `Event streaming test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        status: 'error'
      });
    }
  };

  const runAllTests = async () => {
    setIsTestRunning(true);
    setTestResults({ websocket: false, sse: false, eventStreaming: false });
    
    await testWebSocketConnection();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testSSEConnection();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    testEventStreaming();
    
    setIsTestRunning(false);
  };

  const clearEvents = () => {
    setEvents([]);
  };

  const getStatusIcon = () => {
    if (isConnected) {
      return <Wifi className="w-4 h-4 text-green-500" />;
    }
    return <WifiOff className="w-4 h-4 text-red-500" />;
  };

  const getTestIcon = (result: boolean) => {
    return result ? 
      <CheckCircle className="w-4 h-4 text-green-500" /> : 
      <XCircle className="w-4 h-4 text-red-500" />;
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="w-5 h-5" />
            <span>Streaming Connection Test</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Connection Status */}
          <Alert>
            <AlertDescription className="flex items-center space-x-2">
              {getStatusIcon()}
              <span>
                WebSocket Status: <Badge variant={isConnected ? "default" : "destructive"}>
                  {connectionStatus}
                </Badge>
              </span>
            </AlertDescription>
          </Alert>

          {/* Test Results */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              {getTestIcon(testResults.websocket)}
              <span className="text-sm">WebSocket</span>
            </div>
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              {getTestIcon(testResults.sse)}
              <span className="text-sm">Server-Sent Events</span>
            </div>
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              {getTestIcon(testResults.eventStreaming)}
              <span className="text-sm">Event Streaming</span>
            </div>
          </div>

          {/* Controls */}
          <div className="flex items-center space-x-2">
            <Button 
              onClick={runAllTests} 
              disabled={isTestRunning}
              className="flex items-center space-x-2"
            >
              {isTestRunning ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Play className="w-4 h-4" />
              )}
              <span>{isTestRunning ? 'Testing...' : 'Run All Tests'}</span>
            </Button>
            
            <Button variant="outline" onClick={clearEvents}>
              <Square className="w-4 h-4 mr-2" />
              Clear Events
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Event Log */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Event Log</CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-64">
            <div className="space-y-2">
              {events.length === 0 ? (
                <p className="text-sm text-muted-foreground text-center py-4">
                  No events yet. Run tests to see streaming activity.
                </p>
              ) : (
                events.map((event) => (
                  <div key={event.id} className="flex items-start space-x-2 p-2 border rounded text-sm">
                    <Badge 
                      variant={event.status === 'success' ? 'default' : event.status === 'error' ? 'destructive' : 'secondary'}
                      className="text-xs"
                    >
                      {event.status}
                    </Badge>
                    <div className="flex-1">
                      <div className="font-medium">{event.type}</div>
                      <div className="text-muted-foreground">{event.message}</div>
                      <div className="text-xs text-muted-foreground">
                        {event.timestamp.toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
};
