import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  Key, 
  Eye, 
  EyeOff, 
  TestTube, 
  CheckCircle, 
  AlertTriangle, 
  Loader2,
  Upload,
  Download,
  Shield,
  Trash2
} from 'lucide-react';
import { ProviderType } from '@/types/settings';

interface APIKeyManagerProps {
  apiKeys: {
    openai?: string;
    anthropic?: string;
    vertex_ai_project_id?: string;
    vertex_ai_credentials?: object;
  };
  onAPIKeysChange: (keys: any) => void;
  onTestConnection?: (provider: ProviderType, credentials: any) => Promise<boolean>;
}

interface KeyStatus {
  isValid: boolean;
  lastTested?: Date;
  error?: string;
}

export const APIKeyManager: React.FC<APIKeyManagerProps> = ({
  apiKeys,
  onAPIKeysChange,
  onTestConnection,
}) => {
  const [showKeys, setShowKeys] = useState<Record<string, boolean>>({});
  const [testingStatus, setTestingStatus] = useState<Record<string, boolean>>({});
  const [keyStatus, setKeyStatus] = useState<Record<string, KeyStatus>>({});
  const [vertexCredentialsText, setVertexCredentialsText] = useState('');

  useEffect(() => {
    // Initialize Vertex AI credentials text if available
    if (apiKeys.vertex_ai_credentials) {
      setVertexCredentialsText(JSON.stringify(apiKeys.vertex_ai_credentials, null, 2));
    }
  }, [apiKeys.vertex_ai_credentials]);

  const toggleKeyVisibility = (provider: string) => {
    setShowKeys(prev => ({ ...prev, [provider]: !prev[provider] }));
  };

  const handleKeyChange = (provider: string, value: string) => {
    onAPIKeysChange({
      ...apiKeys,
      [provider]: value || undefined,
    });
    
    // Clear previous status when key changes
    setKeyStatus(prev => ({ ...prev, [provider]: { isValid: false } }));
  };

  const handleVertexCredentialsChange = (value: string) => {
    setVertexCredentialsText(value);
    
    try {
      const credentials = JSON.parse(value);
      onAPIKeysChange({
        ...apiKeys,
        vertex_ai_credentials: credentials,
      });
      setKeyStatus(prev => ({ ...prev, vertex_ai: { isValid: true } }));
    } catch (error) {
      setKeyStatus(prev => ({ 
        ...prev, 
        vertex_ai: { 
          isValid: false, 
          error: 'Invalid JSON format' 
        } 
      }));
    }
  };

  const testConnection = async (provider: ProviderType) => {
    if (!onTestConnection) return;

    setTestingStatus(prev => ({ ...prev, [provider]: true }));
    
    try {
      let credentials;
      switch (provider) {
        case 'openai':
          credentials = { apiKey: apiKeys.openai };
          break;
        case 'anthropic':
          credentials = { apiKey: apiKeys.anthropic };
          break;
        case 'vertex-ai':
          credentials = {
            projectId: apiKeys.vertex_ai_project_id,
            credentials: apiKeys.vertex_ai_credentials,
          };
          break;
      }

      const isValid = await onTestConnection(provider, credentials);
      
      setKeyStatus(prev => ({
        ...prev,
        [provider]: {
          isValid,
          lastTested: new Date(),
          error: isValid ? undefined : 'Connection failed',
        },
      }));
    } catch (error) {
      setKeyStatus(prev => ({
        ...prev,
        [provider]: {
          isValid: false,
          lastTested: new Date(),
          error: error instanceof Error ? error.message : 'Connection failed',
        },
      }));
    } finally {
      setTestingStatus(prev => ({ ...prev, [provider]: false }));
    }
  };

  const clearKey = (provider: string) => {
    onAPIKeysChange({
      ...apiKeys,
      [provider]: undefined,
    });
    setKeyStatus(prev => ({ ...prev, [provider]: { isValid: false } }));
    
    if (provider === 'vertex_ai_credentials') {
      setVertexCredentialsText('');
    }
  };

  const exportCredentials = () => {
    const exportData = {
      openai: apiKeys.openai ? '***MASKED***' : undefined,
      anthropic: apiKeys.anthropic ? '***MASKED***' : undefined,
      vertex_ai_project_id: apiKeys.vertex_ai_project_id,
      vertex_ai_credentials: apiKeys.vertex_ai_credentials ? '***MASKED***' : undefined,
      exportedAt: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `api-keys-backup-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getStatusIcon = (provider: string) => {
    const status = keyStatus[provider];
    if (!status) return null;
    
    if (testingStatus[provider]) {
      return <Loader2 className="w-4 h-4 animate-spin text-blue-400" />;
    }
    
    return status.isValid ? (
      <CheckCircle className="w-4 h-4 text-green-400" />
    ) : (
      <AlertTriangle className="w-4 h-4 text-red-400" />
    );
  };

  const getStatusBadge = (provider: string) => {
    const status = keyStatus[provider];
    if (!status) return null;

    return (
      <Badge 
        variant={status.isValid ? "default" : "destructive"}
        className="text-xs"
      >
        {status.isValid ? "Valid" : "Invalid"}
      </Badge>
    );
  };

  return (
    <Card className="bg-slate-800/50 border-slate-700">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <Key className="w-5 h-5 text-yellow-400" />
          API Key Management
          <Badge variant="outline" className="text-slate-300">
            Enhanced Security
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* OpenAI API Key */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-slate-300 font-medium">OpenAI API Key</Label>
            <div className="flex items-center gap-2">
              {getStatusBadge('openai')}
              {getStatusIcon('openai')}
            </div>
          </div>
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Input
                type={showKeys.openai ? 'text' : 'password'}
                value={apiKeys.openai || ''}
                onChange={(e) => handleKeyChange('openai', e.target.value)}
                placeholder="sk-..."
                className="bg-slate-700 border-slate-600 text-white pr-20"
              />
              <div className="absolute right-1 top-1 flex gap-1">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 text-slate-400 hover:text-white"
                  onClick={() => toggleKeyVisibility('openai')}
                >
                  {showKeys.openai ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
                {apiKeys.openai && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 text-slate-400 hover:text-red-400"
                    onClick={() => clearKey('openai')}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
            {onTestConnection && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => testConnection('openai')}
                disabled={!apiKeys.openai || testingStatus.openai}
                className="border-slate-600 text-slate-300 hover:text-white"
              >
                {testingStatus.openai ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <TestTube className="h-4 w-4" />
                )}
                Test
              </Button>
            )}
          </div>
          {keyStatus.openai?.error && (
            <p className="text-red-400 text-sm">✗ {keyStatus.openai.error}</p>
          )}
          {keyStatus.openai?.isValid && (
            <p className="text-green-400 text-sm">
              ✓ Connection verified {keyStatus.openai.lastTested && 
                `at ${keyStatus.openai.lastTested.toLocaleTimeString()}`}
            </p>
          )}
        </div>

        {/* Anthropic API Key */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-slate-300 font-medium">Anthropic API Key</Label>
            <div className="flex items-center gap-2">
              {getStatusBadge('anthropic')}
              {getStatusIcon('anthropic')}
            </div>
          </div>
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Input
                type={showKeys.anthropic ? 'text' : 'password'}
                value={apiKeys.anthropic || ''}
                onChange={(e) => handleKeyChange('anthropic', e.target.value)}
                placeholder="sk-ant-..."
                className="bg-slate-700 border-slate-600 text-white pr-20"
              />
              <div className="absolute right-1 top-1 flex gap-1">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 text-slate-400 hover:text-white"
                  onClick={() => toggleKeyVisibility('anthropic')}
                >
                  {showKeys.anthropic ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
                {apiKeys.anthropic && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 text-slate-400 hover:text-red-400"
                    onClick={() => clearKey('anthropic')}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
            {onTestConnection && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => testConnection('anthropic')}
                disabled={!apiKeys.anthropic || testingStatus.anthropic}
                className="border-slate-600 text-slate-300 hover:text-white"
              >
                {testingStatus.anthropic ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <TestTube className="h-4 w-4" />
                )}
                Test
              </Button>
            )}
          </div>
          {keyStatus.anthropic?.error && (
            <p className="text-red-400 text-sm">✗ {keyStatus.anthropic.error}</p>
          )}
          {keyStatus.anthropic?.isValid && (
            <p className="text-green-400 text-sm">
              ✓ Connection verified {keyStatus.anthropic.lastTested && 
                `at ${keyStatus.anthropic.lastTested.toLocaleTimeString()}`}
            </p>
          )}
        </div>

        {/* Vertex AI Configuration */}
        <div className="space-y-4 p-4 bg-slate-900/50 rounded-lg border border-slate-700">
          <div className="flex items-center justify-between">
            <Label className="text-slate-300 font-medium">Google Vertex AI Configuration</Label>
            <div className="flex items-center gap-2">
              {getStatusBadge('vertex-ai')}
              {getStatusIcon('vertex-ai')}
            </div>
          </div>
          
          {/* Project ID */}
          <div className="space-y-2">
            <Label className="text-slate-400">Project ID</Label>
            <Input
              value={apiKeys.vertex_ai_project_id || ''}
              onChange={(e) => handleKeyChange('vertex_ai_project_id', e.target.value)}
              placeholder="my-gcp-project"
              className="bg-slate-700 border-slate-600 text-white"
            />
          </div>

          {/* Service Account Credentials */}
          <div className="space-y-2">
            <Label className="text-slate-400">Service Account Credentials (JSON)</Label>
            <Textarea
              value={vertexCredentialsText}
              onChange={(e) => handleVertexCredentialsChange(e.target.value)}
              placeholder='{"type": "service_account", "project_id": "...", ...}'
              className="bg-slate-700 border-slate-600 text-white min-h-[120px] font-mono text-sm"
            />
            <p className="text-xs text-slate-400">
              Paste your Google Cloud service account JSON credentials here
            </p>
          </div>

          {onTestConnection && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => testConnection('vertex-ai')}
              disabled={!apiKeys.vertex_ai_project_id || !apiKeys.vertex_ai_credentials || testingStatus['vertex-ai']}
              className="border-slate-600 text-slate-300 hover:text-white"
            >
              {testingStatus['vertex-ai'] ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <TestTube className="h-4 w-4 mr-2" />
              )}
              Test Vertex AI Connection
            </Button>
          )}

          {keyStatus['vertex-ai']?.error && (
            <Alert className="border-red-500/20 bg-red-500/10">
              <AlertTriangle className="h-4 w-4 text-red-500" />
              <AlertDescription className="text-red-200">
                {keyStatus['vertex-ai'].error}
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Security Notice */}
        <Alert className="border-blue-500/20 bg-blue-500/10">
          <Shield className="h-4 w-4 text-blue-500" />
          <AlertDescription className="text-blue-200">
            <div className="font-medium mb-1">Security Notice</div>
            All API keys are encrypted before storage. Keys are never transmitted in plain text 
            and are only decrypted when making API calls to the respective providers.
          </AlertDescription>
        </Alert>

        {/* Export/Import Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-slate-700">
          <div className="text-sm text-slate-400">
            Manage your API key configuration
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={exportCredentials}
              className="border-slate-600 text-slate-300 hover:text-white"
            >
              <Download className="w-4 h-4 mr-2" />
              Export (Masked)
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
