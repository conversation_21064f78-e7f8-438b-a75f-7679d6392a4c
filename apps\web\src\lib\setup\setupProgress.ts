import { z } from 'zod';

// Setup Step Status
export enum SetupStepStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  SKIPPED = 'skipped',
  FAILED = 'failed',
}

// Setup Step Definition
export interface SetupStep {
  id: string;
  name: string;
  description: string;
  status: SetupStepStatus;
  completedAt?: Date;
  data?: Record<string, any>;
  errors?: string[];
  warnings?: string[];
}

// Setup Progress Schema
const setupProgressSchema = z.object({
  version: z.string(),
  startedAt: z.string().transform(str => new Date(str)),
  lastUpdatedAt: z.string().transform(str => new Date(str)),
  completedAt: z.string().transform(str => new Date(str)).optional(),
  currentStepId: z.string().optional(),
  steps: z.record(z.object({
    id: z.string(),
    name: z.string(),
    description: z.string(),
    status: z.nativeEnum(SetupStepStatus),
    completedAt: z.string().transform(str => new Date(str)).optional(),
    data: z.record(z.any()).optional(),
    errors: z.array(z.string()).optional(),
    warnings: z.array(z.string()).optional(),
  })),
  isComplete: z.boolean(),
  metadata: z.record(z.any()).optional(),
});

export type SetupProgress = z.infer<typeof setupProgressSchema>;

// Default Setup Steps
export const DEFAULT_SETUP_STEPS: Record<string, Omit<SetupStep, 'status' | 'completedAt'>> = {
  welcome: {
    id: 'welcome',
    name: 'Welcome',
    description: 'Introduction to Metamorphic Reactor',
  },
  supabase: {
    id: 'supabase',
    name: 'Supabase Configuration',
    description: 'Configure database connection',
  },
  github: {
    id: 'github',
    name: 'GitHub Integration',
    description: 'Connect GitHub account',
  },
  models: {
    id: 'models',
    name: 'AI Models',
    description: 'Configure AI model preferences',
  },
  apiKeys: {
    id: 'apiKeys',
    name: 'API Keys',
    description: 'Configure AI provider API keys',
  },
  complete: {
    id: 'complete',
    name: 'Setup Complete',
    description: 'Finalize configuration',
  },
};

// Setup Progress Manager
export class SetupProgressManager {
  private static readonly STORAGE_KEY = 'metamorphic_reactor_setup_progress';
  private static readonly CURRENT_VERSION = '1.0.0';

  // Initialize setup progress
  static initializeProgress(): SetupProgress {
    const now = new Date();
    const steps: Record<string, SetupStep> = {};

    // Initialize all steps with NOT_STARTED status
    Object.values(DEFAULT_SETUP_STEPS).forEach(stepDef => {
      steps[stepDef.id] = {
        ...stepDef,
        status: SetupStepStatus.NOT_STARTED,
      };
    });

    const progress: SetupProgress = {
      version: this.CURRENT_VERSION,
      startedAt: now,
      lastUpdatedAt: now,
      currentStepId: 'welcome',
      steps,
      isComplete: false,
    };

    this.saveProgress(progress);
    return progress;
  }

  // Load progress from storage
  static loadProgress(): SetupProgress | null {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return null;

      const parsed = JSON.parse(stored);
      const result = setupProgressSchema.safeParse(parsed);

      if (!result.success) {
        console.warn('Invalid setup progress data, reinitializing:', result.error);
        return null;
      }

      // Check version compatibility
      if (result.data.version !== this.CURRENT_VERSION) {
        console.info('Setup progress version mismatch, reinitializing');
        return null;
      }

      return result.data;
    } catch (error) {
      console.error('Failed to load setup progress:', error);
      return null;
    }
  }

  // Save progress to storage
  static saveProgress(progress: SetupProgress): void {
    try {
      progress.lastUpdatedAt = new Date();
      const serialized = JSON.stringify(progress, (key, value) => {
        if (value instanceof Date) {
          return value.toISOString();
        }
        return value;
      });
      localStorage.setItem(this.STORAGE_KEY, serialized);
    } catch (error) {
      console.error('Failed to save setup progress:', error);
    }
  }

  // Get or initialize progress
  static getProgress(): SetupProgress {
    const existing = this.loadProgress();
    return existing || this.initializeProgress();
  }

  // Update step status
  static updateStepStatus(
    stepId: string, 
    status: SetupStepStatus, 
    data?: Record<string, any>,
    errors?: string[],
    warnings?: string[]
  ): SetupProgress {
    const progress = this.getProgress();
    
    if (!progress.steps[stepId]) {
      throw new Error(`Step ${stepId} not found`);
    }

    progress.steps[stepId].status = status;
    
    if (status === SetupStepStatus.COMPLETED) {
      progress.steps[stepId].completedAt = new Date();
    }
    
    if (data) {
      progress.steps[stepId].data = { ...progress.steps[stepId].data, ...data };
    }
    
    if (errors) {
      progress.steps[stepId].errors = errors;
    }
    
    if (warnings) {
      progress.steps[stepId].warnings = warnings;
    }

    // Update current step if this step is completed
    if (status === SetupStepStatus.COMPLETED) {
      const stepIds = Object.keys(DEFAULT_SETUP_STEPS);
      const currentIndex = stepIds.indexOf(stepId);
      const nextIndex = currentIndex + 1;
      
      if (nextIndex < stepIds.length) {
        progress.currentStepId = stepIds[nextIndex];
      } else {
        progress.isComplete = true;
        progress.completedAt = new Date();
      }
    }

    this.saveProgress(progress);
    return progress;
  }

  // Mark step as completed
  static completeStep(stepId: string, data?: Record<string, any>): SetupProgress {
    return this.updateStepStatus(stepId, SetupStepStatus.COMPLETED, data);
  }

  // Mark step as failed
  static failStep(stepId: string, errors: string[]): SetupProgress {
    return this.updateStepStatus(stepId, SetupStepStatus.FAILED, undefined, errors);
  }

  // Skip step
  static skipStep(stepId: string): SetupProgress {
    return this.updateStepStatus(stepId, SetupStepStatus.SKIPPED);
  }

  // Set current step
  static setCurrentStep(stepId: string): SetupProgress {
    const progress = this.getProgress();
    progress.currentStepId = stepId;
    this.saveProgress(progress);
    return progress;
  }

  // Get step progress percentage
  static getProgressPercentage(): number {
    const progress = this.getProgress();
    const steps = Object.values(progress.steps);
    const completedSteps = steps.filter(step => 
      step.status === SetupStepStatus.COMPLETED || 
      step.status === SetupStepStatus.SKIPPED
    );
    
    return Math.round((completedSteps.length / steps.length) * 100);
  }

  // Check if setup is complete
  static isSetupComplete(): boolean {
    const progress = this.getProgress();
    return progress.isComplete;
  }

  // Get next incomplete step
  static getNextIncompleteStep(): SetupStep | null {
    const progress = this.getProgress();
    const stepIds = Object.keys(DEFAULT_SETUP_STEPS);
    
    for (const stepId of stepIds) {
      const step = progress.steps[stepId];
      if (step.status === SetupStepStatus.NOT_STARTED || 
          step.status === SetupStepStatus.FAILED) {
        return step;
      }
    }
    
    return null;
  }

  // Reset progress
  static resetProgress(): SetupProgress {
    localStorage.removeItem(this.STORAGE_KEY);
    return this.initializeProgress();
  }

  // Export progress for debugging
  static exportProgress(): string {
    const progress = this.getProgress();
    return JSON.stringify(progress, null, 2);
  }

  // Get setup summary
  static getSummary(): {
    totalSteps: number;
    completedSteps: number;
    skippedSteps: number;
    failedSteps: number;
    progressPercentage: number;
    isComplete: boolean;
    currentStep?: string;
  } {
    const progress = this.getProgress();
    const steps = Object.values(progress.steps);
    
    return {
      totalSteps: steps.length,
      completedSteps: steps.filter(s => s.status === SetupStepStatus.COMPLETED).length,
      skippedSteps: steps.filter(s => s.status === SetupStepStatus.SKIPPED).length,
      failedSteps: steps.filter(s => s.status === SetupStepStatus.FAILED).length,
      progressPercentage: this.getProgressPercentage(),
      isComplete: progress.isComplete,
      currentStep: progress.currentStepId,
    };
  }

  // Validate step data
  static validateStepData(stepId: string, data: any): { isValid: boolean; errors: string[] } {
    // Add step-specific validation logic here
    switch (stepId) {
      case 'supabase':
        return this.validateSupabaseData(data);
      case 'github':
        return this.validateGitHubData(data);
      case 'models':
        return this.validateModelsData(data);
      case 'apiKeys':
        return this.validateAPIKeysData(data);
      default:
        return { isValid: true, errors: [] };
    }
  }

  private static validateSupabaseData(data: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!data?.url) errors.push('Supabase URL is required');
    if (!data?.anonKey) errors.push('Anonymous key is required');
    
    return { isValid: errors.length === 0, errors };
  }

  private static validateGitHubData(data: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!data?.clientId) errors.push('GitHub Client ID is required');
    
    return { isValid: errors.length === 0, errors };
  }

  private static validateModelsData(data: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!data?.plannerModel) errors.push('Planner model is required');
    if (!data?.criticModel) errors.push('Critic model is required');
    
    return { isValid: errors.length === 0, errors };
  }

  private static validateAPIKeysData(data: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    const hasAnyKey = data?.openai || data?.anthropic || data?.google;
    if (!hasAnyKey) errors.push('At least one API key is required');
    
    return { isValid: errors.length === 0, errors };
  }
}
