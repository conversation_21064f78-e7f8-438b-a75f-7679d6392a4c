import { Router } from 'express';
import { z } from 'zod';
import { supabaseClient } from '../services/supabase.js';
import { loggingService } from '../services/loggingService.js';

const router = Router();

// Request validation schemas
const createNotificationSchema = z.object({
  type: z.enum(['success', 'error', 'warning', 'info', 'system']),
  priority: z.enum(['low', 'medium', 'high', 'critical']),
  title: z.string().min(1).max(200),
  message: z.string().min(1).max(1000),
  actionUrl: z.string().url().optional(),
  actionLabel: z.string().max(50).optional(),
  metadata: z.object({
    source: z.string().optional(),
    category: z.string().optional()
  }).optional()
});

const updateNotificationSchema = z.object({
  read: z.boolean().optional(),
  dismissed: z.boolean().optional()
});

// Simple auth middleware (replace with proper auth in production)
const requireAuth = (req: any, res: any, next: any) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
  }
  
  // Extract user ID from token (simplified - use proper JWT validation in production)
  req.userId = 'user-123'; // Placeholder
  next();
};

// GET /api/notifications - Get user notifications
router.get('/notifications', requireAuth, async (req, res) => {
  try {
    const userId = req.userId;
    const { 
      type, 
      priority, 
      read, 
      limit = 50, 
      offset = 0 
    } = req.query;

    let query = supabaseClient
      .from('notifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    // Apply filters
    if (type) {
      query = query.eq('type', type);
    }
    if (priority) {
      query = query.eq('priority', priority);
    }
    if (read !== undefined) {
      query = query.eq('read', read === 'true');
    }

    // Apply pagination
    query = query.range(parseInt(offset as string), parseInt(offset as string) + parseInt(limit as string) - 1);

    const { data: notifications, error, count } = await query;

    if (error) {
      throw error;
    }

    // Get unread count
    const { count: unreadCount } = await supabaseClient
      .from('notifications')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('read', false);

    res.json({
      success: true,
      data: notifications || [],
      pagination: {
        total: count || 0,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        unreadCount: unreadCount || 0
      }
    });

  } catch (error) {
    console.error('Get notifications error:', error);
    await loggingService.log('error', 'Failed to get notifications', {
      error: error.message,
      userId: req.userId
    });

    res.status(500).json({
      success: false,
      error: 'Failed to get notifications'
    });
  }
});

// POST /api/notifications - Create notification (internal/admin use)
router.post('/notifications', requireAuth, async (req, res) => {
  try {
    const userId = req.userId;
    const validatedNotification = createNotificationSchema.parse(req.body);

    const { data: notification, error } = await supabaseClient
      .from('notifications')
      .insert({
        id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        user_id: userId,
        ...validatedNotification,
        read: false,
        dismissible: true,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    await loggingService.log('info', 'Notification created', {
      userId,
      notificationId: notification.id,
      type: validatedNotification.type
    });

    res.status(201).json({
      success: true,
      data: notification,
      message: 'Notification created successfully'
    });

  } catch (error) {
    console.error('Create notification error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    await loggingService.log('error', 'Failed to create notification', {
      error: error.message,
      userId: req.userId
    });

    res.status(500).json({
      success: false,
      error: 'Failed to create notification'
    });
  }
});

// PUT /api/notifications/mark-all-read - Mark all notifications as read
router.put('/notifications/mark-all-read', requireAuth, async (req, res) => {
  try {
    const userId = req.userId;

    const { error } = await supabaseClient
      .from('notifications')
      .update({ 
        read: true,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .eq('read', false);

    if (error) {
      throw error;
    }

    await loggingService.log('info', 'All notifications marked as read', {
      userId
    });

    res.json({
      success: true,
      message: 'All notifications marked as read'
    });

  } catch (error) {
    console.error('Mark all read error:', error);
    await loggingService.log('error', 'Failed to mark all notifications as read', {
      error: error.message,
      userId: req.userId
    });

    res.status(500).json({
      success: false,
      error: 'Failed to mark all notifications as read'
    });
  }
});

// DELETE /api/notifications - Clear all dismissible notifications
router.delete('/notifications', requireAuth, async (req, res) => {
  try {
    const userId = req.userId;

    const { error } = await supabaseClient
      .from('notifications')
      .delete()
      .eq('user_id', userId)
      .eq('dismissible', true);

    if (error) {
      throw error;
    }

    await loggingService.log('info', 'All dismissible notifications cleared', {
      userId
    });

    res.json({
      success: true,
      message: 'All dismissible notifications cleared'
    });

  } catch (error) {
    console.error('Clear notifications error:', error);
    await loggingService.log('error', 'Failed to clear notifications', {
      error: error.message,
      userId: req.userId
    });

    res.status(500).json({
      success: false,
      error: 'Failed to clear notifications'
    });
  }
});

// PUT /api/notifications/:id/read - Mark individual notification as read
router.put('/notifications/:id/read', requireAuth, async (req, res) => {
  try {
    const userId = req.userId;
    const notificationId = req.params.id;

    const { data: notification, error } = await supabaseClient
      .from('notifications')
      .update({ 
        read: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', notificationId)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    if (!notification) {
      return res.status(404).json({
        success: false,
        error: 'Notification not found'
      });
    }

    res.json({
      success: true,
      data: notification,
      message: 'Notification marked as read'
    });

  } catch (error) {
    console.error('Mark notification read error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to mark notification as read'
    });
  }
});

// POST /api/notifications/:id/action - Execute notification action
router.post('/notifications/:id/action', requireAuth, async (req, res) => {
  try {
    const userId = req.userId;
    const notificationId = req.params.id;

    // Get notification details
    const { data: notification, error } = await supabaseClient
      .from('notifications')
      .select('*')
      .eq('id', notificationId)
      .eq('user_id', userId)
      .single();

    if (error || !notification) {
      return res.status(404).json({
        success: false,
        error: 'Notification not found'
      });
    }

    // Mark as read when action is executed
    await supabaseClient
      .from('notifications')
      .update({ 
        read: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', notificationId);

    await loggingService.log('info', 'Notification action executed', {
      userId,
      notificationId,
      actionUrl: notification.action_url
    });

    res.json({
      success: true,
      data: {
        actionUrl: notification.action_url,
        actionLabel: notification.action_label
      },
      message: 'Notification action executed'
    });

  } catch (error) {
    console.error('Execute notification action error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to execute notification action'
    });
  }
});

// DELETE /api/notifications/:id - Dismiss individual notification
router.delete('/notifications/:id', requireAuth, async (req, res) => {
  try {
    const userId = req.userId;
    const notificationId = req.params.id;

    const { error } = await supabaseClient
      .from('notifications')
      .delete()
      .eq('id', notificationId)
      .eq('user_id', userId)
      .eq('dismissible', true);

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      message: 'Notification dismissed'
    });

  } catch (error) {
    console.error('Dismiss notification error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to dismiss notification'
    });
  }
});

export { router as notificationsRouter };
