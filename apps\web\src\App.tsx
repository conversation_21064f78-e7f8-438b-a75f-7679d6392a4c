
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ThemeProvider } from "@/components/theme-provider";
import { AppProvider } from "@/contexts/AppContext";
import { PageErrorBoundary, setupGlobalErrorHandling } from "@/components/ErrorBoundary";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import {
  LazyDashboardWithSkeleton,
  LazyMonitoringDashboardWithSkeleton,
  LazySettings,
  LazyHistory,
  LazyWrapper,
  initializePerformanceOptimizations
} from "./components/LazyComponents";
import NotFound from "./pages/NotFound";
import { LoadingSpinner } from "./components/ui/loading-states";

const queryClient = new QueryClient();

// Initialize performance optimizations
initializePerformanceOptimizations();

// Setup global error handling
setupGlobalErrorHandling();

const App = () => (
  <PageErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <AppProvider>
        <ThemeProvider defaultTheme="dark" storageKey="metamorphic-reactor-theme">
          <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/dashboard" element={<LazyDashboardWithSkeleton />} />
              <Route path="/monitoring" element={<LazyMonitoringDashboardWithSkeleton />} />
              <Route
                path="/settings"
                element={
                  <LazyWrapper fallback={<LoadingSpinner message="Loading settings..." />}>
                    <LazySettings />
                  </LazyWrapper>
                }
              />
              <Route
                path="/history"
                element={
                  <LazyWrapper fallback={<LoadingSpinner message="Loading history..." />}>
                    <LazyHistory />
                  </LazyWrapper>
                }
              />
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </ThemeProvider>
    </AppProvider>
  </QueryClientProvider>
  </PageErrorBoundary>
);

export default App;
