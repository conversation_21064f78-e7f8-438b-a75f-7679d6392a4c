import * as React from "react"
import { useSwipe, TouchWrapperProps } from "@/hooks/useGestures"

// Touch-friendly component wrapper
export function TouchWrapper({
  children,
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  className
}: TouchWrapperProps) {
  const gestureHandlers = useSwipe({
    onSwipeLeft: onSwipeLeft ? () => onSwipeLeft() : undefined,
    onSwipeRight: onSwipeRight ? () => onSwipeRight() : undefined,
    onSwipeUp: onSwipeUp ? () => onSwipeUp() : undefined,
    onSwipeDown: onSwipeDown ? () => onSwipeDown() : undefined
  })

  return (
    <div
      className={className}
      onTouchStart={gestureHandlers.onTouchStart}
      onTouchMove={gestureHandlers.onTouchMove}
      onTouchEnd={gestureHandlers.onTouchEnd}
      style={{ touchAction: 'pan-y' }} // Allow vertical scrolling
    >
      {children}
    </div>
  )
}
