import * as React from "react"

// Touch gesture types
interface TouchPoint {
  x: number
  y: number
  id: number
}

interface SwipeGesture {
  direction: 'left' | 'right' | 'up' | 'down'
  distance: number
  velocity: number
  duration: number
}

interface PinchGesture {
  scale: number
  center: { x: number; y: number }
}

interface PanGesture {
  deltaX: number
  deltaY: number
  velocityX: number
  velocityY: number
}

// Swipe gesture hook
interface UseSwipeOptions {
  onSwipeLeft?: (gesture: SwipeGesture) => void
  onSwipeRight?: (gesture: SwipeGesture) => void
  onSwipeUp?: (gesture: SwipeGesture) => void
  onSwipeDown?: (gesture: SwipeGesture) => void
  threshold?: number
  velocityThreshold?: number
}

export function useSwipe(options: UseSwipeOptions) {
  const {
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    threshold = 50,
    velocityThreshold = 0.3
  } = options

  const touchStart = React.useRef<TouchPoint | null>(null)
  const touchEnd = React.useRef<TouchPoint | null>(null)
  const startTime = React.useRef<number>(0)

  const handleTouchStart = React.useCallback((e: TouchEvent) => {
    const touch = e.touches[0]
    touchStart.current = {
      x: touch.clientX,
      y: touch.clientY,
      id: touch.identifier
    }
    startTime.current = Date.now()
  }, [])

  const handleTouchMove = React.useCallback((e: TouchEvent) => {
    if (!touchStart.current) return
    
    const touch = e.touches[0]
    touchEnd.current = {
      x: touch.clientX,
      y: touch.clientY,
      id: touch.identifier
    }
  }, [])

  const handleTouchEnd = React.useCallback(() => {
    if (!touchStart.current || !touchEnd.current) return

    const deltaX = touchEnd.current.x - touchStart.current.x
    const deltaY = touchEnd.current.y - touchStart.current.y
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
    const duration = Date.now() - startTime.current
    const velocity = distance / duration

    if (distance < threshold || velocity < velocityThreshold) return

    const gesture: SwipeGesture = {
      direction: Math.abs(deltaX) > Math.abs(deltaY)
        ? deltaX > 0 ? 'right' : 'left'
        : deltaY > 0 ? 'down' : 'up',
      distance,
      velocity,
      duration
    }

    switch (gesture.direction) {
      case 'left':
        onSwipeLeft?.(gesture)
        break
      case 'right':
        onSwipeRight?.(gesture)
        break
      case 'up':
        onSwipeUp?.(gesture)
        break
      case 'down':
        onSwipeDown?.(gesture)
        break
    }

    touchStart.current = null
    touchEnd.current = null
  }, [onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown, threshold, velocityThreshold])

  return {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd
  }
}

// Pinch gesture hook
interface UsePinchOptions {
  onPinch?: (gesture: PinchGesture) => void
  onPinchStart?: () => void
  onPinchEnd?: () => void
}

export function usePinch(options: UsePinchOptions) {
  const { onPinch, onPinchStart, onPinchEnd } = options
  const initialDistance = React.useRef<number>(0)
  const initialCenter = React.useRef<{ x: number; y: number }>({ x: 0, y: 0 })
  const isPinching = React.useRef<boolean>(false)

  const getDistance = (touch1: Touch, touch2: Touch) => {
    const dx = touch1.clientX - touch2.clientX
    const dy = touch1.clientY - touch2.clientY
    return Math.sqrt(dx * dx + dy * dy)
  }

  const getCenter = (touch1: Touch, touch2: Touch) => ({
    x: (touch1.clientX + touch2.clientX) / 2,
    y: (touch1.clientY + touch2.clientY) / 2
  })

  const handleTouchStart = React.useCallback((e: TouchEvent) => {
    if (e.touches.length === 2) {
      const [touch1, touch2] = e.touches
      initialDistance.current = getDistance(touch1, touch2)
      initialCenter.current = getCenter(touch1, touch2)
      isPinching.current = true
      onPinchStart?.()
    }
  }, [onPinchStart])

  const handleTouchMove = React.useCallback((e: TouchEvent) => {
    if (e.touches.length === 2 && isPinching.current) {
      e.preventDefault()
      
      const [touch1, touch2] = e.touches
      const currentDistance = getDistance(touch1, touch2)
      const currentCenter = getCenter(touch1, touch2)
      
      const scale = currentDistance / initialDistance.current
      
      onPinch?.({
        scale,
        center: currentCenter
      })
    }
  }, [onPinch])

  const handleTouchEnd = React.useCallback(() => {
    if (isPinching.current) {
      isPinching.current = false
      onPinchEnd?.()
    }
  }, [onPinchEnd])

  return {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd
  }
}

// Pan gesture hook
interface UsePanOptions {
  onPan?: (gesture: PanGesture) => void
  onPanStart?: () => void
  onPanEnd?: () => void
}

export function usePan(options: UsePanOptions) {
  const { onPan, onPanStart, onPanEnd } = options
  const startPosition = React.useRef<{ x: number; y: number }>({ x: 0, y: 0 })
  const lastPosition = React.useRef<{ x: number; y: number }>({ x: 0, y: 0 })
  const startTime = React.useRef<number>(0)
  const isPanning = React.useRef<boolean>(false)

  const handleTouchStart = React.useCallback((e: TouchEvent) => {
    if (e.touches.length === 1) {
      const touch = e.touches[0]
      startPosition.current = { x: touch.clientX, y: touch.clientY }
      lastPosition.current = { x: touch.clientX, y: touch.clientY }
      startTime.current = Date.now()
      isPanning.current = true
      onPanStart?.()
    }
  }, [onPanStart])

  const handleTouchMove = React.useCallback((e: TouchEvent) => {
    if (e.touches.length === 1 && isPanning.current) {
      const touch = e.touches[0]
      const currentPosition = { x: touch.clientX, y: touch.clientY }
      
      const deltaX = currentPosition.x - lastPosition.current.x
      const deltaY = currentPosition.y - lastPosition.current.y
      const duration = Date.now() - startTime.current
      
      const velocityX = deltaX / (duration || 1)
      const velocityY = deltaY / (duration || 1)
      
      onPan?.({
        deltaX,
        deltaY,
        velocityX,
        velocityY
      })
      
      lastPosition.current = currentPosition
    }
  }, [onPan])

  const handleTouchEnd = React.useCallback(() => {
    if (isPanning.current) {
      isPanning.current = false
      onPanEnd?.()
    }
  }, [onPanEnd])

  return {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd
  }
}

// Combined gesture hook
interface UseGesturesOptions extends UseSwipeOptions, UsePinchOptions, UsePanOptions {
  enableSwipe?: boolean
  enablePinch?: boolean
  enablePan?: boolean
}

export function useGestures(options: UseGesturesOptions) {
  const {
    enableSwipe = true,
    enablePinch = false,
    enablePan = false,
    ...gestureOptions
  } = options

  const swipeHandlers = useSwipe(gestureOptions)
  const pinchHandlers = usePinch(gestureOptions)
  const panHandlers = usePan(gestureOptions)

  const handleTouchStart = React.useCallback((e: TouchEvent) => {
    if (enableSwipe) swipeHandlers.onTouchStart(e)
    if (enablePinch) pinchHandlers.onTouchStart(e)
    if (enablePan) panHandlers.onTouchStart(e)
  }, [enableSwipe, enablePinch, enablePan, swipeHandlers, pinchHandlers, panHandlers])

  const handleTouchMove = React.useCallback((e: TouchEvent) => {
    if (enableSwipe) swipeHandlers.onTouchMove(e)
    if (enablePinch) pinchHandlers.onTouchMove(e)
    if (enablePan) panHandlers.onTouchMove(e)
  }, [enableSwipe, enablePinch, enablePan, swipeHandlers, pinchHandlers, panHandlers])

  const handleTouchEnd = React.useCallback((e: TouchEvent) => {
    if (enableSwipe) swipeHandlers.onTouchEnd()
    if (enablePinch) pinchHandlers.onTouchEnd()
    if (enablePan) panHandlers.onTouchEnd()
  }, [enableSwipe, enablePinch, enablePan, swipeHandlers, pinchHandlers, panHandlers])

  return {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd
  }
}

// Touch-friendly component wrapper - moved to separate component file
export interface TouchWrapperProps {
  children: React.ReactNode
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  onSwipeUp?: () => void
  onSwipeDown?: () => void
  className?: string
}

// This will be implemented in a separate .tsx file
export type TouchWrapperComponent = React.FC<TouchWrapperProps>
