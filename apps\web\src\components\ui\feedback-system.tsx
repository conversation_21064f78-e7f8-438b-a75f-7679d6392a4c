import * as React from "react"
import { cn } from "@/lib/utils"
import { 
  CheckIcon, 
  ErrorIcon, 
  WarningIcon, 
  InfoIcon, 
  LoadingIcon,
  CloseIcon 
} from "@/components/ui/icon"
import { Button } from "@/components/ui/button"
import { VStack, HStack } from "@/components/ui/layout"
import { Typography } from "@/components/ui/typography"

// Toast notification system
interface ToastProps {
  id: string
  title: string
  description?: string
  type: 'success' | 'error' | 'warning' | 'info' | 'loading'
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
  onDismiss: (id: string) => void
}

export function Toast({ 
  id, 
  title, 
  description, 
  type, 
  duration = 5000, 
  action, 
  onDismiss 
}: ToastProps) {
  React.useEffect(() => {
    if (type !== 'loading' && duration > 0) {
      const timer = setTimeout(() => onDismiss(id), duration)
      return () => clearTimeout(timer)
    }
  }, [id, duration, type, onDismiss])

  const typeConfig = {
    success: { icon: CheckIcon, className: "border-success bg-success/10 text-success" },
    error: { icon: ErrorIcon, className: "border-destructive bg-destructive/10 text-destructive" },
    warning: { icon: WarningIcon, className: "border-warning bg-warning/10 text-warning" },
    info: { icon: InfoIcon, className: "border-info bg-info/10 text-info" },
    loading: { icon: LoadingIcon, className: "border-muted bg-muted/10 text-muted-foreground" }
  }

  const { icon: Icon, className } = typeConfig[type]

  return (
    <div className={cn(
      "relative w-full max-w-sm p-4 border rounded-lg shadow-lg backdrop-blur-sm",
      "animate-in slide-in-from-right-full duration-300",
      className
    )}>
      <HStack spacing="sm" align="start">
        <Icon 
          size="sm" 
          className={cn("flex-shrink-0 mt-0.5", {
            "animate-spin": type === 'loading'
          })} 
        />
        
        <VStack spacing="xs" className="flex-1 min-w-0">
          <Typography variant="body-sm" className="font-medium">
            {title}
          </Typography>
          {description && (
            <Typography variant="caption" className="text-muted-foreground">
              {description}
            </Typography>
          )}
          {action && (
            <Button
              size="sm"
              variant="outline"
              onClick={action.onClick}
              className="self-start mt-2"
            >
              {action.label}
            </Button>
          )}
        </VStack>
        
        <Button
          size="sm"
          variant="ghost"
          onClick={() => onDismiss(id)}
          className="flex-shrink-0 h-6 w-6 p-0"
        >
          <CloseIcon size="xs" />
        </Button>
      </HStack>
    </div>
  )
}

// Confirmation dialog
interface ConfirmationDialogProps {
  isOpen: boolean
  title: string
  description: string
  confirmLabel?: string
  cancelLabel?: string
  variant?: 'default' | 'destructive' | 'warning'
  onConfirm: () => void
  onCancel: () => void
}

export function ConfirmationDialog({
  isOpen,
  title,
  description,
  confirmLabel = "Confirm",
  cancelLabel = "Cancel",
  variant = 'default',
  onConfirm,
  onCancel
}: ConfirmationDialogProps) {
  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') onCancel()
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      return () => document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, onCancel])

  if (!isOpen) return null

  const variantConfig = {
    default: { 
      icon: InfoIcon, 
      confirmClass: "bg-primary text-primary-foreground hover:bg-primary/90" 
    },
    destructive: { 
      icon: WarningIcon, 
      confirmClass: "bg-destructive text-destructive-foreground hover:bg-destructive/90" 
    },
    warning: { 
      icon: WarningIcon, 
      confirmClass: "bg-warning text-warning-foreground hover:bg-warning/90" 
    }
  }

  const { icon: Icon, confirmClass } = variantConfig[variant]

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-background/80 backdrop-blur-sm"
        onClick={onCancel}
      />
      
      {/* Dialog */}
      <div className="relative w-full max-w-md mx-4 bg-card border border-border rounded-lg shadow-lg p-6">
        <VStack spacing="md">
          <HStack spacing="sm" align="start">
            <Icon className="flex-shrink-0 mt-1 text-muted-foreground" />
            <VStack spacing="xs" className="flex-1">
              <Typography variant="h6" className="text-foreground">
                {title}
              </Typography>
              <Typography variant="body-sm" className="text-muted-foreground">
                {description}
              </Typography>
            </VStack>
          </HStack>
          
          <HStack spacing="sm" className="justify-end">
            <Button variant="outline" onClick={onCancel}>
              {cancelLabel}
            </Button>
            <Button className={confirmClass} onClick={onConfirm}>
              {confirmLabel}
            </Button>
          </HStack>
        </VStack>
      </div>
    </div>
  )
}

// Progress indicator
interface ProgressIndicatorProps {
  value: number
  max?: number
  label?: string
  description?: string
  showPercentage?: boolean
  variant?: 'default' | 'success' | 'warning' | 'error'
  className?: string
}

export function ProgressIndicator({
  value,
  max = 100,
  label,
  description,
  showPercentage = true,
  variant = 'default',
  className
}: ProgressIndicatorProps) {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100)
  
  const variantClasses = {
    default: "bg-primary",
    success: "bg-success",
    warning: "bg-warning", 
    error: "bg-destructive"
  }

  return (
    <VStack spacing="xs" className={className}>
      {(label || showPercentage) && (
        <HStack spacing="sm" className="justify-between items-center">
          {label && (
            <Typography variant="body-sm" className="font-medium text-foreground">
              {label}
            </Typography>
          )}
          {showPercentage && (
            <Typography variant="caption" className="text-muted-foreground">
              {Math.round(percentage)}%
            </Typography>
          )}
        </HStack>
      )}
      
      <div className="w-full bg-muted rounded-full h-2">
        <div
          className={cn(
            "h-2 rounded-full transition-all duration-300 ease-out",
            variantClasses[variant]
          )}
          style={{ width: `${percentage}%` }}
        />
      </div>
      
      {description && (
        <Typography variant="caption" className="text-muted-foreground">
          {description}
        </Typography>
      )}
    </VStack>
  )
}

// Status indicator
interface StatusIndicatorProps {
  status: 'idle' | 'loading' | 'success' | 'error' | 'warning'
  label?: string
  description?: string
  className?: string
}

export function StatusIndicator({ 
  status, 
  label, 
  description, 
  className 
}: StatusIndicatorProps) {
  const statusConfig = {
    idle: { 
      icon: null, 
      className: "text-muted-foreground",
      dotClassName: "bg-muted-foreground"
    },
    loading: { 
      icon: LoadingIcon, 
      className: "text-info",
      dotClassName: "bg-info animate-pulse"
    },
    success: { 
      icon: CheckIcon, 
      className: "text-success",
      dotClassName: "bg-success"
    },
    error: { 
      icon: ErrorIcon, 
      className: "text-destructive",
      dotClassName: "bg-destructive"
    },
    warning: { 
      icon: WarningIcon, 
      className: "text-warning",
      dotClassName: "bg-warning"
    }
  }

  const { icon: Icon, className: statusClassName, dotClassName } = statusConfig[status]

  return (
    <HStack spacing="sm" align="center" className={cn(statusClassName, className)}>
      {Icon ? (
        <Icon 
          size="sm" 
          className={cn("flex-shrink-0", {
            "animate-spin": status === 'loading'
          })} 
        />
      ) : (
        <div className={cn("w-2 h-2 rounded-full flex-shrink-0", dotClassName)} />
      )}
      
      {(label || description) && (
        <VStack spacing="xs" className="min-w-0">
          {label && (
            <Typography variant="body-sm" className="font-medium">
              {label}
            </Typography>
          )}
          {description && (
            <Typography variant="caption" className="text-muted-foreground">
              {description}
            </Typography>
          )}
        </VStack>
      )}
    </HStack>
  )
}

// Feedback context for managing global feedback state
interface FeedbackContextValue {
  showToast: (toast: Omit<ToastProps, 'id' | 'onDismiss'>) => void
  showConfirmation: (props: Omit<ConfirmationDialogProps, 'isOpen'>) => Promise<boolean>
  dismissToast: (id: string) => void
}

const FeedbackContext = React.createContext<FeedbackContextValue | null>(null)

export function FeedbackProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = React.useState<ToastProps[]>([])
  const [confirmation, setConfirmation] = React.useState<ConfirmationDialogProps | null>(null)

  const showToast = React.useCallback((toast: Omit<ToastProps, 'id' | 'onDismiss'>) => {
    const id = Math.random().toString(36).substr(2, 9)
    setToasts(prev => [...prev, { ...toast, id, onDismiss: dismissToast }])
  }, [])

  const dismissToast = React.useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }, [])

  const showConfirmation = React.useCallback((props: Omit<ConfirmationDialogProps, 'isOpen'>) => {
    return new Promise<boolean>((resolve) => {
      setConfirmation({
        ...props,
        isOpen: true,
        onConfirm: () => {
          setConfirmation(null)
          resolve(true)
        },
        onCancel: () => {
          setConfirmation(null)
          resolve(false)
        }
      })
    })
  }, [])

  const value = React.useMemo(() => ({
    showToast,
    showConfirmation,
    dismissToast
  }), [showToast, showConfirmation, dismissToast])

  return (
    <FeedbackContext.Provider value={value}>
      {children}
      
      {/* Toast container */}
      <div className="fixed top-4 right-4 z-50 space-y-2">
        {toasts.map(toast => (
          <Toast key={toast.id} {...toast} />
        ))}
      </div>
      
      {/* Confirmation dialog */}
      {confirmation && <ConfirmationDialog {...confirmation} />}
    </FeedbackContext.Provider>
  )
}

export function useFeedback() {
  const context = React.useContext(FeedbackContext)
  if (!context) {
    throw new Error('useFeedback must be used within a FeedbackProvider')
  }
  return context
}
