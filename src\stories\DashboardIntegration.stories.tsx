import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import DashboardIntegration from '../components/dashboard/DashboardIntegration';

const meta: Meta<typeof DashboardIntegration> = {
  title: 'Components/DashboardIntegration',
  component: DashboardIntegration,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: `
# Dashboard Integration Component

A comprehensive dashboard widget system that provides:

- **Overview**: Performance metrics, cost tracking, and system health
- **Analytics**: Usage statistics and transformation analytics
- **Admin**: Queue management and system logs
- **Billing**: Subscription plans and cost tracking
- **Notifications**: Alert center and notification settings

## Features

- 🎨 **Responsive Grid**: Adapts to different screen sizes
- 📊 **Real-time Widgets**: Live updating metrics and status
- ♿ **Accessible**: Proper ARIA labels and keyboard navigation
- 🎯 **Modular**: Individual widgets can be used independently
- 📱 **Mobile Friendly**: Touch-optimized interface

## Widget Types

- **Performance Metrics**: Response time, success rate, uptime
- **Cost Tracking**: Budget usage, spending trends, alerts
- **System Health**: Service status, connectivity, warnings
- **Activity Timeline**: Recent events and system activities
- **Notification Center**: Alerts, warnings, and status updates
        `,
      },
    },
    a11y: {
      config: {
        rules: [
          {
            id: 'color-contrast',
            enabled: true,
          },
          {
            id: 'keyboard-navigation',
            enabled: true,
          },
        ],
      },
    },
  },
  argTypes: {
    className: {
      control: 'text',
      description: 'Additional CSS classes to apply',
    },
    showOnboarding: {
      control: 'boolean',
      description: 'Whether to show the onboarding wizard',
    },
    onOnboardingComplete: {
      action: 'onboarding-complete',
      description: 'Callback when onboarding is completed',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  name: 'Default View',
  args: {
    showOnboarding: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'The default dashboard integration with overview tab selected.',
      },
    },
  },
};

export const Overview: Story = {
  name: 'Overview Tab',
  args: {
    showOnboarding: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Overview tab showing performance metrics, cost tracking, and system health widgets.',
      },
    },
  },
};

export const Analytics: Story = {
  name: 'Analytics Tab',
  args: {
    showOnboarding: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Analytics tab with usage statistics and queue status information.',
      },
    },
  },
  play: async ({ canvasElement }) => {
    const analyticsTab = canvasElement.querySelector('[value="analytics"]');
    if (analyticsTab) {
      (analyticsTab as HTMLElement).click();
    }
  },
};

export const AdminPanel: Story = {
  name: 'Admin Panel',
  args: {
    showOnboarding: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Admin panel with queue management and system logs.',
      },
    },
  },
  play: async ({ canvasElement }) => {
    const adminTab = canvasElement.querySelector('[value="admin"]');
    if (adminTab) {
      (adminTab as HTMLElement).click();
    }
  },
};

export const BillingSection: Story = {
  name: 'Billing Section',
  args: {
    showOnboarding: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Billing section showing subscription plans and pricing.',
      },
    },
  },
  play: async ({ canvasElement }) => {
    const billingTab = canvasElement.querySelector('[value="billing"]');
    if (billingTab) {
      (billingTab as HTMLElement).click();
    }
  },
};

export const NotificationCenter: Story = {
  name: 'Notification Center',
  args: {
    showOnboarding: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Notification center with alerts and settings.',
      },
    },
  },
  play: async ({ canvasElement }) => {
    const notificationsTab = canvasElement.querySelector('[value="notifications"]');
    if (notificationsTab) {
      (notificationsTab as HTMLElement).click();
    }
  },
};

export const WithOnboarding: Story = {
  name: 'Onboarding Flow',
  args: {
    showOnboarding: true,
    onOnboardingComplete: () => console.log('Onboarding completed'),
  },
  parameters: {
    docs: {
      description: {
        story: 'Dashboard with onboarding wizard for new users.',
      },
    },
  },
};

export const MobileLayout: Story = {
  name: 'Mobile Layout',
  args: {
    showOnboarding: false,
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
    docs: {
      description: {
        story: 'Dashboard integration optimized for mobile devices.',
      },
    },
  },
};

export const TabletLayout: Story = {
  name: 'Tablet Layout',
  args: {
    showOnboarding: false,
  },
  parameters: {
    viewport: {
      defaultViewport: 'tablet',
    },
    docs: {
      description: {
        story: 'Dashboard integration optimized for tablet devices.',
      },
    },
  },
};

export const CustomStyling: Story = {
  name: 'Custom Styling',
  args: {
    showOnboarding: false,
    className: 'border-2 border-primary rounded-lg shadow-lg',
  },
  parameters: {
    docs: {
      description: {
        story: 'Dashboard integration with custom styling applied.',
      },
    },
  },
};

export const DarkTheme: Story = {
  name: 'Dark Theme',
  args: {
    showOnboarding: false,
  },
  parameters: {
    backgrounds: {
      default: 'dark',
    },
    docs: {
      description: {
        story: 'Dashboard integration in dark theme mode.',
      },
    },
  },
};

export const HighContrastMode: Story = {
  name: 'High Contrast',
  args: {
    showOnboarding: false,
  },
  parameters: {
    a11y: {
      config: {
        rules: [
          {
            id: 'color-contrast',
            enabled: true,
            options: {
              level: 'AAA',
            },
          },
        ],
      },
    },
    docs: {
      description: {
        story: 'Dashboard integration with high contrast for accessibility.',
      },
    },
  },
};
