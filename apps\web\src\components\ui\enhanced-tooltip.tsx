import * as React from "react"
import * as TooltipPrimitive from "@radix-ui/react-tooltip"
import { cn } from "@/lib/utils"
import { InfoIcon, HelpIcon } from "@/components/ui/icon"
import { VStack, HStack } from "@/components/ui/layout"
import { Typography } from "@/components/ui/typography"

// Enhanced tooltip with rich content support
interface EnhancedTooltipProps {
  children: React.ReactNode
  content: React.ReactNode
  title?: string
  shortcut?: string
  side?: "top" | "right" | "bottom" | "left"
  align?: "start" | "center" | "end"
  delayDuration?: number
  className?: string
  contentClassName?: string
  disabled?: boolean
}

export function EnhancedTooltip({
  children,
  content,
  title,
  shortcut,
  side = "top",
  align = "center",
  delayDuration = 300,
  className,
  contentClassName,
  disabled = false,
  ...props
}: EnhancedTooltipProps) {
  if (disabled) {
    return <>{children}</>
  }

  return (
    <TooltipPrimitive.Root delayDuration={delayDuration}>
      <TooltipPrimitive.Trigger asChild className={className}>
        {children}
      </TooltipPrimitive.Trigger>
      <TooltipPrimitive.Portal>
        <TooltipPrimitive.Content
          side={side}
          align={align}
          className={cn(
            "z-50 overflow-hidden rounded-md bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
            contentClassName
          )}
          {...props}
        >
          {title || shortcut ? (
            <VStack spacing="xs">
              {title && (
                <Typography variant="body-sm" className="font-medium text-popover-foreground">
                  {title}
                </Typography>
              )}
              <Typography variant="body-sm" className="text-muted-foreground">
                {content}
              </Typography>
              {shortcut && (
                <HStack spacing="xs" className="justify-between items-center">
                  <span className="text-xs text-muted-foreground">Shortcut:</span>
                  <kbd className="px-1.5 py-0.5 text-xs font-mono bg-muted border border-border rounded">
                    {shortcut}
                  </kbd>
                </HStack>
              )}
            </VStack>
          ) : (
            content
          )}
          <TooltipPrimitive.Arrow className="fill-popover" />
        </TooltipPrimitive.Content>
      </TooltipPrimitive.Portal>
    </TooltipPrimitive.Root>
  )
}

// Help tooltip with info icon
interface HelpTooltipProps {
  content: React.ReactNode
  title?: string
  side?: "top" | "right" | "bottom" | "left"
  className?: string
}

export function HelpTooltip({ content, title, side = "top", className }: HelpTooltipProps) {
  return (
    <EnhancedTooltip
      content={content}
      title={title}
      side={side}
      className={cn("inline-flex", className)}
    >
      <button className="text-muted-foreground hover:text-foreground transition-colors focus-ring rounded-sm p-0.5">
        <InfoIcon size="xs" />
      </button>
    </EnhancedTooltip>
  )
}

// Feature tooltip for onboarding
interface FeatureTooltipProps {
  children: React.ReactNode
  title: string
  description: string
  shortcut?: string
  isVisible: boolean
  onDismiss?: () => void
  position?: "top" | "right" | "bottom" | "left"
}

export function FeatureTooltip({
  children,
  title,
  description,
  shortcut,
  isVisible,
  onDismiss,
  position = "bottom"
}: FeatureTooltipProps) {
  if (!isVisible) {
    return <>{children}</>
  }

  return (
    <div className="relative">
      {children}
      <div
        className={cn(
          "absolute z-50 w-64 p-4 bg-card border border-border rounded-lg shadow-lg",
          {
            "bottom-full mb-2 left-1/2 transform -translate-x-1/2": position === "top",
            "top-full mt-2 left-1/2 transform -translate-x-1/2": position === "bottom",
            "right-full mr-2 top-1/2 transform -translate-y-1/2": position === "left",
            "left-full ml-2 top-1/2 transform -translate-y-1/2": position === "right",
          }
        )}
      >
        <VStack spacing="sm">
          <HStack spacing="sm" className="justify-between items-start">
            <Typography variant="h6" className="text-foreground">
              {title}
            </Typography>
            {onDismiss && (
              <button
                onClick={onDismiss}
                className="text-muted-foreground hover:text-foreground transition-colors focus-ring rounded-sm p-0.5"
              >
                <InfoIcon size="xs" />
              </button>
            )}
          </HStack>
          
          <Typography variant="body-sm" className="text-muted-foreground">
            {description}
          </Typography>
          
          {shortcut && (
            <HStack spacing="xs" className="justify-between items-center pt-2 border-t border-border">
              <Typography variant="caption" className="text-muted-foreground">
                Keyboard shortcut:
              </Typography>
              <kbd className="px-2 py-1 text-xs font-mono bg-muted border border-border rounded">
                {shortcut}
              </kbd>
            </HStack>
          )}
        </VStack>
        
        {/* Arrow */}
        <div
          className={cn(
            "absolute w-2 h-2 bg-card border-border transform rotate-45",
            {
              "top-full left-1/2 -translate-x-1/2 -translate-y-1/2 border-b border-r": position === "top",
              "bottom-full left-1/2 -translate-x-1/2 translate-y-1/2 border-t border-l": position === "bottom",
              "top-1/2 left-full -translate-y-1/2 -translate-x-1/2 border-t border-r": position === "left",
              "top-1/2 right-full -translate-y-1/2 translate-x-1/2 border-b border-l": position === "right",
            }
          )}
        />
      </div>
    </div>
  )
}

// Onboarding tour system
interface TourStep {
  target: string
  title: string
  description: string
  shortcut?: string
  position?: "top" | "right" | "bottom" | "left"
}

interface OnboardingTourProps {
  steps: TourStep[]
  isActive: boolean
  currentStep: number
  onNext: () => void
  onPrevious: () => void
  onComplete: () => void
  onSkip: () => void
}

export function OnboardingTour({
  steps,
  isActive,
  currentStep,
  onNext,
  onPrevious,
  onComplete,
  onSkip
}: OnboardingTourProps) {
  const [targetElement, setTargetElement] = React.useState<HTMLElement | null>(null)
  
  React.useEffect(() => {
    if (isActive && steps[currentStep]) {
      const element = document.querySelector(steps[currentStep].target) as HTMLElement
      setTargetElement(element)
    }
  }, [isActive, currentStep, steps])

  if (!isActive || !steps[currentStep] || !targetElement) {
    return null
  }

  const step = steps[currentStep]
  const isLastStep = currentStep === steps.length - 1

  return (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 z-40 bg-background/80 backdrop-blur-sm" />
      
      {/* Highlight */}
      <div
        className="fixed z-50 pointer-events-none"
        style={{
          top: targetElement.offsetTop - 4,
          left: targetElement.offsetLeft - 4,
          width: targetElement.offsetWidth + 8,
          height: targetElement.offsetHeight + 8,
          border: '2px solid hsl(var(--primary))',
          borderRadius: '8px',
          boxShadow: '0 0 0 4px hsl(var(--primary) / 0.2)',
        }}
      />
      
      {/* Tour content */}
      <div
        className="fixed z-50 w-80 p-4 bg-card border border-border rounded-lg shadow-lg"
        style={{
          top: step.position === 'bottom' ? targetElement.offsetTop + targetElement.offsetHeight + 12 : 
               step.position === 'top' ? targetElement.offsetTop - 200 :
               targetElement.offsetTop,
          left: step.position === 'right' ? targetElement.offsetLeft + targetElement.offsetWidth + 12 :
                step.position === 'left' ? targetElement.offsetLeft - 332 :
                targetElement.offsetLeft,
        }}
      >
        <VStack spacing="md">
          <VStack spacing="sm">
            <HStack spacing="sm" className="justify-between items-start">
              <Typography variant="h6" className="text-foreground">
                {step.title}
              </Typography>
              <button
                onClick={onSkip}
                className="text-muted-foreground hover:text-foreground transition-colors focus-ring rounded-sm p-0.5"
              >
                ×
              </button>
            </HStack>
            
            <Typography variant="body-sm" className="text-muted-foreground">
              {step.description}
            </Typography>
            
            {step.shortcut && (
              <HStack spacing="xs" className="justify-between items-center pt-2 border-t border-border">
                <Typography variant="caption" className="text-muted-foreground">
                  Shortcut:
                </Typography>
                <kbd className="px-2 py-1 text-xs font-mono bg-muted border border-border rounded">
                  {step.shortcut}
                </kbd>
              </HStack>
            )}
          </VStack>
          
          <HStack spacing="sm" className="justify-between items-center">
            <Typography variant="caption" className="text-muted-foreground">
              {currentStep + 1} of {steps.length}
            </Typography>
            
            <HStack spacing="xs">
              {currentStep > 0 && (
                <button
                  onClick={onPrevious}
                  className="px-3 py-1 text-sm text-muted-foreground hover:text-foreground transition-colors focus-ring rounded"
                >
                  Previous
                </button>
              )}
              <button
                onClick={isLastStep ? onComplete : onNext}
                className="px-3 py-1 text-sm bg-primary text-primary-foreground hover:opacity-90 transition-opacity focus-ring rounded"
              >
                {isLastStep ? 'Complete' : 'Next'}
              </button>
            </HStack>
          </HStack>
        </VStack>
      </div>
    </>
  )
}

export { TooltipPrimitive as Tooltip }
