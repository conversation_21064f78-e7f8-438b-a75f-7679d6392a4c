import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Key, 
  Plus, 
  Edit, 
  Trash2, 
  Copy, 
  Eye, 
  EyeOff,
  RefreshCw,
  Calendar,
  Activity,
  Shield,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { HStack, VStack } from '@/components/ui/layout';
import { Typography } from '@/components/ui/typography';
import { formatDistanceToNow } from 'date-fns';

interface ApiKey {
  id: string;
  name: string;
  description?: string;
  key: string;
  permissions: string[];
  rate_limit: number;
  created_at: string;
  expires_at?: string;
  last_used: string | null;
  usage_count: number;
  is_active: boolean;
  created_by: string;
  usage_stats: {
    daily_usage: number;
    monthly_usage: number;
    error_rate: number;
    avg_response_time: number;
  };
}

interface ApiKeyFormData {
  name: string;
  description: string;
  permissions: string[];
  rate_limit: number;
  expires_in_days?: number;
}

interface ApiKeyManagementProps {
  className?: string;
}

const AVAILABLE_PERMISSIONS = [
  { id: 'read', label: 'Read Access', description: 'View data and configurations' },
  { id: 'write', label: 'Write Access', description: 'Create and modify data' },
  { id: 'delete', label: 'Delete Access', description: 'Remove data and configurations' },
  { id: 'admin', label: 'Admin Access', description: 'Full system administration' },
  { id: 'billing', label: 'Billing Access', description: 'View and manage billing' },
  { id: 'logs', label: 'Logs Access', description: 'View system logs' },
  { id: 'analytics', label: 'Analytics Access', description: 'View usage analytics' }
];

export const ApiKeyManagement: React.FC<ApiKeyManagementProps> = ({ className }) => {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingKey, setEditingKey] = useState<ApiKey | null>(null);
  const [revealedKeys, setRevealedKeys] = useState<Set<string>>(new Set());
  const [formData, setFormData] = useState<ApiKeyFormData>({
    name: '',
    description: '',
    permissions: ['read'],
    rate_limit: 60,
    expires_in_days: undefined
  });

  // Fetch API keys
  const fetchApiKeys = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/secrets');
      if (response.ok) {
        const data = await response.json();
        // Mock enhanced data structure
        const enhancedKeys = (data.data || []).map((key: any) => ({
          ...key,
          description: key.description || '',
          rate_limit: key.rate_limit || 60,
          usage_stats: {
            daily_usage: Math.floor(Math.random() * 100),
            monthly_usage: Math.floor(Math.random() * 1000),
            error_rate: Math.random() * 5,
            avg_response_time: Math.random() * 500 + 100
          },
          created_by: '<EMAIL>'
        }));
        setApiKeys(enhancedKeys);
      }
    } catch (error) {
      console.error('Failed to fetch API keys:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchApiKeys();
  }, []);

  const handleCreateKey = async () => {
    try {
      const response = await fetch('/api/secrets', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: formData.name,
          description: formData.description,
          permissions: formData.permissions,
          rate_limit: formData.rate_limit,
          expires_in_days: formData.expires_in_days
        })
      });
      
      if (response.ok) {
        setShowCreateDialog(false);
        setFormData({
          name: '',
          description: '',
          permissions: ['read'],
          rate_limit: 60,
          expires_in_days: undefined
        });
        fetchApiKeys();
      }
    } catch (error) {
      console.error('Failed to create API key:', error);
    }
  };

  const handleUpdateKey = async (keyId: string, updates: Partial<ApiKey>) => {
    try {
      const response = await fetch(`/api/secrets/${keyId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates)
      });
      
      if (response.ok) {
        setApiKeys(prev => prev.map(key => 
          key.id === keyId ? { ...key, ...updates } : key
        ));
        setEditingKey(null);
      }
    } catch (error) {
      console.error('Failed to update API key:', error);
    }
  };

  const handleDeleteKey = async (keyId: string) => {
    if (!confirm('Are you sure you want to delete this API key? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/secrets/${keyId}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        setApiKeys(prev => prev.filter(key => key.id !== keyId));
      }
    } catch (error) {
      console.error('Failed to delete API key:', error);
    }
  };

  const handleRotateKey = async (keyId: string) => {
    if (!confirm('Are you sure you want to rotate this API key? The old key will be invalidated immediately.')) {
      return;
    }

    try {
      const response = await fetch(`/api/secrets/${keyId}/rotate`, {
        method: 'POST'
      });
      
      if (response.ok) {
        fetchApiKeys();
      }
    } catch (error) {
      console.error('Failed to rotate API key:', error);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // Could show a toast notification here
  };

  const toggleKeyVisibility = (keyId: string) => {
    setRevealedKeys(prev => {
      const newSet = new Set(prev);
      if (newSet.has(keyId)) {
        newSet.delete(keyId);
      } else {
        newSet.add(keyId);
      }
      return newSet;
    });
  };

  const formatKey = (key: string, revealed: boolean) => {
    if (revealed) return key;
    return key.substring(0, 8) + '•'.repeat(key.length - 12) + key.substring(key.length - 4);
  };

  const getStatusColor = (key: ApiKey) => {
    if (!key.is_active) return 'text-gray-500';
    if (key.expires_at && new Date(key.expires_at) < new Date()) return 'text-red-500';
    if (key.usage_stats.error_rate > 10) return 'text-yellow-500';
    return 'text-green-500';
  };

  const getStatusIcon = (key: ApiKey) => {
    if (!key.is_active) return <XCircle className="w-4 h-4 text-gray-500" />;
    if (key.expires_at && new Date(key.expires_at) < new Date()) return <AlertTriangle className="w-4 h-4 text-red-500" />;
    if (key.usage_stats.error_rate > 10) return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
    return <CheckCircle className="w-4 h-4 text-green-500" />;
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Key className="w-6 h-6 text-primary" />
            <div>
              <CardTitle className="text-xl">API Key Management</CardTitle>
              <CardDescription>
                Create, manage, and monitor API keys for system access
              </CardDescription>
            </div>
          </div>
          
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Create API Key
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New API Key</DialogTitle>
                <DialogDescription>
                  Generate a new API key with specific permissions and rate limits
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Name *</Label>
                    <Input
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., Production API Key"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Rate Limit (requests/minute)</Label>
                    <Input
                      type="number"
                      value={formData.rate_limit}
                      onChange={(e) => setFormData(prev => ({ ...prev, rate_limit: parseInt(e.target.value) || 60 }))}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label>Description</Label>
                  <Textarea
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Optional description of this API key's purpose"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Permissions</Label>
                  <div className="grid grid-cols-2 gap-3">
                    {AVAILABLE_PERMISSIONS.map((permission) => (
                      <div key={permission.id} className="flex items-start space-x-2">
                        <Checkbox
                          id={permission.id}
                          checked={formData.permissions.includes(permission.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setFormData(prev => ({
                                ...prev,
                                permissions: [...prev.permissions, permission.id]
                              }));
                            } else {
                              setFormData(prev => ({
                                ...prev,
                                permissions: prev.permissions.filter(p => p !== permission.id)
                              }));
                            }
                          }}
                        />
                        <div className="space-y-1">
                          <Label htmlFor={permission.id} className="text-sm font-medium">
                            {permission.label}
                          </Label>
                          <Typography variant="caption" className="text-muted-foreground">
                            {permission.description}
                          </Typography>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label>Expiration (optional)</Label>
                  <Input
                    type="number"
                    value={formData.expires_in_days || ''}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      expires_in_days: e.target.value ? parseInt(e.target.value) : undefined 
                    }))}
                    placeholder="Days until expiration (leave empty for no expiration)"
                  />
                </div>
                
                <div className="flex justify-end space-x-2 pt-4">
                  <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateKey} disabled={!formData.name}>
                    Create API Key
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>

      <CardContent>
        <ScrollArea className="h-96">
          <div className="space-y-4">
            {apiKeys.length === 0 ? (
              <div className="text-center py-8">
                <Key className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                <Typography variant="body-sm" className="text-muted-foreground">
                  No API keys found. Create your first API key to get started.
                </Typography>
              </div>
            ) : (
              apiKeys.map((apiKey) => (
                <Card key={apiKey.id} className="p-4">
                  <div className="space-y-4">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(apiKey)}
                        <div>
                          <Typography variant="body-sm" className="font-medium">
                            {apiKey.name}
                          </Typography>
                          {apiKey.description && (
                            <Typography variant="caption" className="text-muted-foreground">
                              {apiKey.description}
                            </Typography>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Badge variant={apiKey.is_active ? 'default' : 'secondary'}>
                          {apiKey.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                        {apiKey.expires_at && (
                          <Badge variant="outline" className="text-xs">
                            Expires {formatDistanceToNow(new Date(apiKey.expires_at), { addSuffix: true })}
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* API Key */}
                    <div className="space-y-2">
                      <Label className="text-xs">API Key</Label>
                      <div className="flex items-center space-x-2">
                        <Input
                          value={formatKey(apiKey.key, revealedKeys.has(apiKey.id))}
                          readOnly
                          className="font-mono text-sm"
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleKeyVisibility(apiKey.id)}
                        >
                          {revealedKeys.has(apiKey.id) ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(apiKey.key)}
                        >
                          <Copy className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Stats */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <Typography variant="caption" className="text-muted-foreground">Usage Count</Typography>
                        <Typography variant="body-sm" className="font-mono">{apiKey.usage_count}</Typography>
                      </div>
                      <div>
                        <Typography variant="caption" className="text-muted-foreground">Daily Usage</Typography>
                        <Typography variant="body-sm" className="font-mono">{apiKey.usage_stats.daily_usage}</Typography>
                      </div>
                      <div>
                        <Typography variant="caption" className="text-muted-foreground">Error Rate</Typography>
                        <Typography variant="body-sm" className="font-mono">{apiKey.usage_stats.error_rate.toFixed(1)}%</Typography>
                      </div>
                      <div>
                        <Typography variant="caption" className="text-muted-foreground">Avg Response</Typography>
                        <Typography variant="body-sm" className="font-mono">{apiKey.usage_stats.avg_response_time.toFixed(0)}ms</Typography>
                      </div>
                    </div>

                    {/* Permissions */}
                    <div className="space-y-2">
                      <Typography variant="caption" className="text-muted-foreground">Permissions</Typography>
                      <div className="flex flex-wrap gap-1">
                        {apiKey.permissions.map((permission) => (
                          <Badge key={permission} variant="outline" className="text-xs">
                            {permission}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center justify-between pt-2 border-t">
                      <div className="text-xs text-muted-foreground">
                        Created {formatDistanceToNow(new Date(apiKey.created_at), { addSuffix: true })} by {apiKey.created_by}
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Button variant="ghost" size="sm">
                          <BarChart3 className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleRotateKey(apiKey.id)}
                        >
                          <RefreshCw className="w-4 h-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleDeleteKey(apiKey.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};

export default ApiKeyManagement;
