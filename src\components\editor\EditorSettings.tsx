import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Settings, 
  Palette, 
  Type, 
  Keyboard, 
  Layout, 
  Code, 
  Eye, 
  Zap,
  Monitor,
  Moon,
  Sun,
  Laptop
} from 'lucide-react';

export interface EditorSettings {
  // Appearance
  theme: 'light' | 'dark' | 'auto';
  fontSize: number;
  fontFamily: string;
  lineHeight: number;
  letterSpacing: number;
  
  // Editor behavior
  tabSize: number;
  insertSpaces: boolean;
  wordWrap: 'on' | 'off' | 'wordWrapColumn';
  wordWrapColumn: number;
  autoIndent: 'none' | 'keep' | 'brackets' | 'advanced' | 'full';
  
  // Features
  minimap: boolean;
  lineNumbers: 'on' | 'off' | 'relative' | 'interval';
  codeFolding: boolean;
  bracketMatching: boolean;
  autoClosingBrackets: 'always' | 'languageDefined' | 'beforeWhitespace' | 'never';
  autoClosingQuotes: 'always' | 'languageDefined' | 'beforeWhitespace' | 'never';
  
  // IntelliSense
  quickSuggestions: boolean;
  parameterHints: boolean;
  autoCompletion: boolean;
  wordBasedSuggestions: boolean;
  
  // Visual aids
  renderWhitespace: 'none' | 'boundary' | 'selection' | 'trailing' | 'all';
  renderIndentGuides: boolean;
  highlightActiveIndentGuide: boolean;
  occurrencesHighlight: boolean;
  
  // Performance
  largeFileOptimizations: boolean;
  maxTokenizationLineLength: number;
}

interface EditorSettingsProps {
  settings: EditorSettings;
  onSettingsChange: (settings: EditorSettings) => void;
  onReset: () => void;
  onExport: () => void;
  onImport: (settings: EditorSettings) => void;
}

const defaultSettings: EditorSettings = {
  theme: 'dark',
  fontSize: 14,
  fontFamily: 'JetBrains Mono',
  lineHeight: 1.5,
  letterSpacing: 0,
  tabSize: 2,
  insertSpaces: true,
  wordWrap: 'on',
  wordWrapColumn: 80,
  autoIndent: 'full',
  minimap: false,
  lineNumbers: 'on',
  codeFolding: true,
  bracketMatching: true,
  autoClosingBrackets: 'languageDefined',
  autoClosingQuotes: 'languageDefined',
  quickSuggestions: true,
  parameterHints: true,
  autoCompletion: true,
  wordBasedSuggestions: true,
  renderWhitespace: 'selection',
  renderIndentGuides: true,
  highlightActiveIndentGuide: true,
  occurrencesHighlight: true,
  largeFileOptimizations: true,
  maxTokenizationLineLength: 20000,
};

export const EditorSettings = ({
  settings,
  onSettingsChange,
  onReset,
  onExport,
  onImport
}: EditorSettingsProps) => {
  const [activeTab, setActiveTab] = useState('appearance');

  const updateSetting = <K extends keyof EditorSettings>(
    key: K,
    value: EditorSettings[K]
  ) => {
    onSettingsChange({
      ...settings,
      [key]: value
    });
  };

  const fontFamilies = [
    'JetBrains Mono',
    'Fira Code',
    'Source Code Pro',
    'Monaco',
    'Menlo',
    'Consolas',
    'Courier New',
    'SF Mono',
    'Roboto Mono',
    'Ubuntu Mono'
  ];

  return (
    <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Settings className="w-5 h-5" />
          <h2 className="text-2xl font-bold">Editor Settings</h2>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={onExport}>
            Export
          </Button>
          <Button variant="outline" size="sm" onClick={() => {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = (e) => {
              const file = (e.target as HTMLInputElement).files?.[0];
              if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                  try {
                    const imported = JSON.parse(e.target?.result as string);
                    onImport(imported);
                  } catch (error) {
                    console.error('Failed to import settings:', error);
                  }
                };
                reader.readAsText(file);
              }
            };
            input.click();
          }}>
            Import
          </Button>
          <Button variant="outline" size="sm" onClick={onReset}>
            Reset
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="appearance" className="flex items-center space-x-2">
            <Palette className="w-4 h-4" />
            <span className="hidden sm:inline">Appearance</span>
          </TabsTrigger>
          <TabsTrigger value="editor" className="flex items-center space-x-2">
            <Code className="w-4 h-4" />
            <span className="hidden sm:inline">Editor</span>
          </TabsTrigger>
          <TabsTrigger value="features" className="flex items-center space-x-2">
            <Zap className="w-4 h-4" />
            <span className="hidden sm:inline">Features</span>
          </TabsTrigger>
          <TabsTrigger value="intellisense" className="flex items-center space-x-2">
            <Eye className="w-4 h-4" />
            <span className="hidden sm:inline">IntelliSense</span>
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center space-x-2">
            <Monitor className="w-4 h-4" />
            <span className="hidden sm:inline">Performance</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="appearance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Palette className="w-5 h-5" />
                <span>Theme & Typography</span>
              </CardTitle>
              <CardDescription>
                Customize the visual appearance of the editor
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="theme">Theme</Label>
                  <Select value={settings.theme} onValueChange={(value: any) => updateSetting('theme', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="light">
                        <div className="flex items-center space-x-2">
                          <Sun className="w-4 h-4" />
                          <span>Light</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="dark">
                        <div className="flex items-center space-x-2">
                          <Moon className="w-4 h-4" />
                          <span>Dark</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="auto">
                        <div className="flex items-center space-x-2">
                          <Laptop className="w-4 h-4" />
                          <span>Auto</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fontFamily">Font Family</Label>
                  <Select value={settings.fontFamily} onValueChange={(value) => updateSetting('fontFamily', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {fontFamilies.map((font) => (
                        <SelectItem key={font} value={font}>
                          <span style={{ fontFamily: font }}>{font}</span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="fontSize">Font Size: {settings.fontSize}px</Label>
                  <Slider
                    value={[settings.fontSize]}
                    onValueChange={([value]) => updateSetting('fontSize', value)}
                    min={10}
                    max={24}
                    step={1}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lineHeight">Line Height: {settings.lineHeight}</Label>
                  <Slider
                    value={[settings.lineHeight]}
                    onValueChange={([value]) => updateSetting('lineHeight', value)}
                    min={1.0}
                    max={2.0}
                    step={0.1}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="letterSpacing">Letter Spacing: {settings.letterSpacing}px</Label>
                  <Slider
                    value={[settings.letterSpacing]}
                    onValueChange={([value]) => updateSetting('letterSpacing', value)}
                    min={-2}
                    max={2}
                    step={0.1}
                    className="w-full"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="editor" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Code className="w-5 h-5" />
                <span>Editor Behavior</span>
              </CardTitle>
              <CardDescription>
                Configure how the editor handles text and indentation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="tabSize">Tab Size: {settings.tabSize}</Label>
                  <Slider
                    value={[settings.tabSize]}
                    onValueChange={([value]) => updateSetting('tabSize', value)}
                    min={1}
                    max={8}
                    step={1}
                    className="w-full"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="insertSpaces">Insert Spaces</Label>
                  <Switch
                    checked={settings.insertSpaces}
                    onCheckedChange={(checked) => updateSetting('insertSpaces', checked)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="wordWrap">Word Wrap</Label>
                  <Select value={settings.wordWrap} onValueChange={(value: any) => updateSetting('wordWrap', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="off">Off</SelectItem>
                      <SelectItem value="on">On</SelectItem>
                      <SelectItem value="wordWrapColumn">At Column</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {settings.wordWrap === 'wordWrapColumn' && (
                  <div className="space-y-2">
                    <Label htmlFor="wordWrapColumn">Wrap Column: {settings.wordWrapColumn}</Label>
                    <Slider
                      value={[settings.wordWrapColumn]}
                      onValueChange={([value]) => updateSetting('wordWrapColumn', value)}
                      min={40}
                      max={200}
                      step={10}
                      className="w-full"
                    />
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="autoIndent">Auto Indent</Label>
                <Select value={settings.autoIndent} onValueChange={(value: any) => updateSetting('autoIndent', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    <SelectItem value="keep">Keep</SelectItem>
                    <SelectItem value="brackets">Brackets</SelectItem>
                    <SelectItem value="advanced">Advanced</SelectItem>
                    <SelectItem value="full">Full</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="features" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Zap className="w-5 h-5" />
                <span>Editor Features</span>
              </CardTitle>
              <CardDescription>
                Enable or disable various editor features
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="minimap">Minimap</Label>
                    <p className="text-sm text-muted-foreground">Show code overview</p>
                  </div>
                  <Switch
                    checked={settings.minimap}
                    onCheckedChange={(checked) => updateSetting('minimap', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="codeFolding">Code Folding</Label>
                    <p className="text-sm text-muted-foreground">Collapse code blocks</p>
                  </div>
                  <Switch
                    checked={settings.codeFolding}
                    onCheckedChange={(checked) => updateSetting('codeFolding', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="bracketMatching">Bracket Matching</Label>
                    <p className="text-sm text-muted-foreground">Highlight matching brackets</p>
                  </div>
                  <Switch
                    checked={settings.bracketMatching}
                    onCheckedChange={(checked) => updateSetting('bracketMatching', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="occurrencesHighlight">Highlight Occurrences</Label>
                    <p className="text-sm text-muted-foreground">Highlight same words</p>
                  </div>
                  <Switch
                    checked={settings.occurrencesHighlight}
                    onCheckedChange={(checked) => updateSetting('occurrencesHighlight', checked)}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="lineNumbers">Line Numbers</Label>
                  <Select value={settings.lineNumbers} onValueChange={(value: any) => updateSetting('lineNumbers', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="off">Off</SelectItem>
                      <SelectItem value="on">On</SelectItem>
                      <SelectItem value="relative">Relative</SelectItem>
                      <SelectItem value="interval">Interval</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="renderWhitespace">Render Whitespace</Label>
                  <Select value={settings.renderWhitespace} onValueChange={(value: any) => updateSetting('renderWhitespace', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      <SelectItem value="boundary">Boundary</SelectItem>
                      <SelectItem value="selection">Selection</SelectItem>
                      <SelectItem value="trailing">Trailing</SelectItem>
                      <SelectItem value="all">All</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="intellisense" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Eye className="w-5 h-5" />
                <span>IntelliSense & Completion</span>
              </CardTitle>
              <CardDescription>
                Configure code completion and assistance features
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="quickSuggestions">Quick Suggestions</Label>
                    <p className="text-sm text-muted-foreground">Show suggestions as you type</p>
                  </div>
                  <Switch
                    checked={settings.quickSuggestions}
                    onCheckedChange={(checked) => updateSetting('quickSuggestions', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="parameterHints">Parameter Hints</Label>
                    <p className="text-sm text-muted-foreground">Show function parameters</p>
                  </div>
                  <Switch
                    checked={settings.parameterHints}
                    onCheckedChange={(checked) => updateSetting('parameterHints', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="autoCompletion">Auto Completion</Label>
                    <p className="text-sm text-muted-foreground">Complete code automatically</p>
                  </div>
                  <Switch
                    checked={settings.autoCompletion}
                    onCheckedChange={(checked) => updateSetting('autoCompletion', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="wordBasedSuggestions">Word-based Suggestions</Label>
                    <p className="text-sm text-muted-foreground">Suggest from document words</p>
                  </div>
                  <Switch
                    checked={settings.wordBasedSuggestions}
                    onCheckedChange={(checked) => updateSetting('wordBasedSuggestions', checked)}
                  />
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="autoClosingBrackets">Auto Closing Brackets</Label>
                  <Select value={settings.autoClosingBrackets} onValueChange={(value: any) => updateSetting('autoClosingBrackets', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="always">Always</SelectItem>
                      <SelectItem value="languageDefined">Language Defined</SelectItem>
                      <SelectItem value="beforeWhitespace">Before Whitespace</SelectItem>
                      <SelectItem value="never">Never</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="autoClosingQuotes">Auto Closing Quotes</Label>
                  <Select value={settings.autoClosingQuotes} onValueChange={(value: any) => updateSetting('autoClosingQuotes', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="always">Always</SelectItem>
                      <SelectItem value="languageDefined">Language Defined</SelectItem>
                      <SelectItem value="beforeWhitespace">Before Whitespace</SelectItem>
                      <SelectItem value="never">Never</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Monitor className="w-5 h-5" />
                <span>Performance Settings</span>
              </CardTitle>
              <CardDescription>
                Optimize editor performance for large files
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="largeFileOptimizations">Large File Optimizations</Label>
                  <p className="text-sm text-muted-foreground">Enable optimizations for large files</p>
                </div>
                <Switch
                  checked={settings.largeFileOptimizations}
                  onCheckedChange={(checked) => updateSetting('largeFileOptimizations', checked)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxTokenizationLineLength">
                  Max Tokenization Line Length: {settings.maxTokenizationLineLength}
                </Label>
                <p className="text-sm text-muted-foreground">
                  Lines longer than this will not be tokenized for performance
                </p>
                <Slider
                  value={[settings.maxTokenizationLineLength]}
                  onValueChange={([value]) => updateSetting('maxTokenizationLineLength', value)}
                  min={1000}
                  max={50000}
                  step={1000}
                  className="w-full"
                />
              </div>

              <div className="p-4 bg-muted rounded-lg">
                <h4 className="font-medium mb-2">Performance Tips</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Disable minimap for very large files</li>
                  <li>• Turn off word-based suggestions for better performance</li>
                  <li>• Reduce max tokenization line length for files with very long lines</li>
                  <li>• Enable large file optimizations for files over 1MB</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
