import * as React from "react"
import { cn } from "@/lib/utils"
import {
  Bot,
  Eye,
  Cog,
  Play,
  Square,
  ArrowLeft,
  RotateCcw,
  Download,
  BookOpen,
  Keyboard,
  GitPullRequest,
  Github,
  Settings,
  History,
  Zap,
  FileText,
  Code,
  Database,
  Cpu,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Info,
  Search,
  Copy,
  ExternalLink,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  Plus,
  Minus,
  X,
  Check,
  Loader2,
  Upload,
  Trash2,
  Edit,
  Save,
  RefreshCw,
  Filter,
  SortAsc,
  SortDesc,
  MoreHorizontal,
  MoreVertical,
  Bug,
  Pause,
  TrendingUp,
  TrendingDown,
  Activity,
  Clock,
  IterationCw,
  Star,
  Calendar,
  BarChart3
} from "lucide-react"

// Icon size variants
const iconSizes = {
  xs: "w-3 h-3",
  sm: "w-4 h-4", 
  md: "w-5 h-5",
  lg: "w-6 h-6",
  xl: "w-8 h-8",
  "2xl": "w-10 h-10"
} as const

type IconSize = keyof typeof iconSizes

interface IconProps extends React.SVGAttributes<SVGElement> {
  size?: IconSize
  className?: string
}

// Generic Icon wrapper component
const Icon = React.forwardRef<SVGSVGElement, IconProps>(
  ({ size = "sm", className, ...props }, ref) => {
    return (
      <svg
        ref={ref}
        className={cn(iconSizes[size], className)}
        {...props}
      />
    )
  }
)
Icon.displayName = "Icon"

// Specific icon components with consistent sizing
const createIconComponent = (LucideIcon: React.ComponentType<any>, displayName: string) => {
  const IconComponent = React.forwardRef<SVGSVGElement, IconProps>(
    ({ size = "sm", className, ...props }, ref) => (
      <LucideIcon
        ref={ref}
        className={cn(iconSizes[size], className)}
        {...props}
      />
    )
  )
  IconComponent.displayName = displayName
  return IconComponent
}

// Agent Icons
export const AgentPlannerIcon = createIconComponent(Bot, "AgentPlannerIcon")
export const AgentCriticIcon = createIconComponent(Eye, "AgentCriticIcon")
export const AgentSystemIcon = createIconComponent(Cog, "AgentSystemIcon")

// Action Icons
export const PlayIcon = createIconComponent(Play, "PlayIcon")
export const StopIcon = createIconComponent(Square, "StopIcon")
export const BackIcon = createIconComponent(ArrowLeft, "BackIcon")
export const ApplyIcon = createIconComponent(RotateCcw, "ApplyIcon")
export const DownloadIcon = createIconComponent(Download, "DownloadIcon")
export const UploadIcon = createIconComponent(Upload, "UploadIcon")
export const SaveIcon = createIconComponent(Save, "SaveIcon")
export const EditIcon = createIconComponent(Edit, "EditIcon")
export const DeleteIcon = createIconComponent(Trash2, "DeleteIcon")
export const CopyIcon = createIconComponent(Copy, "CopyIcon")
export const RefreshIcon = createIconComponent(RefreshCw, "RefreshIcon")

// Navigation Icons
export const ExamplesIcon = createIconComponent(BookOpen, "ExamplesIcon")
export const HelpIcon = createIconComponent(Keyboard, "HelpIcon")
export const SettingsIcon = createIconComponent(Settings, "SettingsIcon")
export const HistoryIcon = createIconComponent(History, "HistoryIcon")
export const SearchIcon = createIconComponent(Search, "SearchIcon")
export const ExternalLinkIcon = createIconComponent(ExternalLink, "ExternalLinkIcon")

// Status Icons
export const SuccessIcon = createIconComponent(CheckCircle, "SuccessIcon")
export const ErrorIcon = createIconComponent(XCircle, "ErrorIcon")
export const WarningIcon = createIconComponent(AlertTriangle, "WarningIcon")
export const InfoIcon = createIconComponent(Info, "InfoIcon")
export const LoadingIcon = createIconComponent(Loader2, "LoadingIcon")

// Content Icons
export const CodeIcon = createIconComponent(Code, "CodeIcon")
export const FileIcon = createIconComponent(FileText, "FileIcon")
export const DatabaseIcon = createIconComponent(Database, "DatabaseIcon")
export const CpuIcon = createIconComponent(Cpu, "CpuIcon")
export const ReactorIcon = createIconComponent(Zap, "ReactorIcon")

// Integration Icons
export const GitHubIcon = createIconComponent(Github, "GitHubIcon")
export const PullRequestIcon = createIconComponent(GitPullRequest, "PullRequestIcon")

// UI Control Icons
export const ChevronDownIcon = createIconComponent(ChevronDown, "ChevronDownIcon")
export const ChevronUpIcon = createIconComponent(ChevronUp, "ChevronUpIcon")
export const ChevronLeftIcon = createIconComponent(ChevronLeft, "ChevronLeftIcon")
export const ChevronRightIcon = createIconComponent(ChevronRight, "ChevronRightIcon")
export const PlusIcon = createIconComponent(Plus, "PlusIcon")
export const MinusIcon = createIconComponent(Minus, "MinusIcon")
export const CloseIcon = createIconComponent(X, "CloseIcon")
export const CheckIcon = createIconComponent(Check, "CheckIcon")
export const FilterIcon = createIconComponent(Filter, "FilterIcon")
export const SortAscIcon = createIconComponent(SortAsc, "SortAscIcon")
export const SortDescIcon = createIconComponent(SortDesc, "SortDescIcon")
export const MoreHorizontalIcon = createIconComponent(MoreHorizontal, "MoreHorizontalIcon")
export const MoreVerticalIcon = createIconComponent(MoreVertical, "MoreVerticalIcon")

// Additional Icons
export const BugIcon = createIconComponent(Bug, "BugIcon")
export const PauseIcon = createIconComponent(Pause, "PauseIcon")
export const TrendingUpIcon = createIconComponent(TrendingUp, "TrendingUpIcon")
export const TrendingDownIcon = createIconComponent(TrendingDown, "TrendingDownIcon")
export const ActivityIcon = createIconComponent(Activity, "ActivityIcon")
export const ClockIcon = createIconComponent(Clock, "ClockIcon")
export const IterationIcon = createIconComponent(IterationCw, "IterationIcon")
export const ScoreIcon = createIconComponent(Star, "ScoreIcon")
export const CalendarIcon = createIconComponent(Calendar, "CalendarIcon")
export const ChartIcon = createIconComponent(BarChart3, "ChartIcon")

// Status indicator component
interface StatusIndicatorProps {
  status: 'success' | 'error' | 'warning' | 'info' | 'loading'
  size?: IconSize
  className?: string
}

export const StatusIndicator = React.forwardRef<SVGSVGElement, StatusIndicatorProps>(
  ({ status, size = "sm", className, ...props }, ref) => {
    const statusConfig = {
      success: { Icon: SuccessIcon, className: "text-success" },
      error: { Icon: ErrorIcon, className: "text-destructive" },
      warning: { Icon: WarningIcon, className: "text-warning" },
      info: { Icon: InfoIcon, className: "text-info" },
      loading: { Icon: LoadingIcon, className: "text-muted-foreground animate-spin" }
    }
    
    const { Icon: StatusIcon, className: statusClassName } = statusConfig[status]
    
    return (
      <StatusIcon
        ref={ref}
        size={size}
        className={cn(statusClassName, className)}
        {...props}
      />
    )
  }
)
StatusIndicator.displayName = "StatusIndicator"

// Agent indicator component
interface AgentIndicatorProps {
  agent: 'planner' | 'critic' | 'system'
  size?: IconSize
  className?: string
}

export const AgentIndicator = React.forwardRef<SVGSVGElement, AgentIndicatorProps>(
  ({ agent, size = "sm", className, ...props }, ref) => {
    const agentConfig = {
      planner: { Icon: AgentPlannerIcon, className: "text-agent-planner" },
      critic: { Icon: AgentCriticIcon, className: "text-agent-critic" },
      system: { Icon: AgentSystemIcon, className: "text-agent-system" }
    }
    
    const { Icon: AgentIcon, className: agentClassName } = agentConfig[agent]
    
    return (
      <AgentIcon
        ref={ref}
        size={size}
        className={cn(agentClassName, className)}
        {...props}
      />
    )
  }
)
AgentIndicator.displayName = "AgentIndicator"

export { Icon, iconSizes, type IconSize, type IconProps }
