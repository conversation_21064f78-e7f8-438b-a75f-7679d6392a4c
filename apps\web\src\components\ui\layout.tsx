import * as React from "react"
import { cn } from "@/lib/utils"

// Layout Container Components
interface ContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
}

const Container = React.forwardRef<HTMLDivElement, ContainerProps>(
  ({ className, size = 'lg', ...props }, ref) => {
    const sizeClasses = {
      sm: 'max-w-2xl',
      md: 'max-w-4xl', 
      lg: 'max-w-6xl',
      xl: 'max-w-7xl',
      full: 'max-w-full'
    }
    
    return (
      <div
        ref={ref}
        className={cn(
          "mx-auto px-4 sm:px-6 lg:px-8",
          sizeClasses[size],
          className
        )}
        {...props}
      />
    )
  }
)
Container.displayName = "Container"

// Section Components with consistent spacing
interface SectionProps extends React.HTMLAttributes<HTMLElement> {
  spacing?: 'sm' | 'md' | 'lg' | 'xl'
  as?: React.ElementType
}

const Section = React.forwardRef<HTMLElement, SectionProps>(
  ({ className, spacing = 'md', as: Component = 'section', ...props }, ref) => {
    const spacingClasses = {
      sm: 'py-8',
      md: 'py-12',
      lg: 'py-16',
      xl: 'py-24'
    }
    
    return (
      <Component
        ref={ref}
        className={cn(spacingClasses[spacing], className)}
        {...props}
      />
    )
  }
)
Section.displayName = "Section"

// Stack Components for consistent vertical spacing
interface StackProps extends React.HTMLAttributes<HTMLDivElement> {
  spacing?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  align?: 'start' | 'center' | 'end' | 'stretch'
}

const VStack = React.forwardRef<HTMLDivElement, StackProps>(
  ({ className, spacing = 'md', align = 'stretch', ...props }, ref) => {
    const spacingClasses = {
      xs: 'space-y-1',
      sm: 'space-y-2',
      md: 'space-y-4',
      lg: 'space-y-6',
      xl: 'space-y-8'
    }
    
    const alignClasses = {
      start: 'items-start',
      center: 'items-center',
      end: 'items-end',
      stretch: 'items-stretch'
    }
    
    return (
      <div
        ref={ref}
        className={cn(
          "flex flex-col",
          spacingClasses[spacing],
          alignClasses[align],
          className
        )}
        {...props}
      />
    )
  }
)
VStack.displayName = "VStack"

const HStack = React.forwardRef<HTMLDivElement, StackProps>(
  ({ className, spacing = 'md', align = 'center', ...props }, ref) => {
    const spacingClasses = {
      xs: 'space-x-1',
      sm: 'space-x-2',
      md: 'space-x-4',
      lg: 'space-x-6',
      xl: 'space-x-8'
    }
    
    const alignClasses = {
      start: 'items-start',
      center: 'items-center',
      end: 'items-end',
      stretch: 'items-stretch'
    }
    
    return (
      <div
        ref={ref}
        className={cn(
          "flex flex-row",
          spacingClasses[spacing],
          alignClasses[align],
          className
        )}
        {...props}
      />
    )
  }
)
HStack.displayName = "HStack"

// Grid Components
interface GridProps extends React.HTMLAttributes<HTMLDivElement> {
  cols?: 1 | 2 | 3 | 4 | 5 | 6 | 12
  gap?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  responsive?: boolean
}

const Grid = React.forwardRef<HTMLDivElement, GridProps>(
  ({ className, cols = 1, gap = 'md', responsive = true, ...props }, ref) => {
    const colClasses = {
      1: 'grid-cols-1',
      2: responsive ? 'grid-cols-1 sm:grid-cols-2' : 'grid-cols-2',
      3: responsive ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' : 'grid-cols-3',
      4: responsive ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4' : 'grid-cols-4',
      5: responsive ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-5' : 'grid-cols-5',
      6: responsive ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-6' : 'grid-cols-6',
      12: responsive ? 'grid-cols-1 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-12' : 'grid-cols-12'
    }
    
    const gapClasses = {
      xs: 'gap-1',
      sm: 'gap-2',
      md: 'gap-4',
      lg: 'gap-6',
      xl: 'gap-8'
    }
    
    return (
      <div
        ref={ref}
        className={cn(
          "grid",
          colClasses[cols],
          gapClasses[gap],
          className
        )}
        {...props}
      />
    )
  }
)
Grid.displayName = "Grid"

// Panel Components for consistent card layouts
interface PanelProps extends React.HTMLAttributes<HTMLDivElement> {
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'default' | 'outlined' | 'elevated' | 'glass'
}

const Panel = React.forwardRef<HTMLDivElement, PanelProps>(
  ({ className, padding = 'md', variant = 'default', ...props }, ref) => {
    const paddingClasses = {
      none: '',
      sm: 'p-3',
      md: 'p-4',
      lg: 'p-6',
      xl: 'p-8'
    }
    
    const variantClasses = {
      default: 'bg-card border border-border',
      outlined: 'border border-border',
      elevated: 'bg-card border border-border shadow-lg',
      glass: 'glass'
    }
    
    return (
      <div
        ref={ref}
        className={cn(
          "rounded-lg",
          paddingClasses[padding],
          variantClasses[variant],
          className
        )}
        {...props}
      />
    )
  }
)
Panel.displayName = "Panel"

// Divider Component
interface DividerProps extends React.HTMLAttributes<HTMLDivElement> {
  orientation?: 'horizontal' | 'vertical'
  spacing?: 'sm' | 'md' | 'lg'
}

const Divider = React.forwardRef<HTMLDivElement, DividerProps>(
  ({ className, orientation = 'horizontal', spacing = 'md', ...props }, ref) => {
    const spacingClasses = {
      sm: orientation === 'horizontal' ? 'my-2' : 'mx-2',
      md: orientation === 'horizontal' ? 'my-4' : 'mx-4',
      lg: orientation === 'horizontal' ? 'my-6' : 'mx-6'
    }
    
    const orientationClasses = {
      horizontal: 'w-full h-px',
      vertical: 'h-full w-px'
    }
    
    return (
      <div
        ref={ref}
        className={cn(
          "bg-border",
          orientationClasses[orientation],
          spacingClasses[spacing],
          className
        )}
        {...props}
      />
    )
  }
)
Divider.displayName = "Divider"

export {
  Container,
  Section,
  VStack,
  HStack,
  Grid,
  Panel,
  Divider,
  type ContainerProps,
  type SectionProps,
  type StackProps,
  type GridProps,
  type PanelProps,
  type DividerProps
}
