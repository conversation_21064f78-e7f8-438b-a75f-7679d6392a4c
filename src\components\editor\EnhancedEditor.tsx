import { useState, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { MultiFileEditor } from './MultiFileEditor';
import { AdvancedDiffViewer } from './AdvancedDiffViewer';
import { CodeSnippets } from './CodeSnippets';
import { SearchReplace } from './SearchReplace';
import { EditorSettings, EditorSettings as EditorSettingsType } from './EditorSettings';
import { 
  Settings, 
  Search, 
  Code, 
  GitCompare, 
  Save, 
  Play, 
  Square, 
  RotateCcw,
  Zap,
  Eye,
  FileText,
  Layers,
  Terminal,
  Bug
} from 'lucide-react';

interface EditorFile {
  id: string;
  name: string;
  path: string;
  content: string;
  language: string;
  isDirty: boolean;
  isReadOnly: boolean;
  lastModified: Date;
}

interface EnhancedEditorProps {
  files: EditorFile[];
  originalCode?: string;
  transformedCode?: string;
  isTransforming?: boolean;
  onFileChange: (fileId: string, content: string) => void;
  onFileCreate: (name: string, path: string, language: string) => void;
  onFileDelete: (fileId: string) => void;
  onFileRename: (fileId: string, newName: string) => void;
  onFileSave: (fileId: string) => void;
  onFileSaveAll: () => void;
  onRunTransformation?: () => void;
  onStopTransformation?: () => void;
  onApplyChanges?: () => void;
  onResetChanges?: () => void;
}

const defaultEditorSettings: EditorSettingsType = {
  theme: 'dark',
  fontSize: 14,
  fontFamily: 'JetBrains Mono',
  lineHeight: 1.5,
  letterSpacing: 0,
  tabSize: 2,
  insertSpaces: true,
  wordWrap: 'on',
  wordWrapColumn: 80,
  autoIndent: 'full',
  minimap: false,
  lineNumbers: 'on',
  codeFolding: true,
  bracketMatching: true,
  autoClosingBrackets: 'languageDefined',
  autoClosingQuotes: 'languageDefined',
  quickSuggestions: true,
  parameterHints: true,
  autoCompletion: true,
  wordBasedSuggestions: true,
  renderWhitespace: 'selection',
  renderIndentGuides: true,
  highlightActiveIndentGuide: true,
  occurrencesHighlight: true,
  largeFileOptimizations: true,
  maxTokenizationLineLength: 20000,
};

export const EnhancedEditor = ({
  files,
  originalCode = '',
  transformedCode = '',
  isTransforming = false,
  onFileChange,
  onFileCreate,
  onFileDelete,
  onFileRename,
  onFileSave,
  onFileSaveAll,
  onRunTransformation,
  onStopTransformation,
  onApplyChanges,
  onResetChanges
}: EnhancedEditorProps) => {
  const [activeView, setActiveView] = useState<'editor' | 'diff' | 'snippets'>('editor');
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [editorSettings, setEditorSettings] = useState<EditorSettingsType>(defaultEditorSettings);
  const [validationErrors, setValidationErrors] = useState<any[]>([]);
  const [activeFileId, setActiveFileId] = useState<string>(files[0]?.id || '');

  // Auto-save settings to localStorage
  useEffect(() => {
    const saved = localStorage.getItem('editor-settings');
    if (saved) {
      try {
        setEditorSettings(JSON.parse(saved));
      } catch (error) {
        console.error('Failed to load editor settings:', error);
      }
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('editor-settings', JSON.stringify(editorSettings));
  }, [editorSettings]);

  const handleInsertSnippet = useCallback((code: string) => {
    if (activeFileId) {
      const activeFile = files.find(f => f.id === activeFileId);
      if (activeFile) {
        // Insert snippet at current cursor position
        // In a real implementation, you'd get the cursor position from Monaco
        const newContent = activeFile.content + '\n' + code;
        onFileChange(activeFileId, newContent);
      }
    }
  }, [activeFileId, files, onFileChange]);

  const handleSearchReplace = useCallback((fileId: string, replacements: Array<{
    start: number;
    end: number;
    replacement: string;
  }>) => {
    const file = files.find(f => f.id === fileId);
    if (!file) return;

    let newContent = file.content;
    
    // Apply replacements in reverse order to maintain correct offsets
    replacements.sort((a, b) => b.start - a.start);
    
    replacements.forEach(({ start, end, replacement }) => {
      newContent = newContent.substring(0, start) + replacement + newContent.substring(end);
    });
    
    onFileChange(fileId, newContent);
  }, [files, onFileChange]);

  const handleValidationErrors = useCallback((markers: any[]) => {
    setValidationErrors(markers);
  }, []);

  const exportSettings = () => {
    const dataStr = JSON.stringify(editorSettings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'editor-settings.json';
    link.click();
    URL.revokeObjectURL(url);
  };

  const importSettings = (settings: EditorSettingsType) => {
    setEditorSettings(settings);
  };

  const resetSettings = () => {
    setEditorSettings(defaultEditorSettings);
  };

  const getStatusInfo = () => {
    const totalFiles = files.length;
    const dirtyFiles = files.filter(f => f.isDirty).length;
    const totalLines = files.reduce((acc, file) => acc + file.content.split('\n').length, 0);
    const errors = validationErrors.filter(m => m.severity === 8).length; // Monaco error severity
    const warnings = validationErrors.filter(m => m.severity === 4).length; // Monaco warning severity

    return { totalFiles, dirtyFiles, totalLines, errors, warnings };
  };

  const status = getStatusInfo();

  return (
    <div className="w-full h-full flex flex-col">
      {/* Toolbar */}
      <div className="border-b border-border bg-background p-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Tabs value={activeView} onValueChange={(value: any) => setActiveView(value)}>
              <TabsList className="h-8">
                <TabsTrigger value="editor" className="text-xs">
                  <FileText className="w-3 h-3 mr-1" />
                  Editor
                </TabsTrigger>
                <TabsTrigger value="diff" className="text-xs">
                  <GitCompare className="w-3 h-3 mr-1" />
                  Diff
                </TabsTrigger>
                <TabsTrigger value="snippets" className="text-xs">
                  <Code className="w-3 h-3 mr-1" />
                  Snippets
                </TabsTrigger>
              </TabsList>
            </Tabs>

            <Separator orientation="vertical" className="h-6" />

            {/* Action buttons */}
            <div className="flex items-center space-x-1">
              {onRunTransformation && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={isTransforming ? onStopTransformation : onRunTransformation}
                  disabled={files.length === 0}
                >
                  {isTransforming ? (
                    <>
                      <Square className="w-3 h-3 mr-1" />
                      Stop
                    </>
                  ) : (
                    <>
                      <Play className="w-3 h-3 mr-1" />
                      Transform
                    </>
                  )}
                </Button>
              )}

              <Button variant="outline" size="sm" onClick={onFileSaveAll}>
                <Save className="w-3 h-3 mr-1" />
                Save All
              </Button>

              {onApplyChanges && (
                <Button variant="outline" size="sm" onClick={onApplyChanges}>
                  <Zap className="w-3 h-3 mr-1" />
                  Apply
                </Button>
              )}

              {onResetChanges && (
                <Button variant="outline" size="sm" onClick={onResetChanges}>
                  <RotateCcw className="w-3 h-3 mr-1" />
                  Reset
                </Button>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {/* Status indicators */}
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <Badge variant="outline" className="text-xs">
                {status.totalFiles} files
              </Badge>
              {status.dirtyFiles > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {status.dirtyFiles} unsaved
                </Badge>
              )}
              <Badge variant="outline" className="text-xs">
                {status.totalLines} lines
              </Badge>
              {status.errors > 0 && (
                <Badge variant="destructive" className="text-xs">
                  <Bug className="w-3 h-3 mr-1" />
                  {status.errors} errors
                </Badge>
              )}
              {status.warnings > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {status.warnings} warnings
                </Badge>
              )}
            </div>

            <Separator orientation="vertical" className="h-6" />

            {/* Tool buttons */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsSearchOpen(true)}
            >
              <Search className="w-4 h-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsSettingsOpen(true)}
            >
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1">
        {activeView === 'editor' && (
          <MultiFileEditor
            files={files}
            onFileChange={onFileChange}
            onFileCreate={onFileCreate}
            onFileDelete={onFileDelete}
            onFileRename={onFileRename}
            onFileSave={onFileSave}
            onFileSaveAll={onFileSaveAll}
            enableAutoSave={true}
            autoSaveDelay={2000}
          />
        )}

        {activeView === 'diff' && (
          <AdvancedDiffViewer
            original={originalCode}
            modified={transformedCode}
            language="typescript"
            fileName="transformation.ts"
            onAcceptAll={onApplyChanges}
            onRejectAll={onResetChanges}
            enableConflictResolution={true}
          />
        )}

        {activeView === 'snippets' && (
          <CodeSnippets
            onInsertSnippet={handleInsertSnippet}
            onCreateFromTemplate={() => {}}
            language={files.find(f => f.id === activeFileId)?.language}
          />
        )}
      </div>

      {/* Search & Replace Dialog */}
      <SearchReplace
        files={files}
        onReplace={handleSearchReplace}
        onNavigateToResult={(fileId, lineNumber, columnStart) => {
          setActiveFileId(fileId);
          // In a real implementation, you'd navigate to the specific position
        }}
        isVisible={isSearchOpen}
        onClose={() => setIsSearchOpen(false)}
      />

      {/* Settings Dialog */}
      <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
        <DialogContent className="max-w-6xl max-h-[80vh] overflow-auto">
          <DialogHeader>
            <DialogTitle>Editor Settings</DialogTitle>
          </DialogHeader>
          <EditorSettings
            settings={editorSettings}
            onSettingsChange={setEditorSettings}
            onReset={resetSettings}
            onExport={exportSettings}
            onImport={importSettings}
          />
        </DialogContent>
      </Dialog>

      {/* Status bar */}
      <div className="border-t border-border bg-muted/30 px-3 py-1">
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center space-x-4">
            <span>Ready</span>
            {isTransforming && (
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                <span>Transforming...</span>
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-4">
            <span>Ln {1}, Col {1}</span>
            <span>{editorSettings.fontSize}px</span>
            <span>{editorSettings.tabSize} spaces</span>
            <span>{editorSettings.theme}</span>
          </div>
        </div>
      </div>
    </div>
  );
};
