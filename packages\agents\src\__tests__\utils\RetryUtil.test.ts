import { jest } from '@jest/globals';
import { RetryUtil } from '../../utils/RetryUtil.js';
import { ProviderError, RateLimitError } from '../../providers/AgentProvider.js';

describe('RetryUtil', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('withRetry', () => {
    it('should succeed on first attempt', async () => {
      const mockFn = jest.fn().mockResolvedValue('success');
      
      const result = await RetryUtil.withRetry(mockFn);
      
      expect(result.success).toBe(true);
      expect(result.result).toBe('success');
      expect(result.attempts).toBe(1);
      expect(result.totalDelay).toBe(0);
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it('should retry on retryable errors', async () => {
      const mockFn = jest.fn()
        .mockRejectedValueOnce(new RateLimitError('openai', 1000))
        .mockRejectedValueOnce(new ProviderError('Server error', 'openai', 'SERVER_ERROR', 500))
        .mockResolvedValue('success');
      
      const result = await RetryUtil.withRetry(mockFn, { maxAttempts: 5 });
      
      expect(result.success).toBe(true);
      expect(result.result).toBe('success');
      expect(result.attempts).toBe(3);
      expect(result.totalDelay).toBeGreaterThan(0);
      expect(mockFn).toHaveBeenCalledTimes(3);
    });

    it('should not retry on non-retryable errors', async () => {
      const mockFn = jest.fn()
        .mockRejectedValue(new ProviderError('Authentication failed', 'openai', 'AUTH_ERROR', 401));
      
      const result = await RetryUtil.withRetry(mockFn);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(ProviderError);
      expect(result.attempts).toBe(1);
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it('should respect max attempts limit', async () => {
      const mockFn = jest.fn()
        .mockRejectedValue(new RateLimitError('openai'));
      
      const result = await RetryUtil.withRetry(mockFn, { maxAttempts: 3 });
      
      expect(result.success).toBe(false);
      expect(result.attempts).toBe(3);
      expect(mockFn).toHaveBeenCalledTimes(3);
    });

    it('should calculate exponential backoff delays', async () => {
      const mockFn = jest.fn()
        .mockRejectedValueOnce(new RateLimitError('openai'))
        .mockRejectedValueOnce(new RateLimitError('openai'))
        .mockResolvedValue('success');
      
      const onAttempt = jest.fn();
      
      const result = await RetryUtil.withRetry(
        mockFn, 
        { baseDelay: 100, jitter: false },
        onAttempt
      );
      
      expect(result.success).toBe(true);
      expect(onAttempt).toHaveBeenCalledTimes(2);
      
      // Check that delays increase exponentially
      const firstDelay = onAttempt.mock.calls[0][0].delay;
      const secondDelay = onAttempt.mock.calls[1][0].delay;
      expect(secondDelay).toBeGreaterThan(firstDelay);
    });

    it('should apply jitter when enabled', async () => {
      const mockFn = jest.fn()
        .mockRejectedValueOnce(new RateLimitError('openai'))
        .mockResolvedValue('success');
      
      const onAttempt = jest.fn();
      
      await RetryUtil.withRetry(
        mockFn, 
        { baseDelay: 1000, jitter: true },
        onAttempt
      );
      
      const delay = onAttempt.mock.calls[0][0].delay;
      // With jitter, delay should be within ±25% of base delay
      expect(delay).toBeGreaterThan(750);
      expect(delay).toBeLessThan(1250);
    });

    it('should respect max delay limit', async () => {
      const mockFn = jest.fn()
        .mockRejectedValue(new RateLimitError('openai'));
      
      const onAttempt = jest.fn();
      
      await RetryUtil.withRetry(
        mockFn, 
        { 
          baseDelay: 1000, 
          maxDelay: 2000, 
          maxAttempts: 5,
          jitter: false 
        },
        onAttempt
      );
      
      // Check that no delay exceeds maxDelay
      for (const call of onAttempt.mock.calls) {
        expect(call[0].delay).toBeLessThanOrEqual(2000);
      }
    });
  });

  describe('withProviderRetry', () => {
    it('should use provider-specific configuration', async () => {
      const mockFn = jest.fn()
        .mockRejectedValueOnce(new RateLimitError('vertex-ai'))
        .mockResolvedValue('success');
      
      const result = await RetryUtil.withProviderRetry(mockFn, 'vertex-ai');
      
      expect(result.success).toBe(true);
      expect(result.attempts).toBe(2);
    });

    it('should log provider-specific retry information', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      const mockFn = jest.fn()
        .mockRejectedValueOnce(new RateLimitError('openai'))
        .mockResolvedValue('success');
      
      await RetryUtil.withProviderRetry(mockFn, 'openai');
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('[openai] Retry attempt')
      );
      
      consoleSpy.mockRestore();
    });
  });

  describe('createRetryWrapper', () => {
    it('should create a wrapped function with retry logic', async () => {
      const originalFn = jest.fn()
        .mockRejectedValueOnce(new RateLimitError('openai'))
        .mockResolvedValue('wrapped success');
      
      const wrappedFn = RetryUtil.createRetryWrapper(originalFn);
      
      const result = await wrappedFn('arg1', 'arg2');
      
      expect(result).toBe('wrapped success');
      expect(originalFn).toHaveBeenCalledTimes(2);
      expect(originalFn).toHaveBeenCalledWith('arg1', 'arg2');
    });

    it('should throw error if all retries fail', async () => {
      const originalFn = jest.fn()
        .mockRejectedValue(new RateLimitError('openai'));
      
      const wrappedFn = RetryUtil.createRetryWrapper(originalFn, { maxAttempts: 2 });
      
      await expect(wrappedFn()).rejects.toThrow();
    });
  });

  describe('batchRetry', () => {
    it('should execute multiple operations with retry', async () => {
      const operations = [
        {
          fn: jest.fn().mockResolvedValue('result1'),
          id: 'op1',
        },
        {
          fn: jest.fn()
            .mockRejectedValueOnce(new RateLimitError('openai'))
            .mockResolvedValue('result2'),
          id: 'op2',
        },
        {
          fn: jest.fn().mockRejectedValue(new Error('permanent failure')),
          id: 'op3',
        },
      ];
      
      const results = await RetryUtil.batchRetry(operations);
      
      expect(results).toHaveLength(3);
      expect(results[0].success).toBe(true);
      expect(results[0].result).toBe('result1');
      expect(results[1].success).toBe(true);
      expect(results[1].result).toBe('result2');
      expect(results[2].success).toBe(false);
    });
  });

  describe('getRetryStats', () => {
    it('should calculate retry statistics correctly', () => {
      const results = [
        { success: true, attempts: 1, totalDelay: 0, lastAttemptAt: new Date() },
        { success: true, attempts: 3, totalDelay: 3000, lastAttemptAt: new Date() },
        { success: false, attempts: 5, totalDelay: 15000, lastAttemptAt: new Date() },
      ];
      
      const stats = RetryUtil.getRetryStats(results);
      
      expect(stats.totalOperations).toBe(3);
      expect(stats.successfulOperations).toBe(2);
      expect(stats.failedOperations).toBe(1);
      expect(stats.averageAttempts).toBe(3); // (1+3+5)/3
      expect(stats.totalDelay).toBe(18000);
      expect(stats.successRate).toBeCloseTo(0.667, 3);
    });
  });

  describe('error classification', () => {
    it('should identify network errors as retryable', async () => {
      const networkError = new Error('ECONNRESET: Connection reset by peer');
      const mockFn = jest.fn()
        .mockRejectedValueOnce(networkError)
        .mockResolvedValue('success');
      
      const result = await RetryUtil.withRetry(mockFn);
      
      expect(result.success).toBe(true);
      expect(result.attempts).toBe(2);
    });

    it('should identify timeout errors as retryable', async () => {
      const timeoutError = new Error('Request timeout');
      const mockFn = jest.fn()
        .mockRejectedValueOnce(timeoutError)
        .mockResolvedValue('success');
      
      const result = await RetryUtil.withRetry(mockFn);
      
      expect(result.success).toBe(true);
      expect(result.attempts).toBe(2);
    });

    it('should not retry on authentication errors', async () => {
      const authError = new ProviderError('Invalid API key', 'openai', 'AUTH_ERROR', 401);
      const mockFn = jest.fn().mockRejectedValue(authError);
      
      const result = await RetryUtil.withRetry(mockFn);
      
      expect(result.success).toBe(false);
      expect(result.attempts).toBe(1);
    });
  });

  describe('delay calculation', () => {
    it('should calculate correct exponential backoff', () => {
      // Access private method for testing
      const calculateDelay = (RetryUtil as any).calculateDelay;
      
      const config = { baseDelay: 1000, maxDelay: 30000, jitter: false };
      
      expect(calculateDelay(1, config)).toBe(1000);  // 1000 * 2^0
      expect(calculateDelay(2, config)).toBe(2000);  // 1000 * 2^1
      expect(calculateDelay(3, config)).toBe(4000);  // 1000 * 2^2
      expect(calculateDelay(4, config)).toBe(8000);  // 1000 * 2^3
    });

    it('should cap delay at maxDelay', () => {
      const calculateDelay = (RetryUtil as any).calculateDelay;
      
      const config = { baseDelay: 1000, maxDelay: 5000, jitter: false };
      
      expect(calculateDelay(10, config)).toBe(5000); // Should be capped at maxDelay
    });
  });
});
