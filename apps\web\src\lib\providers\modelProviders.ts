import { z } from 'zod';

// Model Provider Types
export enum ModelProvider {
  OPENAI = 'openai',
  ANTHROPIC = 'anthropic',
  GOOGLE = 'google',
}

// Model Configuration Schema
export const modelConfigSchema = z.object({
  provider: z.nativeEnum(ModelProvider),
  model: z.string(),
  apiKey: z.string(),
  temperature: z.number().min(0).max(2).default(0.7),
  maxTokens: z.number().min(1).max(8000).default(4000),
  topP: z.number().min(0).max(1).optional(),
  frequencyPenalty: z.number().min(-2).max(2).optional(),
  presencePenalty: z.number().min(-2).max(2).optional(),
});

export type ModelConfig = z.infer<typeof modelConfigSchema>;

// API Request/Response Types
export interface ModelRequest {
  prompt: string;
  systemPrompt?: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stop?: string[];
}

export interface ModelResponse {
  content: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  finishReason: 'stop' | 'length' | 'content_filter' | 'function_call';
  cost?: number;
}

// Error Types
export class ModelProviderError extends Error {
  constructor(
    message: string,
    public provider: ModelProvider,
    public statusCode?: number,
    public originalError?: any
  ) {
    super(message);
    this.name = 'ModelProviderError';
  }
}

// Abstract Model Provider Base Class
export abstract class BaseModelProvider {
  protected config: ModelConfig;

  constructor(config: ModelConfig) {
    this.config = config;
  }

  abstract generateCompletion(request: ModelRequest): Promise<ModelResponse>;
  
  protected abstract calculateCost(usage: ModelResponse['usage']): number;

  // Validate API key format
  protected validateApiKey(): boolean {
    return this.config.apiKey.length > 0;
  }

  // Get provider-specific headers
  protected abstract getHeaders(): Record<string, string>;

  // Transform request to provider-specific format
  protected abstract transformRequest(request: ModelRequest): any;

  // Transform provider response to standard format
  protected abstract transformResponse(response: any): ModelResponse;
}

// OpenAI Provider
export class OpenAIProvider extends BaseModelProvider {
  private static readonly BASE_URL = 'https://api.openai.com/v1';
  
  // Model pricing per 1K tokens (input/output)
  private static readonly PRICING = {
    'gpt-4-turbo': { input: 0.01, output: 0.03 },
    'gpt-4': { input: 0.03, output: 0.06 },
    'gpt-3.5-turbo': { input: 0.002, output: 0.002 },
  };

  constructor(config: ModelConfig) {
    super(config);
    if (!this.validateApiKey()) {
      throw new ModelProviderError('Invalid OpenAI API key', ModelProvider.OPENAI);
    }
  }

  protected validateApiKey(): boolean {
    return this.config.apiKey.startsWith('sk-');
  }

  protected getHeaders(): Record<string, string> {
    return {
      'Authorization': `Bearer ${this.config.apiKey}`,
      'Content-Type': 'application/json',
    };
  }

  protected transformRequest(request: ModelRequest): any {
    const messages = [];
    
    if (request.systemPrompt) {
      messages.push({ role: 'system', content: request.systemPrompt });
    }
    
    messages.push({ role: 'user', content: request.prompt });

    return {
      model: this.config.model,
      messages,
      temperature: request.temperature ?? this.config.temperature,
      max_tokens: request.maxTokens ?? this.config.maxTokens,
      top_p: request.topP ?? this.config.topP,
      frequency_penalty: request.frequencyPenalty ?? this.config.frequencyPenalty,
      presence_penalty: request.presencePenalty ?? this.config.presencePenalty,
      stop: request.stop,
    };
  }

  protected transformResponse(response: any): ModelResponse {
    const usage = response.usage;
    const choice = response.choices[0];
    
    return {
      content: choice.message.content,
      usage: {
        promptTokens: usage.prompt_tokens,
        completionTokens: usage.completion_tokens,
        totalTokens: usage.total_tokens,
      },
      model: response.model,
      finishReason: choice.finish_reason,
      cost: this.calculateCost(usage),
    };
  }

  protected calculateCost(usage: ModelResponse['usage']): number {
    const pricing = OpenAIProvider.PRICING[this.config.model as keyof typeof OpenAIProvider.PRICING];
    if (!pricing) return 0;
    
    const inputCost = (usage.promptTokens / 1000) * pricing.input;
    const outputCost = (usage.completionTokens / 1000) * pricing.output;
    
    return inputCost + outputCost;
  }

  async generateCompletion(request: ModelRequest): Promise<ModelResponse> {
    try {
      const response = await fetch(`${OpenAIProvider.BASE_URL}/chat/completions`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify(this.transformRequest(request)),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new ModelProviderError(
          error.error?.message || 'OpenAI API error',
          ModelProvider.OPENAI,
          response.status,
          error
        );
      }

      const data = await response.json();
      return this.transformResponse(data);
    } catch (error) {
      if (error instanceof ModelProviderError) {
        throw error;
      }
      throw new ModelProviderError(
        `OpenAI request failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        ModelProvider.OPENAI,
        undefined,
        error
      );
    }
  }
}

// Anthropic Provider
export class AnthropicProvider extends BaseModelProvider {
  private static readonly BASE_URL = 'https://api.anthropic.com/v1';
  
  // Model pricing per 1K tokens (input/output)
  private static readonly PRICING = {
    'claude-3-opus-20240229': { input: 0.015, output: 0.075 },
    'claude-3-sonnet-20240229': { input: 0.003, output: 0.015 },
    'claude-3-haiku-20240307': { input: 0.00025, output: 0.00125 },
  };

  constructor(config: ModelConfig) {
    super(config);
    if (!this.validateApiKey()) {
      throw new ModelProviderError('Invalid Anthropic API key', ModelProvider.ANTHROPIC);
    }
  }

  protected validateApiKey(): boolean {
    return this.config.apiKey.startsWith('sk-ant-');
  }

  protected getHeaders(): Record<string, string> {
    return {
      'x-api-key': this.config.apiKey,
      'Content-Type': 'application/json',
      'anthropic-version': '2023-06-01',
    };
  }

  protected transformRequest(request: ModelRequest): any {
    let prompt = request.prompt;
    
    if (request.systemPrompt) {
      prompt = `${request.systemPrompt}\n\nHuman: ${request.prompt}\n\nAssistant:`;
    } else {
      prompt = `Human: ${request.prompt}\n\nAssistant:`;
    }

    return {
      model: this.config.model,
      prompt,
      temperature: request.temperature ?? this.config.temperature,
      max_tokens: request.maxTokens ?? this.config.maxTokens,
      top_p: request.topP ?? this.config.topP,
      stop_sequences: request.stop,
    };
  }

  protected transformResponse(response: any): ModelResponse {
    // Anthropic doesn't provide detailed usage in the same format
    // This is a simplified implementation
    const estimatedTokens = Math.ceil(response.completion.length / 4);
    
    return {
      content: response.completion,
      usage: {
        promptTokens: estimatedTokens,
        completionTokens: estimatedTokens,
        totalTokens: estimatedTokens * 2,
      },
      model: response.model,
      finishReason: response.stop_reason === 'stop_sequence' ? 'stop' : 'length',
      cost: this.calculateCost({
        promptTokens: estimatedTokens,
        completionTokens: estimatedTokens,
        totalTokens: estimatedTokens * 2,
      }),
    };
  }

  protected calculateCost(usage: ModelResponse['usage']): number {
    const pricing = AnthropicProvider.PRICING[this.config.model as keyof typeof AnthropicProvider.PRICING];
    if (!pricing) return 0;
    
    const inputCost = (usage.promptTokens / 1000) * pricing.input;
    const outputCost = (usage.completionTokens / 1000) * pricing.output;
    
    return inputCost + outputCost;
  }

  async generateCompletion(request: ModelRequest): Promise<ModelResponse> {
    try {
      const response = await fetch(`${AnthropicProvider.BASE_URL}/complete`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify(this.transformRequest(request)),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new ModelProviderError(
          error.error?.message || 'Anthropic API error',
          ModelProvider.ANTHROPIC,
          response.status,
          error
        );
      }

      const data = await response.json();
      return this.transformResponse(data);
    } catch (error) {
      if (error instanceof ModelProviderError) {
        throw error;
      }
      throw new ModelProviderError(
        `Anthropic request failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        ModelProvider.ANTHROPIC,
        undefined,
        error
      );
    }
  }
}

// Google Provider (Vertex AI)
export class GoogleProvider extends BaseModelProvider {
  private static readonly BASE_URL = 'https://us-central1-aiplatform.googleapis.com/v1';
  
  constructor(config: ModelConfig) {
    super(config);
  }

  protected getHeaders(): Record<string, string> {
    return {
      'Authorization': `Bearer ${this.config.apiKey}`,
      'Content-Type': 'application/json',
    };
  }

  protected transformRequest(request: ModelRequest): any {
    // Simplified Google Vertex AI implementation
    return {
      instances: [{
        prompt: request.prompt,
        temperature: request.temperature ?? this.config.temperature,
        maxOutputTokens: request.maxTokens ?? this.config.maxTokens,
        topP: request.topP ?? this.config.topP,
      }],
    };
  }

  protected transformResponse(response: any): ModelResponse {
    const prediction = response.predictions[0];
    const estimatedTokens = Math.ceil(prediction.content.length / 4);
    
    return {
      content: prediction.content,
      usage: {
        promptTokens: estimatedTokens,
        completionTokens: estimatedTokens,
        totalTokens: estimatedTokens * 2,
      },
      model: this.config.model,
      finishReason: 'stop',
      cost: this.calculateCost({
        promptTokens: estimatedTokens,
        completionTokens: estimatedTokens,
        totalTokens: estimatedTokens * 2,
      }),
    };
  }

  protected calculateCost(usage: ModelResponse['usage']): number {
    // Simplified pricing calculation for Google
    return (usage.totalTokens / 1000) * 0.002;
  }

  async generateCompletion(request: ModelRequest): Promise<ModelResponse> {
    // Simplified implementation - would need proper Google Cloud setup
    throw new ModelProviderError('Google Provider not fully implemented', ModelProvider.GOOGLE);
  }
}

// Model Provider Factory
export class ModelProviderFactory {
  static createProvider(config: ModelConfig): BaseModelProvider {
    switch (config.provider) {
      case ModelProvider.OPENAI:
        return new OpenAIProvider(config);
      case ModelProvider.ANTHROPIC:
        return new AnthropicProvider(config);
      case ModelProvider.GOOGLE:
        return new GoogleProvider(config);
      default:
        throw new Error(`Unsupported provider: ${config.provider}`);
    }
  }

  static getAvailableModels(provider: ModelProvider): string[] {
    switch (provider) {
      case ModelProvider.OPENAI:
        return ['gpt-4-turbo', 'gpt-4', 'gpt-3.5-turbo'];
      case ModelProvider.ANTHROPIC:
        return ['claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307'];
      case ModelProvider.GOOGLE:
        return ['text-bison', 'chat-bison'];
      default:
        return [];
    }
  }

  static getModelDisplayName(model: string): string {
    const displayNames: Record<string, string> = {
      'gpt-4-turbo': 'GPT-4 Turbo',
      'gpt-4': 'GPT-4',
      'gpt-3.5-turbo': 'GPT-3.5 Turbo',
      'claude-3-opus-20240229': 'Claude 3 Opus',
      'claude-3-sonnet-20240229': 'Claude 3 Sonnet',
      'claude-3-haiku-20240307': 'Claude 3 Haiku',
      'text-bison': 'Text Bison',
      'chat-bison': 'Chat Bison',
    };
    
    return displayNames[model] || model;
  }
}
