name: Quality Gates

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  accessibility-audit:
    name: Accessibility Audit
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build application
      run: npm run build --workspace=apps/web
    
    - name: Start application
      run: |
        npm run preview --workspace=apps/web &
        sleep 10
    
    - name: Run accessibility audit
      run: |
        npx axe-core-cli http://localhost:4173 \
          --exit-on-violations \
          --threshold 97 \
          --reporter json \
          --output-file accessibility-report.json
    
    - name: Upload accessibility report
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: accessibility-report
        path: accessibility-report.json

  bundle-budget:
    name: Bundle Budget Check
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build and analyze bundle
      run: |
        npm run build --workspace=apps/web
        npx vite-bundle-analyzer dist --json > bundle-analysis.json
    
    - name: Check bundle size
      run: |
        node -e "
          const fs = require('fs');
          const analysis = JSON.parse(fs.readFileSync('bundle-analysis.json', 'utf8'));
          const totalSize = analysis.assets.reduce((sum, asset) => sum + asset.gzipSize, 0);
          const budgetKB = 900;
          const actualKB = Math.round(totalSize / 1024);
          
          console.log(\`Bundle size: \${actualKB}KB (Budget: \${budgetKB}KB)\`);
          
          if (actualKB > budgetKB) {
            console.error(\`❌ Bundle size \${actualKB}KB exceeds budget of \${budgetKB}KB\`);
            process.exit(1);
          } else {
            console.log(\`✅ Bundle size \${actualKB}KB is within budget\`);
          }
        "
    
    - name: Upload bundle analysis
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: bundle-analysis
        path: bundle-analysis.json

  test-coverage:
    name: Test Coverage
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests with coverage
      run: |
        npm run test:coverage --workspace=apps/api
        npm run test:coverage --workspace=packages/agents
        npm run test:coverage --workspace=apps/web
    
    - name: Check coverage threshold
      run: |
        node -e "
          const fs = require('fs');
          const coverageFiles = [
            'apps/api/coverage/coverage-summary.json',
            'packages/agents/coverage/coverage-summary.json',
            'apps/web/coverage/coverage-summary.json'
          ];
          
          let totalCoverage = 0;
          let validFiles = 0;
          
          coverageFiles.forEach(file => {
            if (fs.existsSync(file)) {
              const coverage = JSON.parse(fs.readFileSync(file, 'utf8'));
              const lineCoverage = coverage.total.lines.pct;
              console.log(\`\${file}: \${lineCoverage}% line coverage\`);
              totalCoverage += lineCoverage;
              validFiles++;
            }
          });
          
          const avgCoverage = validFiles > 0 ? totalCoverage / validFiles : 0;
          const threshold = 90;
          
          console.log(\`Average coverage: \${avgCoverage.toFixed(2)}%\`);
          
          if (avgCoverage < threshold) {
            console.error(\`❌ Coverage \${avgCoverage.toFixed(2)}% below threshold of \${threshold}%\`);
            process.exit(1);
          } else {
            console.log(\`✅ Coverage \${avgCoverage.toFixed(2)}% meets threshold\`);
          }
        "
    
    - name: Upload coverage reports
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: coverage-reports
        path: |
          apps/api/coverage/
          packages/agents/coverage/
          apps/web/coverage/

  lint-and-format:
    name: Lint and Format Check
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run ESLint
      run: npm run lint --workspace=apps/web
    
    - name: Check Prettier formatting
      run: npx prettier --check "src/**/*.{ts,tsx,js,jsx,json,css,md}"

  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run security audit
      run: npm audit --audit-level moderate
    
    - name: Run dependency vulnerability check
      run: npx audit-ci --moderate

  quality-gate-summary:
    name: Quality Gate Summary
    runs-on: ubuntu-latest
    needs: [accessibility-audit, bundle-budget, test-coverage, lint-and-format, security-audit]
    if: always()
    
    steps:
    - name: Check all gates passed
      run: |
        echo "Quality Gate Results:"
        echo "- Accessibility: ${{ needs.accessibility-audit.result }}"
        echo "- Bundle Budget: ${{ needs.bundle-budget.result }}"
        echo "- Test Coverage: ${{ needs.test-coverage.result }}"
        echo "- Lint & Format: ${{ needs.lint-and-format.result }}"
        echo "- Security Audit: ${{ needs.security-audit.result }}"
        
        if [[ "${{ needs.accessibility-audit.result }}" != "success" ]] || \
           [[ "${{ needs.bundle-budget.result }}" != "success" ]] || \
           [[ "${{ needs.test-coverage.result }}" != "success" ]] || \
           [[ "${{ needs.lint-and-format.result }}" != "success" ]] || \
           [[ "${{ needs.security-audit.result }}" != "success" ]]; then
          echo "❌ One or more quality gates failed"
          exit 1
        else
          echo "✅ All quality gates passed"
        fi
