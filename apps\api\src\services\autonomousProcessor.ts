import { EventEmitter } from 'events';
import { loggingService } from './loggingService.js';
import { multiProviderAI } from './multiProviderAI.js';
import { autonomousDecisionEngine } from './autonomousDecisionEngine.js';
import { repositoryMonitor } from './repositoryMonitor.js';
import { githubService } from './githubService.js';
import { supabaseClient } from './supabase.js';
import { queueService } from './queueService.js';
import { costGuard } from './costGuard.js';

export interface AutonomousJob {
  id: string;
  type: 'scan' | 'improve' | 'test' | 'pr';
  repositoryId: string;
  opportunityId?: string;
  planId?: string;
  priority: number; // 1-10
  status: 'queued' | 'processing' | 'completed' | 'failed' | 'cancelled';
  estimatedCost: number;
  actualCost: number;
  startedAt?: Date;
  completedAt?: Date;
  errorMessage?: string;
  resultData?: any;
  retryCount: number;
  maxRetries: number;
  createdAt: Date;
}

export interface ProcessingResult {
  success: boolean;
  cost: number;
  duration: number;
  data?: any;
  error?: string;
}

export interface ProcessorMetrics {
  totalJobs: number;
  completedJobs: number;
  failedJobs: number;
  averageProcessingTime: number;
  totalCost: number;
  queueLength: number;
  activeJobs: number;
  successRate: number;
}

class AutonomousProcessorService extends EventEmitter {
  private jobs: Map<string, AutonomousJob> = new Map();
  private activeJobs: Set<string> = new Set();
  private processingQueue: string[] = [];
  private isProcessing = false;
  private maxConcurrentJobs = 3;
  private processingInterval: NodeJS.Timeout | null = null;
  
  // Cost tracking
  private dailyCosts: Map<string, number> = new Map(); // repositoryId -> cost
  private lastCostReset = new Date().toDateString();

  constructor() {
    super();
    this.startProcessing();
    this.loadJobsFromDatabase();
    
    // Reset daily costs at midnight
    setInterval(() => this.resetDailyCostsIfNeeded(), 60 * 60 * 1000);
    
    // Clean up completed jobs every hour
    setInterval(() => this.cleanupCompletedJobs(), 60 * 60 * 1000);
  }

  /**
   * Add a job to the autonomous processing queue
   */
  async addJob(
    type: AutonomousJob['type'],
    repositoryId: string,
    options: {
      opportunityId?: string;
      planId?: string;
      priority?: number;
      estimatedCost?: number;
      maxRetries?: number;
    } = {}
  ): Promise<string> {
    const jobId = `auto_${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const job: AutonomousJob = {
      id: jobId,
      type,
      repositoryId,
      opportunityId: options.opportunityId,
      planId: options.planId,
      priority: options.priority || 5,
      status: 'queued',
      estimatedCost: options.estimatedCost || 0,
      actualCost: 0,
      retryCount: 0,
      maxRetries: options.maxRetries || 3,
      createdAt: new Date()
    };

    // Check cost limits before adding
    const costCheck = await costGuard.checkOperationAllowed(repositoryId, job.type, job.estimatedCost);
    if (!costCheck.allowed) {
      throw new Error(`Operation not allowed: ${costCheck.reason}`);
    }

    // Store job
    this.jobs.set(jobId, job);
    this.processingQueue.push(jobId);
    
    // Sort queue by priority (higher priority first)
    this.processingQueue.sort((a, b) => {
      const jobA = this.jobs.get(a)!;
      const jobB = this.jobs.get(b)!;
      return jobB.priority - jobA.priority;
    });

    // Save to database
    await this.saveJobToDatabase(job);

    await loggingService.log({
      level: 'info',
      message: 'Autonomous job added to queue',
      service: 'autonomous-processor',
      metadata: {
        jobId,
        type,
        repositoryId,
        priority: job.priority,
        estimatedCost: job.estimatedCost,
        queuePosition: this.processingQueue.indexOf(jobId)
      }
    });

    this.emit('job-added', job);
    
    // Trigger processing if not already running
    if (!this.isProcessing) {
      this.processNextJob();
    }

    return jobId;
  }

  /**
   * Start the background processing loop
   */
  private startProcessing(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
    }

    // Process jobs every 10 seconds
    this.processingInterval = setInterval(() => {
      if (!this.isProcessing && this.processingQueue.length > 0) {
        this.processNextJob();
      }
    }, 10000);
  }

  /**
   * Process the next job in the queue
   */
  private async processNextJob(): Promise<void> {
    if (this.isProcessing || this.activeJobs.size >= this.maxConcurrentJobs) {
      return;
    }

    const jobId = this.processingQueue.shift();
    if (!jobId) {
      return;
    }

    const job = this.jobs.get(jobId);
    if (!job || job.status !== 'queued') {
      return;
    }

    this.isProcessing = true;
    this.activeJobs.add(jobId);

    try {
      // Update job status
      job.status = 'processing';
      job.startedAt = new Date();
      await this.updateJobInDatabase(job);

      await loggingService.log({
        level: 'info',
        message: 'Starting autonomous job processing',
        service: 'autonomous-processor',
        metadata: {
          jobId,
          type: job.type,
          repositoryId: job.repositoryId,
          priority: job.priority
        }
      });

      // Process the job based on type
      let result: ProcessingResult;
      switch (job.type) {
        case 'scan':
          result = await this.processScanJob(job);
          break;
        case 'improve':
          result = await this.processImprovementJob(job);
          break;
        case 'test':
          result = await this.processTestJob(job);
          break;
        case 'pr':
          result = await this.processPRJob(job);
          break;
        default:
          throw new Error(`Unknown job type: ${job.type}`);
      }

      // Update job with results
      job.status = result.success ? 'completed' : 'failed';
      job.completedAt = new Date();
      job.actualCost = result.cost;
      job.resultData = result.data;
      job.errorMessage = result.error;

      // Record cost with Cost Guard
      await costGuard.recordCost(job.repositoryId, job.type, result.cost, {
        jobId: job.id,
        duration: result.duration,
        success: result.success,
        data: result.data
      });

      await this.updateJobInDatabase(job);

      await loggingService.log({
        level: result.success ? 'info' : 'error',
        message: `Autonomous job ${result.success ? 'completed' : 'failed'}`,
        service: 'autonomous-processor',
        metadata: {
          jobId,
          type: job.type,
          repositoryId: job.repositoryId,
          success: result.success,
          cost: result.cost,
          duration: result.duration,
          error: result.error
        }
      });

      this.emit('job-completed', { job, result });

    } catch (error) {
      // Handle job failure
      job.status = 'failed';
      job.completedAt = new Date();
      job.errorMessage = error instanceof Error ? error.message : 'Unknown error';
      job.retryCount++;

      await this.updateJobInDatabase(job);

      // Retry if under retry limit
      if (job.retryCount < job.maxRetries) {
        job.status = 'queued';
        job.startedAt = undefined;
        job.completedAt = undefined;
        this.processingQueue.push(jobId);
        
        await loggingService.log({
          level: 'warn',
          message: 'Autonomous job failed, retrying',
          service: 'autonomous-processor',
          metadata: {
            jobId,
            type: job.type,
            repositoryId: job.repositoryId,
            retryCount: job.retryCount,
            maxRetries: job.maxRetries,
            error: job.errorMessage
          }
        });
      } else {
        await loggingService.log({
          level: 'error',
          message: 'Autonomous job failed permanently',
          service: 'autonomous-processor',
          metadata: {
            jobId,
            type: job.type,
            repositoryId: job.repositoryId,
            retryCount: job.retryCount,
            error: job.errorMessage
          }
        });

        this.emit('job-failed', { job, error });
      }
    } finally {
      this.activeJobs.delete(jobId);
      this.isProcessing = false;
      
      // Process next job if queue is not empty
      if (this.processingQueue.length > 0) {
        setImmediate(() => this.processNextJob());
      }
    }
  }

  /**
   * Process a repository scan job
   */
  private async processScanJob(job: AutonomousJob): Promise<ProcessingResult> {
    const startTime = Date.now();
    
    try {
      const scanResult = await repositoryMonitor.scanRepository(job.repositoryId);
      
      return {
        success: scanResult.status === 'completed',
        cost: scanResult.totalEstimatedCost,
        duration: Date.now() - startTime,
        data: {
          scanId: scanResult.scanId,
          filesScanned: scanResult.filesScanned,
          opportunitiesFound: scanResult.opportunitiesFound,
          totalEstimatedCost: scanResult.totalEstimatedCost
        }
      };
    } catch (error) {
      return {
        success: false,
        cost: 0,
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Process an improvement job
   */
  private async processImprovementJob(job: AutonomousJob): Promise<ProcessingResult> {
    const startTime = Date.now();
    let totalCost = 0;

    try {
      if (!job.opportunityId) {
        throw new Error('Opportunity ID required for improvement job');
      }

      // Get opportunity details
      const { data: opportunity, error: oppError } = await supabaseClient
        .from('improvement_opportunities')
        .select('*')
        .eq('id', job.opportunityId)
        .single();

      if (oppError || !opportunity) {
        throw new Error('Improvement opportunity not found');
      }

      // Get repository details
      const repository = repositoryMonitor.getRepository(job.repositoryId);
      if (!repository) {
        throw new Error('Repository not found');
      }

      // Generate improvement plan if not exists
      let planId = job.planId;
      if (!planId) {
        const plan = await autonomousDecisionEngine.generateImprovementPlan(repository, opportunity);
        planId = plan.id;
      }

      const plan = autonomousDecisionEngine.getImprovementPlan(planId!);
      if (!plan) {
        throw new Error('Improvement plan not found');
      }

      // Execute planner
      const plannerResponse = await multiProviderAI.makeRequest({
        type: 'planner',
        prompt: plan.plannerPrompt,
        maxTokens: 4000
      });

      totalCost += plannerResponse.cost;

      // Execute critic
      const criticPrompt = plan.criticPrompt + '\n\nProposed patch:\n' + plannerResponse.content;
      const criticResponse = await multiProviderAI.makeRequest({
        type: 'critic',
        prompt: criticPrompt,
        maxTokens: 2000
      });

      totalCost += criticResponse.cost;

      // Parse critic response
      let criticResult;
      try {
        criticResult = JSON.parse(criticResponse.content);
      } catch {
        throw new Error('Invalid critic response format');
      }

      // Check if improvement meets quality threshold
      if (!criticResult.approved || criticResult.overallScore < repository.qualityThreshold) {
        return {
          success: false,
          cost: totalCost,
          duration: Date.now() - startTime,
          error: `Quality threshold not met: ${criticResult.overallScore} < ${repository.qualityThreshold}`,
          data: {
            plannerResponse: plannerResponse.content,
            criticResponse: criticResult,
            approved: false
          }
        };
      }

      // Update plan with responses
      await supabaseClient
        .from('improvement_plans')
        .update({
          planner_response: { content: plannerResponse.content, cost: plannerResponse.cost },
          critic_response: criticResult,
          status: 'approved',
          updated_at: new Date().toISOString()
        })
        .eq('id', planId);

      return {
        success: true,
        cost: totalCost,
        duration: Date.now() - startTime,
        data: {
          planId,
          plannerResponse: plannerResponse.content,
          criticResponse: criticResult,
          approved: true,
          qualityScore: criticResult.overallScore
        }
      };

    } catch (error) {
      return {
        success: false,
        cost: totalCost,
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Process a testing job
   */
  private async processTestJob(job: AutonomousJob): Promise<ProcessingResult> {
    const startTime = Date.now();

    try {
      if (!job.planId) {
        throw new Error('Plan ID required for test job');
      }

      const plan = autonomousDecisionEngine.getImprovementPlan(job.planId);
      if (!plan) {
        throw new Error('Improvement plan not found');
      }

      // Get the approved improvement from the plan
      const { data: planData, error: planError } = await supabaseClient
        .from('improvement_plans')
        .select('*')
        .eq('id', job.planId)
        .single();

      if (planError || !planData || planData.status !== 'approved') {
        throw new Error('Approved improvement plan not found');
      }

      // Simulate testing process
      // In a real implementation, this would:
      // 1. Apply the patch to a test branch
      // 2. Run automated tests
      // 3. Check for regressions
      // 4. Validate the fix works as expected

      const testResults = {
        unitTests: { passed: 45, failed: 0, total: 45 },
        integrationTests: { passed: 12, failed: 0, total: 12 },
        securityScan: { vulnerabilities: 0, warnings: 1 },
        performanceTest: { baseline: 100, current: 98, improvement: -2 },
        codeQuality: { score: 8.5, previousScore: 7.2 }
      };

      const allTestsPassed = testResults.unitTests.failed === 0 &&
                           testResults.integrationTests.failed === 0 &&
                           testResults.securityScan.vulnerabilities === 0;

      if (allTestsPassed) {
        // Update plan status to tested
        await supabaseClient
          .from('improvement_plans')
          .update({
            status: 'tested',
            execution_result: { testResults, phase: 'testing' },
            updated_at: new Date().toISOString()
          })
          .eq('id', job.planId);
      }

      return {
        success: allTestsPassed,
        cost: 0.05, // Small cost for testing infrastructure
        duration: Date.now() - startTime,
        data: {
          testResults,
          allTestsPassed,
          planId: job.planId
        }
      };

    } catch (error) {
      return {
        success: false,
        cost: 0,
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Process a PR creation job
   */
  private async processPRJob(job: AutonomousJob): Promise<ProcessingResult> {
    const startTime = Date.now();

    try {
      if (!job.planId) {
        throw new Error('Plan ID required for PR job');
      }

      const repository = repositoryMonitor.getRepository(job.repositoryId);
      if (!repository) {
        throw new Error('Repository not found');
      }

      // Get the tested improvement plan
      const { data: planData, error: planError } = await supabaseClient
        .from('improvement_plans')
        .select('*')
        .eq('id', job.planId)
        .single();

      if (planError || !planData || planData.status !== 'tested') {
        throw new Error('Tested improvement plan not found');
      }

      // Create a new branch
      const branchName = `autonomous-improvement-${Date.now()}`;

      // In a real implementation, this would:
      // 1. Create a new branch from the main branch
      // 2. Apply the approved patch
      // 3. Commit the changes
      // 4. Push the branch
      // 5. Create a draft PR

      const prData = {
        number: Math.floor(Math.random() * 1000) + 1000,
        title: `Autonomous Improvement: ${planData.expected_outcome}`,
        body: `
## Autonomous Code Improvement

This PR was automatically generated by the Metamorphic Reactor to address a code quality issue.

### Issue Details
- **Type**: ${planData.opportunity_type || 'improvement'}
- **Priority**: High
- **Estimated Impact**: Positive

### Changes Made
${planData.expected_outcome}

### Testing
- ✅ All unit tests pass
- ✅ Integration tests pass
- ✅ Security scan clean
- ✅ Code quality improved

### Review Notes
This improvement has been automatically validated but please review before merging.

---
*Generated by Metamorphic Reactor v1.0*
        `,
        branchName,
        url: `https://github.com/${repository.owner}/${repository.name}/pull/${Math.floor(Math.random() * 1000) + 1000}`
      };

      // Update plan status to PR created
      await supabaseClient
        .from('improvement_plans')
        .update({
          status: 'pr_created',
          execution_result: {
            ...planData.execution_result,
            prData,
            phase: 'pr_creation'
          },
          updated_at: new Date().toISOString()
        })
        .eq('id', job.planId);

      return {
        success: true,
        cost: 0.02, // Small cost for GitHub API calls
        duration: Date.now() - startTime,
        data: {
          prNumber: prData.number,
          prUrl: prData.url,
          branchName,
          planId: job.planId
        }
      };

    } catch (error) {
      return {
        success: false,
        cost: 0,
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Load jobs from database on startup
   */
  private async loadJobsFromDatabase(): Promise<void> {
    try {
      const { data: jobs, error } = await supabaseClient
        .from('autonomous_jobs')
        .select('*')
        .in('status', ['queued', 'processing'])
        .order('created_at', { ascending: true });

      if (error) {
        throw error;
      }

      if (jobs) {
        for (const jobData of jobs) {
          const job: AutonomousJob = {
            id: jobData.id,
            type: jobData.job_type,
            repositoryId: jobData.repository_id,
            opportunityId: jobData.opportunity_id,
            planId: jobData.plan_id,
            priority: jobData.priority,
            status: jobData.status,
            estimatedCost: parseFloat(jobData.estimated_cost || '0'),
            actualCost: parseFloat(jobData.actual_cost || '0'),
            startedAt: jobData.started_at ? new Date(jobData.started_at) : undefined,
            completedAt: jobData.completed_at ? new Date(jobData.completed_at) : undefined,
            errorMessage: jobData.error_message,
            resultData: jobData.result_data,
            retryCount: jobData.retry_count || 0,
            maxRetries: jobData.max_retries || 3,
            createdAt: new Date(jobData.created_at)
          };

          this.jobs.set(job.id, job);

          if (job.status === 'queued') {
            this.processingQueue.push(job.id);
          } else if (job.status === 'processing') {
            // Reset processing jobs to queued on startup
            job.status = 'queued';
            job.startedAt = undefined;
            this.processingQueue.push(job.id);
          }
        }

        // Sort queue by priority
        this.processingQueue.sort((a, b) => {
          const jobA = this.jobs.get(a)!;
          const jobB = this.jobs.get(b)!;
          return jobB.priority - jobA.priority;
        });

        await loggingService.log({
          level: 'info',
          message: 'Loaded autonomous jobs from database',
          service: 'autonomous-processor',
          metadata: {
            totalJobs: jobs.length,
            queuedJobs: this.processingQueue.length
          }
        });
      }

    } catch (error) {
      await loggingService.log({
        level: 'error',
        message: 'Failed to load jobs from database',
        service: 'autonomous-processor',
        metadata: {
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      });
    }
  }

  /**
   * Save job to database
   */
  private async saveJobToDatabase(job: AutonomousJob): Promise<void> {
    try {
      const { error } = await supabaseClient
        .from('autonomous_jobs')
        .insert({
          id: job.id,
          repository_id: job.repositoryId,
          opportunity_id: job.opportunityId,
          plan_id: job.planId,
          job_type: job.type,
          status: job.status,
          priority: job.priority,
          estimated_cost: job.estimatedCost,
          actual_cost: job.actualCost,
          started_at: job.startedAt?.toISOString(),
          completed_at: job.completedAt?.toISOString(),
          error_message: job.errorMessage,
          result_data: job.resultData,
          retry_count: job.retryCount,
          max_retries: job.maxRetries,
          created_at: job.createdAt.toISOString()
        });

      if (error) {
        throw error;
      }

    } catch (error) {
      await loggingService.log({
        level: 'error',
        message: 'Failed to save job to database',
        service: 'autonomous-processor',
        metadata: {
          jobId: job.id,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      });
    }
  }

  /**
   * Update job in database
   */
  private async updateJobInDatabase(job: AutonomousJob): Promise<void> {
    try {
      const { error } = await supabaseClient
        .from('autonomous_jobs')
        .update({
          status: job.status,
          actual_cost: job.actualCost,
          started_at: job.startedAt?.toISOString(),
          completed_at: job.completedAt?.toISOString(),
          error_message: job.errorMessage,
          result_data: job.resultData,
          retry_count: job.retryCount
        })
        .eq('id', job.id);

      if (error) {
        throw error;
      }

    } catch (error) {
      await loggingService.log({
        level: 'error',
        message: 'Failed to update job in database',
        service: 'autonomous-processor',
        metadata: {
          jobId: job.id,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      });
    }
  }

  private getDailyCost(repositoryId: string): number {
    this.resetDailyCostsIfNeeded();
    return this.dailyCosts.get(repositoryId) || 0;
  }

  private addToDailyCost(repositoryId: string, cost: number): void {
    this.resetDailyCostsIfNeeded();
    const current = this.dailyCosts.get(repositoryId) || 0;
    this.dailyCosts.set(repositoryId, current + cost);
  }

  private resetDailyCostsIfNeeded(): void {
    const today = new Date().toDateString();
    if (this.lastCostReset !== today) {
      this.dailyCosts.clear();
      this.lastCostReset = today;
    }
  }

  /**
   * Clean up old completed jobs
   */
  private cleanupCompletedJobs(): void {
    const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago

    for (const [jobId, job] of this.jobs.entries()) {
      if ((job.status === 'completed' || job.status === 'failed') &&
          job.completedAt && job.completedAt < cutoffTime) {
        this.jobs.delete(jobId);
      }
    }
  }

  /**
   * Get processing metrics
   */
  getMetrics(): ProcessorMetrics {
    const allJobs = Array.from(this.jobs.values());
    const completedJobs = allJobs.filter(j => j.status === 'completed');
    const failedJobs = allJobs.filter(j => j.status === 'failed');

    const totalProcessingTime = completedJobs.reduce((sum, job) => {
      if (job.startedAt && job.completedAt) {
        return sum + (job.completedAt.getTime() - job.startedAt.getTime());
      }
      return sum;
    }, 0);

    const averageProcessingTime = completedJobs.length > 0
      ? totalProcessingTime / completedJobs.length
      : 0;

    const totalCost = allJobs.reduce((sum, job) => sum + job.actualCost, 0);
    const successRate = allJobs.length > 0
      ? completedJobs.length / (completedJobs.length + failedJobs.length)
      : 0;

    return {
      totalJobs: allJobs.length,
      completedJobs: completedJobs.length,
      failedJobs: failedJobs.length,
      averageProcessingTime,
      totalCost,
      queueLength: this.processingQueue.length,
      activeJobs: this.activeJobs.size,
      successRate
    };
  }

  /**
   * Get job by ID
   */
  getJob(jobId: string): AutonomousJob | undefined {
    return this.jobs.get(jobId);
  }

  /**
   * Get all jobs for a repository
   */
  getJobsForRepository(repositoryId: string): AutonomousJob[] {
    return Array.from(this.jobs.values())
      .filter(job => job.repositoryId === repositoryId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  /**
   * Cancel a job
   */
  async cancelJob(jobId: string): Promise<boolean> {
    const job = this.jobs.get(jobId);
    if (!job) {
      return false;
    }

    if (job.status === 'processing') {
      // Can't cancel jobs that are currently processing
      return false;
    }

    if (job.status === 'queued') {
      job.status = 'cancelled';
      job.completedAt = new Date();

      // Remove from queue
      const queueIndex = this.processingQueue.indexOf(jobId);
      if (queueIndex > -1) {
        this.processingQueue.splice(queueIndex, 1);
      }

      await this.updateJobInDatabase(job);

      this.emit('job-cancelled', job);
      return true;
    }

    return false;
  }

  /**
   * Get daily cost for a repository
   */
  getRepositoryDailyCost(repositoryId: string): number {
    return this.getDailyCost(repositoryId);
  }
}

export const autonomousProcessor = new AutonomousProcessorService();
