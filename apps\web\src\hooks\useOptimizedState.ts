import * as React from "react"

// Optimized state hook with shallow comparison
export function useOptimizedState<T>(
  initialState: T | (() => T),
  compareFn?: (prev: T, next: T) => boolean
): [T, React.Dispatch<React.SetStateAction<T>>] {
  const [state, setState] = React.useState(initialState)
  
  const optimizedSetState = React.useCallback((value: React.SetStateAction<T>) => {
    setState(prevState => {
      const nextState = typeof value === 'function' 
        ? (value as (prev: T) => T)(prevState)
        : value
      
      // Use custom comparison function or shallow comparison
      const areEqual = compareFn 
        ? compareFn(prevState, nextState)
        : shallowEqual(prevState, nextState)
      
      return areEqual ? prevState : nextState
    })
  }, [compareFn])
  
  return [state, optimizedSetState]
}

// Shallow equality comparison
function shallowEqual<T>(obj1: T, obj2: T): boolean {
  if (obj1 === obj2) return true
  
  if (typeof obj1 !== 'object' || obj1 === null || typeof obj2 !== 'object' || obj2 === null) {
    return false
  }
  
  const keys1 = Object.keys(obj1) as (keyof T)[]
  const keys2 = Object.keys(obj2) as (keyof T)[]
  
  if (keys1.length !== keys2.length) return false
  
  for (const key of keys1) {
    if (obj1[key] !== obj2[key]) return false
  }
  
  return true
}

// Memoized selector hook
export function useSelector<TState, TSelected>(
  selector: (state: TState) => TSelected,
  state: TState,
  equalityFn?: (left: TSelected, right: TSelected) => boolean
): TSelected {
  const selectedState = React.useMemo(() => selector(state), [selector, state])
  
  const [memoizedState, setMemoizedState] = React.useState(selectedState)
  
  React.useEffect(() => {
    const isEqual = equalityFn 
      ? equalityFn(memoizedState, selectedState)
      : shallowEqual(memoizedState, selectedState)
    
    if (!isEqual) {
      setMemoizedState(selectedState)
    }
  }, [selectedState, memoizedState, equalityFn])
  
  return memoizedState
}

// Debounced state hook
export function useDebouncedState<T>(
  initialValue: T,
  delay: number
): [T, T, React.Dispatch<React.SetStateAction<T>>] {
  const [immediateValue, setImmediateValue] = React.useState(initialValue)
  const [debouncedValue, setDebouncedValue] = React.useState(initialValue)
  
  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(immediateValue)
    }, delay)
    
    return () => clearTimeout(handler)
  }, [immediateValue, delay])
  
  return [immediateValue, debouncedValue, setImmediateValue]
}

// Throttled state hook
export function useThrottledState<T>(
  initialValue: T,
  delay: number
): [T, React.Dispatch<React.SetStateAction<T>>] {
  const [value, setValue] = React.useState(initialValue)
  const lastUpdated = React.useRef(Date.now())
  const timeoutRef = React.useRef<NodeJS.Timeout>()
  
  const throttledSetValue = React.useCallback((newValue: React.SetStateAction<T>) => {
    const now = Date.now()
    const timeSinceLastUpdate = now - lastUpdated.current
    
    if (timeSinceLastUpdate >= delay) {
      setValue(newValue)
      lastUpdated.current = now
    } else {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
      
      timeoutRef.current = setTimeout(() => {
        setValue(newValue)
        lastUpdated.current = Date.now()
      }, delay - timeSinceLastUpdate)
    }
  }, [delay])
  
  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])
  
  return [value, throttledSetValue]
}

// Async state hook with loading and error handling
interface AsyncState<T> {
  data: T | null
  loading: boolean
  error: Error | null
}

export function useAsyncState<T>(
  asyncFn: () => Promise<T>,
  deps: React.DependencyList = []
): AsyncState<T> & { refetch: () => Promise<void> } {
  const [state, setState] = React.useState<AsyncState<T>>({
    data: null,
    loading: false,
    error: null
  })
  
  const execute = React.useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }))
    
    try {
      const data = await asyncFn()
      setState({ data, loading: false, error: null })
    } catch (error) {
      setState({ data: null, loading: false, error: error as Error })
    }
  }, [asyncFn])
  
  React.useEffect(() => {
    execute()
  }, deps)
  
  return { ...state, refetch: execute }
}

// Previous value hook
export function usePrevious<T>(value: T): T | undefined {
  const ref = React.useRef<T>()
  
  React.useEffect(() => {
    ref.current = value
  })
  
  return ref.current
}

// Stable callback hook
export function useStableCallback<T extends (...args: any[]) => any>(
  callback: T
): T {
  const callbackRef = React.useRef(callback)
  
  React.useLayoutEffect(() => {
    callbackRef.current = callback
  })
  
  return React.useCallback(
    ((...args) => callbackRef.current(...args)) as T,
    []
  )
}

// Batch state updates hook
export function useBatchedState<T extends Record<string, any>>(
  initialState: T
): [T, (updates: Partial<T>) => void, React.Dispatch<React.SetStateAction<T>>] {
  const [state, setState] = React.useState(initialState)
  
  const batchUpdate = React.useCallback((updates: Partial<T>) => {
    setState(prevState => ({ ...prevState, ...updates }))
  }, [])
  
  return [state, batchUpdate, setState]
}

// Local storage state hook
export function useLocalStorageState<T>(
  key: string,
  defaultValue: T,
  serializer?: {
    serialize: (value: T) => string
    deserialize: (value: string) => T
  }
): [T, React.Dispatch<React.SetStateAction<T>>] {
  const serialize = serializer?.serialize || JSON.stringify
  const deserialize = serializer?.deserialize || JSON.parse
  
  const [state, setState] = React.useState<T>(() => {
    try {
      const item = localStorage.getItem(key)
      return item ? deserialize(item) : defaultValue
    } catch {
      return defaultValue
    }
  })
  
  const setValue = React.useCallback((value: React.SetStateAction<T>) => {
    setState(prevState => {
      const nextState = typeof value === 'function' 
        ? (value as (prev: T) => T)(prevState)
        : value
      
      try {
        localStorage.setItem(key, serialize(nextState))
      } catch (error) {
        console.warn(`Failed to save to localStorage:`, error)
      }
      
      return nextState
    })
  }, [key, serialize])
  
  return [state, setValue]
}

// Optimized list state hook
export function useOptimizedList<T>(
  initialList: T[] = [],
  keyExtractor: (item: T, index: number) => string | number = (_, index) => index
) {
  const [list, setList] = React.useState(initialList)
  const keyMap = React.useMemo(() => {
    const map = new Map<string | number, T>()
    list.forEach((item, index) => {
      map.set(keyExtractor(item, index), item)
    })
    return map
  }, [list, keyExtractor])
  
  const addItem = React.useCallback((item: T) => {
    setList(prev => [...prev, item])
  }, [])
  
  const removeItem = React.useCallback((key: string | number) => {
    setList(prev => prev.filter((item, index) => keyExtractor(item, index) !== key))
  }, [keyExtractor])
  
  const updateItem = React.useCallback((key: string | number, updater: (item: T) => T) => {
    setList(prev => prev.map((item, index) => {
      const itemKey = keyExtractor(item, index)
      return itemKey === key ? updater(item) : item
    }))
  }, [keyExtractor])
  
  const moveItem = React.useCallback((fromIndex: number, toIndex: number) => {
    setList(prev => {
      const newList = [...prev]
      const [removed] = newList.splice(fromIndex, 1)
      newList.splice(toIndex, 0, removed)
      return newList
    })
  }, [])
  
  return {
    list,
    setList,
    keyMap,
    addItem,
    removeItem,
    updateItem,
    moveItem
  }
}

// Performance monitoring hook
export function usePerformanceMonitor(name: string) {
  const startTime = React.useRef<number>()
  
  React.useEffect(() => {
    startTime.current = performance.now()
    
    return () => {
      if (startTime.current) {
        const duration = performance.now() - startTime.current
        console.log(`[Performance] ${name}: ${duration.toFixed(2)}ms`)
      }
    }
  }, [name])
  
  const measure = React.useCallback((label: string) => {
    if (startTime.current) {
      const duration = performance.now() - startTime.current
      console.log(`[Performance] ${name} - ${label}: ${duration.toFixed(2)}ms`)
    }
  }, [name])
  
  return { measure }
}
