import * as React from "react"

// PWA installation and management hook
export function usePWA() {
  const [isInstallable, setIsInstallable] = React.useState(false)
  const [isInstalled, setIsInstalled] = React.useState(false)
  const [isOnline, setIsOnline] = React.useState(navigator.onLine)
  const [updateAvailable, setUpdateAvailable] = React.useState(false)
  const deferredPrompt = React.useRef<any>(null)

  // Check if app is installed
  React.useEffect(() => {
    const checkInstalled = () => {
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches
      const isInWebAppiOS = (window.navigator as any).standalone === true
      setIsInstalled(isStandalone || isInWebAppiOS)
    }

    checkInstalled()
    window.matchMedia('(display-mode: standalone)').addEventListener('change', checkInstalled)

    return () => {
      window.matchMedia('(display-mode: standalone)').removeEventListener('change', checkInstalled)
    }
  }, [])

  // Handle beforeinstallprompt event
  React.useEffect(() => {
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      deferredPrompt.current = e
      setIsInstallable(true)
    }

    const handleAppInstalled = () => {
      setIsInstalled(true)
      setIsInstallable(false)
      deferredPrompt.current = null
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [])

  // Handle online/offline status
  React.useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // Service worker registration and update handling
  React.useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js')
        .then((registration) => {
          console.log('Service Worker registered:', registration)

          // Check for updates
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  setUpdateAvailable(true)
                }
              })
            }
          })
        })
        .catch((error) => {
          console.error('Service Worker registration failed:', error)
        })

      // Listen for messages from service worker
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data && event.data.type === 'UPDATE_AVAILABLE') {
          setUpdateAvailable(true)
        }
      })
    }
  }, [])

  // Install the PWA
  const installPWA = React.useCallback(async () => {
    if (deferredPrompt.current) {
      deferredPrompt.current.prompt()
      const { outcome } = await deferredPrompt.current.userChoice
      
      if (outcome === 'accepted') {
        console.log('PWA installation accepted')
      } else {
        console.log('PWA installation dismissed')
      }
      
      deferredPrompt.current = null
      setIsInstallable(false)
    }
  }, [])

  // Update the service worker
  const updateServiceWorker = React.useCallback(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistration().then((registration) => {
        if (registration && registration.waiting) {
          registration.waiting.postMessage({ type: 'SKIP_WAITING' })
          window.location.reload()
        }
      })
    }
  }, [])

  // Request notification permission
  const requestNotificationPermission = React.useCallback(async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission()
      return permission === 'granted'
    }
    return false
  }, [])

  // Show notification
  const showNotification = React.useCallback((title: string, options?: NotificationOptions) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      return new Notification(title, {
        icon: '/icons/icon-192x192.png',
        badge: '/icons/badge-72x72.png',
        ...options
      })
    }
    return null
  }, [])

  return {
    isInstallable,
    isInstalled,
    isOnline,
    updateAvailable,
    installPWA,
    updateServiceWorker,
    requestNotificationPermission,
    showNotification
  }
}

// Offline storage hook
export function useOfflineStorage() {
  const [isSupported, setIsSupported] = React.useState(false)

  React.useEffect(() => {
    setIsSupported('indexedDB' in window)
  }, [])

  const saveOfflineData = React.useCallback(async (key: string, data: any) => {
    if (!isSupported) return false

    try {
      const request = indexedDB.open('MetamorphicReactor', 1)
      
      request.onupgradeneeded = () => {
        const db = request.result
        if (!db.objectStoreNames.contains('offlineData')) {
          db.createObjectStore('offlineData', { keyPath: 'key' })
        }
      }

      return new Promise<boolean>((resolve, reject) => {
        request.onsuccess = () => {
          const db = request.result
          const transaction = db.transaction(['offlineData'], 'readwrite')
          const store = transaction.objectStore('offlineData')
          
          store.put({ key, data, timestamp: Date.now() })
          
          transaction.oncomplete = () => resolve(true)
          transaction.onerror = () => reject(false)
        }
        
        request.onerror = () => reject(false)
      })
    } catch (error) {
      console.error('Failed to save offline data:', error)
      return false
    }
  }, [isSupported])

  const getOfflineData = React.useCallback(async (key: string) => {
    if (!isSupported) return null

    try {
      const request = indexedDB.open('MetamorphicReactor', 1)
      
      return new Promise<any>((resolve, reject) => {
        request.onsuccess = () => {
          const db = request.result
          const transaction = db.transaction(['offlineData'], 'readonly')
          const store = transaction.objectStore('offlineData')
          const getRequest = store.get(key)
          
          getRequest.onsuccess = () => {
            resolve(getRequest.result?.data || null)
          }
          
          getRequest.onerror = () => reject(null)
        }
        
        request.onerror = () => reject(null)
      })
    } catch (error) {
      console.error('Failed to get offline data:', error)
      return null
    }
  }, [isSupported])

  const removeOfflineData = React.useCallback(async (key: string) => {
    if (!isSupported) return false

    try {
      const request = indexedDB.open('MetamorphicReactor', 1)
      
      return new Promise<boolean>((resolve, reject) => {
        request.onsuccess = () => {
          const db = request.result
          const transaction = db.transaction(['offlineData'], 'readwrite')
          const store = transaction.objectStore('offlineData')
          
          store.delete(key)
          
          transaction.oncomplete = () => resolve(true)
          transaction.onerror = () => reject(false)
        }
        
        request.onerror = () => reject(false)
      })
    } catch (error) {
      console.error('Failed to remove offline data:', error)
      return false
    }
  }, [isSupported])

  return {
    isSupported,
    saveOfflineData,
    getOfflineData,
    removeOfflineData
  }
}

// Network status hook
export function useNetworkStatus() {
  const [isOnline, setIsOnline] = React.useState(navigator.onLine)
  const [connectionType, setConnectionType] = React.useState<string>('unknown')

  React.useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Get connection info if available
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection
    if (connection) {
      setConnectionType(connection.effectiveType || 'unknown')
      
      const handleConnectionChange = () => {
        setConnectionType(connection.effectiveType || 'unknown')
      }
      
      connection.addEventListener('change', handleConnectionChange)
      
      return () => {
        window.removeEventListener('online', handleOnline)
        window.removeEventListener('offline', handleOffline)
        connection.removeEventListener('change', handleConnectionChange)
      }
    }

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  return {
    isOnline,
    connectionType,
    isSlowConnection: connectionType === 'slow-2g' || connectionType === '2g'
  }
}
