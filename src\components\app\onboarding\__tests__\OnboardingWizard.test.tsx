import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { OnboardingWizard } from '../OnboardingWizard';

// Mock fetch
global.fetch = jest.fn();

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn()
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
});

const mockOnboardingProgress = {
  completed: false,
  current_step: 1,
  steps: {
    api_keys: false,
    github_connection: false,
    sample_loop: false
  }
};

describe('OnboardingWizard', () => {
  beforeEach(() => {
    (fetch as jest.Mock).mockClear();
    mockLocalStorage.getItem.mockClear();
    mockLocalStorage.setItem.mockClear();
    mockLocalStorage.removeItem.mockClear();
  });

  it('renders the onboarding wizard when visible', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        needsOnboarding: true,
        currentStep: 1,
        progress: mockOnboardingProgress
      })
    });

    render(<OnboardingWizard autoStart={true} />);

    await waitFor(() => {
      expect(screen.getByText('Welcome to Metamorphic Reactor')).toBeInTheDocument();
      expect(screen.getByText('Let\'s get you set up in just a few steps')).toBeInTheDocument();
    });
  });

  it('does not render when onboarding is not needed', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        needsOnboarding: false,
        currentStep: 4,
        progress: {
          ...mockOnboardingProgress,
          completed: true
        }
      })
    });

    const { container } = render(<OnboardingWizard autoStart={true} />);

    await waitFor(() => {
      expect(container.firstChild).toBeNull();
    });
  });

  it('displays progress correctly', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        needsOnboarding: true,
        currentStep: 2,
        progress: {
          ...mockOnboardingProgress,
          current_step: 2,
          steps: {
            api_keys: true,
            github_connection: false,
            sample_loop: false
          }
        }
      })
    });

    render(<OnboardingWizard autoStart={true} />);

    await waitFor(() => {
      expect(screen.getByText('Step 2 of 3')).toBeInTheDocument();
      expect(screen.getByText('1/3 completed')).toBeInTheDocument();
    });
  });

  it('shows step indicators with correct states', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        needsOnboarding: true,
        currentStep: 2,
        progress: {
          ...mockOnboardingProgress,
          current_step: 2,
          steps: {
            api_keys: true,
            github_connection: false,
            sample_loop: false
          }
        }
      })
    });

    render(<OnboardingWizard autoStart={true} />);

    await waitFor(() => {
      expect(screen.getByText('API Keys Setup')).toBeInTheDocument();
      expect(screen.getByText('GitHub Integration')).toBeInTheDocument();
      expect(screen.getByText('Test Run')).toBeInTheDocument();
      expect(screen.getByText('Optional')).toBeInTheDocument(); // GitHub is optional
    });
  });

  it('calls onComplete when onboarding is finished', async () => {
    const mockOnComplete = jest.fn();
    
    (fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          needsOnboarding: true,
          currentStep: 3,
          progress: {
            ...mockOnboardingProgress,
            current_step: 3,
            steps: {
              api_keys: true,
              github_connection: true,
              sample_loop: false
            }
          }
        })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          progress: {
            ...mockOnboardingProgress,
            completed: true,
            steps: {
              api_keys: true,
              github_connection: true,
              sample_loop: true
            }
          }
        })
      });

    render(<OnboardingWizard onComplete={mockOnComplete} autoStart={true} />);

    await waitFor(() => {
      expect(screen.getByText('Test Run')).toBeInTheDocument();
    });

    // Simulate completing the last step
    // This would normally be triggered by the SampleLoop component
    // For testing, we'll simulate the completion
    expect(mockOnComplete).not.toHaveBeenCalled();
  });

  it('calls onSkip when skip button is clicked', async () => {
    const mockOnSkip = jest.fn();
    
    (fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          needsOnboarding: true,
          currentStep: 1,
          progress: mockOnboardingProgress
        })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          message: 'Onboarding skipped'
        })
      });

    render(<OnboardingWizard onSkip={mockOnSkip} autoStart={true} />);

    await waitFor(() => {
      expect(screen.getByText('Welcome to Metamorphic Reactor')).toBeInTheDocument();
    });

    const skipButton = screen.getByText('Skip Setup');
    fireEvent.click(skipButton);

    await waitFor(() => {
      expect(mockOnSkip).toHaveBeenCalledTimes(1);
    });
  });

  it('handles navigation between steps', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        needsOnboarding: true,
        currentStep: 2,
        progress: {
          ...mockOnboardingProgress,
          current_step: 2,
          steps: {
            api_keys: true,
            github_connection: false,
            sample_loop: false
          }
        }
      })
    });

    render(<OnboardingWizard autoStart={true} />);

    await waitFor(() => {
      expect(screen.getByText('Step 2 of 3')).toBeInTheDocument();
    });

    // Check that Previous button is enabled (not on first step)
    const previousButton = screen.getByText('Previous');
    expect(previousButton).not.toBeDisabled();
  });

  it('handles API errors gracefully', async () => {
    (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

    render(<OnboardingWizard autoStart={true} />);

    // Should still show onboarding even if status check fails
    await waitFor(() => {
      expect(screen.getByText('Welcome to Metamorphic Reactor')).toBeInTheDocument();
    });
  });
});
