import * as React from "react"
import { cn } from "@/lib/utils"
import { Card } from "@/components/ui/card"
import { VStack, HStack } from "@/components/ui/layout"
import { Typography } from "@/components/ui/typography"
import { ProgressBar, CircularProgress, LoadingSpinner } from "@/components/ui/loading-states"
import { 
  PlayIcon, 
  PauseIcon, 
  StopIcon, 
  CheckIcon, 
  ErrorIcon,
  ClockIcon,
  CodeIcon,
  IterationIcon
} from "@/components/ui/icon"

// Real-time progress data interfaces
interface ProgressStep {
  id: string
  name: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number
  startTime?: Date
  endTime?: Date
  error?: string
}

interface RealtimeProgressData {
  id: string
  title: string
  status: 'idle' | 'running' | 'paused' | 'completed' | 'failed'
  overallProgress: number
  currentStep: number
  totalSteps: number
  steps: ProgressStep[]
  startTime?: Date
  estimatedCompletion?: Date
  metadata?: {
    iterations?: number
    score?: number
    changes?: string[]
  }
}

// Enhanced progress bar with real-time updates
interface EnhancedProgressBarProps {
  progress: number
  status: 'idle' | 'running' | 'paused' | 'completed' | 'failed'
  label?: string
  showPercentage?: boolean
  showTime?: boolean
  estimatedTime?: number
  animated?: boolean
  className?: string
}

export function EnhancedProgressBar({
  progress,
  status,
  label,
  showPercentage = true,
  showTime = false,
  estimatedTime,
  animated = true,
  className
}: EnhancedProgressBarProps) {
  const getVariant = () => {
    switch (status) {
      case 'completed':
        return 'success'
      case 'failed':
        return 'error'
      case 'paused':
        return 'warning'
      default:
        return 'default'
    }
  }

  const getStatusIcon = () => {
    switch (status) {
      case 'running':
        return <LoadingSpinner size="sm" className="w-4 h-4" />
      case 'paused':
        return <PauseIcon className="w-4 h-4 text-warning" />
      case 'completed':
        return <CheckIcon className="w-4 h-4 text-success" />
      case 'failed':
        return <ErrorIcon className="w-4 h-4 text-destructive" />
      default:
        return null
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <VStack spacing="xs" className={className}>
      <HStack spacing="sm" className="justify-between items-center">
        <HStack spacing="xs" align="center">
          {getStatusIcon()}
          {label && (
            <Typography variant="body-sm" className="text-foreground">
              {label}
            </Typography>
          )}
        </HStack>
        
        <HStack spacing="sm" align="center">
          {showPercentage && (
            <Typography variant="caption" className="text-muted-foreground">
              {Math.round(progress)}%
            </Typography>
          )}
          {showTime && estimatedTime && (
            <Typography variant="caption" className="text-muted-foreground">
              ~{formatTime(estimatedTime)}
            </Typography>
          )}
        </HStack>
      </HStack>
      
      <div className="relative">
        <ProgressBar
          value={progress}
          variant={getVariant()}
          showPercentage={false}
          className={cn(
            animated && status === 'running' && "animate-pulse",
            "transition-all duration-300"
          )}
        />
        
        {/* Animated progress indicator */}
        {status === 'running' && animated && (
          <div className="absolute top-0 left-0 h-full w-full overflow-hidden rounded-full">
            <div className="absolute top-0 left-0 h-full w-8 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer" />
          </div>
        )}
      </div>
    </VStack>
  )
}

// Step-by-step progress indicator
interface StepProgressProps {
  steps: ProgressStep[]
  currentStep: number
  className?: string
}

export function StepProgress({ steps, currentStep, className }: StepProgressProps) {
  const getStepIcon = (step: ProgressStep, index: number) => {
    if (step.status === 'completed') {
      return <CheckIcon className="w-4 h-4 text-success" />
    }
    if (step.status === 'failed') {
      return <ErrorIcon className="w-4 h-4 text-destructive" />
    }
    if (step.status === 'running') {
      return <LoadingSpinner size="sm" className="w-4 h-4" />
    }
    
    return (
      <div className={cn(
        "w-6 h-6 rounded-full border-2 flex items-center justify-center text-xs font-medium",
        index <= currentStep ? "border-primary bg-primary text-primary-foreground" : "border-muted bg-background text-muted-foreground"
      )}>
        {index + 1}
      </div>
    )
  }

  const getStepStatus = (step: ProgressStep, index: number) => {
    if (step.status === 'completed') return 'text-success'
    if (step.status === 'failed') return 'text-destructive'
    if (step.status === 'running') return 'text-primary'
    if (index <= currentStep) return 'text-foreground'
    return 'text-muted-foreground'
  }

  return (
    <VStack spacing="sm" className={className}>
      {steps.map((step, index) => (
        <HStack key={step.id} spacing="sm" align="center">
          {getStepIcon(step, index)}
          
          <VStack spacing="xs" className="flex-1">
            <HStack spacing="sm" className="justify-between items-center">
              <Typography 
                variant="body-sm" 
                className={getStepStatus(step, index)}
              >
                {step.name}
              </Typography>
              
              {step.status === 'running' && (
                <Typography variant="caption" className="text-muted-foreground">
                  {Math.round(step.progress)}%
                </Typography>
              )}
            </HStack>
            
            {step.status === 'running' && (
              <ProgressBar
                value={step.progress}
                size="sm"
                showPercentage={false}
                className="w-full"
              />
            )}
            
            {step.error && (
              <Typography variant="caption" className="text-destructive">
                {step.error}
              </Typography>
            )}
          </VStack>
          
          {/* Connector line */}
          {index < steps.length - 1 && (
            <div className="absolute left-3 mt-8 w-0.5 h-6 bg-border" />
          )}
        </HStack>
      ))}
    </VStack>
  )
}

// Real-time transformation progress
interface TransformationProgressProps {
  data: RealtimeProgressData
  onPause?: () => void
  onResume?: () => void
  onStop?: () => void
  className?: string
}

export function TransformationProgress({
  data,
  onPause,
  onResume,
  onStop,
  className
}: TransformationProgressProps) {
  const [elapsedTime, setElapsedTime] = React.useState(0)

  React.useEffect(() => {
    if (data.status === 'running' && data.startTime) {
      const interval = setInterval(() => {
        setElapsedTime(Date.now() - data.startTime!.getTime())
      }, 1000)
      
      return () => clearInterval(interval)
    }
  }, [data.status, data.startTime])

  const formatElapsedTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    
    if (hours > 0) {
      return `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`
    }
    return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`
  }

  const getEstimatedCompletion = () => {
    if (!data.estimatedCompletion) return null
    
    const now = new Date()
    const diff = data.estimatedCompletion.getTime() - now.getTime()
    
    if (diff <= 0) return 'Completing...'
    
    const minutes = Math.floor(diff / 60000)
    const seconds = Math.floor((diff % 60000) / 1000)
    
    if (minutes > 0) {
      return `~${minutes}m ${seconds}s remaining`
    }
    return `~${seconds}s remaining`
  }

  return (
    <Card className={cn("p-6", className)}>
      <VStack spacing="md">
        {/* Header */}
        <HStack spacing="sm" className="justify-between items-start">
          <VStack spacing="xs">
            <Typography variant="h6" className="text-foreground">
              {data.title}
            </Typography>
            <Typography variant="caption" className="text-muted-foreground">
              Step {data.currentStep + 1} of {data.totalSteps}
            </Typography>
          </VStack>
          
          <HStack spacing="xs">
            {data.status === 'running' && onPause && (
              <button
                onClick={onPause}
                className="p-2 rounded-md hover:bg-muted transition-colors"
              >
                <PauseIcon className="w-4 h-4" />
              </button>
            )}
            {data.status === 'paused' && onResume && (
              <button
                onClick={onResume}
                className="p-2 rounded-md hover:bg-muted transition-colors"
              >
                <PlayIcon className="w-4 h-4" />
              </button>
            )}
            {(data.status === 'running' || data.status === 'paused') && onStop && (
              <button
                onClick={onStop}
                className="p-2 rounded-md hover:bg-muted transition-colors text-destructive"
              >
                <StopIcon className="w-4 h-4" />
              </button>
            )}
          </HStack>
        </HStack>

        {/* Overall Progress */}
        <EnhancedProgressBar
          progress={data.overallProgress}
          status={data.status}
          label="Overall Progress"
          showTime={true}
          estimatedTime={data.estimatedCompletion ? Math.floor((data.estimatedCompletion.getTime() - Date.now()) / 1000) : undefined}
        />

        {/* Time and Stats */}
        <HStack spacing="md" className="text-sm text-muted-foreground">
          <HStack spacing="xs" align="center">
            <ClockIcon className="w-4 h-4" />
            <span>Elapsed: {formatElapsedTime(elapsedTime)}</span>
          </HStack>
          
          {data.metadata?.iterations && (
            <HStack spacing="xs" align="center">
              <IterationIcon className="w-4 h-4" />
              <span>Iterations: {data.metadata.iterations}</span>
            </HStack>
          )}
          
          {data.metadata?.score && (
            <HStack spacing="xs" align="center">
              <CodeIcon className="w-4 h-4" />
              <span>Score: {data.metadata.score}/100</span>
            </HStack>
          )}
        </HStack>

        {/* Estimated Completion */}
        {data.estimatedCompletion && data.status === 'running' && (
          <Typography variant="caption" className="text-muted-foreground">
            {getEstimatedCompletion()}
          </Typography>
        )}

        {/* Step Progress */}
        <StepProgress steps={data.steps} currentStep={data.currentStep} />

        {/* Recent Changes */}
        {data.metadata?.changes && data.metadata.changes.length > 0 && (
          <VStack spacing="xs">
            <Typography variant="body-sm" className="text-foreground font-medium">
              Recent Changes:
            </Typography>
            <VStack spacing="xs">
              {data.metadata.changes.slice(-3).map((change, index) => (
                <Typography key={index} variant="caption" className="text-muted-foreground">
                  • {change}
                </Typography>
              ))}
            </VStack>
          </VStack>
        )}
      </VStack>
    </Card>
  )
}

// Mini progress indicator for compact spaces
interface MiniProgressProps {
  progress: number
  status: 'idle' | 'running' | 'completed' | 'failed'
  size?: 'sm' | 'md'
  showPercentage?: boolean
  className?: string
}

export function MiniProgress({
  progress,
  status,
  size = 'sm',
  showPercentage = false,
  className
}: MiniProgressProps) {
  const sizeClasses = {
    sm: 'w-16 h-16',
    md: 'w-20 h-20'
  }

  const getVariant = () => {
    switch (status) {
      case 'completed':
        return 'success'
      case 'failed':
        return 'error'
      default:
        return 'default'
    }
  }

  return (
    <div className={cn("relative", sizeClasses[size], className)}>
      <CircularProgress
        value={progress}
        size={size === 'sm' ? 64 : 80}
        strokeWidth={size === 'sm' ? 4 : 6}
        variant={getVariant()}
        showPercentage={showPercentage}
      />
      
      {status === 'running' && (
        <div className="absolute inset-0 flex items-center justify-center">
          <LoadingSpinner size="sm" />
        </div>
      )}
    </div>
  )
}
