import { z } from 'zod';

// Supabase Configuration Schema
export const supabaseConfigSchema = z.object({
  url: z
    .string()
    .url('Must be a valid URL')
    .refine(
      (url) => url.includes('supabase.co') || url.includes('localhost'),
      'Must be a valid Supabase URL'
    ),
  anonKey: z
    .string()
    .min(1, 'Anonymous key is required')
    .refine(
      (key) => key.startsWith('eyJ'),
      'Must be a valid JWT token'
    ),
  serviceKey: z
    .string()
    .optional()
    .refine(
      (key) => !key || key.startsWith('eyJ'),
      'Must be a valid JWT token'
    ),
});

// GitHub Configuration Schema
export const githubConfigSchema = z.object({
  clientId: z
    .string()
    .min(1, 'Client ID is required')
    .regex(/^[a-zA-Z0-9]+$/, 'Must contain only alphanumeric characters'),
  clientSecret: z
    .string()
    .optional(),
  redirectUri: z
    .string()
    .url('Must be a valid URL'),
  token: z
    .string()
    .optional()
    .refine(
      (token) => !token || token.startsWith('ghp_') || token.startsWith('gho_'),
      'Must be a valid GitHub token'
    ),
});

// AI Model Configuration Schema
export const aiModelConfigSchema = z.object({
  plannerModel: z.enum([
    'gpt-4-turbo',
    'gpt-4',
    'gpt-3.5-turbo',
    'claude-3-opus',
    'claude-3-sonnet',
    'claude-3-haiku',
  ]),
  criticModel: z.enum([
    'gpt-4-turbo',
    'gpt-4',
    'gpt-3.5-turbo',
    'claude-3-opus',
    'claude-3-sonnet',
    'claude-3-haiku',
  ]),
  temperature: z
    .number()
    .min(0, 'Temperature must be between 0 and 2')
    .max(2, 'Temperature must be between 0 and 2'),
  maxTokens: z
    .number()
    .min(100, 'Max tokens must be at least 100')
    .max(8000, 'Max tokens cannot exceed 8000'),
  maxIterations: z
    .number()
    .min(1, 'Must have at least 1 iteration')
    .max(20, 'Cannot exceed 20 iterations'),
  scoreThreshold: z
    .number()
    .min(0, 'Score threshold must be between 0 and 1')
    .max(1, 'Score threshold must be between 0 and 1'),
  costLimit: z
    .number()
    .min(0.1, 'Cost limit must be at least $0.10')
    .max(100, 'Cost limit cannot exceed $100'),
  timeout: z
    .number()
    .min(30, 'Timeout must be at least 30 seconds')
    .max(600, 'Timeout cannot exceed 10 minutes'),
});

// API Keys Configuration Schema
export const apiKeysConfigSchema = z.object({
  openai: z
    .string()
    .optional()
    .refine(
      (key) => !key || key.startsWith('sk-'),
      'Must be a valid OpenAI API key'
    ),
  anthropic: z
    .string()
    .optional()
    .refine(
      (key) => !key || key.startsWith('sk-ant-'),
      'Must be a valid Anthropic API key'
    ),
  google: z
    .string()
    .optional(),
});

// Complete Configuration Schema
export const completeConfigSchema = z.object({
  supabase: supabaseConfigSchema,
  github: githubConfigSchema,
  aiModels: aiModelConfigSchema,
  apiKeys: apiKeysConfigSchema,
});

// Validation Result Types
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ValidationWarning {
  field: string;
  message: string;
  code: string;
}

// Configuration Validation Class
export class ConfigurationValidator {
  static validateSupabase(config: any): ValidationResult {
    const result = supabaseConfigSchema.safeParse(config);
    
    if (result.success) {
      const warnings: ValidationWarning[] = [];
      
      // Check for development vs production URLs
      if (config.url.includes('localhost')) {
        warnings.push({
          field: 'url',
          message: 'Using localhost URL - ensure this is intended for development',
          code: 'DEV_URL',
        });
      }
      
      // Check if service key is provided
      if (!config.serviceKey) {
        warnings.push({
          field: 'serviceKey',
          message: 'Service key not provided - some features may be limited',
          code: 'MISSING_SERVICE_KEY',
        });
      }
      
      return {
        isValid: true,
        errors: [],
        warnings,
      };
    }
    
    return {
      isValid: false,
      errors: result.error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code,
      })),
      warnings: [],
    };
  }
  
  static validateGitHub(config: any): ValidationResult {
    const result = githubConfigSchema.safeParse(config);
    
    if (result.success) {
      const warnings: ValidationWarning[] = [];
      
      // Check if using localhost redirect
      if (config.redirectUri.includes('localhost')) {
        warnings.push({
          field: 'redirectUri',
          message: 'Using localhost redirect - ensure this matches your OAuth app settings',
          code: 'DEV_REDIRECT',
        });
      }
      
      // Check if token is present
      if (!config.token) {
        warnings.push({
          field: 'token',
          message: 'No access token found - complete OAuth flow to enable GitHub features',
          code: 'MISSING_TOKEN',
        });
      }
      
      return {
        isValid: true,
        errors: [],
        warnings,
      };
    }
    
    return {
      isValid: false,
      errors: result.error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code,
      })),
      warnings: [],
    };
  }
  
  static validateAIModels(config: any): ValidationResult {
    const result = aiModelConfigSchema.safeParse(config);
    
    if (result.success) {
      const warnings: ValidationWarning[] = [];
      
      // Check for same model used for both planner and critic
      if (config.plannerModel === config.criticModel) {
        warnings.push({
          field: 'models',
          message: 'Using same model for planner and critic - consider using different models for better results',
          code: 'SAME_MODELS',
        });
      }
      
      // Check for high temperature
      if (config.temperature > 1.5) {
        warnings.push({
          field: 'temperature',
          message: 'High temperature may produce inconsistent results',
          code: 'HIGH_TEMPERATURE',
        });
      }
      
      // Check for low score threshold
      if (config.scoreThreshold < 0.8) {
        warnings.push({
          field: 'scoreThreshold',
          message: 'Low score threshold may accept poor quality transformations',
          code: 'LOW_THRESHOLD',
        });
      }
      
      return {
        isValid: true,
        errors: [],
        warnings,
      };
    }
    
    return {
      isValid: false,
      errors: result.error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code,
      })),
      warnings: [],
    };
  }
  
  static validateAPIKeys(config: any): ValidationResult {
    const result = apiKeysConfigSchema.safeParse(config);
    
    if (result.success) {
      const warnings: ValidationWarning[] = [];
      
      // Check if at least one API key is provided
      const hasAnyKey = config.openai || config.anthropic || config.google;
      if (!hasAnyKey) {
        warnings.push({
          field: 'apiKeys',
          message: 'No API keys configured - AI features will not work',
          code: 'NO_API_KEYS',
        });
      }
      
      return {
        isValid: true,
        errors: [],
        warnings,
      };
    }
    
    return {
      isValid: false,
      errors: result.error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code,
      })),
      warnings: [],
    };
  }
  
  static validateComplete(config: any): ValidationResult {
    const supabaseResult = this.validateSupabase(config.supabase);
    const githubResult = this.validateGitHub(config.github);
    const aiModelsResult = this.validateAIModels(config.aiModels);
    const apiKeysResult = this.validateAPIKeys(config.apiKeys);
    
    const allErrors = [
      ...supabaseResult.errors,
      ...githubResult.errors,
      ...aiModelsResult.errors,
      ...apiKeysResult.errors,
    ];
    
    const allWarnings = [
      ...supabaseResult.warnings,
      ...githubResult.warnings,
      ...aiModelsResult.warnings,
      ...apiKeysResult.warnings,
    ];
    
    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
      warnings: allWarnings,
    };
  }
}

// Utility functions for common validations
export const validateUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

export const validateJWT = (token: string): boolean => {
  try {
    const parts = token.split('.');
    return parts.length === 3 && parts.every(part => part.length > 0);
  } catch {
    return false;
  }
};

export const validateGitHubToken = (token: string): boolean => {
  return token.startsWith('ghp_') || token.startsWith('gho_');
};

export const validateOpenAIKey = (key: string): boolean => {
  return key.startsWith('sk-') && key.length > 20;
};

export const validateAnthropicKey = (key: string): boolean => {
  return key.startsWith('sk-ant-') && key.length > 20;
};

// Export types
export type SupabaseConfig = z.infer<typeof supabaseConfigSchema>;
export type GitHubConfig = z.infer<typeof githubConfigSchema>;
export type AIModelConfig = z.infer<typeof aiModelConfigSchema>;
export type APIKeysConfig = z.infer<typeof apiKeysConfigSchema>;
export type CompleteConfig = z.infer<typeof completeConfigSchema>;
