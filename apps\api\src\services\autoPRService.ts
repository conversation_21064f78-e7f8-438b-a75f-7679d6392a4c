import { EventEmitter } from 'events';
import { loggingService } from './loggingService.js';
import { githubService } from './githubService.js';
import { supabaseClient } from './supabase.js';
import { autonomousProcessor } from './autonomousProcessor.js';

export interface PRCreationRequest {
  repositoryId: string;
  planId: string;
  opportunityId: string;
  patch: string;
  branchName?: string;
  title?: string;
  description?: string;
  isDraft?: boolean;
}

export interface PRCreationResult {
  success: boolean;
  prNumber?: number;
  prUrl?: string;
  branchName?: string;
  error?: string;
  cost: number;
}

export interface BranchInfo {
  name: string;
  sha: string;
  url: string;
  created: boolean;
}

export interface CommitInfo {
  sha: string;
  message: string;
  url: string;
  filesChanged: number;
}

class AutoPRService extends EventEmitter {
  private activePRs: Map<string, any> = new Map();
  private branchPrefix = 'autonomous-improvement';
  
  constructor() {
    super();
    this.setupEventListeners();
  }

  /**
   * Create an automatic PR for an approved improvement
   */
  async createAutomaticPR(request: PRCreationRequest): Promise<PRCreationResult> {
    const startTime = Date.now();
    let totalCost = 0;

    try {
      // Get repository details
      const { data: repoData, error: repoError } = await supabaseClient
        .from('autonomous_repositories')
        .select('*')
        .eq('id', request.repositoryId)
        .single();

      if (repoError || !repoData) {
        throw new Error('Repository not found');
      }

      // Get improvement plan details
      const { data: planData, error: planError } = await supabaseClient
        .from('improvement_plans')
        .select(`
          *,
          improvement_opportunities!inner(*)
        `)
        .eq('id', request.planId)
        .single();

      if (planError || !planData) {
        throw new Error('Improvement plan not found');
      }

      const opportunity = planData.improvement_opportunities;

      // Generate branch name
      const branchName = request.branchName || 
        `${this.branchPrefix}-${opportunity.type}-${Date.now()}`;

      await loggingService.log({
        level: 'info',
        message: 'Starting automatic PR creation',
        service: 'auto-pr',
        metadata: {
          repositoryId: request.repositoryId,
          planId: request.planId,
          branchName,
          opportunityType: opportunity.type
        }
      });

      // Step 1: Create branch
      const branchResult = await this.createBranch(
        repoData.owner,
        repoData.name,
        branchName,
        repoData.branch
      );

      if (!branchResult.created) {
        throw new Error(`Failed to create branch: ${branchResult.name}`);
      }

      totalCost += 0.01; // GitHub API cost

      // Step 2: Apply patch and commit changes
      const commitResult = await this.applyPatchAndCommit(
        repoData.owner,
        repoData.name,
        branchName,
        request.patch,
        opportunity,
        planData
      );

      totalCost += 0.02; // GitHub API cost

      // Step 3: Create pull request
      const prResult = await this.createPullRequest(
        repoData.owner,
        repoData.name,
        branchName,
        repoData.branch,
        request.title || this.generatePRTitle(opportunity),
        request.description || this.generatePRDescription(opportunity, planData),
        request.isDraft !== false // Default to draft
      );

      totalCost += 0.01; // GitHub API cost

      // Step 4: Update improvement plan with PR info
      await supabaseClient
        .from('improvement_plans')
        .update({
          status: 'pr_created',
          execution_result: {
            ...planData.execution_result,
            prData: {
              number: prResult.number,
              url: prResult.url,
              branchName,
              isDraft: request.isDraft !== false
            },
            phase: 'pr_creation'
          },
          updated_at: new Date().toISOString()
        })
        .eq('id', request.planId);

      // Store PR info for tracking
      this.activePRs.set(request.planId, {
        prNumber: prResult.number,
        prUrl: prResult.url,
        branchName,
        repositoryId: request.repositoryId,
        createdAt: new Date()
      });

      const result: PRCreationResult = {
        success: true,
        prNumber: prResult.number,
        prUrl: prResult.url,
        branchName,
        cost: totalCost
      };

      await loggingService.log({
        level: 'info',
        message: 'Automatic PR created successfully',
        service: 'auto-pr',
        metadata: {
          repositoryId: request.repositoryId,
          planId: request.planId,
          prNumber: prResult.number,
          prUrl: prResult.url,
          branchName,
          cost: totalCost,
          duration: Date.now() - startTime
        }
      });

      this.emit('pr-created', { request, result });

      return result;

    } catch (error) {
      const result: PRCreationResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        cost: totalCost
      };

      await loggingService.log({
        level: 'error',
        message: 'Automatic PR creation failed',
        service: 'auto-pr',
        metadata: {
          repositoryId: request.repositoryId,
          planId: request.planId,
          error: result.error,
          cost: totalCost,
          duration: Date.now() - startTime
        }
      });

      this.emit('pr-failed', { request, result, error });

      return result;
    }
  }

  /**
   * Create a new branch from the base branch
   */
  private async createBranch(
    owner: string,
    repo: string,
    branchName: string,
    baseBranch: string
  ): Promise<BranchInfo> {
    try {
      // Get the SHA of the base branch
      const baseBranchData = await githubService.getBranch(owner, repo, baseBranch);
      const baseSha = baseBranchData.commit.sha;

      // Create new branch
      const branchData = await githubService.createBranch(owner, repo, branchName, baseSha);

      return {
        name: branchName,
        sha: branchData.object.sha,
        url: branchData.url,
        created: true
      };

    } catch (error) {
      await loggingService.log({
        level: 'error',
        message: 'Failed to create branch',
        service: 'auto-pr',
        metadata: {
          owner,
          repo,
          branchName,
          baseBranch,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      });

      return {
        name: branchName,
        sha: '',
        url: '',
        created: false
      };
    }
  }

  /**
   * Apply patch and commit changes to the branch
   */
  private async applyPatchAndCommit(
    owner: string,
    repo: string,
    branchName: string,
    patch: string,
    opportunity: any,
    planData: any
  ): Promise<CommitInfo> {
    try {
      // Parse the JSON patch
      let patchOperations;
      try {
        patchOperations = JSON.parse(patch);
      } catch {
        throw new Error('Invalid JSON patch format');
      }

      // Get the current file content
      const fileContent = await githubService.getFileContent(
        owner,
        repo,
        opportunity.file_path,
        branchName
      );

      // Apply patch operations (simplified implementation)
      const lines = fileContent.split('\n');
      let modifiedLines = [...lines];

      for (const operation of patchOperations) {
        if (operation.op === 'replace' && operation.path.startsWith('/line/')) {
          const lineNumber = parseInt(operation.path.split('/')[2]) - 1; // Convert to 0-based
          if (lineNumber >= 0 && lineNumber < modifiedLines.length) {
            modifiedLines[lineNumber] = operation.value;
          }
        } else if (operation.op === 'add' && operation.path.startsWith('/line/')) {
          const lineNumber = parseInt(operation.path.split('/')[2]) - 1;
          if (lineNumber >= 0 && lineNumber <= modifiedLines.length) {
            modifiedLines.splice(lineNumber, 0, operation.value);
          }
        } else if (operation.op === 'remove' && operation.path.startsWith('/line/')) {
          const lineNumber = parseInt(operation.path.split('/')[2]) - 1;
          if (lineNumber >= 0 && lineNumber < modifiedLines.length) {
            modifiedLines.splice(lineNumber, 1);
          }
        }
      }

      const modifiedContent = modifiedLines.join('\n');

      // Create commit message
      const commitMessage = this.generateCommitMessage(opportunity, planData);

      // Commit the changes
      const commitData = await githubService.updateFile(
        owner,
        repo,
        opportunity.file_path,
        modifiedContent,
        commitMessage,
        branchName
      );

      return {
        sha: commitData.commit.sha,
        message: commitMessage,
        url: commitData.commit.html_url,
        filesChanged: 1
      };

    } catch (error) {
      await loggingService.log({
        level: 'error',
        message: 'Failed to apply patch and commit',
        service: 'auto-pr',
        metadata: {
          owner,
          repo,
          branchName,
          filePath: opportunity.file_path,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      });

      throw error;
    }
  }

  /**
   * Create a pull request
   */
  private async createPullRequest(
    owner: string,
    repo: string,
    headBranch: string,
    baseBranch: string,
    title: string,
    body: string,
    isDraft: boolean
  ): Promise<{ number: number; url: string }> {
    try {
      const prData = await githubService.createPullRequest(
        owner,
        repo,
        title,
        body,
        headBranch,
        baseBranch,
        isDraft
      );

      return {
        number: prData.number,
        url: prData.html_url
      };

    } catch (error) {
      await loggingService.log({
        level: 'error',
        message: 'Failed to create pull request',
        service: 'auto-pr',
        metadata: {
          owner,
          repo,
          headBranch,
          baseBranch,
          title,
          isDraft,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      });

      throw error;
    }
  }

  /**
   * Generate PR title
   */
  private generatePRTitle(opportunity: any): string {
    const typeEmojis = {
      security: '🔒',
      performance: '⚡',
      maintainability: '🧹',
      bugs: '🐛',
      style: '💄'
    };

    const emoji = typeEmojis[opportunity.type] || '✨';
    const typeLabel = opportunity.type.charAt(0).toUpperCase() + opportunity.type.slice(1);
    
    return `${emoji} [Autonomous] ${typeLabel}: ${opportunity.description}`;
  }

  /**
   * Generate PR description
   */
  private generatePRDescription(opportunity: any, planData: any): string {
    const criticResponse = planData.critic_response || {};
    const testResults = planData.execution_result?.testResults || {};

    return `## 🤖 Autonomous Code Improvement

This PR was automatically generated by the **Metamorphic Reactor** to improve code quality.

### 📋 Issue Details
- **Type**: ${opportunity.type}
- **Priority**: ${opportunity.priority}/10
- **Confidence**: ${(opportunity.confidence * 100).toFixed(1)}%
- **File**: \`${opportunity.file_path}\`

### 📝 Description
${opportunity.description}

### 🎯 Improvement
${opportunity.suggested_improvement}

### 🔍 Quality Assessment
${criticResponse.overallScore ? `- **Overall Score**: ${(criticResponse.overallScore * 100).toFixed(1)}%` : ''}
${criticResponse.scores ? Object.entries(criticResponse.scores).map(([key, value]) => 
  `- **${key.charAt(0).toUpperCase() + key.slice(1)}**: ${((value as number) * 100).toFixed(1)}%`
).join('\n') : ''}

### ✅ Testing Results
${testResults.unitTests ? `- **Unit Tests**: ${testResults.unitTests.passed}/${testResults.unitTests.total} passed` : ''}
${testResults.integrationTests ? `- **Integration Tests**: ${testResults.integrationTests.passed}/${testResults.integrationTests.total} passed` : ''}
${testResults.securityScan ? `- **Security Scan**: ${testResults.securityScan.vulnerabilities} vulnerabilities found` : ''}

### 🔄 Review Process
1. ✅ **Automated Analysis** - Issue identified and analyzed
2. ✅ **AI Planning** - Solution generated and validated
3. ✅ **Quality Check** - Meets ${((planData.quality_threshold || 0.95) * 100).toFixed(0)}% quality threshold
4. ✅ **Automated Testing** - All tests pass
5. 🔄 **Human Review** - Please review before merging

### 🛡️ Safety Measures
- Changes are minimal and focused
- All existing functionality preserved
- Comprehensive testing completed
- Rollback plan available if needed

---
*🌟 Generated by [Metamorphic Reactor](https://github.com/your-org/metamorphic-reactor) - Autonomous Code Evolution*

**Need help?** Check our [documentation](https://docs.metamorphic-reactor.dev) or contact the team.`;
  }

  /**
   * Generate commit message
   */
  private generateCommitMessage(opportunity: any, planData: any): string {
    const typeEmojis = {
      security: '🔒',
      performance: '⚡',
      maintainability: '🧹',
      bugs: '🐛',
      style: '💄'
    };

    const emoji = typeEmojis[opportunity.type] || '✨';
    const scope = this.extractScope(opportunity.file_path);
    
    return `${emoji} ${opportunity.type}${scope}: ${opportunity.description}

Autonomous improvement applied by Metamorphic Reactor

- Priority: ${opportunity.priority}/10
- Confidence: ${(opportunity.confidence * 100).toFixed(1)}%
- Quality Score: ${planData.critic_response?.overallScore ? (planData.critic_response.overallScore * 100).toFixed(1) + '%' : 'N/A'}

Co-authored-by: Metamorphic Reactor <<EMAIL>>`;
  }

  /**
   * Extract scope from file path for conventional commits
   */
  private extractScope(filePath: string): string {
    const parts = filePath.split('/');
    
    // Common scope patterns
    if (parts.includes('api') || parts.includes('server')) return '(api)';
    if (parts.includes('ui') || parts.includes('components')) return '(ui)';
    if (parts.includes('auth')) return '(auth)';
    if (parts.includes('database') || parts.includes('db')) return '(db)';
    if (parts.includes('config')) return '(config)';
    if (parts.includes('utils') || parts.includes('helpers')) return '(utils)';
    if (parts.includes('tests') || parts.includes('test')) return '(test)';
    
    // Use directory name if recognizable
    const dir = parts[parts.length - 2];
    if (dir && dir.length < 10) {
      return `(${dir})`;
    }
    
    return '';
  }

  /**
   * Setup event listeners for autonomous processor
   */
  private setupEventListeners(): void {
    // Listen for completed improvement jobs
    autonomousProcessor.on('job-completed', async ({ job, result }) => {
      if (job.type === 'improve' && result.success && result.data?.approved) {
        // Automatically create PR for approved improvements
        try {
          await this.createAutomaticPR({
            repositoryId: job.repositoryId,
            planId: result.data.planId,
            opportunityId: job.opportunityId!,
            patch: result.data.plannerResponse,
            isDraft: true
          });
        } catch (error) {
          await loggingService.log({
            level: 'error',
            message: 'Failed to auto-create PR for completed improvement',
            service: 'auto-pr',
            metadata: {
              jobId: job.id,
              planId: result.data.planId,
              error: error instanceof Error ? error.message : 'Unknown error'
            }
          });
        }
      }
    });
  }

  /**
   * Get active PRs
   */
  getActivePRs(): Array<any> {
    return Array.from(this.activePRs.values());
  }

  /**
   * Get PR info by plan ID
   */
  getPRByPlanId(planId: string): any {
    return this.activePRs.get(planId);
  }

  /**
   * Clean up old PR tracking data
   */
  cleanupOldPRs(): void {
    const cutoffTime = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 days ago
    
    for (const [planId, prInfo] of this.activePRs.entries()) {
      if (prInfo.createdAt < cutoffTime) {
        this.activePRs.delete(planId);
      }
    }
  }
}

export const autoPRService = new AutoPRService();
