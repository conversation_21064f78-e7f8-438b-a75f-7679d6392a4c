import { CostGuard } from '../../utils/CostGuard.js';
import { CostLimitError } from '../../providers/AgentProvider.js';
import { TokenUsage } from '../../types.js';

describe('CostGuard', () => {
  let costGuard: CostGuard;

  beforeEach(() => {
    costGuard = new CostGuard({ maxCostPerLoop: 2.0, enabled: true });
  });

  describe('constructor', () => {
    it('should initialize with default configuration', () => {
      const defaultGuard = new CostGuard();
      
      expect(defaultGuard.getCurrentCost()).toBe(0);
      expect(defaultGuard.getRemainingBudget()).toBe(3.0); // Default max cost
    });

    it('should initialize with custom configuration', () => {
      const customGuard = new CostGuard({ 
        maxCostPerLoop: 5.0, 
        currentCost: 1.0,
        enabled: true 
      });
      
      expect(customGuard.getCurrentCost()).toBe(0); // Should reset to 0 on init
      expect(customGuard.getRemainingBudget()).toBe(5.0);
    });
  });

  describe('checkCostLimit', () => {
    it('should pass when under limit', () => {
      expect(() => costGuard.checkCostLimit(0.5)).not.toThrow();
    });

    it('should throw when exceeding limit', () => {
      expect(() => costGuard.checkCostLimit(2.5)).toThrow(CostLimitError);
    });

    it('should throw when current cost plus estimated exceeds limit', () => {
      // Record some cost first
      const usage: TokenUsage = {
        inputTokens: 1000,
        outputTokens: 500,
        totalTokens: 1500,
        cost: 1.5,
        provider: 'openai',
        model: 'gpt-4o',
        timestamp: new Date(),
      };
      
      costGuard.recordCost('openai', 'gpt-4o', 'test', usage);
      
      expect(() => costGuard.checkCostLimit(1.0)).toThrow(CostLimitError);
    });

    it('should not check when disabled', () => {
      const disabledGuard = new CostGuard({ maxCostPerLoop: 1.0, enabled: false });
      
      expect(() => disabledGuard.checkCostLimit(5.0)).not.toThrow();
    });
  });

  describe('recordCost', () => {
    const mockUsage: TokenUsage = {
      inputTokens: 1000,
      outputTokens: 500,
      totalTokens: 1500,
      cost: 0.5,
      provider: 'openai',
      model: 'gpt-4o',
      timestamp: new Date(),
    };

    it('should record cost entry', () => {
      const entry = costGuard.recordCost('openai', 'gpt-4o', 'generatePatch', mockUsage);
      
      expect(entry).toBeDefined();
      expect(entry.provider).toBe('openai');
      expect(entry.model).toBe('gpt-4o');
      expect(entry.operation).toBe('generatePatch');
      expect(entry.cost).toBe(0.5);
      expect(entry.tokens).toBe(1500);
    });

    it('should update current cost', () => {
      const initialCost = costGuard.getCurrentCost();
      
      costGuard.recordCost('openai', 'gpt-4o', 'generatePatch', mockUsage);
      
      expect(costGuard.getCurrentCost()).toBe(initialCost + 0.5);
    });

    it('should update provider costs', () => {
      costGuard.recordCost('openai', 'gpt-4o', 'generatePatch', mockUsage);
      
      expect(costGuard.getCostByProvider('openai')).toBe(0.5);
      expect(costGuard.getCostByProvider('vertex-ai')).toBe(0);
    });

    it('should check cost limit after recording', () => {
      const highCostUsage: TokenUsage = {
        ...mockUsage,
        cost: 2.5,
      };
      
      expect(() => costGuard.recordCost('openai', 'gpt-4o', 'test', highCostUsage))
        .toThrow(CostLimitError);
    });
  });

  describe('cost tracking', () => {
    it('should track remaining budget', () => {
      const usage: TokenUsage = {
        inputTokens: 1000,
        outputTokens: 500,
        totalTokens: 1500,
        cost: 0.8,
        provider: 'openai',
        model: 'gpt-4o',
        timestamp: new Date(),
      };
      
      costGuard.recordCost('openai', 'gpt-4o', 'test', usage);
      
      expect(costGuard.getRemainingBudget()).toBe(1.2); // 2.0 - 0.8
    });

    it('should detect when near limit', () => {
      const usage: TokenUsage = {
        inputTokens: 1000,
        outputTokens: 500,
        totalTokens: 1500,
        cost: 1.7, // 85% of 2.0 limit
        provider: 'openai',
        model: 'gpt-4o',
        timestamp: new Date(),
      };
      
      costGuard.recordCost('openai', 'gpt-4o', 'test', usage);
      
      expect(costGuard.isNearLimit()).toBe(true);
    });

    it('should not be near limit when well under', () => {
      const usage: TokenUsage = {
        inputTokens: 1000,
        outputTokens: 500,
        totalTokens: 1500,
        cost: 0.5, // 25% of 2.0 limit
        provider: 'openai',
        model: 'gpt-4o',
        timestamp: new Date(),
      };
      
      costGuard.recordCost('openai', 'gpt-4o', 'test', usage);
      
      expect(costGuard.isNearLimit()).toBe(false);
    });
  });

  describe('getStats', () => {
    beforeEach(() => {
      // Add some test data
      const usages = [
        {
          inputTokens: 1000,
          outputTokens: 500,
          totalTokens: 1500,
          cost: 0.3,
          provider: 'openai' as const,
          model: 'gpt-4o',
          timestamp: new Date(),
        },
        {
          inputTokens: 800,
          outputTokens: 400,
          totalTokens: 1200,
          cost: 0.2,
          provider: 'vertex-ai' as const,
          model: 'gemini-2.5-flash',
          timestamp: new Date(),
        },
        {
          inputTokens: 1200,
          outputTokens: 600,
          totalTokens: 1800,
          cost: 0.4,
          provider: 'openai' as const,
          model: 'gpt-4o',
          timestamp: new Date(),
        },
      ];

      usages.forEach((usage, index) => {
        costGuard.recordCost(usage.provider, usage.model, `operation${index}`, usage);
      });
    });

    it('should provide comprehensive statistics', () => {
      const stats = costGuard.getStats();
      
      expect(stats.totalCost).toBe(0.9);
      expect(stats.totalTokens).toBe(4500);
      expect(stats.totalRequests).toBe(3);
      expect(stats.remainingBudget).toBe(1.1);
      expect(stats.isNearLimit).toBe(false);
    });

    it('should provide cost by provider', () => {
      const stats = costGuard.getStats();
      
      expect(stats.costByProvider.get('openai')?.totalCost).toBe(0.7);
      expect(stats.costByProvider.get('vertex-ai')?.totalCost).toBe(0.2);
      expect(stats.costByProvider.get('openai')?.requestCount).toBe(2);
      expect(stats.costByProvider.get('vertex-ai')?.requestCount).toBe(1);
    });

    it('should provide cost by model', () => {
      const stats = costGuard.getStats();
      
      expect(stats.costByModel.get('gpt-4o')).toBe(0.7);
      expect(stats.costByModel.get('gemini-2.5-flash')).toBe(0.2);
    });

    it('should provide cost by operation', () => {
      const stats = costGuard.getStats();
      
      expect(stats.costByOperation.get('operation0')).toBe(0.3);
      expect(stats.costByOperation.get('operation1')).toBe(0.2);
      expect(stats.costByOperation.get('operation2')).toBe(0.4);
    });
  });

  describe('getCostEntries', () => {
    beforeEach(() => {
      const usage: TokenUsage = {
        inputTokens: 1000,
        outputTokens: 500,
        totalTokens: 1500,
        cost: 0.3,
        provider: 'openai',
        model: 'gpt-4o',
        timestamp: new Date(),
      };
      
      costGuard.recordCost('openai', 'gpt-4o', 'generatePatch', usage);
      costGuard.recordCost('vertex-ai', 'gemini-2.5-flash', 'scorePatch', {
        ...usage,
        provider: 'vertex-ai',
        model: 'gemini-2.5-flash',
        cost: 0.1,
      });
    });

    it('should return all entries without filter', () => {
      const entries = costGuard.getCostEntries();
      
      expect(entries).toHaveLength(2);
    });

    it('should filter by provider', () => {
      const entries = costGuard.getCostEntries({ provider: 'openai' });
      
      expect(entries).toHaveLength(1);
      expect(entries[0].provider).toBe('openai');
    });

    it('should filter by model', () => {
      const entries = costGuard.getCostEntries({ model: 'gpt-4o' });
      
      expect(entries).toHaveLength(1);
      expect(entries[0].model).toBe('gpt-4o');
    });

    it('should filter by operation', () => {
      const entries = costGuard.getCostEntries({ operation: 'generatePatch' });
      
      expect(entries).toHaveLength(1);
      expect(entries[0].operation).toBe('generatePatch');
    });

    it('should limit results', () => {
      const entries = costGuard.getCostEntries({ limit: 1 });
      
      expect(entries).toHaveLength(1);
    });
  });

  describe('projectCost', () => {
    it('should project cost for planned operations', () => {
      const plannedOps = [
        { provider: 'openai' as const, estimatedTokens: 2000 },
        { provider: 'vertex-ai' as const, estimatedTokens: 3000 },
      ];
      
      const projection = costGuard.projectCost(plannedOps);
      
      expect(projection.estimatedCost).toBeGreaterThan(0);
      expect(projection.wouldExceedLimit).toBe(false);
      expect(projection.remainingBudgetAfter).toBeLessThan(2.0);
    });

    it('should detect when projection would exceed limit', () => {
      const expensiveOps = [
        { provider: 'openai' as const, estimatedTokens: 100000 },
      ];
      
      const projection = costGuard.projectCost(expensiveOps);
      
      expect(projection.wouldExceedLimit).toBe(true);
      expect(projection.remainingBudgetAfter).toBe(0);
    });
  });

  describe('getCostEfficiency', () => {
    beforeEach(() => {
      const usages = [
        {
          inputTokens: 1000,
          outputTokens: 500,
          totalTokens: 1500,
          cost: 0.3,
          provider: 'openai' as const,
          model: 'gpt-4o',
          timestamp: new Date(),
        },
        {
          inputTokens: 2000,
          outputTokens: 1000,
          totalTokens: 3000,
          cost: 0.2,
          provider: 'vertex-ai' as const,
          model: 'gemini-2.5-flash',
          timestamp: new Date(),
        },
      ];

      usages.forEach((usage, index) => {
        costGuard.recordCost(usage.provider, usage.model, `operation${index}`, usage);
      });
    });

    it('should calculate efficiency metrics', () => {
      const efficiency = costGuard.getCostEfficiency();
      
      expect(efficiency.costPerToken).toBeGreaterThan(0);
      expect(efficiency.costPerRequest).toBeGreaterThan(0);
      expect(efficiency.mostExpensiveProvider).toBe('openai'); // Higher cost per token
      expect(efficiency.mostEfficientProvider).toBe('vertex-ai'); // Lower cost per token
    });
  });

  describe('configuration management', () => {
    it('should update cost limit', () => {
      costGuard.updateCostLimit(5.0);
      
      expect(costGuard.getRemainingBudget()).toBe(5.0);
    });

    it('should throw error for invalid cost limit', () => {
      expect(() => costGuard.updateCostLimit(-1)).toThrow('Cost limit must be positive');
    });

    it('should throw error if current cost exceeds new limit', () => {
      const usage: TokenUsage = {
        inputTokens: 1000,
        outputTokens: 500,
        totalTokens: 1500,
        cost: 1.5,
        provider: 'openai',
        model: 'gpt-4o',
        timestamp: new Date(),
      };
      
      costGuard.recordCost('openai', 'gpt-4o', 'test', usage);
      
      expect(() => costGuard.updateCostLimit(1.0)).toThrow(CostLimitError);
    });

    it('should enable and disable cost guard', () => {
      costGuard.setEnabled(false);
      
      expect(() => costGuard.checkCostLimit(10.0)).not.toThrow();
      
      costGuard.setEnabled(true);
      
      expect(() => costGuard.checkCostLimit(10.0)).toThrow(CostLimitError);
    });
  });

  describe('resetCosts', () => {
    it('should reset all cost data', () => {
      const usage: TokenUsage = {
        inputTokens: 1000,
        outputTokens: 500,
        totalTokens: 1500,
        cost: 0.5,
        provider: 'openai',
        model: 'gpt-4o',
        timestamp: new Date(),
      };
      
      costGuard.recordCost('openai', 'gpt-4o', 'test', usage);
      
      expect(costGuard.getCurrentCost()).toBe(0.5);
      
      costGuard.resetCosts();
      
      expect(costGuard.getCurrentCost()).toBe(0);
      expect(costGuard.getCostEntries()).toHaveLength(0);
    });
  });

  describe('exportCostData', () => {
    it('should export comprehensive cost data', () => {
      const usage: TokenUsage = {
        inputTokens: 1000,
        outputTokens: 500,
        totalTokens: 1500,
        cost: 0.3,
        provider: 'openai',
        model: 'gpt-4o',
        timestamp: new Date(),
      };
      
      costGuard.recordCost('openai', 'gpt-4o', 'test', usage);
      
      const exportData = costGuard.exportCostData();
      
      expect(exportData.config).toBeDefined();
      expect(exportData.entries).toHaveLength(1);
      expect(exportData.summary).toBeDefined();
      expect(exportData.exportedAt).toBeInstanceOf(Date);
    });
  });
});
