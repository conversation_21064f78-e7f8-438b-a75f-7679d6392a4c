import { z } from 'zod';
import { WebSocketManager, WebSocketEventType, getWebSocketManager } from '../websocket/websocketManager';

// Stream event types
export enum StreamEventType {
  // Transformation events
  TRANSFORMATION_QUEUED = 'transformation_queued',
  TRANSFORMATION_STARTED = 'transformation_started',
  TRANSFORMATION_PROGRESS = 'transformation_progress',
  TRANSFORMATION_COMPLETED = 'transformation_completed',
  TRANSFORMATION_FAILED = 'transformation_failed',
  
  // Agent events
  PLANNER_STARTED = 'planner_started',
  PLANNER_THINKING = 'planner_thinking',
  PLANNER_COMPLETED = 'planner_completed',
  CRITIC_STARTED = 'critic_started',
  CRITIC_EVALUATING = 'critic_evaluating',
  CRITIC_COMPLETED = 'critic_completed',
  
  // System events
  QUEUE_UPDATED = 'queue_updated',
  COST_UPDATED = 'cost_updated',
  SYSTEM_STATUS_CHANGED = 'system_status_changed',
  
  // Error events
  ERROR_OCCURRED = 'error_occurred',
  WARNING_ISSUED = 'warning_issued',
}

// Stream event schema
export const streamEventSchema = z.object({
  type: z.nativeEnum(StreamEventType),
  id: z.string(),
  timestamp: z.string().transform(str => new Date(str)),
  data: z.any(),
  metadata: z.record(z.any()).optional(),
  priority: z.enum(['low', 'normal', 'high', 'critical']).default('normal'),
  category: z.string().optional(),
  source: z.string().optional(),
});

export type StreamEvent = z.infer<typeof streamEventSchema>;

// Event filter configuration
export interface EventFilter {
  types?: StreamEventType[];
  categories?: string[];
  sources?: string[];
  priority?: ('low' | 'normal' | 'high' | 'critical')[];
  since?: Date;
  limit?: number;
}

// Event listener type
export type StreamEventListener = (event: StreamEvent) => void;

// Event streaming manager
export class EventStreamingManager {
  private wsManager: WebSocketManager;
  private listeners: Map<StreamEventType, StreamEventListener[]> = new Map();
  private eventHistory: StreamEvent[] = [];
  private maxHistorySize = 1000;
  private filters: EventFilter[] = [];

  constructor(wsManager?: WebSocketManager) {
    this.wsManager = wsManager || getWebSocketManager();
    this.setupWebSocketListeners();
  }

  // Event subscription
  subscribe(eventType: StreamEventType, listener: StreamEventListener): () => void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }
    
    this.listeners.get(eventType)!.push(listener);
    
    // Return unsubscribe function
    return () => {
      const listeners = this.listeners.get(eventType);
      if (listeners) {
        const index = listeners.indexOf(listener);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      }
    };
  }

  // Subscribe to multiple event types
  subscribeToMultiple(
    eventTypes: StreamEventType[], 
    listener: StreamEventListener
  ): () => void {
    const unsubscribeFunctions = eventTypes.map(type => 
      this.subscribe(type, listener)
    );
    
    return () => {
      unsubscribeFunctions.forEach(unsub => unsub());
    };
  }

  // Subscribe with filter
  subscribeWithFilter(
    filter: EventFilter,
    listener: StreamEventListener
  ): () => void {
    const filteredListener = (event: StreamEvent) => {
      if (this.matchesFilter(event, filter)) {
        listener(event);
      }
    };

    // Subscribe to all event types if none specified
    const eventTypes = filter.types || Object.values(StreamEventType);
    return this.subscribeToMultiple(eventTypes, filteredListener);
  }

  // Emit event
  emit(event: Omit<StreamEvent, 'id' | 'timestamp'>): void {
    const fullEvent: StreamEvent = {
      ...event,
      id: this.generateEventId(),
      timestamp: new Date(),
    };

    // Add to history
    this.addToHistory(fullEvent);

    // Notify listeners
    this.notifyListeners(fullEvent);

    // Send via WebSocket if connected
    if (this.wsManager.isConnected()) {
      this.wsManager.send(WebSocketEventType.TRANSFORMATION_PROGRESS, fullEvent);
    }
  }

  // Get event history
  getHistory(filter?: EventFilter): StreamEvent[] {
    let events = [...this.eventHistory];

    if (filter) {
      events = events.filter(event => this.matchesFilter(event, filter));
    }

    // Sort by timestamp (newest first)
    events.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    // Apply limit
    if (filter?.limit) {
      events = events.slice(0, filter.limit);
    }

    return events;
  }

  // Clear history
  clearHistory(): void {
    this.eventHistory = [];
  }

  // Get events by transformation ID
  getTransformationEvents(transformationId: string): StreamEvent[] {
    return this.eventHistory.filter(event => 
      event.metadata?.transformationId === transformationId
    );
  }

  // Get recent events
  getRecentEvents(limit: number = 10): StreamEvent[] {
    return this.getHistory({ limit });
  }

  // Event statistics
  getEventStats(): {
    total: number;
    byType: Record<StreamEventType, number>;
    byPriority: Record<string, number>;
    recentActivity: Array<{ hour: string; count: number }>;
  } {
    const byType = {} as Record<StreamEventType, number>;
    const byPriority = { low: 0, normal: 0, high: 0, critical: 0 };
    
    // Initialize type counts
    Object.values(StreamEventType).forEach(type => {
      byType[type] = 0;
    });

    // Count events
    this.eventHistory.forEach(event => {
      byType[event.type]++;
      byPriority[event.priority]++;
    });

    // Recent activity (last 24 hours)
    const now = new Date();
    const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const recentEvents = this.eventHistory.filter(event => 
      event.timestamp >= twentyFourHoursAgo
    );

    const activityByHour: Record<string, number> = {};
    recentEvents.forEach(event => {
      const hour = event.timestamp.getHours().toString().padStart(2, '0');
      activityByHour[hour] = (activityByHour[hour] || 0) + 1;
    });

    const recentActivity = Object.entries(activityByHour)
      .map(([hour, count]) => ({ hour, count }))
      .sort((a, b) => a.hour.localeCompare(b.hour));

    return {
      total: this.eventHistory.length,
      byType,
      byPriority,
      recentActivity,
    };
  }

  // Private methods
  private setupWebSocketListeners(): void {
    // Listen for WebSocket events and convert them to stream events
    this.wsManager.on(WebSocketEventType.TRANSFORMATION_STARTED, (message) => {
      this.emit({
        type: StreamEventType.TRANSFORMATION_STARTED,
        data: message.data,
        source: 'websocket',
        priority: 'normal',
      });
    });

    this.wsManager.on(WebSocketEventType.TRANSFORMATION_PROGRESS, (message) => {
      this.emit({
        type: StreamEventType.TRANSFORMATION_PROGRESS,
        data: message.data,
        source: 'websocket',
        priority: 'normal',
      });
    });

    this.wsManager.on(WebSocketEventType.TRANSFORMATION_COMPLETED, (message) => {
      this.emit({
        type: StreamEventType.TRANSFORMATION_COMPLETED,
        data: message.data,
        source: 'websocket',
        priority: 'high',
      });
    });

    this.wsManager.on(WebSocketEventType.TRANSFORMATION_FAILED, (message) => {
      this.emit({
        type: StreamEventType.TRANSFORMATION_FAILED,
        data: message.data,
        source: 'websocket',
        priority: 'critical',
      });
    });

    this.wsManager.on(WebSocketEventType.QUEUE_UPDATE, (message) => {
      this.emit({
        type: StreamEventType.QUEUE_UPDATED,
        data: message.data,
        source: 'websocket',
        priority: 'low',
      });
    });

    this.wsManager.on(WebSocketEventType.COST_UPDATE, (message) => {
      this.emit({
        type: StreamEventType.COST_UPDATED,
        data: message.data,
        source: 'websocket',
        priority: 'normal',
      });
    });
  }

  private addToHistory(event: StreamEvent): void {
    this.eventHistory.unshift(event);
    
    // Trim history if it gets too large
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory = this.eventHistory.slice(0, this.maxHistorySize);
    }
  }

  private notifyListeners(event: StreamEvent): void {
    const listeners = this.listeners.get(event.type) || [];
    
    listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in stream event listener:', error);
      }
    });
  }

  private matchesFilter(event: StreamEvent, filter: EventFilter): boolean {
    // Check event types
    if (filter.types && !filter.types.includes(event.type)) {
      return false;
    }

    // Check categories
    if (filter.categories && event.category && !filter.categories.includes(event.category)) {
      return false;
    }

    // Check sources
    if (filter.sources && event.source && !filter.sources.includes(event.source)) {
      return false;
    }

    // Check priority
    if (filter.priority && !filter.priority.includes(event.priority)) {
      return false;
    }

    // Check timestamp
    if (filter.since && event.timestamp < filter.since) {
      return false;
    }

    return true;
  }

  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Cleanup
  destroy(): void {
    this.listeners.clear();
    this.eventHistory = [];
  }
}

// Global event streaming manager
let globalEventStreamingManager: EventStreamingManager | null = null;

export const getEventStreamingManager = (): EventStreamingManager => {
  if (!globalEventStreamingManager) {
    globalEventStreamingManager = new EventStreamingManager();
  }
  return globalEventStreamingManager;
};

// Convenience functions for common events
export const emitTransformationEvent = (
  type: StreamEventType,
  transformationId: string,
  data: any,
  priority: 'low' | 'normal' | 'high' | 'critical' = 'normal'
) => {
  const manager = getEventStreamingManager();
  manager.emit({
    type,
    data,
    metadata: { transformationId },
    priority,
    category: 'transformation',
    source: 'system',
  });
};

export const emitSystemEvent = (
  type: StreamEventType,
  data: any,
  priority: 'low' | 'normal' | 'high' | 'critical' = 'normal'
) => {
  const manager = getEventStreamingManager();
  manager.emit({
    type,
    data,
    priority,
    category: 'system',
    source: 'system',
  });
};

export const emitErrorEvent = (
  error: Error,
  context?: any,
  priority: 'high' | 'critical' = 'high'
) => {
  const manager = getEventStreamingManager();
  manager.emit({
    type: StreamEventType.ERROR_OCCURRED,
    data: {
      message: error.message,
      stack: error.stack,
      context,
    },
    priority,
    category: 'error',
    source: 'system',
  });
};
