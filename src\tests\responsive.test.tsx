import { render, screen } from '@testing-library/react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import Dashboard from '../pages/Dashboard';
import DashboardIntegration from '../components/dashboard/DashboardIntegration';

// Mock hooks and components
jest.mock('../hooks/useCodeTransformation', () => ({
  useCodeTransformation: () => ({
    isRunning: false,
    transformedCode: '',
    diffContent: '',
    logs: [],
    handleRunLoop: jest.fn(),
    stopTransformation: jest.fn(),
  }),
}));

jest.mock('../hooks/useTransformationHistory', () => ({
  useTransformationHistory: () => ({
    handleApplyChanges: jest.fn(),
    handleDownloadCode: jest.fn(),
  }),
}));

jest.mock('../hooks/useKeyboardShortcuts', () => ({
  useKeyboardShortcuts: () => {},
}));

jest.mock('../components/CodeEditor', () => ({
  CodeEditor: ({ value, onChange }: any) => (
    <textarea
      value={value}
      onChange={(e) => onChange(e.target.value)}
      aria-label="Code editor"
      data-testid="code-editor"
    />
  ),
}));

jest.mock('../components/DiffViewer', () => ({
  DiffViewer: ({ diffContent }: any) => (
    <div role="region" aria-label="Code diff viewer" data-testid="diff-viewer">
      {diffContent || 'No changes to display'}
    </div>
  ),
}));

jest.mock('../components/AgentLog', () => ({
  AgentLog: ({ logs }: any) => (
    <div role="log" aria-label="Agent logs" data-testid="agent-log">
      {logs.length === 0 ? 'No logs available' : logs.join('\n')}
    </div>
  ),
}));

jest.mock('../components/ControlPanel', () => ({
  ControlPanel: ({ isRunning, onRunLoop, onStop }: any) => (
    <div role="group" aria-label="Control panel">
      <button onClick={onRunLoop} disabled={isRunning} aria-label="Start transformation">
        {isRunning ? 'Running...' : 'Start'}
      </button>
      <button onClick={onStop} disabled={!isRunning} aria-label="Stop transformation">
        Stop
      </button>
    </div>
  ),
}));

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

// Helper to mock viewport size
const mockViewport = (width: number, height: number = 800) => {
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width,
  });
  Object.defineProperty(window, 'innerHeight', {
    writable: true,
    configurable: true,
    value: height,
  });
  
  // Trigger resize event
  window.dispatchEvent(new Event('resize'));
};

describe('Responsive Design Tests', () => {
  beforeEach(() => {
    // Reset to desktop size before each test
    mockViewport(1280, 800);
  });

  describe('Mobile Viewport (320px)', () => {
    beforeEach(() => {
      mockViewport(320, 568);
    });

    test('Dashboard should render properly on mobile', () => {
      renderWithRouter(<Dashboard />);
      
      // Should have main elements
      expect(screen.getByRole('main')).toBeInTheDocument();
      expect(screen.getByRole('tablist')).toBeInTheDocument();
    });

    test('Tab labels should be hidden on mobile', () => {
      renderWithRouter(<Dashboard />);
      
      // Tab text should be hidden on mobile (using hidden sm:inline classes)
      const tabs = screen.getAllByRole('tab');
      expect(tabs.length).toBeGreaterThan(0);
      
      // Icons should still be visible
      tabs.forEach(tab => {
        const icon = tab.querySelector('svg');
        expect(icon).toBeInTheDocument();
      });
    });

    test('Mobile layout should stack code editor and diff viewer', () => {
      renderWithRouter(<Dashboard />);
      
      // Mobile layout should be present
      const mobileLayout = document.querySelector('.lg\\:hidden');
      expect(mobileLayout).toBeInTheDocument();
    });

    test('Padding should be reduced on mobile', () => {
      renderWithRouter(<Dashboard />);
      
      // Check for mobile padding classes
      const tabContainer = document.querySelector('.px-2');
      expect(tabContainer).toBeInTheDocument();
    });
  });

  describe('Tablet Viewport (768px)', () => {
    beforeEach(() => {
      mockViewport(768, 1024);
    });

    test('Dashboard should render properly on tablet', () => {
      renderWithRouter(<Dashboard />);
      
      expect(screen.getByRole('main')).toBeInTheDocument();
      expect(screen.getByRole('tablist')).toBeInTheDocument();
    });

    test('Tab labels should be visible on tablet', () => {
      renderWithRouter(<Dashboard />);
      
      const tabs = screen.getAllByRole('tab');
      expect(tabs.length).toBeGreaterThan(0);
      
      // Should have both icons and text
      tabs.forEach(tab => {
        const icon = tab.querySelector('svg');
        expect(icon).toBeInTheDocument();
      });
    });

    test('Grid should adjust for tablet layout', () => {
      render(<DashboardIntegration />);
      
      // Should have responsive grid classes
      const gridContainer = document.querySelector('.sm\\:grid-cols-2');
      expect(gridContainer).toBeInTheDocument();
    });
  });

  describe('Desktop Viewport (1280px)', () => {
    beforeEach(() => {
      mockViewport(1280, 800);
    });

    test('Dashboard should render properly on desktop', () => {
      renderWithRouter(<Dashboard />);
      
      expect(screen.getByRole('main')).toBeInTheDocument();
      expect(screen.getByRole('tablist')).toBeInTheDocument();
    });

    test('All tabs should be visible on desktop', () => {
      renderWithRouter(<Dashboard />);
      
      const tabs = screen.getAllByRole('tab');
      expect(tabs.length).toBe(6); // All 6 tabs should be present
    });

    test('Desktop layout should use resizable panels', () => {
      renderWithRouter(<Dashboard />);
      
      // Desktop layout should be present
      const desktopLayout = document.querySelector('.hidden.lg\\:flex');
      expect(desktopLayout).toBeInTheDocument();
    });

    test('Full grid layout should be available on desktop', () => {
      render(<DashboardIntegration />);
      
      // Should have full grid layout
      const gridContainer = document.querySelector('.xl\\:grid-cols-3');
      expect(gridContainer).toBeInTheDocument();
    });
  });

  describe('Responsive Breakpoint Tests', () => {
    test('Should handle viewport changes gracefully', () => {
      const { rerender } = renderWithRouter(<Dashboard />);
      
      // Start with desktop
      mockViewport(1280, 800);
      rerender(<Dashboard />);
      expect(screen.getByRole('main')).toBeInTheDocument();
      
      // Switch to mobile
      mockViewport(320, 568);
      rerender(<Dashboard />);
      expect(screen.getByRole('main')).toBeInTheDocument();
      
      // Switch to tablet
      mockViewport(768, 1024);
      rerender(<Dashboard />);
      expect(screen.getByRole('main')).toBeInTheDocument();
    });

    test('Tab grid should respond to breakpoints', () => {
      renderWithRouter(<Dashboard />);
      
      const tabsList = screen.getByRole('tablist');
      
      // Should have responsive grid classes
      expect(tabsList).toHaveClass('grid-cols-2');
      expect(tabsList).toHaveClass('sm:grid-cols-3');
      expect(tabsList).toHaveClass('lg:grid-cols-6');
    });

    test('Dashboard integration should respond to breakpoints', () => {
      render(<DashboardIntegration />);
      
      const tabsList = screen.getByRole('tablist');
      
      // Should have responsive grid classes
      expect(tabsList).toHaveClass('grid-cols-2');
      expect(tabsList).toHaveClass('sm:grid-cols-3');
      expect(tabsList).toHaveClass('lg:grid-cols-5');
    });
  });

  describe('Content Overflow Tests', () => {
    test('Should handle long content without horizontal scroll', () => {
      renderWithRouter(<Dashboard />);
      
      // Check that main container doesn't cause horizontal overflow
      const main = screen.getByRole('main');
      expect(main).toHaveClass('h-[calc(100vh-73px)]');
    });

    test('Should handle text overflow in tabs', () => {
      renderWithRouter(<Dashboard />);
      
      const tabs = screen.getAllByRole('tab');
      
      // Tabs should have proper flex layout to handle content
      tabs.forEach(tab => {
        expect(tab).toHaveClass('flex');
        expect(tab).toHaveClass('items-center');
      });
    });
  });

  describe('Touch Target Tests', () => {
    test('Interactive elements should have adequate touch targets on mobile', () => {
      mockViewport(320, 568);
      renderWithRouter(<Dashboard />);
      
      const buttons = screen.getAllByRole('button');
      const tabs = screen.getAllByRole('tab');
      
      // All interactive elements should be present and accessible
      [...buttons, ...tabs].forEach(element => {
        expect(element).toBeInTheDocument();
        expect(element).toBeVisible();
      });
    });
  });
});
