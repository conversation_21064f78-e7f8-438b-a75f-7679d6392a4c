import type { Meta, StoryObj } from '@storybook/react';
import { QueueStatus } from './QueueStatus';

// Mock fetch for Storybook
const mockFetch = (data: any, delay = 1000) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        ok: true,
        json: async () => ({
          success: true,
          data
        })
      });
    }, delay);
  });
};

const meta: Meta<typeof QueueStatus> = {
  title: 'App/Queue/QueueStatus',
  component: QueueStatus,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'Displays real-time queue status with metrics and item details for the reactor loop processing queue.'
      }
    }
  },
  tags: ['autodocs'],
  argTypes: {
    onRefresh: { action: 'refresh clicked' },
    autoRefresh: {
      control: 'boolean',
      description: 'Enable automatic refresh every 5 seconds'
    },
    refreshInterval: {
      control: 'number',
      description: 'Refresh interval in milliseconds'
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;

// Mock data
const mockQueueData = {
  totalItems: 8,
  runningItems: 2,
  queuedItems: 4,
  completedToday: 15,
  averageWaitTime: 7.5,
  currentCapacity: 3,
  maxCapacity: 5,
  items: [
    {
      sessionId: 'session-001',
      prompt: 'Optimize database queries for better performance',
      priority: 8,
      queuePosition: 0,
      status: 'running' as const,
      createdAt: new Date(Date.now() - 300000).toISOString() // 5 minutes ago
    },
    {
      sessionId: 'session-002',
      prompt: 'Refactor authentication middleware',
      priority: 6,
      queuePosition: 1,
      status: 'queued' as const,
      estimatedWaitTime: 5,
      createdAt: new Date(Date.now() - 120000).toISOString() // 2 minutes ago
    },
    {
      sessionId: 'session-003',
      prompt: 'Add error handling to API endpoints',
      priority: 4,
      queuePosition: 2,
      status: 'queued' as const,
      estimatedWaitTime: 12,
      createdAt: new Date(Date.now() - 60000).toISOString() // 1 minute ago
    },
    {
      sessionId: 'session-004',
      prompt: 'Implement caching layer',
      priority: 2,
      queuePosition: 3,
      status: 'queued' as const,
      estimatedWaitTime: 18,
      createdAt: new Date().toISOString()
    }
  ]
};

const emptyQueueData = {
  totalItems: 0,
  runningItems: 0,
  queuedItems: 0,
  completedToday: 5,
  averageWaitTime: 0,
  currentCapacity: 0,
  maxCapacity: 5,
  items: []
};

const highVolumeQueueData = {
  totalItems: 25,
  runningItems: 5,
  queuedItems: 18,
  completedToday: 42,
  averageWaitTime: 15.3,
  currentCapacity: 5,
  maxCapacity: 5,
  items: [
    {
      sessionId: 'session-101',
      prompt: 'Critical security patch implementation',
      priority: 10,
      queuePosition: 0,
      status: 'running' as const,
      createdAt: new Date(Date.now() - 600000).toISOString()
    },
    {
      sessionId: 'session-102',
      prompt: 'Performance optimization for large datasets',
      priority: 9,
      queuePosition: 1,
      status: 'queued' as const,
      estimatedWaitTime: 25,
      createdAt: new Date(Date.now() - 300000).toISOString()
    },
    {
      sessionId: 'session-103',
      prompt: 'Code cleanup and documentation',
      priority: 1,
      queuePosition: 15,
      status: 'queued' as const,
      estimatedWaitTime: 45,
      createdAt: new Date(Date.now() - 180000).toISOString()
    }
  ]
};

export const Default: Story = {
  args: {
    autoRefresh: false
  },
  parameters: {
    mockData: [
      {
        url: '/api/queue/status',
        method: 'GET',
        status: 200,
        response: {
          success: true,
          data: mockQueueData
        }
      }
    ]
  }
};

export const Loading: Story = {
  args: {
    autoRefresh: false
  },
  parameters: {
    mockData: [
      {
        url: '/api/queue/status',
        method: 'GET',
        delay: 5000, // Long delay to show loading state
        status: 200,
        response: {
          success: true,
          data: mockQueueData
        }
      }
    ]
  }
};

export const EmptyQueue: Story = {
  args: {
    autoRefresh: false
  },
  parameters: {
    mockData: [
      {
        url: '/api/queue/status',
        method: 'GET',
        status: 200,
        response: {
          success: true,
          data: emptyQueueData
        }
      }
    ]
  }
};

export const HighVolume: Story = {
  args: {
    autoRefresh: false
  },
  parameters: {
    mockData: [
      {
        url: '/api/queue/status',
        method: 'GET',
        status: 200,
        response: {
          success: true,
          data: highVolumeQueueData
        }
      }
    ]
  }
};

export const ErrorState: Story = {
  args: {
    autoRefresh: false
  },
  parameters: {
    mockData: [
      {
        url: '/api/queue/status',
        method: 'GET',
        status: 500,
        response: {
          success: false,
          error: 'Internal server error'
        }
      }
    ]
  }
};

export const WithAutoRefresh: Story = {
  args: {
    autoRefresh: true,
    refreshInterval: 3000
  },
  parameters: {
    mockData: [
      {
        url: '/api/queue/status',
        method: 'GET',
        status: 200,
        response: {
          success: true,
          data: mockQueueData
        }
      }
    ]
  }
};
