#!/usr/bin/env node

/**
 * Accessibility Audit Script for Metamorphic Reactor
 * Uses axe-core and Puppeteer to test accessibility across browsers
 */

import puppeteer from 'puppeteer';
import { AxePuppeteer } from '@axe-core/puppeteer';
import fs from 'fs/promises';
import path from 'path';

// Configuration
const config = {
  baseUrl: process.env.BASE_URL || 'http://localhost:8080',
  outputDir: './artifacts',
  browsers: ['chromium'], // Add 'firefox', 'webkit' when available
  pages: [
    { name: 'home', path: '/' },
    { name: 'dashboard', path: '/dashboard' },
    { name: 'settings', path: '/settings' },
    { name: 'history', path: '/history' }
  ],
  axeOptions: {
    tags: ['wcag2a', 'wcag2aa', 'wcag21aa', 'best-practice'],
    rules: {
      // Customize rules as needed
      'color-contrast': { enabled: true },
      'keyboard-navigation': { enabled: true },
      'focus-management': { enabled: true },
      'aria-labels': { enabled: true }
    }
  },
  thresholds: {
    violations: 0, // No violations allowed
    incomplete: 5, // Max 5 incomplete tests
    minScore: 95   // Minimum accessibility score
  }
};

class AccessibilityAuditor {
  constructor() {
    this.results = [];
    this.overallScore = 0;
  }

  /**
   * Run accessibility audit across all browsers and pages
   */
  async runAudit() {
    console.log('🔍 Starting Accessibility Audit for Metamorphic Reactor');
    console.log(`📍 Base URL: ${config.baseUrl}`);
    console.log(`🌐 Browsers: ${config.browsers.join(', ')}`);
    console.log(`📄 Pages: ${config.pages.map(p => p.name).join(', ')}`);

    // Ensure output directory exists
    await this.ensureOutputDir();

    // Test each browser
    for (const browserName of config.browsers) {
      console.log(`\n🚀 Testing with ${browserName}...`);
      await this.testBrowser(browserName);
    }

    // Generate summary report
    await this.generateSummaryReport();

    // Check if audit passed
    const passed = this.checkAuditResults();
    
    console.log(`\n${passed ? '✅' : '❌'} Accessibility Audit ${passed ? 'PASSED' : 'FAILED'}`);
    console.log(`📊 Overall Score: ${this.overallScore.toFixed(1)}/100`);

    if (!passed) {
      process.exit(1);
    }
  }

  /**
   * Test a specific browser
   */
  async testBrowser(browserName) {
    let browser;
    
    try {
      // Launch browser
      browser = await puppeteer.launch({
        product: browserName === 'firefox' ? 'firefox' : 'chrome',
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-web-security',
          '--allow-running-insecure-content'
        ]
      });

      const page = await browser.newPage();
      
      // Set viewport for consistent testing
      await page.setViewport({ width: 1280, height: 720 });

      // Test each page
      for (const pageConfig of config.pages) {
        console.log(`  📄 Testing ${pageConfig.name} page...`);
        await this.testPage(page, browserName, pageConfig);
      }

    } catch (error) {
      console.error(`❌ Error testing ${browserName}:`, error.message);
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Test accessibility for a specific page
   */
  async testPage(page, browserName, pageConfig) {
    try {
      const url = `${config.baseUrl}${pageConfig.path}`;
      
      // Navigate to page
      await page.goto(url, { 
        waitUntil: 'networkidle0',
        timeout: 30000 
      });

      // Wait for React to render
      await page.waitForTimeout(2000);

      // Run axe accessibility tests
      const axeResults = await new AxePuppeteer(page)
        .withTags(config.axeOptions.tags)
        .analyze();

      // Calculate score
      const score = this.calculateAccessibilityScore(axeResults);

      // Store results
      const result = {
        browser: browserName,
        page: pageConfig.name,
        url,
        score,
        violations: axeResults.violations,
        incomplete: axeResults.incomplete,
        passes: axeResults.passes,
        timestamp: new Date().toISOString()
      };

      this.results.push(result);

      // Save detailed report for this page
      await this.savePageReport(result);

      // Log summary
      console.log(`    Score: ${score.toFixed(1)}/100`);
      console.log(`    Violations: ${axeResults.violations.length}`);
      console.log(`    Incomplete: ${axeResults.incomplete.length}`);
      console.log(`    Passes: ${axeResults.passes.length}`);

      if (axeResults.violations.length > 0) {
        console.log(`    ⚠️  Violations found:`);
        axeResults.violations.forEach(violation => {
          console.log(`      - ${violation.id}: ${violation.description}`);
        });
      }

    } catch (error) {
      console.error(`    ❌ Error testing ${pageConfig.name}:`, error.message);
      
      // Store error result
      this.results.push({
        browser: browserName,
        page: pageConfig.name,
        url: `${config.baseUrl}${pageConfig.path}`,
        score: 0,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Calculate accessibility score based on axe results
   */
  calculateAccessibilityScore(axeResults) {
    const totalTests = axeResults.violations.length + 
                      axeResults.incomplete.length + 
                      axeResults.passes.length;
    
    if (totalTests === 0) return 0;

    // Score calculation:
    // - Violations: -10 points each
    // - Incomplete: -2 points each
    // - Passes: +1 point each
    const violationPenalty = axeResults.violations.length * 10;
    const incompletePenalty = axeResults.incomplete.length * 2;
    const passPoints = axeResults.passes.length;

    const rawScore = Math.max(0, passPoints - violationPenalty - incompletePenalty);
    const maxPossibleScore = totalTests;

    return Math.min(100, (rawScore / maxPossibleScore) * 100);
  }

  /**
   * Save detailed report for a page
   */
  async savePageReport(result) {
    const filename = `${result.browser}-${result.page}-${Date.now()}.json`;
    const filepath = path.join(config.outputDir, filename);
    
    await fs.writeFile(filepath, JSON.stringify(result, null, 2));
  }

  /**
   * Generate summary report
   */
  async generateSummaryReport() {
    // Calculate overall score
    const validResults = this.results.filter(r => !r.error);
    this.overallScore = validResults.length > 0 
      ? validResults.reduce((sum, r) => sum + r.score, 0) / validResults.length
      : 0;

    // Generate summary
    const summary = {
      timestamp: new Date().toISOString(),
      overallScore: this.overallScore,
      totalTests: this.results.length,
      passed: this.overallScore >= config.thresholds.minScore,
      thresholds: config.thresholds,
      results: this.results.map(r => ({
        browser: r.browser,
        page: r.page,
        score: r.score,
        violations: r.violations?.length || 0,
        incomplete: r.incomplete?.length || 0,
        error: r.error
      })),
      recommendations: this.generateRecommendations()
    };

    // Save summary report
    const summaryPath = path.join(config.outputDir, 'accessibility-summary.json');
    await fs.writeFile(summaryPath, JSON.stringify(summary, null, 2));

    // Generate HTML report
    await this.generateHtmlReport(summary);

    console.log(`\n📊 Summary Report saved to: ${summaryPath}`);
  }

  /**
   * Generate HTML report
   */
  async generateHtmlReport(summary) {
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accessibility Audit Report - Metamorphic Reactor</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 8px; }
        .score { font-size: 2em; font-weight: bold; color: ${summary.passed ? '#28a745' : '#dc3545'}; }
        .results { margin-top: 20px; }
        .result-item { margin: 10px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .violations { color: #dc3545; }
        .incomplete { color: #ffc107; }
        .passes { color: #28a745; }
        .recommendations { background: #e9ecef; padding: 15px; border-radius: 5px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Accessibility Audit Report</h1>
        <p><strong>Project:</strong> Metamorphic Reactor</p>
        <p><strong>Date:</strong> ${summary.timestamp}</p>
        <p><strong>Overall Score:</strong> <span class="score">${summary.overallScore.toFixed(1)}/100</span></p>
        <p><strong>Status:</strong> ${summary.passed ? '✅ PASSED' : '❌ FAILED'}</p>
    </div>

    <div class="results">
        <h2>Test Results</h2>
        ${summary.results.map(result => `
            <div class="result-item">
                <h3>${result.browser} - ${result.page}</h3>
                <p><strong>Score:</strong> ${result.score?.toFixed(1) || 'N/A'}/100</p>
                ${result.error ? `<p class="violations">Error: ${result.error}</p>` : `
                    <p class="violations">Violations: ${result.violations}</p>
                    <p class="incomplete">Incomplete: ${result.incomplete}</p>
                `}
            </div>
        `).join('')}
    </div>

    <div class="recommendations">
        <h2>Recommendations</h2>
        <ul>
            ${summary.recommendations.map(rec => `<li>${rec}</li>`).join('')}
        </ul>
    </div>
</body>
</html>`;

    const htmlPath = path.join(config.outputDir, 'accessibility-report.html');
    await fs.writeFile(htmlPath, html);
    console.log(`📄 HTML Report saved to: ${htmlPath}`);
  }

  /**
   * Generate recommendations based on results
   */
  generateRecommendations() {
    const recommendations = [];
    
    if (this.overallScore < config.thresholds.minScore) {
      recommendations.push('Overall accessibility score is below threshold. Review and fix violations.');
    }

    const hasViolations = this.results.some(r => r.violations && r.violations.length > 0);
    if (hasViolations) {
      recommendations.push('Fix accessibility violations found in the audit.');
      recommendations.push('Ensure all interactive elements have proper ARIA labels.');
      recommendations.push('Verify color contrast meets WCAG AA standards.');
      recommendations.push('Test keyboard navigation for all functionality.');
    }

    const hasErrors = this.results.some(r => r.error);
    if (hasErrors) {
      recommendations.push('Resolve page loading errors that prevented testing.');
    }

    if (recommendations.length === 0) {
      recommendations.push('Great job! All accessibility tests passed.');
      recommendations.push('Continue to test accessibility with each new feature.');
    }

    return recommendations;
  }

  /**
   * Check if audit results meet thresholds
   */
  checkAuditResults() {
    return this.overallScore >= config.thresholds.minScore &&
           !this.results.some(r => r.error);
  }

  /**
   * Ensure output directory exists
   */
  async ensureOutputDir() {
    try {
      await fs.access(config.outputDir);
    } catch {
      await fs.mkdir(config.outputDir, { recursive: true });
    }
  }
}

// Run the audit if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const auditor = new AccessibilityAuditor();
  auditor.runAudit().catch(error => {
    console.error('❌ Audit failed:', error);
    process.exit(1);
  });
}

export default AccessibilityAuditor;
