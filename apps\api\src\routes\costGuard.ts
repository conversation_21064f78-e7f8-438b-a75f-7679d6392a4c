import { Router } from 'express';
import { costGuard } from '../services/costGuard.js';
import { loggingService } from '../services/loggingService.js';
import { supabaseClient } from '../services/supabase.js';

const router = Router();

/**
 * GET /api/cost-guard/limits/:repositoryId
 * Get cost limits for a repository
 */
router.get('/cost-guard/limits/:repositoryId', async (req, res) => {
  try {
    const { repositoryId } = req.params;
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return res.status(401).json({ error: 'Authorization header required' });
    }

    // Get user from Supabase auth
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      return res.status(401).json({ error: 'Invalid authentication token' });
    }

    // Verify repository ownership
    const { data: repository, error: repoError } = await supabaseClient
      .from('autonomous_repositories')
      .select('*')
      .eq('id', repositoryId)
      .eq('user_id', user.id)
      .single();

    if (repoError || !repository) {
      return res.status(404).json({ error: 'Repository not found or access denied' });
    }

    const limits = costGuard.getCostLimits(repositoryId);
    const isShutdown = costGuard.isRepositoryShutdown(repositoryId);

    res.json({
      repositoryId,
      limits: limits || null,
      isShutdown,
      hasLimits: !!limits
    });

  } catch (error) {
    await loggingService.log({
      level: 'error',
      message: 'Failed to get cost limits',
      service: 'cost-guard-api',
      metadata: {
        repositoryId: req.params.repositoryId,
        error: error instanceof Error ? error.message : 'Unknown error',
        endpoint: 'GET /cost-guard/limits/:repositoryId'
      }
    });

    res.status(500).json({
      error: 'Failed to get cost limits',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * PUT /api/cost-guard/limits/:repositoryId
 * Set cost limits for a repository
 */
router.put('/cost-guard/limits/:repositoryId', async (req, res) => {
  try {
    const { repositoryId } = req.params;
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return res.status(401).json({ error: 'Authorization header required' });
    }

    // Get user from Supabase auth
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      return res.status(401).json({ error: 'Invalid authentication token' });
    }

    // Verify repository ownership
    const { data: repository, error: repoError } = await supabaseClient
      .from('autonomous_repositories')
      .select('*')
      .eq('id', repositoryId)
      .eq('user_id', user.id)
      .single();

    if (repoError || !repository) {
      return res.status(404).json({ error: 'Repository not found or access denied' });
    }

    const {
      dailyLimit,
      weeklyLimit,
      monthlyLimit,
      perOperationLimit,
      alertThreshold,
      enabled
    } = req.body;

    // Validate limits
    if (dailyLimit !== undefined && (dailyLimit < 0 || dailyLimit > 100)) {
      return res.status(400).json({
        error: 'Invalid daily limit',
        details: 'Daily limit must be between $0 and $100'
      });
    }

    if (perOperationLimit !== undefined && (perOperationLimit < 0 || perOperationLimit > 10)) {
      return res.status(400).json({
        error: 'Invalid per-operation limit',
        details: 'Per-operation limit must be between $0 and $10'
      });
    }

    if (alertThreshold !== undefined && (alertThreshold < 0 || alertThreshold > 1)) {
      return res.status(400).json({
        error: 'Invalid alert threshold',
        details: 'Alert threshold must be between 0 and 1'
      });
    }

    // Set the limits
    await costGuard.setCostLimits(repositoryId, {
      dailyLimit: dailyLimit || 3.0,
      weeklyLimit: weeklyLimit || 20.0,
      monthlyLimit: monthlyLimit || 80.0,
      perOperationLimit: perOperationLimit || 0.5,
      alertThreshold: alertThreshold || 0.8,
      enabled: enabled !== false
    });

    await loggingService.log({
      level: 'info',
      message: 'Cost limits updated',
      service: 'cost-guard-api',
      metadata: {
        userId: user.id,
        repositoryId,
        dailyLimit,
        weeklyLimit,
        monthlyLimit,
        perOperationLimit,
        alertThreshold,
        enabled
      }
    });

    res.json({
      message: 'Cost limits updated successfully',
      repositoryId,
      limits: costGuard.getCostLimits(repositoryId)
    });

  } catch (error) {
    await loggingService.log({
      level: 'error',
      message: 'Failed to set cost limits',
      service: 'cost-guard-api',
      metadata: {
        repositoryId: req.params.repositoryId,
        error: error instanceof Error ? error.message : 'Unknown error',
        endpoint: 'PUT /cost-guard/limits/:repositoryId',
        body: req.body
      }
    });

    res.status(500).json({
      error: 'Failed to set cost limits',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/cost-guard/alerts/:repositoryId
 * Get active alerts for a repository
 */
router.get('/cost-guard/alerts/:repositoryId', async (req, res) => {
  try {
    const { repositoryId } = req.params;
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return res.status(401).json({ error: 'Authorization header required' });
    }

    // Get user from Supabase auth
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      return res.status(401).json({ error: 'Invalid authentication token' });
    }

    // Verify repository ownership
    const { data: repository, error: repoError } = await supabaseClient
      .from('autonomous_repositories')
      .select('id')
      .eq('id', repositoryId)
      .eq('user_id', user.id)
      .single();

    if (repoError || !repository) {
      return res.status(404).json({ error: 'Repository not found or access denied' });
    }

    const activeAlerts = costGuard.getActiveAlerts(repositoryId);

    // Also get alerts from database for complete history
    const { data: dbAlerts, error: alertsError } = await supabaseClient
      .from('cost_alerts')
      .select('*')
      .eq('repository_id', repositoryId)
      .order('created_at', { ascending: false })
      .limit(50);

    if (alertsError) {
      throw alertsError;
    }

    res.json({
      repositoryId,
      activeAlerts,
      recentAlerts: dbAlerts || [],
      alertCounts: {
        total: (dbAlerts || []).length,
        unacknowledged: (dbAlerts || []).filter(a => !a.acknowledged).length,
        critical: (dbAlerts || []).filter(a => a.severity === 'critical').length,
        high: (dbAlerts || []).filter(a => a.severity === 'high').length
      }
    });

  } catch (error) {
    await loggingService.log({
      level: 'error',
      message: 'Failed to get cost alerts',
      service: 'cost-guard-api',
      metadata: {
        repositoryId: req.params.repositoryId,
        error: error instanceof Error ? error.message : 'Unknown error',
        endpoint: 'GET /cost-guard/alerts/:repositoryId'
      }
    });

    res.status(500).json({
      error: 'Failed to get cost alerts',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/cost-guard/alerts/:alertId/acknowledge
 * Acknowledge a cost alert
 */
router.post('/cost-guard/alerts/:alertId/acknowledge', async (req, res) => {
  try {
    const { alertId } = req.params;
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return res.status(401).json({ error: 'Authorization header required' });
    }

    // Get user from Supabase auth
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      return res.status(401).json({ error: 'Invalid authentication token' });
    }

    // Verify alert ownership through repository
    const { data: alert, error: alertError } = await supabaseClient
      .from('cost_alerts')
      .select(`
        *,
        autonomous_repositories!inner(user_id)
      `)
      .eq('id', alertId)
      .single();

    if (alertError || !alert || alert.autonomous_repositories.user_id !== user.id) {
      return res.status(404).json({ error: 'Alert not found or access denied' });
    }

    const acknowledged = await costGuard.acknowledgeAlert(alertId);

    if (acknowledged) {
      await loggingService.log({
        level: 'info',
        message: 'Cost alert acknowledged',
        service: 'cost-guard-api',
        metadata: {
          userId: user.id,
          alertId,
          repositoryId: alert.repository_id
        }
      });

      res.json({
        message: 'Alert acknowledged successfully',
        alertId
      });
    } else {
      res.status(404).json({
        error: 'Alert not found'
      });
    }

  } catch (error) {
    await loggingService.log({
      level: 'error',
      message: 'Failed to acknowledge alert',
      service: 'cost-guard-api',
      metadata: {
        alertId: req.params.alertId,
        error: error instanceof Error ? error.message : 'Unknown error',
        endpoint: 'POST /cost-guard/alerts/:alertId/acknowledge'
      }
    });

    res.status(500).json({
      error: 'Failed to acknowledge alert',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/cost-guard/breakdown/:repositoryId
 * Get cost breakdown for a repository
 */
router.get('/cost-guard/breakdown/:repositoryId', async (req, res) => {
  try {
    const { repositoryId } = req.params;
    const { timeframe = 'daily' } = req.query;
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      return res.status(401).json({ error: 'Authorization header required' });
    }

    // Get user from Supabase auth
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      return res.status(401).json({ error: 'Invalid authentication token' });
    }

    // Verify repository ownership
    const { data: repository, error: repoError } = await supabaseClient
      .from('autonomous_repositories')
      .select('id')
      .eq('id', repositoryId)
      .eq('user_id', user.id)
      .single();

    if (repoError || !repository) {
      return res.status(404).json({ error: 'Repository not found or access denied' });
    }

    if (!['daily', 'weekly', 'monthly'].includes(timeframe as string)) {
      return res.status(400).json({
        error: 'Invalid timeframe',
        details: 'timeframe must be one of: daily, weekly, monthly'
      });
    }

    const breakdown = await costGuard.getCostBreakdown(repositoryId, timeframe as any);

    res.json({
      repositoryId,
      timeframe,
      breakdown
    });

  } catch (error) {
    await loggingService.log({
      level: 'error',
      message: 'Failed to get cost breakdown',
      service: 'cost-guard-api',
      metadata: {
        repositoryId: req.params.repositoryId,
        timeframe: req.query.timeframe,
        error: error instanceof Error ? error.message : 'Unknown error',
        endpoint: 'GET /cost-guard/breakdown/:repositoryId'
      }
    });

    res.status(500).json({
      error: 'Failed to get cost breakdown',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/cost-guard/prediction/:repositoryId
 * Get cost prediction for a repository
 */
router.get('/cost-guard/prediction/:repositoryId', async (req, res) => {
  try {
    const { repositoryId } = req.params;
    const { timeframe = 'daily' } = req.query;
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      return res.status(401).json({ error: 'Authorization header required' });
    }

    // Get user from Supabase auth
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      return res.status(401).json({ error: 'Invalid authentication token' });
    }

    // Verify repository ownership
    const { data: repository, error: repoError } = await supabaseClient
      .from('autonomous_repositories')
      .select('id')
      .eq('id', repositoryId)
      .eq('user_id', user.id)
      .single();

    if (repoError || !repository) {
      return res.status(404).json({ error: 'Repository not found or access denied' });
    }

    if (!['daily', 'weekly', 'monthly'].includes(timeframe as string)) {
      return res.status(400).json({
        error: 'Invalid timeframe',
        details: 'timeframe must be one of: daily, weekly, monthly'
      });
    }

    const prediction = await costGuard.generateCostPrediction(repositoryId, timeframe as any);

    res.json({
      repositoryId,
      timeframe,
      prediction
    });

  } catch (error) {
    await loggingService.log({
      level: 'error',
      message: 'Failed to get cost prediction',
      service: 'cost-guard-api',
      metadata: {
        repositoryId: req.params.repositoryId,
        timeframe: req.query.timeframe,
        error: error instanceof Error ? error.message : 'Unknown error',
        endpoint: 'GET /cost-guard/prediction/:repositoryId'
      }
    });

    res.status(500).json({
      error: 'Failed to get cost prediction',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/cost-guard/check-operation
 * Check if an operation is allowed based on cost limits
 */
router.post('/cost-guard/check-operation', async (req, res) => {
  try {
    const { repositoryId, operationType, estimatedCost } = req.body;
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      return res.status(401).json({ error: 'Authorization header required' });
    }

    // Get user from Supabase auth
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      return res.status(401).json({ error: 'Invalid authentication token' });
    }

    if (!repositoryId || !operationType || estimatedCost === undefined) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: 'repositoryId, operationType, and estimatedCost are required'
      });
    }

    if (!['scan', 'improve', 'test', 'pr'].includes(operationType)) {
      return res.status(400).json({
        error: 'Invalid operation type',
        details: 'operationType must be one of: scan, improve, test, pr'
      });
    }

    // Verify repository ownership
    const { data: repository, error: repoError } = await supabaseClient
      .from('autonomous_repositories')
      .select('id')
      .eq('id', repositoryId)
      .eq('user_id', user.id)
      .single();

    if (repoError || !repository) {
      return res.status(404).json({ error: 'Repository not found or access denied' });
    }

    const result = await costGuard.checkOperationAllowed(repositoryId, operationType, estimatedCost);

    res.json({
      repositoryId,
      operationType,
      estimatedCost,
      allowed: result.allowed,
      reason: result.reason,
      alert: result.alert,
      isShutdown: costGuard.isRepositoryShutdown(repositoryId)
    });

  } catch (error) {
    await loggingService.log({
      level: 'error',
      message: 'Failed to check operation',
      service: 'cost-guard-api',
      metadata: {
        error: error instanceof Error ? error.message : 'Unknown error',
        endpoint: 'POST /cost-guard/check-operation',
        body: req.body
      }
    });

    res.status(500).json({
      error: 'Failed to check operation',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export { router as costGuardRouter };
