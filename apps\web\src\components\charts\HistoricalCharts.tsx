import * as React from "react"
import { cn } from "@/lib/utils"
import { Card } from "@/components/ui/card"
import { VStack, HStack } from "@/components/ui/layout"
import { Typography } from "@/components/ui/typography"
import { But<PERSON> } from "@/components/ui/button"
import { 
  CalendarIcon, 
  TrendingUpIcon, 
  TrendingDownIcon,
  FilterIcon,
  DownloadIcon
} from "@/components/ui/icon"

// Historical data interfaces
interface HistoricalDataPoint {
  date: Date
  transformations: number
  successRate: number
  averageScore: number
  averageTime: number
  codeQualityImprovement: number
}

interface TrendData {
  period: string
  value: number
  change: number
  trend: 'up' | 'down' | 'stable'
}

// Time period selector
interface TimePeriodSelectorProps {
  selectedPeriod: '7d' | '30d' | '90d' | '1y'
  onPeriodChange: (period: '7d' | '30d' | '90d' | '1y') => void
  className?: string
}

function TimePeriodSelector({ selectedPeriod, onPeriodChange, className }: TimePeriodSelectorProps) {
  const periods = [
    { key: '7d', label: '7 Days' },
    { key: '30d', label: '30 Days' },
    { key: '90d', label: '90 Days' },
    { key: '1y', label: '1 Year' }
  ] as const

  return (
    <HStack spacing="xs" className={className}>
      {periods.map(period => (
        <Button
          key={period.key}
          variant={selectedPeriod === period.key ? 'default' : 'outline'}
          size="sm"
          onClick={() => onPeriodChange(period.key)}
        >
          {period.label}
        </Button>
      ))}
    </HStack>
  )
}

// Simple line chart component
interface SimpleLineChartProps {
  data: HistoricalDataPoint[]
  dataKey: keyof HistoricalDataPoint
  title: string
  color?: string
  format?: (value: number) => string
  className?: string
}

function SimpleLineChart({ 
  data, 
  dataKey, 
  title, 
  color = 'rgb(99, 102, 241)', 
  format = (v) => v.toString(),
  className 
}: SimpleLineChartProps) {
  const chartRef = React.useRef<SVGSVGElement>(null)
  
  const values = data.map(d => typeof d[dataKey] === 'number' ? d[dataKey] as number : 0)
  const maxValue = Math.max(...values)
  const minValue = Math.min(...values)
  const range = maxValue - minValue || 1

  const points = data.map((point, index) => {
    const x = (index / (data.length - 1)) * 340 + 40
    const value = typeof point[dataKey] === 'number' ? point[dataKey] as number : 0
    const y = 160 - ((value - minValue) / range) * 120
    return { x, y, value }
  })

  return (
    <Card className={cn("p-6", className)}>
      <VStack spacing="md">
        <Typography variant="h6" className="text-foreground">
          {title}
        </Typography>
        
        <div className="relative h-48 w-full">
          <svg ref={chartRef} className="w-full h-full" viewBox="0 0 400 200">
            {/* Grid lines */}
            {[0, 25, 50, 75, 100].map(percent => {
              const y = 160 - (percent * 1.2)
              return (
                <line
                  key={percent}
                  x1="40"
                  y1={y}
                  x2="380"
                  y2={y}
                  stroke="currentColor"
                  strokeWidth="1"
                  className="text-muted opacity-20"
                />
              )
            })}
            
            {/* Y-axis labels */}
            {[0, 25, 50, 75, 100].map(percent => {
              const value = minValue + (range * percent / 100)
              const y = 165 - (percent * 1.2)
              return (
                <text
                  key={percent}
                  x="30"
                  y={y}
                  className="text-xs fill-muted-foreground"
                  textAnchor="end"
                >
                  {format(value)}
                </text>
              )
            })}
            
            {/* Data line */}
            {points.length > 1 && (
              <polyline
                points={points.map(p => `${p.x},${p.y}`).join(' ')}
                fill="none"
                stroke={color}
                strokeWidth="2"
              />
            )}
            
            {/* Data points */}
            {points.map((point, index) => (
              <circle
                key={index}
                cx={point.x}
                cy={point.y}
                r="4"
                fill={color}
              />
            ))}
            
            {/* Axes */}
            <line x1="40" y1="160" x2="380" y2="160" stroke="currentColor" strokeWidth="1" className="text-muted-foreground" />
            <line x1="40" y1="20" x2="40" y2="160" stroke="currentColor" strokeWidth="1" className="text-muted-foreground" />
          </svg>
        </div>
        
        <HStack spacing="sm" className="justify-between text-xs text-muted-foreground">
          <span>{data[0]?.date.toLocaleDateString()}</span>
          <span>{data[data.length - 1]?.date.toLocaleDateString()}</span>
        </HStack>
      </VStack>
    </Card>
  )
}

// Trend summary card
interface TrendSummaryProps {
  trends: TrendData[]
  className?: string
}

function TrendSummary({ trends, className }: TrendSummaryProps) {
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUpIcon className="w-4 h-4 text-success" />
      case 'down':
        return <TrendingDownIcon className="w-4 h-4 text-destructive" />
      default:
        return <div className="w-4 h-4 rounded-full bg-muted" />
    }
  }

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up':
        return 'text-success'
      case 'down':
        return 'text-destructive'
      default:
        return 'text-muted-foreground'
    }
  }

  return (
    <Card className={cn("p-6", className)}>
      <VStack spacing="md">
        <Typography variant="h6" className="text-foreground">
          Trend Summary
        </Typography>
        
        <VStack spacing="sm">
          {trends.map((trend, index) => (
            <HStack key={index} spacing="sm" className="justify-between items-center">
              <HStack spacing="xs" align="center">
                {getTrendIcon(trend.trend)}
                <Typography variant="body-sm" className="text-foreground">
                  {trend.period}
                </Typography>
              </HStack>
              
              <HStack spacing="xs" align="center">
                <Typography variant="body-sm" className="font-medium">
                  {trend.value.toLocaleString()}
                </Typography>
                <Typography variant="caption" className={getTrendColor(trend.trend)}>
                  {trend.change > 0 ? '+' : ''}{trend.change.toFixed(1)}%
                </Typography>
              </HStack>
            </HStack>
          ))}
        </VStack>
      </VStack>
    </Card>
  )
}

// Usage patterns heatmap
interface UsageHeatmapProps {
  data: Array<{ hour: number; day: number; value: number }>
  className?: string
}

function UsageHeatmap({ data, className }: UsageHeatmapProps) {
  const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
  const hours = Array.from({ length: 24 }, (_, i) => i)
  
  const maxValue = Math.max(...data.map(d => d.value))
  
  const getIntensity = (hour: number, day: number) => {
    const point = data.find(d => d.hour === hour && d.day === day)
    if (!point) return 0
    return point.value / maxValue
  }

  return (
    <Card className={cn("p-6", className)}>
      <VStack spacing="md">
        <Typography variant="h6" className="text-foreground">
          Usage Patterns
        </Typography>
        
        <div className="overflow-x-auto">
          <div className="grid grid-cols-25 gap-1 min-w-[600px]">
            {/* Header row */}
            <div></div>
            {hours.map(hour => (
              <div key={hour} className="text-xs text-muted-foreground text-center">
                {hour % 6 === 0 ? hour : ''}
              </div>
            ))}
            
            {/* Data rows */}
            {days.map((day, dayIndex) => (
              <React.Fragment key={day}>
                <div className="text-xs text-muted-foreground text-right pr-2">
                  {day}
                </div>
                {hours.map(hour => {
                  const intensity = getIntensity(hour, dayIndex)
                  return (
                    <div
                      key={`${day}-${hour}`}
                      className="w-4 h-4 rounded-sm"
                      style={{
                        backgroundColor: `rgba(99, 102, 241, ${intensity})`
                      }}
                      title={`${day} ${hour}:00 - ${data.find(d => d.hour === hour && d.day === dayIndex)?.value || 0} transformations`}
                    />
                  )
                })}
              </React.Fragment>
            ))}
          </div>
        </div>
        
        <HStack spacing="sm" className="justify-between items-center text-xs text-muted-foreground">
          <span>Less</span>
          <HStack spacing="xs">
            {[0, 0.25, 0.5, 0.75, 1].map(intensity => (
              <div
                key={intensity}
                className="w-3 h-3 rounded-sm"
                style={{ backgroundColor: `rgba(99, 102, 241, ${intensity})` }}
              />
            ))}
          </HStack>
          <span>More</span>
        </HStack>
      </VStack>
    </Card>
  )
}

// Main historical dashboard
interface HistoricalDashboardProps {
  data: HistoricalDataPoint[]
  className?: string
}

export function HistoricalDashboard({ data, className }: HistoricalDashboardProps) {
  const [selectedPeriod, setSelectedPeriod] = React.useState<'7d' | '30d' | '90d' | '1y'>('30d')
  
  // Filter data based on selected period
  const filteredData = React.useMemo(() => {
    const now = new Date()
    const periodDays = {
      '7d': 7,
      '30d': 30,
      '90d': 90,
      '1y': 365
    }
    
    const cutoffDate = new Date(now.getTime() - periodDays[selectedPeriod] * 24 * 60 * 60 * 1000)
    return data.filter(d => d.date >= cutoffDate)
  }, [data, selectedPeriod])

  // Generate trend data
  const trends: TrendData[] = [
    {
      period: 'Total Transformations',
      value: filteredData.reduce((sum, d) => sum + d.transformations, 0),
      change: 12.5,
      trend: 'up'
    },
    {
      period: 'Average Success Rate',
      value: filteredData.reduce((sum, d) => sum + d.successRate, 0) / filteredData.length,
      change: 8.2,
      trend: 'up'
    },
    {
      period: 'Code Quality Score',
      value: filteredData.reduce((sum, d) => sum + d.averageScore, 0) / filteredData.length,
      change: -2.1,
      trend: 'down'
    }
  ]

  // Generate usage heatmap data
  const heatmapData = React.useMemo(() => {
    const data: Array<{ hour: number; day: number; value: number }> = []
    for (let day = 0; day < 7; day++) {
      for (let hour = 0; hour < 24; hour++) {
        // Simulate usage patterns
        const baseValue = Math.random() * 10
        const workHourMultiplier = hour >= 9 && hour <= 17 ? 2 : 0.5
        const weekdayMultiplier = day >= 1 && day <= 5 ? 1.5 : 0.7
        data.push({
          hour,
          day,
          value: Math.floor(baseValue * workHourMultiplier * weekdayMultiplier)
        })
      }
    }
    return data
  }, [])

  const handleExport = () => {
    // Export functionality
    console.log('Exporting historical data...')
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <VStack spacing="sm">
        <HStack spacing="sm" className="justify-between items-start">
          <VStack spacing="xs">
            <Typography variant="h5" className="text-foreground">
              Historical Analytics
            </Typography>
            <Typography variant="body-sm" className="text-muted-foreground">
              Track trends and patterns over time
            </Typography>
          </VStack>
          
          <HStack spacing="sm">
            <Button variant="outline" size="sm" onClick={handleExport}>
              <DownloadIcon className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm">
              <FilterIcon className="w-4 h-4 mr-2" />
              Filter
            </Button>
          </HStack>
        </HStack>
        
        <TimePeriodSelector
          selectedPeriod={selectedPeriod}
          onPeriodChange={setSelectedPeriod}
        />
      </VStack>

      {/* Trend Summary */}
      <TrendSummary trends={trends} />

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SimpleLineChart
          data={filteredData}
          dataKey="transformations"
          title="Transformations Over Time"
          format={(v) => v.toLocaleString()}
        />
        
        <SimpleLineChart
          data={filteredData}
          dataKey="successRate"
          title="Success Rate Trend"
          color="rgb(34, 197, 94)"
          format={(v) => `${v.toFixed(1)}%`}
        />
        
        <SimpleLineChart
          data={filteredData}
          dataKey="averageScore"
          title="Average Quality Score"
          color="rgb(168, 85, 247)"
          format={(v) => `${v.toFixed(1)}/100`}
        />
        
        <SimpleLineChart
          data={filteredData}
          dataKey="averageTime"
          title="Average Processing Time"
          color="rgb(245, 158, 11)"
          format={(v) => `${v.toFixed(1)}s`}
        />
      </div>

      {/* Usage Patterns */}
      <UsageHeatmap data={heatmapData} />
    </div>
  )
}
