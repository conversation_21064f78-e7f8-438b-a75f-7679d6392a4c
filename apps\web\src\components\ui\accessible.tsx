import * as React from "react"
import { cn } from "@/lib/utils"
import { useAccessibleId, useAriaLive, useFocusTrap } from "@/utils/accessibility"

// Accessible button component
interface AccessibleButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'sm' | 'md' | 'lg'
  loading?: boolean
  loadingText?: string
  children: React.ReactNode
}

export function AccessibleButton({
  variant = 'default',
  size = 'md',
  loading = false,
  loadingText = 'Loading...',
  disabled,
  children,
  className,
  ...props
}: AccessibleButtonProps) {
  const isDisabled = disabled || loading
  
  return (
    <button
      {...props}
      disabled={isDisabled}
      aria-disabled={isDisabled}
      aria-busy={loading}
      className={cn(
        "inline-flex items-center justify-center rounded-md font-medium transition-colors",
        "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
        "disabled:pointer-events-none disabled:opacity-50",
        "min-h-[44px] min-w-[44px]", // WCAG touch target size
        {
          "h-10 px-4 py-2": size === 'sm',
          "h-12 px-6 py-3": size === 'md',
          "h-14 px-8 py-4": size === 'lg',
        },
        className
      )}
    >
      {loading ? (
        <>
          <span className="sr-only">{loadingText}</span>
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
          <span aria-hidden="true">{children}</span>
        </>
      ) : (
        children
      )}
    </button>
  )
}

// Accessible form field wrapper
interface AccessibleFieldProps {
  children: React.ReactNode
  label: string
  description?: string
  error?: string
  required?: boolean
  className?: string
}

export function AccessibleField({
  children,
  label,
  description,
  error,
  required = false,
  className
}: AccessibleFieldProps) {
  const fieldId = useAccessibleId('field')
  const descriptionId = useAccessibleId('description')
  const errorId = useAccessibleId('error')
  
  const childWithProps = React.cloneElement(children as React.ReactElement, {
    id: fieldId,
    'aria-describedby': [
      description ? descriptionId : '',
      error ? errorId : ''
    ].filter(Boolean).join(' ') || undefined,
    'aria-invalid': error ? 'true' : undefined,
    'aria-required': required
  })

  return (
    <div className={cn("space-y-2", className)}>
      <label 
        htmlFor={fieldId}
        className="text-sm font-medium text-foreground"
      >
        {label}
        {required && (
          <span className="text-destructive ml-1" aria-label="required">
            *
          </span>
        )}
      </label>
      
      {childWithProps}
      
      {description && (
        <p 
          id={descriptionId}
          className="text-sm text-muted-foreground"
        >
          {description}
        </p>
      )}
      
      {error && (
        <p 
          id={errorId}
          className="text-sm text-destructive"
          role="alert"
          aria-live="polite"
        >
          {error}
        </p>
      )}
    </div>
  )
}

// Accessible modal/dialog
interface AccessibleModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  description?: string
  children: React.ReactNode
  className?: string
}

export function AccessibleModal({
  isOpen,
  onClose,
  title,
  description,
  children,
  className
}: AccessibleModalProps) {
  const titleId = useAccessibleId('modal-title')
  const descriptionId = useAccessibleId('modal-description')
  const modalRef = useFocusTrap(isOpen)
  const announce = useAriaLive()
  
  React.useEffect(() => {
    if (isOpen) {
      announce(`Dialog opened: ${title}`)
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }
    
    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, title, announce])

  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      return () => document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, onClose])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-background/80 backdrop-blur-sm"
        onClick={onClose}
        aria-hidden="true"
      />
      
      {/* Modal */}
      <div
        ref={modalRef}
        role="dialog"
        aria-modal="true"
        aria-labelledby={titleId}
        aria-describedby={description ? descriptionId : undefined}
        className={cn(
          "absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2",
          "w-full max-w-lg bg-card border border-border rounded-lg shadow-lg p-6",
          className
        )}
      >
        <div className="space-y-4">
          <div className="space-y-2">
            <h2 id={titleId} className="text-lg font-semibold text-foreground">
              {title}
            </h2>
            {description && (
              <p id={descriptionId} className="text-sm text-muted-foreground">
                {description}
              </p>
            )}
          </div>
          
          {children}
        </div>
      </div>
    </div>
  )
}

// Accessible navigation menu
interface AccessibleMenuProps {
  items: Array<{
    id: string
    label: string
    href?: string
    onClick?: () => void
    disabled?: boolean
  }>
  orientation?: 'horizontal' | 'vertical'
  className?: string
}

export function AccessibleMenu({ 
  items, 
  orientation = 'vertical',
  className 
}: AccessibleMenuProps) {
  const [focusedIndex, setFocusedIndex] = React.useState(0)
  const menuRef = React.useRef<HTMLUListElement>(null)
  
  const handleKeyDown = (e: React.KeyboardEvent) => {
    const itemElements = Array.from(
      menuRef.current?.querySelectorAll('[role="menuitem"]') || []
    ) as HTMLElement[]
    
    switch (e.key) {
      case 'ArrowDown':
        if (orientation === 'vertical') {
          e.preventDefault()
          const nextIndex = (focusedIndex + 1) % items.length
          setFocusedIndex(nextIndex)
          itemElements[nextIndex]?.focus()
        }
        break
      case 'ArrowUp':
        if (orientation === 'vertical') {
          e.preventDefault()
          const prevIndex = focusedIndex === 0 ? items.length - 1 : focusedIndex - 1
          setFocusedIndex(prevIndex)
          itemElements[prevIndex]?.focus()
        }
        break
      case 'ArrowRight':
        if (orientation === 'horizontal') {
          e.preventDefault()
          const nextIndex = (focusedIndex + 1) % items.length
          setFocusedIndex(nextIndex)
          itemElements[nextIndex]?.focus()
        }
        break
      case 'ArrowLeft':
        if (orientation === 'horizontal') {
          e.preventDefault()
          const prevIndex = focusedIndex === 0 ? items.length - 1 : focusedIndex - 1
          setFocusedIndex(prevIndex)
          itemElements[prevIndex]?.focus()
        }
        break
      case 'Home':
        e.preventDefault()
        setFocusedIndex(0)
        itemElements[0]?.focus()
        break
      case 'End':
        e.preventDefault()
        const lastIndex = items.length - 1
        setFocusedIndex(lastIndex)
        itemElements[lastIndex]?.focus()
        break
    }
  }

  return (
    <ul
      ref={menuRef}
      role="menu"
      aria-orientation={orientation}
      onKeyDown={handleKeyDown}
      className={cn(
        "space-y-1",
        orientation === 'horizontal' && "flex space-y-0 space-x-1",
        className
      )}
    >
      {items.map((item, index) => (
        <li key={item.id} role="none">
          {item.href ? (
            <a
              href={item.href}
              role="menuitem"
              tabIndex={index === focusedIndex ? 0 : -1}
              aria-disabled={item.disabled}
              onFocus={() => setFocusedIndex(index)}
              className={cn(
                "block px-3 py-2 rounded-md text-sm transition-colors",
                "focus:outline-none focus:bg-accent focus:text-accent-foreground",
                item.disabled && "opacity-50 pointer-events-none"
              )}
            >
              {item.label}
            </a>
          ) : (
            <button
              role="menuitem"
              tabIndex={index === focusedIndex ? 0 : -1}
              disabled={item.disabled}
              onClick={item.onClick}
              onFocus={() => setFocusedIndex(index)}
              className={cn(
                "w-full text-left px-3 py-2 rounded-md text-sm transition-colors",
                "focus:outline-none focus:bg-accent focus:text-accent-foreground",
                "disabled:opacity-50 disabled:pointer-events-none"
              )}
            >
              {item.label}
            </button>
          )}
        </li>
      ))}
    </ul>
  )
}

// Accessible skip link
interface SkipLinkProps {
  href: string
  children: React.ReactNode
}

export function SkipLink({ href, children }: SkipLinkProps) {
  return (
    <a
      href={href}
      className={cn(
        "absolute left-4 top-4 z-50 px-4 py-2 bg-primary text-primary-foreground rounded-md",
        "transform -translate-y-16 focus:translate-y-0 transition-transform",
        "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
      )}
    >
      {children}
    </a>
  )
}

// Accessible live region for announcements
interface LiveRegionProps {
  message: string
  priority?: 'polite' | 'assertive'
  className?: string
}

export function LiveRegion({ 
  message, 
  priority = 'polite', 
  className 
}: LiveRegionProps) {
  return (
    <div
      aria-live={priority}
      aria-atomic="true"
      className={cn("sr-only", className)}
    >
      {message}
    </div>
  )
}

// Screen reader only text
interface ScreenReaderOnlyProps {
  children: React.ReactNode
  as?: React.ElementType
}

export function ScreenReaderOnly({ 
  children, 
  as: Component = 'span' 
}: ScreenReaderOnlyProps) {
  return (
    <Component className="sr-only">
      {children}
    </Component>
  )
}
