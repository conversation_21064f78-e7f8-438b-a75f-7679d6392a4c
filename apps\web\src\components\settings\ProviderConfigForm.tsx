import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import {
  Cpu,
  CheckCircle,
  AlertTriangle,
  Loader2,
  Eye,
  EyeOff,
  TestTube
} from 'lucide-react';
import { 
  ProviderConfig, 
  ProviderType, 
  PROVIDER_MODELS,
  validateProviderConfig 
} from '@/types/settings';

interface ProviderConfigFormProps {
  label: string;
  config: ProviderConfig;
  onChange: (config: ProviderConfig) => void;
  apiKey?: string;
  onApiKeyChange?: (apiKey: string) => void;
  showAdvanced?: boolean;
  testConnection?: boolean;
}

export const ProviderConfigForm: React.FC<ProviderConfigFormProps> = ({
  label,
  config,
  onChange,
  apiKey = '',
  onApiKeyChange,
  showAdvanced = false,
  testConnection = false,
}) => {
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(showAdvanced);
  const [showApiKey, setShowApiKey] = useState(false);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Validate configuration on change
  useEffect(() => {
    try {
      validateProviderConfig(config);
      setValidationErrors([]);
    } catch (error: any) {
      const errors = error.errors?.map((e: any) => e.message) || [error.message];
      setValidationErrors(errors);
    }
  }, [config]);

  const handleProviderTypeChange = (type: ProviderType) => {
    const defaultModel = PROVIDER_MODELS[type][0];
    onChange({
      ...config,
      type,
      model: defaultModel,
      // Reset provider-specific configs
      openai: type === 'openai' ? {} : undefined,
      vertexAI: type === 'vertex-ai' ? { projectId: '', location: 'us-central1' } : undefined,
      anthropic: type === 'anthropic' ? {} : undefined,
    });
  };

  const handleModelChange = (model: string) => {
    onChange({ ...config, model });
  };

  const handleTemperatureChange = (temperature: number[]) => {
    onChange({ ...config, temperature: temperature[0] });
  };

  const handleMaxTokensChange = (value: string) => {
    const maxTokens = parseInt(value);
    if (!isNaN(maxTokens) && maxTokens > 0) {
      onChange({ ...config, maxTokens });
    }
  };

  const handleSystemPromptChange = (systemPrompt: string) => {
    onChange({ ...config, systemPrompt: systemPrompt || undefined });
  };

  const handleProviderSpecificChange = (field: string, value: any) => {
    const providerKey = config.type === 'vertex-ai' ? 'vertexAI' : config.type;
    onChange({
      ...config,
      [providerKey]: {
        ...config[providerKey as keyof ProviderConfig],
        [field]: value,
      },
    });
  };

  const testProviderConnection = async () => {
    if (!testConnection || !onApiKeyChange) return;
    
    setIsTestingConnection(true);
    setConnectionStatus('idle');
    
    try {
      // Simulate API test - replace with actual test logic
      await new Promise(resolve => setTimeout(resolve, 2000));
      setConnectionStatus('success');
    } catch (error) {
      setConnectionStatus('error');
    } finally {
      setIsTestingConnection(false);
    }
  };

  const getProviderIcon = (type: ProviderType) => {
    switch (type) {
      case 'openai': return '🤖';
      case 'vertex-ai': return '🔷';
      case 'anthropic': return '🧠';
      default: return '⚡';
    }
  };

  const getProviderColor = (type: ProviderType) => {
    switch (type) {
      case 'openai': return 'bg-green-500/10 text-green-400 border-green-500/20';
      case 'vertex-ai': return 'bg-blue-500/10 text-blue-400 border-blue-500/20';
      case 'anthropic': return 'bg-purple-500/10 text-purple-400 border-purple-500/20';
      default: return 'bg-slate-500/10 text-slate-400 border-slate-500/20';
    }
  };

  return (
    <Card className="bg-slate-800/50 border-slate-700">
      <CardHeader>
        <CardTitle className="text-white flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Cpu className="w-5 h-5 text-purple-400" />
            {label}
            <Badge className={getProviderColor(config.type)}>
              {getProviderIcon(config.type)} {config.type}
            </Badge>
          </div>
          {validationErrors.length === 0 && (
            <CheckCircle className="w-5 h-5 text-green-400" />
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <Alert className="border-red-500/20 bg-red-500/10">
            <AlertTriangle className="h-4 w-4 text-red-500" />
            <AlertDescription className="text-red-200">
              <ul className="list-disc list-inside space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {/* Provider Type Selection */}
        <div className="space-y-2">
          <Label className="text-slate-300 font-medium">Provider</Label>
          <Select value={config.type} onValueChange={handleProviderTypeChange}>
            <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-600">
              <SelectItem value="openai" className="text-white">
                🤖 OpenAI
              </SelectItem>
              <SelectItem value="vertex-ai" className="text-white">
                🔷 Google Vertex AI
              </SelectItem>
              <SelectItem value="anthropic" className="text-white">
                🧠 Anthropic
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Model Selection */}
        <div className="space-y-2">
          <Label className="text-slate-300 font-medium">Model</Label>
          <Select value={config.model} onValueChange={handleModelChange}>
            <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-600">
              {PROVIDER_MODELS[config.type].map((model) => (
                <SelectItem key={model} value={model} className="text-white">
                  {model}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* API Key (if applicable) */}
        {onApiKeyChange && config.type !== 'vertex-ai' && (
          <div className="space-y-2">
            <Label className="text-slate-300 font-medium">
              API Key
              {config.type === 'openai' && ' (OpenAI)'}
              {config.type === 'anthropic' && ' (Anthropic)'}
            </Label>
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Input
                  type={showApiKey ? 'text' : 'password'}
                  value={apiKey}
                  onChange={(e) => onApiKeyChange(e.target.value)}
                  placeholder={`Enter ${config.type} API key...`}
                  className="bg-slate-700 border-slate-600 text-white pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1 h-8 w-8 p-0 text-slate-400 hover:text-white"
                  onClick={() => setShowApiKey(!showApiKey)}
                >
                  {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
              {testConnection && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={testProviderConnection}
                  disabled={!apiKey || isTestingConnection}
                  className="border-slate-600 text-slate-300 hover:text-white"
                >
                  {isTestingConnection ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <TestTube className="h-4 w-4" />
                  )}
                  Test
                </Button>
              )}
            </div>
            {connectionStatus === 'success' && (
              <p className="text-green-400 text-sm">✓ Connection successful</p>
            )}
            {connectionStatus === 'error' && (
              <p className="text-red-400 text-sm">✗ Connection failed</p>
            )}
          </div>
        )}

        {/* Basic Configuration */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label className="text-slate-300 font-medium">Temperature</Label>
            <div className="px-2">
              <Slider
                value={[config.temperature]}
                onValueChange={handleTemperatureChange}
                min={0}
                max={2}
                step={0.1}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-slate-400 mt-1">
                <span>0 (Focused)</span>
                <span className="text-white font-medium">{config.temperature}</span>
                <span>2 (Creative)</span>
              </div>
            </div>
          </div>
          <div className="space-y-2">
            <Label className="text-slate-300 font-medium">Max Tokens</Label>
            <Input
              type="number"
              value={config.maxTokens}
              onChange={(e) => handleMaxTokensChange(e.target.value)}
              min={1}
              max={32000}
              className="bg-slate-700 border-slate-600 text-white"
            />
          </div>
        </div>

        {/* Advanced Settings Toggle */}
        <div className="flex items-center justify-between pt-2 border-t border-slate-700">
          <Label className="text-slate-300 font-medium">Advanced Settings</Label>
          <Switch
            checked={isAdvancedOpen}
            onCheckedChange={setIsAdvancedOpen}
          />
        </div>

        {/* Advanced Settings */}
        {isAdvancedOpen && (
          <div className="space-y-4 p-4 bg-slate-900/50 rounded-lg border border-slate-700">
            {/* System Prompt */}
            <div className="space-y-2">
              <Label className="text-slate-300 font-medium">System Prompt (Optional)</Label>
              <Textarea
                value={config.systemPrompt || ''}
                onChange={(e) => handleSystemPromptChange(e.target.value)}
                placeholder="Custom system prompt for this provider..."
                className="bg-slate-700 border-slate-600 text-white min-h-[80px]"
              />
            </div>

            {/* Provider-specific settings */}
            {config.type === 'openai' && (
              <div className="space-y-4">
                <h4 className="text-slate-300 font-medium">OpenAI Settings</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-slate-400">Organization ID</Label>
                    <Input
                      value={config.openai?.organization || ''}
                      onChange={(e) => handleProviderSpecificChange('organization', e.target.value)}
                      placeholder="org-..."
                      className="bg-slate-700 border-slate-600 text-white"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-slate-400">Project ID</Label>
                    <Input
                      value={config.openai?.project || ''}
                      onChange={(e) => handleProviderSpecificChange('project', e.target.value)}
                      placeholder="proj_..."
                      className="bg-slate-700 border-slate-600 text-white"
                    />
                  </div>
                </div>
              </div>
            )}

            {config.type === 'vertex-ai' && (
              <div className="space-y-4">
                <h4 className="text-slate-300 font-medium">Vertex AI Settings</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-slate-400">Project ID</Label>
                    <Input
                      value={config.vertexAI?.projectId || ''}
                      onChange={(e) => handleProviderSpecificChange('projectId', e.target.value)}
                      placeholder="my-gcp-project"
                      className="bg-slate-700 border-slate-600 text-white"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-slate-400">Location</Label>
                    <Select
                      value={config.vertexAI?.location || 'us-central1'}
                      onValueChange={(value) => handleProviderSpecificChange('location', value)}
                    >
                      <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-slate-800 border-slate-600">
                        <SelectItem value="us-central1">us-central1</SelectItem>
                        <SelectItem value="us-east1">us-east1</SelectItem>
                        <SelectItem value="us-west1">us-west1</SelectItem>
                        <SelectItem value="europe-west1">europe-west1</SelectItem>
                        <SelectItem value="asia-southeast1">asia-southeast1</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            )}

            {config.type === 'anthropic' && (
              <div className="space-y-4">
                <h4 className="text-slate-300 font-medium">Anthropic Settings</h4>
                <div className="space-y-2">
                  <Label className="text-slate-400">API Version</Label>
                  <Input
                    value={config.anthropic?.version || '2023-06-01'}
                    onChange={(e) => handleProviderSpecificChange('version', e.target.value)}
                    placeholder="2023-06-01"
                    className="bg-slate-700 border-slate-600 text-white"
                  />
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
