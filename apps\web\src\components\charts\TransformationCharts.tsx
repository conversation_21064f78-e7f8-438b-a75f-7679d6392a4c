import * as React from "react"
import { cn } from "@/lib/utils"
import { Card } from "@/components/ui/card"
import { VStack, HStack } from "@/components/ui/layout"
import { Typography } from "@/components/ui/typography"
import { <PERSON><PERSON><PERSON>, CircularProgress } from "@/components/ui/loading-states"
import { 
  TrendingUpIcon, 
  CodeIcon, 
  CheckIcon, 
  ClockIcon,
  IterationIcon,
  ScoreIcon
} from "@/components/ui/icon"

// Data interfaces for transformation visualizations
interface TransformationData {
  id: string
  timestamp: Date
  iterations: number
  finalScore: number
  duration: number
  status: 'success' | 'failed' | 'in_progress'
  codeSize: {
    before: number
    after: number
  }
  metrics: {
    complexity: number
    maintainability: number
    performance: number
    readability: number
  }
}

interface ProgressData {
  iteration: number
  score: number
  timestamp: Date
  changes: string[]
}

// Success rate donut chart
interface SuccessRateChartProps {
  data: TransformationData[]
  className?: string
}

export function SuccessRateChart({ data, className }: SuccessRateChartProps) {
  const successCount = data.filter(t => t.status === 'success').length
  const failedCount = data.filter(t => t.status === 'failed').length
  const total = data.length
  const successRate = total > 0 ? (successCount / total) * 100 : 0

  return (
    <Card className={cn("p-6", className)}>
      <VStack spacing="md">
        <Typography variant="h6" className="text-foreground">
          Transformation Success Rate
        </Typography>
        
        <HStack spacing="lg" align="center">
          <CircularProgress
            value={successRate}
            size={120}
            strokeWidth={8}
            variant="success"
            showPercentage
            label="Success Rate"
          />
          
          <VStack spacing="sm" className="flex-1">
            <HStack spacing="sm" className="justify-between w-full">
              <HStack spacing="xs" align="center">
                <div className="w-3 h-3 bg-success rounded-full" />
                <Typography variant="body-sm">Successful</Typography>
              </HStack>
              <Typography variant="body-sm" className="font-medium">
                {successCount}
              </Typography>
            </HStack>
            
            <HStack spacing="sm" className="justify-between w-full">
              <HStack spacing="xs" align="center">
                <div className="w-3 h-3 bg-destructive rounded-full" />
                <Typography variant="body-sm">Failed</Typography>
              </HStack>
              <Typography variant="body-sm" className="font-medium">
                {failedCount}
              </Typography>
            </HStack>
            
            <HStack spacing="sm" className="justify-between w-full">
              <HStack spacing="xs" align="center">
                <div className="w-3 h-3 bg-warning rounded-full" />
                <Typography variant="body-sm">In Progress</Typography>
              </HStack>
              <Typography variant="body-sm" className="font-medium">
                {data.filter(t => t.status === 'in_progress').length}
              </Typography>
            </HStack>
          </VStack>
        </HStack>
      </VStack>
    </Card>
  )
}

// Iteration progress chart
interface IterationProgressProps {
  progressData: ProgressData[]
  className?: string
}

export function IterationProgress({ progressData, className }: IterationProgressProps) {
  const maxScore = Math.max(...progressData.map(p => p.score), 100)
  
  return (
    <Card className={cn("p-6", className)}>
      <VStack spacing="md">
        <HStack spacing="sm" className="justify-between items-center">
          <Typography variant="h6" className="text-foreground">
            Iteration Progress
          </Typography>
          <HStack spacing="xs" align="center">
            <IterationIcon className="w-4 h-4 text-primary" />
            <Typography variant="caption" className="text-muted-foreground">
              {progressData.length} iterations
            </Typography>
          </HStack>
        </HStack>
        
        <div className="relative h-48 w-full">
          {/* Simple line chart representation */}
          <svg className="w-full h-full" viewBox="0 0 400 200">
            {/* Grid lines */}
            {[0, 25, 50, 75, 100].map(y => (
              <line
                key={y}
                x1="40"
                y1={160 - (y * 1.2)}
                x2="380"
                y2={160 - (y * 1.2)}
                stroke="currentColor"
                strokeWidth="1"
                className="text-muted opacity-20"
              />
            ))}
            
            {/* Y-axis labels */}
            {[0, 25, 50, 75, 100].map(y => (
              <text
                key={y}
                x="30"
                y={165 - (y * 1.2)}
                className="text-xs fill-muted-foreground"
                textAnchor="end"
              >
                {y}
              </text>
            ))}
            
            {/* Data line */}
            {progressData.length > 1 && (
              <polyline
                points={progressData.map((point, index) => {
                  const x = 40 + (index * (340 / (progressData.length - 1)))
                  const y = 160 - (point.score * 1.2)
                  return `${x},${y}`
                }).join(' ')}
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                className="text-primary"
              />
            )}
            
            {/* Data points */}
            {progressData.map((point, index) => {
              const x = 40 + (index * (340 / Math.max(progressData.length - 1, 1)))
              const y = 160 - (point.score * 1.2)
              return (
                <circle
                  key={index}
                  cx={x}
                  cy={y}
                  r="4"
                  fill="currentColor"
                  className="text-primary"
                />
              )
            })}
            
            {/* X-axis */}
            <line
              x1="40"
              y1="160"
              x2="380"
              y2="160"
              stroke="currentColor"
              strokeWidth="1"
              className="text-muted-foreground"
            />
            
            {/* Y-axis */}
            <line
              x1="40"
              y1="20"
              x2="40"
              y2="160"
              stroke="currentColor"
              strokeWidth="1"
              className="text-muted-foreground"
            />
          </svg>
        </div>
        
        <HStack spacing="sm" className="justify-between text-xs text-muted-foreground">
          <span>Iteration 1</span>
          <span>Final Score: {progressData[progressData.length - 1]?.score || 0}</span>
        </HStack>
      </VStack>
    </Card>
  )
}

// Code metrics comparison
interface CodeMetricsProps {
  beforeMetrics: TransformationData['metrics']
  afterMetrics: TransformationData['metrics']
  className?: string
}

export function CodeMetricsComparison({ beforeMetrics, afterMetrics, className }: CodeMetricsProps) {
  const metrics = [
    { key: 'complexity', label: 'Complexity', before: beforeMetrics.complexity, after: afterMetrics.complexity },
    { key: 'maintainability', label: 'Maintainability', before: beforeMetrics.maintainability, after: afterMetrics.maintainability },
    { key: 'performance', label: 'Performance', before: beforeMetrics.performance, after: afterMetrics.performance },
    { key: 'readability', label: 'Readability', before: beforeMetrics.readability, after: afterMetrics.readability }
  ]

  const getImprovement = (before: number, after: number) => {
    const improvement = ((after - before) / before) * 100
    return improvement
  }

  const getImprovementColor = (improvement: number) => {
    if (improvement > 5) return 'text-success'
    if (improvement < -5) return 'text-destructive'
    return 'text-muted-foreground'
  }

  return (
    <Card className={cn("p-6", className)}>
      <VStack spacing="md">
        <Typography variant="h6" className="text-foreground">
          Code Quality Metrics
        </Typography>
        
        <VStack spacing="sm">
          {metrics.map(metric => {
            const improvement = getImprovement(metric.before, metric.after)
            return (
              <div key={metric.key} className="space-y-2">
                <HStack spacing="sm" className="justify-between items-center">
                  <Typography variant="body-sm" className="text-foreground">
                    {metric.label}
                  </Typography>
                  <HStack spacing="xs" align="center">
                    <Typography variant="caption" className="text-muted-foreground">
                      {metric.before} → {metric.after}
                    </Typography>
                    <Typography variant="caption" className={getImprovementColor(improvement)}>
                      {improvement > 0 ? '+' : ''}{improvement.toFixed(1)}%
                    </Typography>
                  </HStack>
                </HStack>
                
                <div className="relative">
                  <ProgressBar
                    value={metric.before}
                    max={100}
                    variant="default"
                    size="sm"
                    showPercentage={false}
                    className="opacity-50"
                  />
                  <ProgressBar
                    value={metric.after}
                    max={100}
                    variant={improvement > 0 ? 'success' : improvement < 0 ? 'warning' : 'default'}
                    size="sm"
                    showPercentage={false}
                    className="absolute top-0 left-0"
                  />
                </div>
              </div>
            )
          })}
        </VStack>
      </VStack>
    </Card>
  )
}

// Transformation timeline
interface TransformationTimelineProps {
  transformations: TransformationData[]
  className?: string
}

export function TransformationTimeline({ transformations, className }: TransformationTimelineProps) {
  const sortedTransformations = [...transformations]
    .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
    .slice(0, 10) // Show last 10

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckIcon className="w-4 h-4 text-success" />
      case 'failed':
        return <div className="w-4 h-4 rounded-full bg-destructive" />
      case 'in_progress':
        return <div className="w-4 h-4 rounded-full bg-warning animate-pulse" />
      default:
        return <div className="w-4 h-4 rounded-full bg-muted" />
    }
  }

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`
    return `${(ms / 60000).toFixed(1)}m`
  }

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date()
    const diff = now.getTime() - timestamp.getTime()
    const hours = Math.floor(diff / 3600000)
    const minutes = Math.floor((diff % 3600000) / 60000)
    
    if (hours === 0) return `${minutes}m ago`
    if (hours < 24) return `${hours}h ago`
    return timestamp.toLocaleDateString()
  }

  return (
    <Card className={cn("p-6", className)}>
      <VStack spacing="md">
        <HStack spacing="sm" className="justify-between items-center">
          <Typography variant="h6" className="text-foreground">
            Recent Transformations
          </Typography>
          <ClockIcon className="w-4 h-4 text-muted-foreground" />
        </HStack>
        
        <VStack spacing="sm" className="max-h-80 overflow-y-auto">
          {sortedTransformations.length === 0 ? (
            <Typography variant="body-sm" className="text-muted-foreground text-center py-8">
              No transformations yet
            </Typography>
          ) : (
            sortedTransformations.map((transformation) => (
              <HStack key={transformation.id} spacing="sm" align="start" className="p-3 rounded-lg border border-border hover:bg-muted/50">
                <div className="mt-1">
                  {getStatusIcon(transformation.status)}
                </div>
                
                <VStack spacing="xs" className="flex-1 min-w-0">
                  <HStack spacing="sm" className="justify-between items-start">
                    <Typography variant="body-sm" className="text-foreground">
                      Transformation #{transformation.id.slice(-6)}
                    </Typography>
                    <Typography variant="caption" className="text-muted-foreground">
                      {formatTimestamp(transformation.timestamp)}
                    </Typography>
                  </HStack>
                  
                  <HStack spacing="md" className="text-xs text-muted-foreground">
                    <HStack spacing="xs" align="center">
                      <IterationIcon className="w-3 h-3" />
                      <span>{transformation.iterations} iterations</span>
                    </HStack>
                    <HStack spacing="xs" align="center">
                      <ScoreIcon className="w-3 h-3" />
                      <span>{transformation.finalScore}/100</span>
                    </HStack>
                    <HStack spacing="xs" align="center">
                      <ClockIcon className="w-3 h-3" />
                      <span>{formatDuration(transformation.duration)}</span>
                    </HStack>
                  </HStack>
                </VStack>
              </HStack>
            ))
          )}
        </VStack>
      </VStack>
    </Card>
  )
}

// Main transformation dashboard
interface TransformationDashboardProps {
  transformations: TransformationData[]
  currentProgress?: ProgressData[]
  className?: string
}

export function TransformationDashboard({
  transformations,
  currentProgress = [],
  className
}: TransformationDashboardProps) {
  const latestTransformation = transformations[transformations.length - 1]
  
  return (
    <div className={cn("space-y-6", className)}>
      <VStack spacing="sm">
        <Typography variant="h5" className="text-foreground">
          Transformation Analytics
        </Typography>
        <Typography variant="body-sm" className="text-muted-foreground">
          Track transformation progress, success rates, and code quality improvements
        </Typography>
      </VStack>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SuccessRateChart data={transformations} />
        {currentProgress.length > 0 && (
          <IterationProgress progressData={currentProgress} />
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {latestTransformation && (
          <CodeMetricsComparison
            beforeMetrics={latestTransformation.metrics}
            afterMetrics={{
              complexity: latestTransformation.metrics.complexity * 0.8,
              maintainability: latestTransformation.metrics.maintainability * 1.2,
              performance: latestTransformation.metrics.performance * 1.1,
              readability: latestTransformation.metrics.readability * 1.15
            }}
          />
        )}
        <TransformationTimeline transformations={transformations} />
      </div>
    </div>
  )
}
