import { render, screen } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { axe, toHaveNoViolations } from 'jest-axe';
import Dashboard from '../pages/Dashboard';
import DashboardIntegration from '../components/dashboard/DashboardIntegration';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock hooks and components that might cause issues in tests
jest.mock('../hooks/useCodeTransformation', () => ({
  useCodeTransformation: () => ({
    isRunning: false,
    transformedCode: '',
    diffContent: '',
    logs: [],
    handleRunLoop: jest.fn(),
    stopTransformation: jest.fn(),
  }),
}));

jest.mock('../hooks/useTransformationHistory', () => ({
  useTransformationHistory: () => ({
    handleApplyChanges: jest.fn(),
    handleDownloadCode: jest.fn(),
  }),
}));

jest.mock('../hooks/useKeyboardShortcuts', () => ({
  useKeyboardShortcuts: () => {},
}));

jest.mock('../components/CodeEditor', () => ({
  CodeEditor: ({ value, onChange }: any) => (
    <textarea
      value={value}
      onChange={(e) => onChange(e.target.value)}
      aria-label="Code editor"
      data-testid="code-editor"
    />
  ),
}));

jest.mock('../components/DiffViewer', () => ({
  DiffViewer: ({ diffContent }: any) => (
    <div role="region" aria-label="Code diff viewer" data-testid="diff-viewer">
      {diffContent || 'No changes to display'}
    </div>
  ),
}));

jest.mock('../components/AgentLog', () => ({
  AgentLog: ({ logs }: any) => (
    <div role="log" aria-label="Agent logs" data-testid="agent-log">
      {logs.length === 0 ? 'No logs available' : logs.join('\n')}
    </div>
  ),
}));

jest.mock('../components/ControlPanel', () => ({
  ControlPanel: ({ isRunning, onRunLoop, onStop }: any) => (
    <div role="group" aria-label="Control panel">
      <button onClick={onRunLoop} disabled={isRunning} aria-label="Start transformation">
        {isRunning ? 'Running...' : 'Start'}
      </button>
      <button onClick={onStop} disabled={!isRunning} aria-label="Stop transformation">
        Stop
      </button>
    </div>
  ),
}));

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('Dashboard Accessibility', () => {
  test('Dashboard should have no accessibility violations', async () => {
    const { container } = renderWithRouter(<Dashboard />);
    
    // Wait for any async content to load
    await screen.findByRole('main');
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('Dashboard should have proper heading structure', async () => {
    renderWithRouter(<Dashboard />);
    
    // Should have main heading
    expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument();
    expect(screen.getByRole('heading', { name: /metamorphic reactor/i })).toBeInTheDocument();
  });

  test('Dashboard should have proper landmark regions', async () => {
    renderWithRouter(<Dashboard />);
    
    // Should have main landmarks
    expect(screen.getByRole('banner')).toBeInTheDocument(); // header
    expect(screen.getByRole('main')).toBeInTheDocument(); // main content
    expect(screen.getByRole('navigation')).toBeInTheDocument(); // nav
  });

  test('Dashboard tabs should be accessible', async () => {
    renderWithRouter(<Dashboard />);
    
    // Should have tablist
    expect(screen.getByRole('tablist')).toBeInTheDocument();
    
    // Should have tabs with proper labels
    const tabs = screen.getAllByRole('tab');
    expect(tabs.length).toBeGreaterThan(0);
    
    // Each tab should have accessible name
    tabs.forEach(tab => {
      expect(tab).toHaveAccessibleName();
    });
  });

  test('Dashboard buttons should have accessible names', async () => {
    renderWithRouter(<Dashboard />);
    
    const buttons = screen.getAllByRole('button');
    
    // Each button should have accessible name
    buttons.forEach(button => {
      expect(button).toHaveAccessibleName();
    });
  });

  test('DashboardIntegration should have no accessibility violations', async () => {
    const { container } = render(<DashboardIntegration />);
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('DashboardIntegration should have proper heading structure', async () => {
    render(<DashboardIntegration />);
    
    // Should have main heading
    expect(screen.getByRole('heading', { level: 2 })).toBeInTheDocument();
    expect(screen.getByRole('heading', { name: /dashboard/i })).toBeInTheDocument();
  });

  test('Color contrast should be sufficient', async () => {
    const { container } = renderWithRouter(<Dashboard />);
    
    // Run axe with color-contrast rule specifically
    const results = await axe(container, {
      rules: {
        'color-contrast': { enabled: true }
      }
    });
    
    expect(results).toHaveNoViolations();
  });

  test('Interactive elements should be keyboard accessible', async () => {
    renderWithRouter(<Dashboard />);
    
    // All interactive elements should be focusable
    const interactiveElements = screen.getAllByRole('button');
    const tabs = screen.getAllByRole('tab');
    
    [...interactiveElements, ...tabs].forEach(element => {
      expect(element).not.toHaveAttribute('tabindex', '-1');
    });
  });

  test('Images and icons should have proper alt text or be marked decorative', async () => {
    const { container } = renderWithRouter(<Dashboard />);
    
    // Check for images without alt text
    const images = container.querySelectorAll('img');
    images.forEach(img => {
      expect(img).toHaveAttribute('alt');
    });

    // Check for decorative icons
    const decorativeIcons = container.querySelectorAll('[aria-hidden="true"]');
    expect(decorativeIcons.length).toBeGreaterThan(0);
  });

  test('Form elements should have proper labels', async () => {
    renderWithRouter(<Dashboard />);
    
    // Code editor should have label
    const codeEditor = screen.getByTestId('code-editor');
    expect(codeEditor).toHaveAccessibleName();
  });

  test('Status information should be announced to screen readers', async () => {
    renderWithRouter(<Dashboard />);
    
    // Log region should be present for status updates
    const logRegion = screen.getByTestId('agent-log');
    expect(logRegion).toHaveAttribute('role', 'log');
  });
});

describe('Responsive Design Accessibility', () => {
  test('Dashboard should be accessible at mobile viewport', async () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 320,
    });

    const { container } = renderWithRouter(<Dashboard />);
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('Dashboard should be accessible at tablet viewport', async () => {
    // Mock tablet viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 768,
    });

    const { container } = renderWithRouter(<Dashboard />);
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('Dashboard should be accessible at desktop viewport', async () => {
    // Mock desktop viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1280,
    });

    const { container } = renderWithRouter(<Dashboard />);
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
