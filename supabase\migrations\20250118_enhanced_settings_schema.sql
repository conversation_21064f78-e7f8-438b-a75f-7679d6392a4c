-- Enhanced Settings Schema Migration
-- Adds comprehensive dual-agent SDK configuration support
-- Migration: 20250118_enhanced_settings_schema

BEGIN;

-- Add new columns to settings table for comprehensive configuration
ALTER TABLE public.settings 
ADD COLUMN IF NOT EXISTS provider_configs JSONB DEFAULT '{
  "planner": {
    "type": "openai",
    "model": "gpt-4o",
    "temperature": 0.7,
    "maxTokens": 2000
  },
  "critic": {
    "type": "openai", 
    "model": "gpt-4o",
    "temperature": 0.3,
    "maxTokens": 1500
  },
  "fallback_providers": []
}'::jsonb;

-- Cost guard configuration
ALTER TABLE public.settings 
ADD COLUMN IF NOT EXISTS cost_guard_config JSONB DEFAULT '{
  "enabled": true,
  "maxCostPerLoop": 3.0,
  "alertThresholds": {
    "nearLimitWarning": 0.8,
    "costPerHour": 10.0
  }
}'::jsonb;

-- Token monitoring configuration  
ALTER TABLE public.settings 
ADD COLUMN IF NOT EXISTS token_monitor_config J<PERSON><PERSON><PERSON> DEFAULT '{
  "enabled": true,
  "maxEvents": 10000,
  "alertThresholds": {
    "tokensPerHour": 1000000,
    "costPerHour": 10.0,
    "requestsPerMinute": 100
  },
  "logLevel": "basic"
}'::jsonb;

-- Failover configuration
ALTER TABLE public.settings 
ADD COLUMN IF NOT EXISTS failover_config JSONB DEFAULT '{
  "enabled": false,
  "maxFailovers": 3,
  "failoverDelay": 1000,
  "healthCheckInterval": 60000,
  "retryPrimaryAfter": 300000
}'::jsonb;

-- Retry configuration
ALTER TABLE public.settings 
ADD COLUMN IF NOT EXISTS retry_config JSONB DEFAULT '{
  "maxAttempts": 5,
  "baseDelay": 1000,
  "maxDelay": 30000,
  "jitter": true
}'::jsonb;

-- Performance configuration
ALTER TABLE public.settings 
ADD COLUMN IF NOT EXISTS performance_config JSONB DEFAULT '{
  "streamingEnabled": true,
  "parallelExecution": false,
  "maxConcurrency": 3,
  "adaptiveStrategy": true
}'::jsonb;

-- Vertex AI configuration (separate from provider_configs for security)
ALTER TABLE public.settings 
ADD COLUMN IF NOT EXISTS vertex_ai_project_id TEXT,
ADD COLUMN IF NOT EXISTS vertex_ai_location TEXT DEFAULT 'us-central1',
ADD COLUMN IF NOT EXISTS vertex_ai_credentials_encrypted TEXT;

-- Enhanced API key management
ALTER TABLE public.settings 
ADD COLUMN IF NOT EXISTS google_api_key_encrypted TEXT;

-- Configuration metadata
ALTER TABLE public.settings 
ADD COLUMN IF NOT EXISTS config_version INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS last_validated_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS validation_errors JSONB DEFAULT '[]'::jsonb;

-- Create indexes for JSONB queries
CREATE INDEX IF NOT EXISTS idx_settings_provider_configs_gin ON public.settings USING gin (provider_configs);
CREATE INDEX IF NOT EXISTS idx_settings_cost_guard_config_gin ON public.settings USING gin (cost_guard_config);
CREATE INDEX IF NOT EXISTS idx_settings_config_version ON public.settings(config_version);

-- Create function to validate provider configuration
CREATE OR REPLACE FUNCTION validate_provider_config(config JSONB)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check required fields
  IF NOT (config ? 'type' AND config ? 'model' AND config ? 'temperature' AND config ? 'maxTokens') THEN
    RETURN FALSE;
  END IF;
  
  -- Validate provider type
  IF NOT (config->>'type' IN ('openai', 'vertex-ai', 'anthropic')) THEN
    RETURN FALSE;
  END IF;
  
  -- Validate temperature range
  IF NOT ((config->>'temperature')::numeric BETWEEN 0 AND 2) THEN
    RETURN FALSE;
  END IF;
  
  -- Validate maxTokens
  IF NOT ((config->>'maxTokens')::integer > 0 AND (config->>'maxTokens')::integer <= 32000) THEN
    RETURN FALSE;
  END IF;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Create function to migrate legacy settings to new format
CREATE OR REPLACE FUNCTION migrate_legacy_settings()
RETURNS VOID AS $$
DECLARE
  setting_record RECORD;
  new_provider_configs JSONB;
BEGIN
  -- Migrate existing settings that don't have provider_configs
  FOR setting_record IN 
    SELECT * FROM public.settings 
    WHERE provider_configs = '{
      "planner": {
        "type": "openai",
        "model": "gpt-4o", 
        "temperature": 0.7,
        "maxTokens": 2000
      },
      "critic": {
        "type": "openai",
        "model": "gpt-4o",
        "temperature": 0.3,
        "maxTokens": 1500
      },
      "fallback_providers": []
    }'::jsonb
  LOOP
    -- Build new provider configs from legacy fields
    new_provider_configs := jsonb_build_object(
      'planner', jsonb_build_object(
        'type', CASE 
          WHEN setting_record.planner_model LIKE 'gpt%' THEN 'openai'
          WHEN setting_record.planner_model LIKE 'claude%' THEN 'anthropic'
          WHEN setting_record.planner_model LIKE 'gemini%' THEN 'vertex-ai'
          ELSE 'openai'
        END,
        'model', setting_record.planner_model,
        'temperature', 0.7,
        'maxTokens', 2000
      ),
      'critic', jsonb_build_object(
        'type', CASE 
          WHEN setting_record.critic_model LIKE 'gpt%' THEN 'openai'
          WHEN setting_record.critic_model LIKE 'claude%' THEN 'anthropic'
          WHEN setting_record.critic_model LIKE 'gemini%' THEN 'vertex-ai'
          ELSE 'openai'
        END,
        'model', setting_record.critic_model,
        'temperature', 0.3,
        'maxTokens', 1500
      ),
      'fallback_providers', '[]'::jsonb
    );
    
    -- Update the record
    UPDATE public.settings 
    SET 
      provider_configs = new_provider_configs,
      config_version = 2,
      updated_at = NOW()
    WHERE id = setting_record.id;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to validate settings on update
CREATE OR REPLACE FUNCTION validate_settings_trigger()
RETURNS TRIGGER AS $$
DECLARE
  validation_errors JSONB := '[]'::jsonb;
  planner_config JSONB;
  critic_config JSONB;
BEGIN
  -- Extract provider configs
  planner_config := NEW.provider_configs->'planner';
  critic_config := NEW.provider_configs->'critic';
  
  -- Validate planner config
  IF NOT validate_provider_config(planner_config) THEN
    validation_errors := validation_errors || jsonb_build_array('Invalid planner configuration');
  END IF;
  
  -- Validate critic config
  IF NOT validate_provider_config(critic_config) THEN
    validation_errors := validation_errors || jsonb_build_array('Invalid critic configuration');
  END IF;
  
  -- Validate cost guard config
  IF NEW.cost_guard_config->>'maxCostPerLoop' IS NOT NULL THEN
    IF NOT ((NEW.cost_guard_config->>'maxCostPerLoop')::numeric > 0) THEN
      validation_errors := validation_errors || jsonb_build_array('Invalid cost limit');
    END IF;
  END IF;
  
  -- Store validation results
  NEW.validation_errors := validation_errors;
  NEW.last_validated_at := NOW();
  NEW.updated_at := NOW();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS settings_validation_trigger ON public.settings;
CREATE TRIGGER settings_validation_trigger
  BEFORE INSERT OR UPDATE ON public.settings
  FOR EACH ROW
  EXECUTE FUNCTION validate_settings_trigger();

-- Run migration for existing data
SELECT migrate_legacy_settings();

-- Create view for easy settings access with computed fields
CREATE OR REPLACE VIEW public.settings_view AS
SELECT 
  s.*,
  (s.provider_configs->'planner'->>'type') as planner_provider_type,
  (s.provider_configs->'planner'->>'model') as planner_provider_model,
  (s.provider_configs->'critic'->>'type') as critic_provider_type,
  (s.provider_configs->'critic'->>'model') as critic_provider_model,
  (s.cost_guard_config->>'enabled')::boolean as cost_guard_enabled,
  (s.cost_guard_config->>'maxCostPerLoop')::numeric as max_cost_per_loop,
  (s.failover_config->>'enabled')::boolean as failover_enabled,
  (s.performance_config->>'streamingEnabled')::boolean as streaming_enabled,
  (s.performance_config->>'parallelExecution')::boolean as parallel_execution_enabled,
  jsonb_array_length(s.validation_errors) as validation_error_count
FROM public.settings s;

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON public.settings TO authenticated;
GRANT SELECT ON public.settings_view TO authenticated;

-- Add RLS policies for new columns
ALTER TABLE public.settings ENABLE ROW LEVEL SECURITY;

-- Update existing RLS policy to include new columns
DROP POLICY IF EXISTS "Users can manage their own settings" ON public.settings;
CREATE POLICY "Users can manage their own settings" ON public.settings
  FOR ALL USING (auth.uid() = user_id);

-- Create policy for settings view
CREATE POLICY "Users can view their own settings" ON public.settings_view
  FOR SELECT USING (auth.uid() = user_id);

COMMIT;
