import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  BarChart3, 
  Activity, 
  CheckCircle, 
  XCircle,
  Clock,
  Zap,
  TrendingUp,
  AlertTriangle,
  RefreshCw,
  Target,
  Users,
  Globe
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { HStack, VStack } from '@/components/ui/layout';
import { Typography } from '@/components/ui/typography';

interface UsageStatistics {
  totalSessions: number;
  successfulSessions: number;
  failedSessions: number;
  averageScore: number;
  averageIterations: number;
  totalCost: number;
  averageCost: number;
  topFeatures: Array<{
    feature: string;
    count: number;
  }>;
  period: string;
}

interface RateLimitInfo {
  limit: number;
  remaining: number;
  resetTime: number;
  windowStart: number;
}

interface QuotaInfo {
  used: number;
  limit: number;
  period: string;
  resetDate: string;
}

interface UsageStatisticsPanelProps {
  className?: string;
  refreshInterval?: number;
  period?: '7d' | '30d' | '90d' | '1y';
}

export const UsageStatisticsPanel: React.FC<UsageStatisticsPanelProps> = ({
  className,
  refreshInterval = 60000, // 1 minute
  period = '30d'
}) => {
  const [statistics, setStatistics] = useState<UsageStatistics>({
    totalSessions: 0,
    successfulSessions: 0,
    failedSessions: 0,
    averageScore: 0,
    averageIterations: 0,
    totalCost: 0,
    averageCost: 0,
    topFeatures: [],
    period: period
  });
  
  const [rateLimits, setRateLimits] = useState<RateLimitInfo>({
    limit: 60,
    remaining: 60,
    resetTime: Date.now() + 60000,
    windowStart: Date.now()
  });

  const [quotaInfo, setQuotaInfo] = useState<QuotaInfo>({
    used: 0,
    limit: 1000,
    period: 'monthly',
    resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1).toISOString()
  });

  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [error, setError] = useState<string | null>(null);

  // Fetch usage statistics
  const fetchStatistics = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Fetch analytics data
      const analyticsResponse = await fetch(`/api/analytics/usage?period=${period}`);
      if (analyticsResponse.ok) {
        const analyticsData = await analyticsResponse.json();
        setStatistics({
          ...analyticsData,
          failedSessions: analyticsData.totalSessions - analyticsData.successfulSessions
        });
      }

      // Fetch rate limit info from headers or API status
      const statusResponse = await fetch('/api/status');
      if (statusResponse.ok) {
        const statusData = await statusResponse.json();
        
        // Extract rate limit info from response headers or status data
        const rateLimitHeader = statusResponse.headers.get('X-RateLimit-Limit');
        const rateLimitRemaining = statusResponse.headers.get('X-RateLimit-Remaining');
        const rateLimitReset = statusResponse.headers.get('X-RateLimit-Reset');
        
        if (rateLimitHeader && rateLimitRemaining && rateLimitReset) {
          setRateLimits({
            limit: parseInt(rateLimitHeader),
            remaining: parseInt(rateLimitRemaining),
            resetTime: parseInt(rateLimitReset) * 1000,
            windowStart: Date.now()
          });
        }

        // Set quota info from status data
        if (statusData.limits) {
          setQuotaInfo(prev => ({
            ...prev,
            limit: statusData.limits.maxRequests || prev.limit
          }));
        }
      }

      // Fetch billing usage for quota information
      const billingResponse = await fetch('/api/billing/usage');
      if (billingResponse.ok) {
        const billingData = await billingResponse.json();
        if (billingData.data) {
          setQuotaInfo(prev => ({
            ...prev,
            used: billingData.data.total_usage || 0,
            limit: billingData.data.limit || prev.limit,
            resetDate: billingData.data.current_period?.end || prev.resetDate
          }));
        }
      }

    } catch (error) {
      console.error('Failed to fetch usage statistics:', error);
      setError('Failed to load usage statistics');
    } finally {
      setIsLoading(false);
      setLastUpdated(new Date());
    }
  };

  // Auto-refresh statistics
  useEffect(() => {
    fetchStatistics();
    const interval = setInterval(fetchStatistics, refreshInterval);
    return () => clearInterval(interval);
  }, [refreshInterval, period]);

  const getSuccessRate = (): number => {
    if (statistics.totalSessions === 0) return 0;
    return (statistics.successfulSessions / statistics.totalSessions) * 100;
  };

  const getRateLimitPercentage = (): number => {
    return ((rateLimits.limit - rateLimits.remaining) / rateLimits.limit) * 100;
  };

  const getQuotaPercentage = (): number => {
    return (quotaInfo.used / quotaInfo.limit) * 100;
  };

  const getTimeUntilReset = (): string => {
    const now = Date.now();
    const resetTime = rateLimits.resetTime;
    const diff = resetTime - now;
    
    if (diff <= 0) return 'Reset now';
    
    const minutes = Math.floor(diff / 60000);
    const seconds = Math.floor((diff % 60000) / 1000);
    
    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${seconds}s`;
  };

  const isNearRateLimit = getRateLimitPercentage() > 80;
  const isNearQuotaLimit = getQuotaPercentage() > 80;
  const hasLowSuccessRate = getSuccessRate() < 80;

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <BarChart3 className="w-5 h-5 text-blue-500" />
            <CardTitle className="text-lg">Usage Statistics</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              {period}
            </Badge>
            {(isNearRateLimit || isNearQuotaLimit || hasLowSuccessRate) && (
              <AlertTriangle className="w-4 h-4 text-yellow-500" />
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={fetchStatistics}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={cn("w-4 h-4", isLoading && "animate-spin")} />
            </Button>
          </div>
        </div>
        <CardDescription>
          API usage, success rates, and quota monitoring
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {error && (
          <Alert className="border-red-500/30 bg-red-900/10">
            <AlertTriangle className="h-4 w-4 text-red-500" />
            <AlertDescription className="text-red-200">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Key Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Activity className="w-4 h-4 text-blue-500" />
              <Typography variant="caption" className="text-muted-foreground">
                Total Sessions
              </Typography>
            </div>
            <Typography variant="h6" className="font-mono">
              {statistics.totalSessions.toLocaleString()}
            </Typography>
            <div className="text-xs text-muted-foreground">
              in last {period}
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <Typography variant="caption" className="text-muted-foreground">
                Success Rate
              </Typography>
            </div>
            <Typography variant="h6" className={cn("font-mono", hasLowSuccessRate ? 'text-yellow-500' : 'text-green-500')}>
              {getSuccessRate().toFixed(1)}%
            </Typography>
            <div className="text-xs text-muted-foreground">
              {statistics.successfulSessions} / {statistics.totalSessions}
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Target className="w-4 h-4 text-purple-500" />
              <Typography variant="caption" className="text-muted-foreground">
                Avg Score
              </Typography>
            </div>
            <Typography variant="h6" className="font-mono">
              {statistics.averageScore.toFixed(2)}
            </Typography>
            <div className="text-xs text-muted-foreground">
              quality score
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Zap className="w-4 h-4 text-orange-500" />
              <Typography variant="caption" className="text-muted-foreground">
                Avg Iterations
              </Typography>
            </div>
            <Typography variant="h6" className="font-mono">
              {statistics.averageIterations.toFixed(1)}
            </Typography>
            <div className="text-xs text-muted-foreground">
              per session
            </div>
          </div>
        </div>

        {/* Rate Limiting */}
        <div className="space-y-3">
          <Typography variant="body-sm" className="font-medium">
            Rate Limiting
          </Typography>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Typography variant="caption" className="text-muted-foreground">
                Current Window Usage
              </Typography>
              <div className="flex items-center space-x-2">
                <Badge variant={isNearRateLimit ? 'destructive' : 'secondary'}>
                  {rateLimits.limit - rateLimits.remaining} / {rateLimits.limit}
                </Badge>
                <Typography variant="caption" className="text-muted-foreground">
                  Reset in {getTimeUntilReset()}
                </Typography>
              </div>
            </div>
            <Progress 
              value={getRateLimitPercentage()} 
              className={cn("h-2", isNearRateLimit && "bg-red-900")}
            />
          </div>
        </div>

        {/* Quota Usage */}
        <div className="space-y-3">
          <Typography variant="body-sm" className="font-medium">
            Quota Usage
          </Typography>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Typography variant="caption" className="text-muted-foreground">
                {quotaInfo.period.charAt(0).toUpperCase() + quotaInfo.period.slice(1)} Quota
              </Typography>
              <Badge variant={isNearQuotaLimit ? 'destructive' : 'secondary'}>
                {quotaInfo.used.toLocaleString()} / {quotaInfo.limit.toLocaleString()}
              </Badge>
            </div>
            <Progress 
              value={getQuotaPercentage()} 
              className={cn("h-2", isNearQuotaLimit && "bg-red-900")}
            />
            <Typography variant="caption" className="text-muted-foreground">
              Resets on {new Date(quotaInfo.resetDate).toLocaleDateString()}
            </Typography>
          </div>
        </div>

        {/* Top Features */}
        {statistics.topFeatures.length > 0 && (
          <div className="space-y-3">
            <Typography variant="body-sm" className="font-medium">
              Most Used Features
            </Typography>
            <div className="space-y-2">
              {statistics.topFeatures.slice(0, 5).map((feature, index) => (
                <div key={feature.feature} className="flex justify-between items-center">
                  <Typography variant="caption" className="text-muted-foreground">
                    {feature.feature.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </Typography>
                  <div className="flex items-center space-x-2">
                    <Typography variant="caption" className="font-mono">
                      {feature.count}
                    </Typography>
                    <div className="w-16 h-1 bg-muted rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-primary rounded-full"
                        style={{ 
                          width: `${(feature.count / statistics.topFeatures[0].count) * 100}%` 
                        }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Alerts */}
        {(isNearRateLimit || isNearQuotaLimit || hasLowSuccessRate) && (
          <Alert className="border-yellow-500/30 bg-yellow-900/10">
            <AlertTriangle className="h-4 w-4 text-yellow-500" />
            <AlertDescription className="text-yellow-200">
              <VStack spacing="xs">
                {isNearRateLimit && <span>• Approaching rate limit for current window</span>}
                {isNearQuotaLimit && <span>• Approaching quota limit for current period</span>}
                {hasLowSuccessRate && <span>• Success rate below 80% - check for issues</span>}
              </VStack>
            </AlertDescription>
          </Alert>
        )}

        {/* Last Updated */}
        <div className="pt-2 border-t border-border">
          <Typography variant="caption" className="text-muted-foreground">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </Typography>
        </div>
      </CardContent>
    </Card>
  );
};

export default UsageStatisticsPanel;
