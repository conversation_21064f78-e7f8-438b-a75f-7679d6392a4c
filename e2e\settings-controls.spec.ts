import { test, expect } from '@playwright/test';

/**
 * Settings Page Controls E2E Tests
 * Tests all interactive controls on the settings page
 * Uses web-first assertions and follows Playwright 2025 best practices
 */

test.describe('Settings Page Controls', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to settings page before each test
    await page.goto('http://localhost:8080/settings');
    
    // Wait for settings page to load
    await expect(page.getByRole('heading', { name: /settings/i })).toBeVisible();
  });

  test.describe('Quick Settings Panel Controls', () => {
    test('should interact with planner model selector', async ({ page }) => {
      const plannerSelect = page.getByTestId('quick-settings-planner-select');
      
      if (await plannerSelect.isVisible()) {
        await expect(plannerSelect).toBeVisible();
        await expect(plannerSelect).toBeEnabled();
        
        // Test selection
        await plannerSelect.click();
        
        // Should show dropdown options
        const options = page.getByRole('option');
        const optionCount = await options.count();
        expect(optionCount).toBeGreaterThan(0);
        
        // Select first option
        if (optionCount > 0) {
          await options.first().click();
        }
      }
    });

    test('should interact with critic model selector', async ({ page }) => {
      const criticSelect = page.getByTestId('quick-settings-critic-select');
      
      if (await criticSelect.isVisible()) {
        await expect(criticSelect).toBeVisible();
        await expect(criticSelect).toBeEnabled();
        
        await criticSelect.click();
        
        // Verify dropdown functionality
        const options = page.getByRole('option');
        if (await options.count() > 0) {
          await options.first().click();
        }
      }
    });

    test('should adjust temperature slider', async ({ page }) => {
      const temperatureSlider = page.getByTestId('quick-settings-temperature-slider');
      
      if (await temperatureSlider.isVisible()) {
        await expect(temperatureSlider).toBeVisible();
        
        // Get the slider handle
        const sliderHandle = temperatureSlider.getByRole('slider');
        await expect(sliderHandle).toBeVisible();
        
        // Test slider interaction
        const initialValue = await sliderHandle.getAttribute('aria-valuenow');
        
        // Move slider to different position
        await sliderHandle.click();
        await page.keyboard.press('ArrowRight');
        await page.keyboard.press('ArrowRight');
        
        const newValue = await sliderHandle.getAttribute('aria-valuenow');
        expect(newValue).not.toBe(initialValue);
        
        // Verify temperature badge updates
        const temperatureBadge = page.getByTestId('quick-settings-temperature-badge');
        if (await temperatureBadge.isVisible()) {
          await expect(temperatureBadge).toContainText(/\d+\.\d+/);
        }
      }
    });

    test('should update numeric input fields', async ({ page }) => {
      const inputs = [
        { testId: 'quick-settings-max-tokens-input', label: 'Max Tokens', value: '4000' },
        { testId: 'quick-settings-max-iterations-input', label: 'Max Iterations', value: '15' },
        { testId: 'quick-settings-score-threshold-input', label: 'Score Threshold', value: '0.90' },
        { testId: 'quick-settings-cost-limit-input', label: 'Cost Limit', value: '5.0' },
        { testId: 'quick-settings-timeout-input', label: 'Timeout', value: '300' }
      ];

      for (const input of inputs) {
        const inputField = page.getByTestId(input.testId);
        
        if (await inputField.isVisible()) {
          await expect(inputField).toBeVisible();
          await expect(inputField).toBeEnabled();
          
          // Clear and enter new value
          await inputField.clear();
          await inputField.fill(input.value);
          
          // Verify value was set
          await expect(inputField).toHaveValue(input.value);
        }
      }
    });

    test('should reset settings to defaults', async ({ page }) => {
      const resetButton = page.getByTestId('quick-settings-reset-button');
      
      if (await resetButton.isVisible()) {
        await expect(resetButton).toBeVisible();
        
        // Button might be disabled if no changes
        if (await resetButton.isEnabled()) {
          await resetButton.click();
          
          // Should show confirmation or success message
          await expect(page.getByText(/reset/i)).toBeVisible({ timeout: 5000 });
        }
      }
    });

    test('should save settings', async ({ page }) => {
      const saveButton = page.getByTestId('quick-settings-save-button');
      
      if (await saveButton.isVisible()) {
        await expect(saveButton).toBeVisible();
        
        // Make a change first to enable save button
        const maxTokensInput = page.getByTestId('quick-settings-max-tokens-input');
        if (await maxTokensInput.isVisible()) {
          await maxTokensInput.clear();
          await maxTokensInput.fill('3000');
        }
        
        // Now save should be enabled
        await expect(saveButton).toBeEnabled();
        await saveButton.click();
        
        // Should show success message
        await expect(page.getByText(/saved|success/i)).toBeVisible({ timeout: 5000 });
      }
    });
  });

  test.describe('Provider Configuration', () => {
    test('should configure AI providers', async ({ page }) => {
      // Look for provider configuration sections
      const providerCards = page.getByRole('region').filter({ hasText: /provider|model/i });
      const cardCount = await providerCards.count();
      
      if (cardCount > 0) {
        // Test first provider card
        const firstCard = providerCards.first();
        await expect(firstCard).toBeVisible();
        
        // Look for dropdowns in provider config
        const selects = firstCard.getByRole('combobox');
        const selectCount = await selects.count();
        
        for (let i = 0; i < selectCount; i++) {
          const select = selects.nth(i);
          if (await select.isVisible() && await select.isEnabled()) {
            await select.click();
            
            // Check if options appear
            const options = page.getByRole('option');
            if (await options.count() > 0) {
              await options.first().click();
            }
          }
        }
      }
    });

    test('should test API connections', async ({ page }) => {
      // Look for test connection buttons
      const testButtons = page.getByRole('button').filter({ hasText: /test|connect/i });
      const buttonCount = await testButtons.count();
      
      for (let i = 0; i < buttonCount; i++) {
        const button = testButtons.nth(i);
        if (await button.isVisible() && await button.isEnabled()) {
          await button.click();
          
          // Should show some feedback (success/error message)
          await expect(page.getByText(/success|error|connected|failed/i)).toBeVisible({ timeout: 10000 });
        }
      }
    });
  });

  test.describe('Form Validation', () => {
    test('should validate numeric inputs', async ({ page }) => {
      const numericInputs = [
        'quick-settings-max-tokens-input',
        'quick-settings-max-iterations-input',
        'quick-settings-cost-limit-input',
        'quick-settings-timeout-input'
      ];

      for (const inputId of numericInputs) {
        const input = page.getByTestId(inputId);
        
        if (await input.isVisible()) {
          // Test invalid input
          await input.clear();
          await input.fill('invalid');
          
          // Should either reject the input or show validation error
          const value = await input.inputValue();
          expect(value).not.toBe('invalid');
        }
      }
    });

    test('should validate range inputs', async ({ page }) => {
      const scoreThresholdInput = page.getByTestId('quick-settings-score-threshold-input');
      
      if (await scoreThresholdInput.isVisible()) {
        // Test out-of-range values
        await scoreThresholdInput.clear();
        await scoreThresholdInput.fill('2.0'); // Should be max 1.0
        
        // Should either clamp the value or show validation error
        const value = parseFloat(await scoreThresholdInput.inputValue());
        expect(value).toBeLessThanOrEqual(1.0);
      }
    });
  });

  test.describe('Accessibility', () => {
    test('should have proper form labels', async ({ page }) => {
      // All form inputs should have associated labels
      const inputs = page.getByRole('textbox').or(page.getByRole('spinbutton'));
      const inputCount = await inputs.count();
      
      for (let i = 0; i < inputCount; i++) {
        const input = inputs.nth(i);
        if (await input.isVisible()) {
          // Check for aria-label or associated label
          const ariaLabel = await input.getAttribute('aria-label');
          const ariaLabelledBy = await input.getAttribute('aria-labelledby');
          
          expect(ariaLabel || ariaLabelledBy).toBeTruthy();
        }
      }
    });

    test('should have proper slider accessibility', async ({ page }) => {
      const sliders = page.getByRole('slider');
      const sliderCount = await sliders.count();
      
      for (let i = 0; i < sliderCount; i++) {
        const slider = sliders.nth(i);
        if (await slider.isVisible()) {
          // Sliders should have proper ARIA attributes
          await expect(slider).toHaveAttribute('aria-valuemin');
          await expect(slider).toHaveAttribute('aria-valuemax');
          await expect(slider).toHaveAttribute('aria-valuenow');
          
          // Should have accessible name
          const ariaLabel = await slider.getAttribute('aria-label');
          const ariaLabelledBy = await slider.getAttribute('aria-labelledby');
          expect(ariaLabel || ariaLabelledBy).toBeTruthy();
        }
      }
    });

    test('should support keyboard navigation', async ({ page }) => {
      // Test tab navigation through form elements
      await page.keyboard.press('Tab');
      
      // Should focus on first interactive element
      const focusedElement = page.locator(':focus');
      await expect(focusedElement).toBeVisible();
      
      // Continue tabbing through form
      for (let i = 0; i < 5; i++) {
        await page.keyboard.press('Tab');
        const newFocusedElement = page.locator(':focus');
        await expect(newFocusedElement).toBeVisible();
      }
    });
  });

  test.describe('Responsive Design', () => {
    test('should adapt to mobile viewport', async ({ page }) => {
      await page.setViewportSize({ width: 320, height: 568 });
      
      // Settings should still be accessible on mobile
      await expect(page.getByRole('heading', { name: /settings/i })).toBeVisible();
      
      // Form elements should be responsive
      const inputs = page.getByRole('textbox').or(page.getByRole('spinbutton'));
      const inputCount = await inputs.count();
      
      if (inputCount > 0) {
        const firstInput = inputs.first();
        await expect(firstInput).toBeVisible();
      }
    });

    test('should maintain layout on tablet', async ({ page }) => {
      await page.setViewportSize({ width: 768, height: 1024 });
      
      await expect(page.getByRole('heading', { name: /settings/i })).toBeVisible();
      
      // Check that form layout is preserved
      const formSections = page.getByRole('region');
      const sectionCount = await formSections.count();
      expect(sectionCount).toBeGreaterThan(0);
    });
  });

  test.describe('Data Persistence', () => {
    test('should persist settings across page reloads', async ({ page }) => {
      // Make a change
      const maxTokensInput = page.getByTestId('quick-settings-max-tokens-input');
      
      if (await maxTokensInput.isVisible()) {
        const testValue = '2500';
        await maxTokensInput.clear();
        await maxTokensInput.fill(testValue);
        
        // Save settings
        const saveButton = page.getByTestId('quick-settings-save-button');
        if (await saveButton.isVisible() && await saveButton.isEnabled()) {
          await saveButton.click();
          await expect(page.getByText(/saved|success/i)).toBeVisible({ timeout: 5000 });
        }
        
        // Reload page
        await page.reload();
        await expect(page.getByRole('heading', { name: /settings/i })).toBeVisible();
        
        // Check if value persisted
        const reloadedInput = page.getByTestId('quick-settings-max-tokens-input');
        if (await reloadedInput.isVisible()) {
          await expect(reloadedInput).toHaveValue(testValue);
        }
      }
    });
  });
});
