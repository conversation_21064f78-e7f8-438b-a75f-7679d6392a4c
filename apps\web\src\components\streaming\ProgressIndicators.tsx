import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Activity, 
  Brain, 
  CheckCircle, 
  Clock, 
  Loader2, 
  Pause, 
  Play, 
  XCircle,
  Zap,
  Eye,
  MessageSquare
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { StreamEventType, getEventStreamingManager } from '@/lib/streaming/eventStreaming';

interface TransformationProgress {
  id: string;
  status: 'queued' | 'running' | 'completed' | 'failed' | 'paused';
  progress: number;
  currentStep: string;
  steps: ProgressStep[];
  startTime?: Date;
  endTime?: Date;
  estimatedTimeRemaining?: number;
  metadata?: Record<string, any>;
}

interface ProgressStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  progress: number;
  startTime?: Date;
  endTime?: Date;
  duration?: number;
  details?: string;
  agent?: 'planner' | 'critic';
}

interface ProgressIndicatorsProps {
  transformationId?: string;
  showDetails?: boolean;
  compact?: boolean;
  onCancel?: () => void;
  onPause?: () => void;
  onResume?: () => void;
  className?: string;
}

export const ProgressIndicators: React.FC<ProgressIndicatorsProps> = ({
  transformationId,
  showDetails = true,
  compact = false,
  onCancel,
  onPause,
  onResume,
  className
}) => {
  const [progress, setProgress] = useState<TransformationProgress | null>(null);
  const [isExpanded, setIsExpanded] = useState(!compact);
  const [realtimeUpdates, setRealtimeUpdates] = useState(true);

  useEffect(() => {
    if (!transformationId) return;

    const eventManager = getEventStreamingManager();
    
    // Subscribe to transformation events
    const unsubscribe = eventManager.subscribeWithFilter(
      {
        types: [
          StreamEventType.TRANSFORMATION_QUEUED,
          StreamEventType.TRANSFORMATION_STARTED,
          StreamEventType.TRANSFORMATION_PROGRESS,
          StreamEventType.TRANSFORMATION_COMPLETED,
          StreamEventType.TRANSFORMATION_FAILED,
          StreamEventType.PLANNER_STARTED,
          StreamEventType.PLANNER_THINKING,
          StreamEventType.PLANNER_COMPLETED,
          StreamEventType.CRITIC_STARTED,
          StreamEventType.CRITIC_EVALUATING,
          StreamEventType.CRITIC_COMPLETED,
        ],
      },
      (event) => {
        if (event.metadata?.transformationId === transformationId) {
          updateProgress(event);
        }
      }
    );

    return unsubscribe;
  }, [transformationId]);

  const updateProgress = (event: any) => {
    setProgress(prev => {
      if (!prev) {
        // Initialize progress
        return {
          id: transformationId!,
          status: 'queued',
          progress: 0,
          currentStep: 'Initializing...',
          steps: [
            {
              id: 'queue',
              name: 'Queued',
              description: 'Waiting in transformation queue',
              status: 'completed',
              progress: 100,
            },
            {
              id: 'planner',
              name: 'Planning',
              description: 'AI Planner analyzing and generating code',
              status: 'pending',
              progress: 0,
              agent: 'planner',
            },
            {
              id: 'critic',
              name: 'Evaluation',
              description: 'AI Critic evaluating the transformation',
              status: 'pending',
              progress: 0,
              agent: 'critic',
            },
            {
              id: 'finalization',
              name: 'Finalization',
              description: 'Finalizing and preparing results',
              status: 'pending',
              progress: 0,
            },
          ],
        };
      }

      const updated = { ...prev };

      switch (event.type) {
        case StreamEventType.TRANSFORMATION_STARTED:
          updated.status = 'running';
          updated.startTime = new Date();
          updated.currentStep = 'Starting transformation...';
          break;

        case StreamEventType.PLANNER_STARTED:
          updated.currentStep = 'AI Planner is analyzing your code...';
          updated.steps = updated.steps.map(step => 
            step.id === 'planner' 
              ? { ...step, status: 'running', startTime: new Date() }
              : step
          );
          break;

        case StreamEventType.PLANNER_THINKING:
          updated.currentStep = event.data.message || 'AI Planner is thinking...';
          updated.steps = updated.steps.map(step => 
            step.id === 'planner' 
              ? { ...step, progress: event.data.progress || 50, details: event.data.details }
              : step
          );
          break;

        case StreamEventType.PLANNER_COMPLETED:
          updated.steps = updated.steps.map(step => 
            step.id === 'planner' 
              ? { ...step, status: 'completed', progress: 100, endTime: new Date() }
              : step
          );
          break;

        case StreamEventType.CRITIC_STARTED:
          updated.currentStep = 'AI Critic is evaluating the transformation...';
          updated.steps = updated.steps.map(step => 
            step.id === 'critic' 
              ? { ...step, status: 'running', startTime: new Date() }
              : step
          );
          break;

        case StreamEventType.CRITIC_EVALUATING:
          updated.currentStep = event.data.message || 'AI Critic is evaluating...';
          updated.steps = updated.steps.map(step => 
            step.id === 'critic' 
              ? { ...step, progress: event.data.progress || 50, details: event.data.details }
              : step
          );
          break;

        case StreamEventType.CRITIC_COMPLETED:
          updated.steps = updated.steps.map(step => 
            step.id === 'critic' 
              ? { ...step, status: 'completed', progress: 100, endTime: new Date() }
              : step
          );
          break;

        case StreamEventType.TRANSFORMATION_COMPLETED:
          updated.status = 'completed';
          updated.endTime = new Date();
          updated.currentStep = 'Transformation completed successfully!';
          updated.progress = 100;
          updated.steps = updated.steps.map(step => ({ ...step, status: 'completed', progress: 100 }));
          break;

        case StreamEventType.TRANSFORMATION_FAILED:
          updated.status = 'failed';
          updated.endTime = new Date();
          updated.currentStep = `Transformation failed: ${event.data.error || 'Unknown error'}`;
          break;
      }

      // Calculate overall progress
      const completedSteps = updated.steps.filter(step => step.status === 'completed').length;
      const totalSteps = updated.steps.length;
      updated.progress = (completedSteps / totalSteps) * 100;

      return updated;
    });
  };

  const getStepIcon = (step: ProgressStep) => {
    switch (step.status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'running':
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-muted-foreground" />;
    }
  };

  const getAgentIcon = (agent?: string) => {
    switch (agent) {
      case 'planner':
        return <Brain className="w-4 h-4 text-blue-500" />;
      case 'critic':
        return <Eye className="w-4 h-4 text-purple-500" />;
      default:
        return <Zap className="w-4 h-4 text-orange-500" />;
    }
  };

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const getEstimatedTimeRemaining = () => {
    if (!progress?.startTime || progress.status !== 'running') return null;
    
    const elapsed = Date.now() - progress.startTime.getTime();
    const progressPercent = progress.progress / 100;
    
    if (progressPercent > 0) {
      const totalEstimated = elapsed / progressPercent;
      const remaining = totalEstimated - elapsed;
      return remaining > 0 ? remaining : 0;
    }
    
    return null;
  };

  if (!progress) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center text-muted-foreground">
            <Activity className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>No active transformation</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const estimatedTimeRemaining = getEstimatedTimeRemaining();

  if (compact) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <Activity className="w-4 h-4" />
              <span className="text-sm font-medium">Transformation Progress</span>
              <Badge variant={progress.status === 'completed' ? 'default' : 'secondary'}>
                {progress.status}
              </Badge>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              <Eye className="w-4 h-4" />
            </Button>
          </div>
          
          <div className="space-y-2">
            <Progress value={progress.progress} className="h-2" />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{progress.currentStep}</span>
              <span>{Math.round(progress.progress)}%</span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Activity className="w-5 h-5" />
            <span>Transformation Progress</span>
            <Badge variant={progress.status === 'completed' ? 'default' : 'secondary'}>
              {progress.status}
            </Badge>
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            {realtimeUpdates && (
              <Badge variant="outline" className="text-xs animate-pulse">
                Live
              </Badge>
            )}
            
            {progress.status === 'running' && onPause && (
              <Button variant="ghost" size="sm" onClick={onPause}>
                <Pause className="w-4 h-4" />
              </Button>
            )}
            
            {progress.status === 'paused' && onResume && (
              <Button variant="ghost" size="sm" onClick={onResume}>
                <Play className="w-4 h-4" />
              </Button>
            )}
            
            {onCancel && progress.status === 'running' && (
              <Button variant="ghost" size="sm" onClick={onCancel}>
                <XCircle className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Overall Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="font-medium">{progress.currentStep}</span>
            <span className="text-muted-foreground">{Math.round(progress.progress)}%</span>
          </div>
          <Progress value={progress.progress} className="h-3" />
        </div>

        {/* Time Information */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          {progress.startTime && (
            <div>
              <span className="text-muted-foreground">Started:</span>
              <div className="font-medium">{progress.startTime.toLocaleTimeString()}</div>
            </div>
          )}
          
          {estimatedTimeRemaining && (
            <div>
              <span className="text-muted-foreground">Est. Remaining:</span>
              <div className="font-medium">{formatDuration(estimatedTimeRemaining)}</div>
            </div>
          )}
        </div>

        {/* Detailed Steps */}
        {showDetails && isExpanded && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Detailed Progress</h4>
            <div className="space-y-2">
              {progress.steps.map((step, index) => (
                <div key={step.id} className="flex items-center space-x-3 p-2 rounded-lg bg-muted/50">
                  <div className="flex items-center space-x-2">
                    {getStepIcon(step)}
                    {step.agent && getAgentIcon(step.agent)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{step.name}</span>
                      <span className="text-xs text-muted-foreground">
                        {step.progress}%
                      </span>
                    </div>
                    <p className="text-xs text-muted-foreground">{step.description}</p>
                    {step.details && (
                      <p className="text-xs text-blue-600 mt-1">{step.details}</p>
                    )}
                  </div>
                  
                  {step.status === 'running' && (
                    <div className="w-16">
                      <Progress value={step.progress} className="h-1" />
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Agent Communication */}
        {progress.status === 'running' && (
          <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <MessageSquare className="w-4 h-4 text-blue-500" />
              <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                AI Agents Working
              </span>
            </div>
            <p className="text-xs text-blue-600 dark:text-blue-400">
              The Planner and Critic agents are collaborating to transform your code with the highest quality.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ProgressIndicators;
