import { jest } from '@jest/globals';
import { VertexAIProvider } from '../../providers/VertexAIProvider.js';
import { ProviderConfig, PlanRequest, CritiqueRequest } from '../../types.js';

// Mock Vertex AI SDK
jest.mock('@google-cloud/vertexai', () => {
  return {
    VertexAI: jest.fn().mockImplementation(() => ({
      getGenerativeModel: jest.fn().mockReturnValue({
        generateContent: jest.fn(),
      }),
    })),
  };
});

describe('VertexAIProvider', () => {
  let provider: VertexAIProvider;
  let mockVertexAI: any;
  let mockModel: any;
  let config: ProviderConfig;

  beforeEach(() => {
    config = {
      type: 'vertex-ai',
      model: 'gemini-2.5-flash',
      temperature: 0.7,
      maxTokens: 2000,
      vertexAI: {
        projectId: 'test-project',
        location: 'us-central1',
      },
    };

    // Reset mocks
    jest.clearAllMocks();
    
    // Setup mocks
    mockModel = {
      generateContent: jest.fn(),
    };
    
    mockVertexAI = {
      getGenerativeModel: jest.fn().mockReturnValue(mockModel),
    };
    
    const VertexAI = require('@google-cloud/vertexai').VertexAI;
    VertexAI.mockReturnValue(mockVertexAI);
    
    provider = new VertexAIProvider(config);
  });

  describe('constructor', () => {
    it('should create provider with valid config', () => {
      expect(provider).toBeInstanceOf(VertexAIProvider);
      expect(provider.getType()).toBe('vertex-ai');
      expect(provider.getModel()).toBe('gemini-2.5-flash');
    });

    it('should throw error without project ID', () => {
      const invalidConfig = { ...config };
      delete invalidConfig.vertexAI?.projectId;

      expect(() => new VertexAIProvider(invalidConfig)).toThrow('Vertex AI project ID is required');
    });

    it('should throw error without location', () => {
      const invalidConfig = { ...config };
      if (invalidConfig.vertexAI) {
        delete invalidConfig.vertexAI.location;
      }

      expect(() => new VertexAIProvider(invalidConfig)).toThrow('Vertex AI location is required');
    });
  });

  describe('validateConfig', () => {
    it('should validate config successfully', async () => {
      mockModel.generateContent.mockResolvedValue({
        response: {
          candidates: [{ content: { parts: [{ text: 'Hello' }] } }],
          usageMetadata: { promptTokenCount: 1, candidatesTokenCount: 1, totalTokenCount: 2 },
        },
      });
      
      const isValid = await provider.validateConfig();
      expect(isValid).toBe(true);
      expect(mockVertexAI.getGenerativeModel).toHaveBeenCalled();
      expect(mockModel.generateContent).toHaveBeenCalled();
    });

    it('should return false on API error', async () => {
      mockModel.generateContent.mockRejectedValue(new Error('API Error'));
      
      const isValid = await provider.validateConfig();
      expect(isValid).toBe(false);
    });
  });

  describe('generatePatch', () => {
    const mockRequest: PlanRequest = {
      prompt: 'Add a new user registration feature',
      context: { framework: 'React' },
    };

    const mockResponse = {
      response: {
        candidates: [{
          content: {
            parts: [{
              text: JSON.stringify({
                operations: [
                  { op: 'add', path: '/components/UserRegistration', value: { component: 'UserRegistration' } }
                ],
                description: 'Added user registration component',
                confidence: 0.9,
              }),
            }],
          },
          index: 0,
        }],
        usageMetadata: {
          promptTokenCount: 100,
          candidatesTokenCount: 200,
          totalTokenCount: 300,
        },
      },
    };

    it('should generate patch successfully', async () => {
      mockModel.generateContent.mockResolvedValue(mockResponse);

      const result = await provider.generatePatch(mockRequest);

      expect(result.data).toEqual({
        operations: [
          { op: 'add', path: '/components/UserRegistration', value: { component: 'UserRegistration' } }
        ],
        description: 'Added user registration component',
        confidence: 0.9,
      });
      expect(result.usage.totalTokens).toBe(300);
      expect(result.requestId).toBe('0');
    });

    it('should handle rate limit errors', async () => {
      const rateLimitError = new Error('Rate limit exceeded');
      (rateLimitError as any).status = 429;
      mockModel.generateContent.mockRejectedValue(rateLimitError);

      await expect(provider.generatePatch(mockRequest)).rejects.toThrow('Rate limit exceeded');
    });

    it('should handle invalid JSON response', async () => {
      const invalidResponse = {
        response: {
          candidates: [{
            content: { parts: [{ text: 'invalid json' }] },
            index: 0,
          }],
          usageMetadata: { promptTokenCount: 100, candidatesTokenCount: 200, totalTokenCount: 300 },
        },
      };
      mockModel.generateContent.mockResolvedValue(invalidResponse);

      await expect(provider.generatePatch(mockRequest)).rejects.toThrow('Failed to parse Vertex AI response as JSON');
    });

    it('should handle empty response', async () => {
      const emptyResponse = {
        response: {
          candidates: [],
          usageMetadata: { promptTokenCount: 100, candidatesTokenCount: 0, totalTokenCount: 100 },
        },
      };
      mockModel.generateContent.mockResolvedValue(emptyResponse);

      await expect(provider.generatePatch(mockRequest)).rejects.toThrow('No content in Vertex AI response');
    });
  });

  describe('scorePatch', () => {
    const mockRequest: CritiqueRequest = {
      patch: {
        operations: [{ op: 'add', path: '/test', value: 'test' }],
        description: 'Test patch',
        confidence: 0.8,
      },
      originalPrompt: 'Add test feature',
    };

    const mockResponse = {
      response: {
        candidates: [{
          content: {
            parts: [{
              text: JSON.stringify({
                score: 0.85,
                feedback: 'Good implementation with minor improvements needed',
                suggestions: ['Add error handling', 'Include tests'],
                isAcceptable: false,
              }),
            }],
          },
          index: 0,
        }],
        usageMetadata: {
          promptTokenCount: 150,
          candidatesTokenCount: 100,
          totalTokenCount: 250,
        },
      },
    };

    it('should score patch successfully', async () => {
      mockModel.generateContent.mockResolvedValue(mockResponse);

      const result = await provider.scorePatch(mockRequest);

      expect(result.data).toEqual({
        score: 0.85,
        feedback: 'Good implementation with minor improvements needed',
        suggestions: ['Add error handling', 'Include tests'],
        isAcceptable: false,
      });
      expect(result.usage.totalTokens).toBe(250);
    });
  });

  describe('supportsStreaming', () => {
    it('should return true for streaming support', () => {
      expect(provider.supportsStreaming()).toBe(true);
    });
  });

  describe('cost calculation', () => {
    it('should calculate cost correctly for known model', () => {
      const usage = { inputTokens: 1000, outputTokens: 500 };
      // gemini-2.5-flash: input $0.000075, output $0.0003 per 1k tokens
      const expectedCost = (1000 * 0.000075 + 500 * 0.0003) / 1000;
      
      const cost = (provider as any).calculateCost(usage);
      expect(cost).toBeCloseTo(expectedCost, 8);
    });

    it('should use default rates for unknown model', () => {
      const unknownConfig = { ...config, model: 'unknown-model' };
      const unknownProvider = new VertexAIProvider(unknownConfig);
      
      const usage = { inputTokens: 1000, outputTokens: 500 };
      const cost = (unknownProvider as any).calculateCost(usage);
      
      expect(cost).toBeGreaterThan(0);
    });
  });

  describe('rate limit detection', () => {
    it('should detect rate limit errors correctly', () => {
      const rateLimitError = new Error('Rate limit exceeded');
      (rateLimitError as any).status = 429;
      
      const isRateLimit = (provider as any).isRateLimitError(rateLimitError);
      expect(isRateLimit).toBe(true);
    });

    it('should detect quota exceeded errors', () => {
      const quotaError = new Error('Quota exceeded');
      
      const isRateLimit = (provider as any).isRateLimitError(quotaError);
      expect(isRateLimit).toBe(true);
    });

    it('should not detect regular errors as rate limits', () => {
      const regularError = new Error('Some other error');
      
      const isRateLimit = (provider as any).isRateLimitError(regularError);
      expect(isRateLimit).toBe(false);
    });
  });

  describe('token usage tracking', () => {
    it('should track token usage correctly', () => {
      const initialUsage = provider.getTokenUsage();
      expect(initialUsage.totalTokens).toBe(0);
      expect(initialUsage.cost).toBe(0);
      expect(initialUsage.provider).toBe('vertex-ai');
    });

    it('should reset metrics', () => {
      provider.resetMetrics();
      const usage = provider.getTokenUsage();
      expect(usage.totalTokens).toBe(0);
      expect(usage.cost).toBe(0);
    });
  });
});
