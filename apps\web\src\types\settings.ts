import { z } from 'zod';

/**
 * Enhanced Settings Types for Dual-Agent SDK Integration
 * Comprehensive configuration interfaces with Zod validation
 */

// Provider Types
export const ProviderTypeSchema = z.enum(['openai', 'vertex-ai', 'anthropic']);
export type ProviderType = z.infer<typeof ProviderTypeSchema>;

// Model definitions by provider
export const OpenAIModels = z.enum([
  'gpt-4o',
  'gpt-4o-mini', 
  'gpt-4-turbo',
  'gpt-4',
  'gpt-3.5-turbo',
]);

export const VertexAIModels = z.enum([
  'gemini-2.5-flash',
  'gemini-2.5-pro',
  'gemini-2.0-flash',
  'gemini-1.5-pro',
  'gemini-1.5-flash',
]);

export const AnthropicModels = z.enum([
  'claude-opus-4-20250514',
  'claude-sonnet-4-20250514', 
  'claude-3-5-sonnet-20241022',
  'claude-3-5-haiku-20241022',
]);

// Provider-specific configurations
export const OpenAIConfigSchema = z.object({
  baseURL: z.string().url().optional(),
  organization: z.string().optional(),
  project: z.string().optional(),
}).optional();

export const VertexAIConfigSchema = z.object({
  projectId: z.string().min(1, 'Project ID is required'),
  location: z.string().min(1, 'Location is required').default('us-central1'),
  credentials: z.any().optional(),
}).optional();

export const AnthropicConfigSchema = z.object({
  baseURL: z.string().url().optional(),
  version: z.string().optional(),
}).optional();

// Main provider configuration
export const ProviderConfigSchema = z.object({
  type: ProviderTypeSchema,
  model: z.string().min(1, 'Model is required'),
  temperature: z.number().min(0).max(2).default(0.7),
  maxTokens: z.number().positive().max(32000).default(2000),
  systemPrompt: z.string().optional(),
  
  // Provider-specific settings
  openai: OpenAIConfigSchema,
  vertexAI: VertexAIConfigSchema,
  anthropic: AnthropicConfigSchema,
}).refine((data) => {
  // Validate model matches provider
  switch (data.type) {
    case 'openai':
      return OpenAIModels.safeParse(data.model).success;
    case 'vertex-ai':
      return VertexAIModels.safeParse(data.model).success;
    case 'anthropic':
      return AnthropicModels.safeParse(data.model).success;
    default:
      return false;
  }
}, {
  message: "Model must be valid for the selected provider",
});

// Provider configurations for planner and critic
export const ProviderConfigsSchema = z.object({
  planner: ProviderConfigSchema,
  critic: ProviderConfigSchema,
  fallback_providers: z.array(ProviderConfigSchema).default([]),
});

// Cost guard configuration
export const CostGuardConfigSchema = z.object({
  enabled: z.boolean().default(true),
  maxCostPerLoop: z.number().positive().max(100).default(3.0),
  alertThresholds: z.object({
    nearLimitWarning: z.number().min(0).max(1).default(0.8), // 80%
    costPerHour: z.number().positive().default(10.0),
  }).default({
    nearLimitWarning: 0.8,
    costPerHour: 10.0,
  }),
});

// Token monitoring configuration
export const TokenMonitorConfigSchema = z.object({
  enabled: z.boolean().default(true),
  maxEvents: z.number().positive().default(10000),
  alertThresholds: z.object({
    tokensPerHour: z.number().positive().default(1000000),
    costPerHour: z.number().positive().default(10.0),
    requestsPerMinute: z.number().positive().default(100),
  }).default({
    tokensPerHour: 1000000,
    costPerHour: 10.0,
    requestsPerMinute: 100,
  }),
  logLevel: z.enum(['none', 'basic', 'detailed']).default('basic'),
});

// Failover configuration
export const FailoverConfigSchema = z.object({
  enabled: z.boolean().default(false),
  maxFailovers: z.number().min(1).max(10).default(3),
  failoverDelay: z.number().positive().default(1000),
  healthCheckInterval: z.number().positive().default(60000),
  retryPrimaryAfter: z.number().positive().default(300000), // 5 minutes
});

// Retry configuration
export const RetryConfigSchema = z.object({
  maxAttempts: z.number().min(1).max(10).default(5),
  baseDelay: z.number().positive().default(1000),
  maxDelay: z.number().positive().default(30000),
  jitter: z.boolean().default(true),
});

// Performance configuration
export const PerformanceConfigSchema = z.object({
  streamingEnabled: z.boolean().default(true),
  parallelExecution: z.boolean().default(false),
  maxConcurrency: z.number().min(1).max(10).default(3),
  adaptiveStrategy: z.boolean().default(true),
});

// API Keys configuration
export const APIKeysSchema = z.object({
  openai: z.string().optional(),
  anthropic: z.string().optional(),
  vertex_ai_credentials: z.any().optional(),
});

// Enhanced user settings schema
export const EnhancedUserSettingsSchema = z.object({
  // Core identification
  id: z.string().uuid().optional(),
  user_id: z.string().uuid().optional(),
  
  // Legacy fields (for backward compatibility)
  planner_model: z.string().default('gpt-4o'),
  critic_model: z.string().default('gpt-4o'),
  default_max_iterations: z.number().min(1).max(50).default(10),
  default_score_threshold: z.number().min(0).max(1).default(0.95),
  telemetry_enabled: z.boolean().default(true),
  auto_create_pr: z.boolean().default(false),
  github_repo_owner: z.string().optional(),
  github_repo_name: z.string().optional(),
  
  // Enhanced configuration
  provider_configs: ProviderConfigsSchema,
  cost_guard_config: CostGuardConfigSchema,
  token_monitor_config: TokenMonitorConfigSchema,
  failover_config: FailoverConfigSchema,
  retry_config: RetryConfigSchema,
  performance_config: PerformanceConfigSchema,
  
  // Enhanced API key management
  api_keys: APIKeysSchema.optional(),
  
  // Vertex AI specific (stored separately for security)
  vertex_ai_project_id: z.string().optional(),
  vertex_ai_location: z.string().default('us-central1'),
  
  // Configuration metadata
  config_version: z.number().default(2),
  last_validated_at: z.string().datetime().optional(),
  validation_errors: z.array(z.string()).default([]),
  
  // Timestamps
  created_at: z.string().datetime().optional(),
  updated_at: z.string().datetime().optional(),
});

// Database settings interface (matches Supabase schema)
export const DatabaseSettingsSchema = z.object({
  id: z.string().uuid().optional(),
  user_id: z.string().uuid(),
  
  // Legacy fields
  planner_model: z.string(),
  critic_model: z.string(),
  default_max_iterations: z.number(),
  default_score_threshold: z.number(),
  telemetry_enabled: z.boolean(),
  auto_create_pr: z.boolean(),
  github_repo_owner: z.string().nullable(),
  github_repo_name: z.string().nullable(),
  
  // Encrypted API keys
  openai_api_key_encrypted: z.string().nullable(),
  anthropic_api_key_encrypted: z.string().nullable(),
  google_api_key_encrypted: z.string().nullable(),
  
  // Enhanced configuration (JSONB fields)
  provider_configs: z.any(), // JSONB
  cost_guard_config: z.any(), // JSONB
  token_monitor_config: z.any(), // JSONB
  failover_config: z.any(), // JSONB
  retry_config: z.any(), // JSONB
  performance_config: z.any(), // JSONB
  
  // Vertex AI configuration
  vertex_ai_project_id: z.string().nullable(),
  vertex_ai_location: z.string().nullable(),
  vertex_ai_credentials_encrypted: z.string().nullable(),
  
  // Metadata
  config_version: z.number(),
  last_validated_at: z.string().datetime().nullable(),
  validation_errors: z.any(), // JSONB array
  
  // Timestamps
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
});

// Type exports
export type ProviderConfig = z.infer<typeof ProviderConfigSchema>;
export type ProviderConfigs = z.infer<typeof ProviderConfigsSchema>;
export type CostGuardConfig = z.infer<typeof CostGuardConfigSchema>;
export type TokenMonitorConfig = z.infer<typeof TokenMonitorConfigSchema>;
export type FailoverConfig = z.infer<typeof FailoverConfigSchema>;
export type RetryConfig = z.infer<typeof RetryConfigSchema>;
export type PerformanceConfig = z.infer<typeof PerformanceConfigSchema>;
export type APIKeys = z.infer<typeof APIKeysSchema>;
export type EnhancedUserSettings = z.infer<typeof EnhancedUserSettingsSchema>;
export type DatabaseSettings = z.infer<typeof DatabaseSettingsSchema>;

// Model type unions
export type OpenAIModel = z.infer<typeof OpenAIModels>;
export type VertexAIModel = z.infer<typeof VertexAIModels>;
export type AnthropicModel = z.infer<typeof AnthropicModels>;

// Provider model mapping
export const PROVIDER_MODELS: Record<ProviderType, readonly string[]> = {
  'openai': OpenAIModels.options,
  'vertex-ai': VertexAIModels.options,
  'anthropic': AnthropicModels.options,
} as const;

// Default configurations
export const DEFAULT_PROVIDER_CONFIGS: ProviderConfigs = {
  planner: {
    type: 'openai',
    model: 'gpt-4o',
    temperature: 0.7,
    maxTokens: 2000,
  },
  critic: {
    type: 'openai',
    model: 'gpt-4o',
    temperature: 0.3,
    maxTokens: 1500,
  },
  fallback_providers: [],
};

export const DEFAULT_COST_GUARD_CONFIG: CostGuardConfig = {
  enabled: true,
  maxCostPerLoop: 3.0,
  alertThresholds: {
    nearLimitWarning: 0.8,
    costPerHour: 10.0,
  },
};

export const DEFAULT_TOKEN_MONITOR_CONFIG: TokenMonitorConfig = {
  enabled: true,
  maxEvents: 10000,
  alertThresholds: {
    tokensPerHour: 1000000,
    costPerHour: 10.0,
    requestsPerMinute: 100,
  },
  logLevel: 'basic',
};

export const DEFAULT_FAILOVER_CONFIG: FailoverConfig = {
  enabled: false,
  maxFailovers: 3,
  failoverDelay: 1000,
  healthCheckInterval: 60000,
  retryPrimaryAfter: 300000,
};

export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 5,
  baseDelay: 1000,
  maxDelay: 30000,
  jitter: true,
};

export const DEFAULT_PERFORMANCE_CONFIG: PerformanceConfig = {
  streamingEnabled: true,
  parallelExecution: false,
  maxConcurrency: 3,
  adaptiveStrategy: true,
};

// Validation helper functions
export function validateProviderConfig(config: unknown): ProviderConfig {
  return ProviderConfigSchema.parse(config);
}

export function validateEnhancedUserSettings(settings: unknown): EnhancedUserSettings {
  return EnhancedUserSettingsSchema.parse(settings);
}

export function validateDatabaseSettings(settings: unknown): DatabaseSettings {
  return DatabaseSettingsSchema.parse(settings);
}

// Migration helpers
export function migrateFromLegacySettings(legacySettings: {
  planner_model: string;
  critic_model: string;
  default_max_iterations: number;
  default_score_threshold: number;
  telemetry_enabled: boolean;
  auto_create_pr: boolean;
}): Partial<EnhancedUserSettings> {
  const getProviderType = (model: string): ProviderType => {
    if (model.startsWith('gpt')) return 'openai';
    if (model.startsWith('claude')) return 'anthropic';
    if (model.startsWith('gemini')) return 'vertex-ai';
    return 'openai'; // default
  };

  return {
    ...legacySettings,
    provider_configs: {
      planner: {
        type: getProviderType(legacySettings.planner_model),
        model: legacySettings.planner_model,
        temperature: 0.7,
        maxTokens: 2000,
      },
      critic: {
        type: getProviderType(legacySettings.critic_model),
        model: legacySettings.critic_model,
        temperature: 0.3,
        maxTokens: 1500,
      },
      fallback_providers: [],
    },
    cost_guard_config: DEFAULT_COST_GUARD_CONFIG,
    token_monitor_config: DEFAULT_TOKEN_MONITOR_CONFIG,
    failover_config: DEFAULT_FAILOVER_CONFIG,
    retry_config: DEFAULT_RETRY_CONFIG,
    performance_config: DEFAULT_PERFORMANCE_CONFIG,
    config_version: 2,
  };
}
