import React from 'react';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle,
  CheckCircle,
  Calendar,
  CreditCard,
  Target
} from 'lucide-react';
import { useCostTracking, useBudgetAlerts, useCostEstimation } from '@/hooks/useCostTracking';
import { cn } from '@/lib/utils';

export const CostTrackingWidget: React.FC = () => {
  const { 
    usageSummary, 
    getBudgetStatus, 
    getCostTrends,
    getEstimatedMonthlyCost 
  } = useCostTracking();
  
  const { 
    alerts, 
    criticalAlerts, 
    warningAlerts,
    acknowledgeAlert,
    hasUnacknowledgedAlerts 
  } = useBudgetAlerts();

  const budgetStatus = getBudgetStatus();
  const costTrends = getCostTrends();
  const estimatedMonthlyCost = getEstimatedMonthlyCost();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 4,
    }).format(amount);
  };

  const getBudgetColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-500';
    if (percentage >= 80) return 'text-yellow-500';
    return 'text-green-500';
  };

  const getBudgetVariant = (percentage: number) => {
    if (percentage >= 90) return 'destructive';
    if (percentage >= 80) return 'secondary';
    return 'default';
  };

  // Calculate trend direction
  const getTrendDirection = () => {
    if (costTrends.length < 2) return 'stable';
    const recent = costTrends.slice(-3).reduce((sum, day) => sum + day.cost, 0) / 3;
    const previous = costTrends.slice(-6, -3).reduce((sum, day) => sum + day.cost, 0) / 3;
    
    if (recent > previous * 1.1) return 'up';
    if (recent < previous * 0.9) return 'down';
    return 'stable';
  };

  const trendDirection = getTrendDirection();

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-sm font-medium flex items-center">
              <DollarSign className="w-4 h-4 mr-2" />
              Cost Tracking
            </CardTitle>
            <CardDescription>
              Usage and budget monitoring
            </CardDescription>
          </div>
          {hasUnacknowledgedAlerts && (
            <Badge variant="destructive" className="text-xs animate-pulse">
              {alerts.length} Alert{alerts.length > 1 ? 's' : ''}
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Critical Alerts */}
        {criticalAlerts.length > 0 && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Budget Alert:</strong> {criticalAlerts[0].message}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => acknowledgeAlert(criticalAlerts[0].id)}
                className="ml-2 h-6 px-2 text-xs"
              >
                Acknowledge
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Warning Alerts */}
        {warningAlerts.length > 0 && criticalAlerts.length === 0 && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Budget Warning:</strong> {warningAlerts[0].message}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => acknowledgeAlert(warningAlerts[0].id)}
                className="ml-2 h-6 px-2 text-xs"
              >
                Acknowledge
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Today's Usage */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Calendar className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium">Today</span>
            </div>
            <div className="text-right">
              <div className="text-sm font-semibold">
                {formatCurrency(usageSummary.today.cost)}
              </div>
              <div className="text-xs text-muted-foreground">
                {usageSummary.today.transformations} transformations
              </div>
            </div>
          </div>
          <div className="space-y-1">
            <div className="flex justify-between text-xs">
              <span>Daily Budget</span>
              <span className={getBudgetColor(budgetStatus.daily.percentage)}>
                {budgetStatus.daily.percentage.toFixed(1)}%
              </span>
            </div>
            <Progress 
              value={budgetStatus.daily.percentage} 
              className="h-2"
              // @ts-ignore - custom variant
              variant={getBudgetVariant(budgetStatus.daily.percentage)}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Remaining: {formatCurrency(budgetStatus.daily.remaining)}</span>
              <span>Limit: {formatCurrency(budgetStatus.daily.limit)}</span>
            </div>
          </div>
        </div>

        {/* Monthly Usage */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <CreditCard className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium">This Month</span>
            </div>
            <div className="text-right">
              <div className="text-sm font-semibold">
                {formatCurrency(usageSummary.thisMonth.cost)}
              </div>
              <div className="text-xs text-muted-foreground">
                {usageSummary.thisMonth.transformations} transformations
              </div>
            </div>
          </div>
          <div className="space-y-1">
            <div className="flex justify-between text-xs">
              <span>Monthly Budget</span>
              <span className={getBudgetColor(budgetStatus.monthly.percentage)}>
                {budgetStatus.monthly.percentage.toFixed(1)}%
              </span>
            </div>
            <Progress 
              value={budgetStatus.monthly.percentage} 
              className="h-2"
              // @ts-ignore - custom variant
              variant={getBudgetVariant(budgetStatus.monthly.percentage)}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Remaining: {formatCurrency(budgetStatus.monthly.remaining)}</span>
              <span>Limit: {formatCurrency(budgetStatus.monthly.limit)}</span>
            </div>
          </div>
        </div>

        {/* Trend and Projection */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-2 rounded border bg-card/50">
            <div className="flex items-center justify-center space-x-1 mb-1">
              {trendDirection === 'up' && <TrendingUp className="w-3 h-3 text-red-500" />}
              {trendDirection === 'down' && <TrendingDown className="w-3 h-3 text-green-500" />}
              {trendDirection === 'stable' && <Target className="w-3 h-3 text-blue-500" />}
              <span className="text-xs font-medium">Trend</span>
            </div>
            <div className="text-xs text-muted-foreground capitalize">
              {trendDirection === 'stable' ? 'Stable' : trendDirection === 'up' ? 'Increasing' : 'Decreasing'}
            </div>
          </div>
          <div className="text-center p-2 rounded border bg-card/50">
            <div className="flex items-center justify-center space-x-1 mb-1">
              <Target className="w-3 h-3 text-muted-foreground" />
              <span className="text-xs font-medium">Projected</span>
            </div>
            <div className="text-xs font-semibold">
              {formatCurrency(estimatedMonthlyCost)}
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-3 gap-2 text-center">
          <div>
            <div className="text-xs text-muted-foreground">All Time</div>
            <div className="text-sm font-semibold">
              {formatCurrency(usageSummary.allTime.cost)}
            </div>
          </div>
          <div>
            <div className="text-xs text-muted-foreground">Total Jobs</div>
            <div className="text-sm font-semibold">
              {usageSummary.allTime.transformations}
            </div>
          </div>
          <div>
            <div className="text-xs text-muted-foreground">Avg/Job</div>
            <div className="text-sm font-semibold">
              {usageSummary.allTime.transformations > 0 
                ? formatCurrency(usageSummary.allTime.cost / usageSummary.allTime.transformations)
                : '$0.00'
              }
            </div>
          </div>
        </div>

        {/* Status Indicator */}
        <div className="flex items-center justify-between pt-2 border-t">
          <div className="flex items-center space-x-2">
            {budgetStatus.daily.percentage < 80 && budgetStatus.monthly.percentage < 80 ? (
              <>
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-xs text-green-500">Within Budget</span>
              </>
            ) : (
              <>
                <AlertTriangle className="w-4 h-4 text-yellow-500" />
                <span className="text-xs text-yellow-500">Approaching Limit</span>
              </>
            )}
          </div>
          <Button
            variant="outline"
            size="sm"
            className="h-7 px-2 text-xs"
          >
            View Details
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default CostTrackingWidget;
