import * as React from "react"

// Global app state interface
interface AppState {
  // UI State
  sidebarOpen: boolean
  activePanel: 'stream' | 'logs'
  showExamples: boolean
  showHelp: boolean
  
  // User preferences
  theme: 'light' | 'dark' | 'system'
  editorSettings: {
    fontSize: number
    tabSize: number
    wordWrap: boolean
    minimap: boolean
  }
  
  // Reactor state
  isRunning: boolean
  currentPrompt: string
  transformationHistory: TransformationEntry[]
  
  // GitHub integration
  githubConnected: boolean
  githubUser: any | null
}

interface TransformationEntry {
  id: string
  timestamp: Date
  originalCode: string
  transformedCode: string
  iterations: number
  finalScore: number
  title: string
}

// Action types for state updates
type AppAction = 
  | { type: 'SET_SIDEBAR_OPEN'; payload: boolean }
  | { type: 'SET_ACTIVE_PANEL'; payload: 'stream' | 'logs' }
  | { type: 'SET_SHOW_EXAMPLES'; payload: boolean }
  | { type: 'SET_SHOW_HELP'; payload: boolean }
  | { type: 'SET_THEME'; payload: 'light' | 'dark' | 'system' }
  | { type: 'UPDATE_EDITOR_SETTINGS'; payload: Partial<AppState['editorSettings']> }
  | { type: 'SET_IS_RUNNING'; payload: boolean }
  | { type: 'SET_CURRENT_PROMPT'; payload: string }
  | { type: 'ADD_TRANSFORMATION'; payload: TransformationEntry }
  | { type: 'SET_GITHUB_CONNECTED'; payload: boolean }
  | { type: 'SET_GITHUB_USER'; payload: any | null }
  | { type: 'RESET_STATE' }

// Initial state
const initialState: AppState = {
  sidebarOpen: true,
  activePanel: 'stream',
  showExamples: false,
  showHelp: false,
  theme: 'dark',
  editorSettings: {
    fontSize: 14,
    tabSize: 2,
    wordWrap: true,
    minimap: false
  },
  isRunning: false,
  currentPrompt: '',
  transformationHistory: [],
  githubConnected: false,
  githubUser: null
}

// State reducer
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_SIDEBAR_OPEN':
      return { ...state, sidebarOpen: action.payload }
    
    case 'SET_ACTIVE_PANEL':
      return { ...state, activePanel: action.payload }
    
    case 'SET_SHOW_EXAMPLES':
      return { ...state, showExamples: action.payload, showHelp: false }
    
    case 'SET_SHOW_HELP':
      return { ...state, showHelp: action.payload, showExamples: false }
    
    case 'SET_THEME':
      return { ...state, theme: action.payload }
    
    case 'UPDATE_EDITOR_SETTINGS':
      return {
        ...state,
        editorSettings: { ...state.editorSettings, ...action.payload }
      }
    
    case 'SET_IS_RUNNING':
      return { ...state, isRunning: action.payload }
    
    case 'SET_CURRENT_PROMPT':
      return { ...state, currentPrompt: action.payload }
    
    case 'ADD_TRANSFORMATION':
      return {
        ...state,
        transformationHistory: [action.payload, ...state.transformationHistory].slice(0, 50) // Keep last 50
      }
    
    case 'SET_GITHUB_CONNECTED':
      return { ...state, githubConnected: action.payload }
    
    case 'SET_GITHUB_USER':
      return { ...state, githubUser: action.payload }
    
    case 'RESET_STATE':
      return { ...initialState, theme: state.theme, editorSettings: state.editorSettings }
    
    default:
      return state
  }
}

// Context interface
interface AppContextValue {
  state: AppState
  dispatch: React.Dispatch<AppAction>
  
  // Convenience methods
  toggleSidebar: () => void
  setActivePanel: (panel: 'stream' | 'logs') => void
  toggleExamples: () => void
  toggleHelp: () => void
  updateEditorSettings: (settings: Partial<AppState['editorSettings']>) => void
  addTransformation: (entry: Omit<TransformationEntry, 'id' | 'timestamp'>) => void
  clearHistory: () => void
}

// Create context
const AppContext = React.createContext<AppContextValue | null>(null)

// Provider component
interface AppProviderProps {
  children: React.ReactNode
  initialState?: Partial<AppState>
}

export function AppProvider({ children, initialState: customInitialState }: AppProviderProps) {
  const [state, dispatch] = React.useReducer(
    appReducer,
    { ...initialState, ...customInitialState }
  )

  // Load state from localStorage on mount
  React.useEffect(() => {
    const savedState = localStorage.getItem('metamorphic-reactor-state')
    if (savedState) {
      try {
        const parsed = JSON.parse(savedState)
        // Only restore certain parts of the state
        dispatch({ type: 'SET_THEME', payload: parsed.theme || 'dark' })
        dispatch({ type: 'UPDATE_EDITOR_SETTINGS', payload: parsed.editorSettings || {} })
        dispatch({ type: 'SET_SIDEBAR_OPEN', payload: parsed.sidebarOpen ?? true })
      } catch (error) {
        console.warn('Failed to load saved state:', error)
      }
    }
  }, [])

  // Save state to localStorage when it changes
  React.useEffect(() => {
    const stateToSave = {
      theme: state.theme,
      editorSettings: state.editorSettings,
      sidebarOpen: state.sidebarOpen,
      transformationHistory: state.transformationHistory
    }
    localStorage.setItem('metamorphic-reactor-state', JSON.stringify(stateToSave))
  }, [state.theme, state.editorSettings, state.sidebarOpen, state.transformationHistory])

  // Convenience methods
  const toggleSidebar = React.useCallback(() => {
    dispatch({ type: 'SET_SIDEBAR_OPEN', payload: !state.sidebarOpen })
  }, [state.sidebarOpen])

  const setActivePanel = React.useCallback((panel: 'stream' | 'logs') => {
    dispatch({ type: 'SET_ACTIVE_PANEL', payload: panel })
  }, [])

  const toggleExamples = React.useCallback(() => {
    dispatch({ type: 'SET_SHOW_EXAMPLES', payload: !state.showExamples })
  }, [state.showExamples])

  const toggleHelp = React.useCallback(() => {
    dispatch({ type: 'SET_SHOW_HELP', payload: !state.showHelp })
  }, [state.showHelp])

  const updateEditorSettings = React.useCallback((settings: Partial<AppState['editorSettings']>) => {
    dispatch({ type: 'UPDATE_EDITOR_SETTINGS', payload: settings })
  }, [])

  const addTransformation = React.useCallback((entry: Omit<TransformationEntry, 'id' | 'timestamp'>) => {
    const fullEntry: TransformationEntry = {
      ...entry,
      id: Math.random().toString(36).substr(2, 9),
      timestamp: new Date()
    }
    dispatch({ type: 'ADD_TRANSFORMATION', payload: fullEntry })
  }, [])

  const clearHistory = React.useCallback(() => {
    dispatch({ type: 'RESET_STATE' })
  }, [])

  const value = React.useMemo(() => ({
    state,
    dispatch,
    toggleSidebar,
    setActivePanel,
    toggleExamples,
    toggleHelp,
    updateEditorSettings,
    addTransformation,
    clearHistory
  }), [
    state,
    dispatch,
    toggleSidebar,
    setActivePanel,
    toggleExamples,
    toggleHelp,
    updateEditorSettings,
    addTransformation,
    clearHistory
  ])

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  )
}

// Hook to use the context
export function useAppContext() {
  const context = React.useContext(AppContext)
  if (!context) {
    throw new Error('useAppContext must be used within an AppProvider')
  }
  return context
}

// Selector hooks for specific parts of state
export function useUIState() {
  const { state } = useAppContext()
  return {
    sidebarOpen: state.sidebarOpen,
    activePanel: state.activePanel,
    showExamples: state.showExamples,
    showHelp: state.showHelp
  }
}

export function useEditorSettings() {
  const { state, updateEditorSettings } = useAppContext()
  return {
    settings: state.editorSettings,
    updateSettings: updateEditorSettings
  }
}

export function useTransformationHistory() {
  const { state, addTransformation, clearHistory } = useAppContext()
  return {
    history: state.transformationHistory,
    addTransformation,
    clearHistory
  }
}

export function useReactorState() {
  const { state, dispatch } = useAppContext()
  return {
    isRunning: state.isRunning,
    currentPrompt: state.currentPrompt,
    setIsRunning: (running: boolean) => dispatch({ type: 'SET_IS_RUNNING', payload: running }),
    setCurrentPrompt: (prompt: string) => dispatch({ type: 'SET_CURRENT_PROMPT', payload: prompt })
  }
}

export function useGitHubState() {
  const { state, dispatch } = useAppContext()
  return {
    connected: state.githubConnected,
    user: state.githubUser,
    setConnected: (connected: boolean) => dispatch({ type: 'SET_GITHUB_CONNECTED', payload: connected }),
    setUser: (user: any) => dispatch({ type: 'SET_GITHUB_USER', payload: user })
  }
}

export type { AppState, AppAction, TransformationEntry }
