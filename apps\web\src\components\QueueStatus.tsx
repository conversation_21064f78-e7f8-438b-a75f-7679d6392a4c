import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Play, 
  Pause, 
  Trash2,
  RotateCcw,
  Activity
} from 'lucide-react';
import { useTransformationQueue, useQueueStats } from '@/hooks/useTransformationQueue';
import { JobStatus } from '@/lib/queue/transformationQueue';
import { cn } from '@/lib/utils';

export const QueueStatus: React.FC = () => {
  const { 
    stats, 
    jobs, 
    isProcessing,
    getRecentJobs,
    cancelJob,
    removeJob,
    clearCompleted 
  } = useTransformationQueue();
  
  const { health, estimatedWaitTime } = useQueueStats();
  const recentJobs = getRecentJobs();

  const getStatusIcon = (status: JobStatus) => {
    switch (status) {
      case JobStatus.PENDING:
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case JobStatus.RUNNING:
        return <Activity className="w-4 h-4 text-blue-500 animate-pulse" />;
      case JobStatus.COMPLETED:
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case JobStatus.FAILED:
        return <XCircle className="w-4 h-4 text-red-500" />;
      case JobStatus.CANCELLED:
        return <XCircle className="w-4 h-4 text-gray-500" />;
      case JobStatus.RETRYING:
        return <RotateCcw className="w-4 h-4 text-orange-500 animate-spin" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: JobStatus) => {
    const variants = {
      [JobStatus.PENDING]: 'secondary',
      [JobStatus.RUNNING]: 'default',
      [JobStatus.COMPLETED]: 'default',
      [JobStatus.FAILED]: 'destructive',
      [JobStatus.CANCELLED]: 'outline',
      [JobStatus.RETRYING]: 'secondary',
    } as const;

    return (
      <Badge variant={variants[status]} className="text-xs">
        {getStatusIcon(status)}
        <span className="ml-1 capitalize">{status.replace('_', ' ')}</span>
      </Badge>
    );
  };

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'healthy':
        return 'text-green-500';
      case 'moderate':
        return 'text-yellow-500';
      case 'unhealthy':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  const formatWaitTime = (ms: number) => {
    if (ms < 60000) return '< 1 min';
    const minutes = Math.floor(ms / 60000);
    if (minutes < 60) return `${minutes} min`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ${minutes % 60}m`;
  };

  if (stats.total === 0) {
    return (
      <Card className="w-full">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Transformation Queue</CardTitle>
          <CardDescription>No transformations in queue</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-4 text-muted-foreground">
            <Clock className="w-8 h-8 mr-2" />
            <span>Queue is empty</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-sm font-medium">Transformation Queue</CardTitle>
            <CardDescription>
              {stats.total} total jobs • {stats.running} running • {stats.pending} pending
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Badge 
              variant="outline" 
              className={cn("text-xs", getHealthColor(health))}
            >
              {health}
            </Badge>
            {isProcessing && (
              <Badge variant="default" className="text-xs animate-pulse">
                Processing
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Queue Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-lg font-semibold text-yellow-500">{stats.pending}</div>
            <div className="text-xs text-muted-foreground">Pending</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-blue-500">{stats.running}</div>
            <div className="text-xs text-muted-foreground">Running</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-green-500">{stats.completed}</div>
            <div className="text-xs text-muted-foreground">Completed</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-red-500">{stats.failed}</div>
            <div className="text-xs text-muted-foreground">Failed</div>
          </div>
        </div>

        {/* Progress Bar */}
        {stats.total > 0 && (
          <div className="space-y-2">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Progress</span>
              <span>{Math.round((stats.completed / stats.total) * 100)}% complete</span>
            </div>
            <Progress 
              value={(stats.completed / stats.total) * 100} 
              className="h-2"
            />
          </div>
        )}

        {/* Wait Time */}
        {stats.pending > 0 && (
          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">Estimated wait time:</span>
            <span className="font-medium">{formatWaitTime(estimatedWaitTime)}</span>
          </div>
        )}

        {/* Recent Jobs */}
        {recentJobs.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="text-xs font-medium text-muted-foreground">Recent Jobs</h4>
              {stats.completed > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => clearCompleted()}
                  className="h-6 px-2 text-xs"
                >
                  <Trash2 className="w-3 h-3 mr-1" />
                  Clear
                </Button>
              )}
            </div>
            <div className="space-y-1 max-h-32 overflow-y-auto">
              {recentJobs.slice(0, 5).map((job) => (
                <div
                  key={job.id}
                  className="flex items-center justify-between p-2 rounded border bg-card/50"
                >
                  <div className="flex items-center space-x-2 flex-1 min-w-0">
                    {getStatusIcon(job.status)}
                    <div className="flex-1 min-w-0">
                      <div className="text-xs font-medium truncate">
                        {job.request.prompt.slice(0, 40)}...
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {new Date(job.createdAt).toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    {getStatusBadge(job.status)}
                    {(job.status === JobStatus.PENDING || job.status === JobStatus.RUNNING) && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => cancelJob(job.id)}
                        className="h-6 w-6 p-0"
                      >
                        <XCircle className="w-3 h-3" />
                      </Button>
                    )}
                    {(job.status === JobStatus.COMPLETED || 
                      job.status === JobStatus.FAILED || 
                      job.status === JobStatus.CANCELLED) && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeJob(job.id)}
                        className="h-6 w-6 p-0"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Queue Actions */}
        <div className="flex items-center justify-between pt-2 border-t">
          <div className="text-xs text-muted-foreground">
            Queue health: <span className={getHealthColor(health)}>{health}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              variant="outline"
              size="sm"
              className="h-7 px-2 text-xs"
              disabled={stats.total === 0}
            >
              View All
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default QueueStatus;
