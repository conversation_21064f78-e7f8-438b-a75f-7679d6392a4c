// WCAG 2.1 AA Compliance Utilities

// Color contrast utilities
export function getContrastRatio(color1: string, color2: string): number {
  const luminance1 = getLuminance(color1)
  const luminance2 = getLuminance(color2)
  
  const lighter = Math.max(luminance1, luminance2)
  const darker = Math.min(luminance1, luminance2)
  
  return (lighter + 0.05) / (darker + 0.05)
}

export function getLuminance(color: string): number {
  const rgb = hexToRgb(color)
  if (!rgb) return 0
  
  const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {
    c = c / 255
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
  })
  
  return 0.2126 * r + 0.7152 * g + 0.0722 * b
}

export function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null
}

export function isContrastCompliant(
  foreground: string, 
  background: string, 
  level: 'AA' | 'AAA' = 'AA',
  size: 'normal' | 'large' = 'normal'
): boolean {
  const ratio = getContrastRatio(foreground, background)
  
  if (level === 'AAA') {
    return size === 'large' ? ratio >= 4.5 : ratio >= 7
  }
  
  return size === 'large' ? ratio >= 3 : ratio >= 4.5
}

// Focus management utilities
export function trapFocus(element: HTMLElement): () => void {
  const focusableElements = getFocusableElements(element)
  const firstElement = focusableElements[0]
  const lastElement = focusableElements[focusableElements.length - 1]
  
  const handleTabKey = (e: KeyboardEvent) => {
    if (e.key !== 'Tab') return
    
    if (e.shiftKey) {
      if (document.activeElement === firstElement) {
        e.preventDefault()
        lastElement?.focus()
      }
    } else {
      if (document.activeElement === lastElement) {
        e.preventDefault()
        firstElement?.focus()
      }
    }
  }
  
  element.addEventListener('keydown', handleTabKey)
  firstElement?.focus()
  
  return () => {
    element.removeEventListener('keydown', handleTabKey)
  }
}

export function getFocusableElements(element: HTMLElement): HTMLElement[] {
  const focusableSelectors = [
    'a[href]',
    'button:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    '[tabindex]:not([tabindex="-1"])',
    '[contenteditable="true"]'
  ].join(', ')
  
  return Array.from(element.querySelectorAll(focusableSelectors))
}

export function restoreFocus(previousActiveElement: Element | null) {
  if (previousActiveElement && 'focus' in previousActiveElement) {
    (previousActiveElement as HTMLElement).focus()
  }
}

// ARIA utilities
export function generateId(prefix: string = 'id'): string {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`
}

export function announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite') {
  const announcement = document.createElement('div')
  announcement.setAttribute('aria-live', priority)
  announcement.setAttribute('aria-atomic', 'true')
  announcement.className = 'sr-only'
  announcement.textContent = message
  
  document.body.appendChild(announcement)
  
  setTimeout(() => {
    document.body.removeChild(announcement)
  }, 1000)
}

export function setAriaExpanded(element: HTMLElement, expanded: boolean) {
  element.setAttribute('aria-expanded', expanded.toString())
}

export function setAriaSelected(element: HTMLElement, selected: boolean) {
  element.setAttribute('aria-selected', selected.toString())
}

export function setAriaChecked(element: HTMLElement, checked: boolean | 'mixed') {
  element.setAttribute('aria-checked', checked.toString())
}

// Keyboard navigation utilities
export function handleArrowNavigation(
  event: KeyboardEvent,
  items: HTMLElement[],
  currentIndex: number,
  orientation: 'horizontal' | 'vertical' | 'both' = 'vertical'
): number {
  const { key } = event
  let newIndex = currentIndex
  
  switch (key) {
    case 'ArrowDown':
      if (orientation === 'vertical' || orientation === 'both') {
        event.preventDefault()
        newIndex = (currentIndex + 1) % items.length
      }
      break
    case 'ArrowUp':
      if (orientation === 'vertical' || orientation === 'both') {
        event.preventDefault()
        newIndex = currentIndex === 0 ? items.length - 1 : currentIndex - 1
      }
      break
    case 'ArrowRight':
      if (orientation === 'horizontal' || orientation === 'both') {
        event.preventDefault()
        newIndex = (currentIndex + 1) % items.length
      }
      break
    case 'ArrowLeft':
      if (orientation === 'horizontal' || orientation === 'both') {
        event.preventDefault()
        newIndex = currentIndex === 0 ? items.length - 1 : currentIndex - 1
      }
      break
    case 'Home':
      event.preventDefault()
      newIndex = 0
      break
    case 'End':
      event.preventDefault()
      newIndex = items.length - 1
      break
  }
  
  if (newIndex !== currentIndex) {
    items[newIndex]?.focus()
  }
  
  return newIndex
}

// Screen reader utilities
export function hideFromScreenReader(element: HTMLElement) {
  element.setAttribute('aria-hidden', 'true')
}

export function showToScreenReader(element: HTMLElement) {
  element.removeAttribute('aria-hidden')
}

export function setScreenReaderText(element: HTMLElement, text: string) {
  element.setAttribute('aria-label', text)
}

// Reduced motion utilities
export function prefersReducedMotion(): boolean {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches
}

export function respectReducedMotion(element: HTMLElement) {
  if (prefersReducedMotion()) {
    element.style.animation = 'none'
    element.style.transition = 'none'
  }
}

// High contrast utilities
export function prefersHighContrast(): boolean {
  return window.matchMedia('(prefers-contrast: high)').matches
}

export function adjustForHighContrast(element: HTMLElement) {
  if (prefersHighContrast()) {
    element.classList.add('high-contrast')
  }
}

// Touch target utilities
export function ensureMinimumTouchTarget(element: HTMLElement, minSize: number = 44) {
  const rect = element.getBoundingClientRect()
  
  if (rect.width < minSize || rect.height < minSize) {
    element.style.minWidth = `${minSize}px`
    element.style.minHeight = `${minSize}px`
    element.style.display = 'inline-flex'
    element.style.alignItems = 'center'
    element.style.justifyContent = 'center'
  }
}

// Form accessibility utilities
export function associateLabelWithInput(label: HTMLLabelElement, input: HTMLInputElement) {
  const id = input.id || generateId('input')
  input.id = id
  label.setAttribute('for', id)
}

export function addErrorToInput(input: HTMLInputElement, errorMessage: string) {
  const errorId = generateId('error')
  const errorElement = document.createElement('div')
  
  errorElement.id = errorId
  errorElement.className = 'error-message'
  errorElement.textContent = errorMessage
  errorElement.setAttribute('role', 'alert')
  
  input.setAttribute('aria-describedby', errorId)
  input.setAttribute('aria-invalid', 'true')
  input.parentNode?.insertBefore(errorElement, input.nextSibling)
}

export function removeErrorFromInput(input: HTMLInputElement) {
  const errorId = input.getAttribute('aria-describedby')
  if (errorId) {
    const errorElement = document.getElementById(errorId)
    errorElement?.remove()
    input.removeAttribute('aria-describedby')
    input.removeAttribute('aria-invalid')
  }
}

// Accessibility testing utilities
export function checkAccessibility(element: HTMLElement): string[] {
  const issues: string[] = []
  
  // Check for missing alt text on images
  const images = element.querySelectorAll('img')
  images.forEach(img => {
    if (!img.alt && !img.getAttribute('aria-label')) {
      issues.push(`Image missing alt text: ${img.src}`)
    }
  })
  
  // Check for buttons without accessible names
  const buttons = element.querySelectorAll('button')
  buttons.forEach(button => {
    if (!button.textContent?.trim() && !button.getAttribute('aria-label')) {
      issues.push('Button missing accessible name')
    }
  })
  
  // Check for form inputs without labels
  const inputs = element.querySelectorAll('input, select, textarea')
  inputs.forEach(input => {
    const id = input.id
    const hasLabel = id && document.querySelector(`label[for="${id}"]`)
    const hasAriaLabel = input.getAttribute('aria-label')
    const hasAriaLabelledBy = input.getAttribute('aria-labelledby')
    
    if (!hasLabel && !hasAriaLabel && !hasAriaLabelledBy) {
      issues.push(`Form input missing label: ${input.tagName}`)
    }
  })
  
  // Check for headings hierarchy
  const headings = Array.from(element.querySelectorAll('h1, h2, h3, h4, h5, h6'))
  let lastLevel = 0
  headings.forEach(heading => {
    const level = parseInt(heading.tagName.charAt(1))
    if (level > lastLevel + 1) {
      issues.push(`Heading hierarchy skip: ${heading.tagName} after h${lastLevel}`)
    }
    lastLevel = level
  })
  
  return issues
}

// Accessibility hooks for React components
export function useAccessibleId(prefix: string = 'accessible'): string {
  return React.useMemo(() => generateId(prefix), [prefix])
}

export function useAriaLive(): (message: string, priority?: 'polite' | 'assertive') => void {
  return React.useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    announceToScreenReader(message, priority)
  }, [])
}

export function useFocusTrap(isActive: boolean): React.RefObject<HTMLElement> {
  const elementRef = React.useRef<HTMLElement>(null)
  
  React.useEffect(() => {
    if (isActive && elementRef.current) {
      return trapFocus(elementRef.current)
    }
  }, [isActive])
  
  return elementRef
}

// Import React for hooks
import * as React from 'react'
