import { test, expect } from '@playwright/test';
import { injectAxe, checkA11y } from 'axe-playwright';

test.describe('Dashboard E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to dashboard
    await page.goto('/dashboard');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test('should load dashboard with all main elements', async ({ page }) => {
    // Check main heading
    await expect(page.getByRole('heading', { name: /metamorphic reactor/i })).toBeVisible();
    
    // Check tab navigation
    await expect(page.getByRole('tablist')).toBeVisible();
    
    // Check all tabs are present
    await expect(page.getByRole('tab', { name: /code editor/i })).toBeVisible();
    await expect(page.getByRole('tab', { name: /overview/i })).toBeVisible();
    await expect(page.getByRole('tab', { name: /analytics/i })).toBeVisible();
    await expect(page.getByRole('tab', { name: /admin/i })).toBeVisible();
    
    // Check main content area
    await expect(page.getByRole('main')).toBeVisible();
  });

  test('should navigate between tabs correctly', async ({ page }) => {
    // Start with code editor tab (default)
    await expect(page.getByRole('tab', { name: /code editor/i })).toHaveAttribute('aria-selected', 'true');
    
    // Click overview tab
    await page.getByRole('tab', { name: /overview/i }).click();
    await expect(page.getByRole('tab', { name: /overview/i })).toHaveAttribute('aria-selected', 'true');
    
    // Check overview content is visible
    await expect(page.getByRole('tabpanel')).toBeVisible();
    
    // Click analytics tab
    await page.getByRole('tab', { name: /analytics/i }).click();
    await expect(page.getByRole('tab', { name: /analytics/i })).toHaveAttribute('aria-selected', 'true');
    
    // Click admin tab
    await page.getByRole('tab', { name: /admin/i }).click();
    await expect(page.getByRole('tab', { name: /admin/i })).toHaveAttribute('aria-selected', 'true');
  });

  test('should have working code editor functionality', async ({ page }) => {
    // Ensure we're on code editor tab
    await page.getByRole('tab', { name: /code editor/i }).click();
    
    // Check for code editor elements
    await expect(page.getByText(/code editor/i)).toBeVisible();
    
    // Check for control panel
    await expect(page.getByRole('group', { name: /control panel/i })).toBeVisible();
    
    // Check for start/run button
    await expect(page.getByRole('button', { name: /start/i })).toBeVisible();
    
    // Check for diff viewer
    await expect(page.getByText(/live diff/i)).toBeVisible();
    
    // Check for agent logs
    await expect(page.getByRole('log')).toBeVisible();
  });

  test('should display dashboard widgets in overview tab', async ({ page }) => {
    // Navigate to overview tab
    await page.getByRole('tab', { name: /overview/i }).click();
    
    // Wait for content to load
    await page.waitForTimeout(1000);
    
    // Check for dashboard widgets
    await expect(page.getByText(/performance/i)).toBeVisible();
    await expect(page.getByText(/cost tracking/i)).toBeVisible();
    await expect(page.getByText(/system health/i)).toBeVisible();
    
    // Check for activity timeline
    await expect(page.getByText(/recent activity/i)).toBeVisible();
    
    // Check for notification center
    await expect(page.getByText(/notifications/i)).toBeVisible();
  });

  test('should have accessible navigation', async ({ page }) => {
    // Test keyboard navigation
    await page.keyboard.press('Tab');
    
    // Check focus is on first interactive element
    const focusedElement = await page.locator(':focus');
    await expect(focusedElement).toBeVisible();
    
    // Navigate through tabs with arrow keys
    await page.getByRole('tab', { name: /code editor/i }).focus();
    await page.keyboard.press('ArrowRight');
    
    // Check that focus moved to next tab
    await expect(page.getByRole('tab', { name: /overview/i })).toBeFocused();
  });

  test('should be responsive on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check that dashboard still loads
    await expect(page.getByRole('heading', { name: /metamorphic reactor/i })).toBeVisible();
    
    // Check that tabs are still accessible (may be icon-only on mobile)
    await expect(page.getByRole('tablist')).toBeVisible();
    
    // Check that main content is visible
    await expect(page.getByRole('main')).toBeVisible();
    
    // Test tab navigation on mobile
    await page.getByRole('tab', { name: /overview/i }).click();
    await expect(page.getByRole('tabpanel')).toBeVisible();
  });

  test('should be responsive on tablet viewport', async ({ page }) => {
    // Set tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    
    // Check that dashboard loads properly
    await expect(page.getByRole('heading', { name: /metamorphic reactor/i })).toBeVisible();
    
    // Check that all tabs are visible with text
    await expect(page.getByRole('tab', { name: /code editor/i })).toBeVisible();
    await expect(page.getByRole('tab', { name: /overview/i })).toBeVisible();
    
    // Test navigation
    await page.getByRole('tab', { name: /analytics/i }).click();
    await expect(page.getByRole('tabpanel')).toBeVisible();
  });

  test('should have proper ARIA attributes', async ({ page }) => {
    // Check main landmarks
    await expect(page.getByRole('banner')).toBeVisible(); // header
    await expect(page.getByRole('main')).toBeVisible(); // main content
    await expect(page.getByRole('navigation')).toBeVisible(); // nav
    
    // Check tablist has proper attributes
    const tablist = page.getByRole('tablist');
    await expect(tablist).toHaveAttribute('aria-label');
    
    // Check tabs have proper attributes
    const tabs = page.getByRole('tab');
    const tabCount = await tabs.count();
    
    for (let i = 0; i < tabCount; i++) {
      const tab = tabs.nth(i);
      await expect(tab).toHaveAttribute('aria-controls');
      await expect(tab).toHaveAttribute('role', 'tab');
    }
    
    // Check tabpanels have proper attributes
    const tabpanel = page.getByRole('tabpanel');
    await expect(tabpanel).toHaveAttribute('role', 'tabpanel');
  });

  test('should pass accessibility audit', async ({ page }) => {
    // Inject axe-core
    await injectAxe(page);
    
    // Run accessibility check on code editor tab
    await checkA11y(page, null, {
      detailedReport: true,
      detailedReportOptions: { html: true },
    });
    
    // Check overview tab
    await page.getByRole('tab', { name: /overview/i }).click();
    await page.waitForTimeout(1000);
    await checkA11y(page, null, {
      detailedReport: true,
      detailedReportOptions: { html: true },
    });
    
    // Check analytics tab
    await page.getByRole('tab', { name: /analytics/i }).click();
    await page.waitForTimeout(1000);
    await checkA11y(page, null, {
      detailedReport: true,
      detailedReportOptions: { html: true },
    });
  });

  test('should handle real-time log updates', async ({ page }) => {
    // Navigate to code editor tab
    await page.getByRole('tab', { name: /code editor/i }).click();
    
    // Check for agent log component
    const logContainer = page.getByRole('log');
    await expect(logContainer).toBeVisible();
    
    // Check for real-time controls
    await expect(page.getByText(/live|offline|connecting/i)).toBeVisible();
    
    // Check for log controls (pause/resume, clear, scroll)
    const controls = page.locator('[aria-label*="pause"], [aria-label*="resume"], [aria-label*="clear"], [aria-label*="scroll"]');
    await expect(controls.first()).toBeVisible();
  });

  test('should handle error states gracefully', async ({ page }) => {
    // Test with network offline
    await page.context().setOffline(true);
    
    // Navigate to overview tab
    await page.getByRole('tab', { name: /overview/i }).click();
    
    // Should still show content (cached or fallback)
    await expect(page.getByRole('tabpanel')).toBeVisible();
    
    // Restore network
    await page.context().setOffline(false);
  });

  test('should maintain state across tab switches', async ({ page }) => {
    // Start on code editor tab
    await page.getByRole('tab', { name: /code editor/i }).click();
    
    // Switch to overview
    await page.getByRole('tab', { name: /overview/i }).click();
    await expect(page.getByRole('tabpanel')).toBeVisible();
    
    // Switch back to code editor
    await page.getByRole('tab', { name: /code editor/i }).click();
    
    // Check that code editor content is still there
    await expect(page.getByText(/code editor/i)).toBeVisible();
    await expect(page.getByRole('log')).toBeVisible();
  });

  test('should have proper color contrast', async ({ page }) => {
    // Inject axe-core
    await injectAxe(page);
    
    // Run color contrast specific check
    await checkA11y(page, null, {
      rules: {
        'color-contrast': { enabled: true }
      }
    });
  });

  test('should work with keyboard-only navigation', async ({ page }) => {
    // Start keyboard navigation
    await page.keyboard.press('Tab');
    
    // Navigate through all interactive elements
    let tabCount = 0;
    const maxTabs = 20; // Prevent infinite loop
    
    while (tabCount < maxTabs) {
      const focusedElement = await page.locator(':focus');
      
      if (await focusedElement.count() === 0) break;
      
      // Check that focused element is visible
      await expect(focusedElement).toBeVisible();
      
      await page.keyboard.press('Tab');
      tabCount++;
    }
    
    // Should have navigated through multiple elements
    expect(tabCount).toBeGreaterThan(5);
  });
});
