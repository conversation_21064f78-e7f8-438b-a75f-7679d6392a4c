import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, Circle, AlertCircle, ArrowRight, ArrowLeft, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SetupStep {
  id: string;
  title: string;
  description: string;
  component: React.ComponentType<StepProps>;
  validation?: () => Promise<boolean>;
  optional?: boolean;
}

interface StepProps {
  onNext: () => void;
  onPrevious: () => void;
  onValidationChange: (isValid: boolean) => void;
  isActive: boolean;
}

interface SetupWizardProps {
  onComplete: () => void;
  onSkip?: () => void;
}

const SETUP_STEPS: SetupStep[] = [
  {
    id: 'welcome',
    title: 'Welcome',
    description: 'Get started with Metamorphic Reactor',
    component: WelcomeStep,
  },
  {
    id: 'supabase',
    title: 'Supabase Configuration',
    description: 'Connect your Supabase database',
    component: SupabaseStep,
    validation: async () => {
      // TODO: Implement Supabase connection validation
      return true;
    },
  },
  {
    id: 'github',
    title: 'GitHub Integration',
    description: 'Connect your GitHub account',
    component: GitHubStep,
    validation: async () => {
      // TODO: Implement GitHub OAuth validation
      return true;
    },
  },
  {
    id: 'models',
    title: 'AI Models',
    description: 'Configure your AI model preferences',
    component: ModelsStep,
  },
  {
    id: 'complete',
    title: 'Setup Complete',
    description: 'You\'re ready to start transforming code!',
    component: CompleteStep,
  },
];

export const SetupWizard: React.FC<SetupWizardProps> = ({ onComplete, onSkip }) => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());
  const [stepValidation, setStepValidation] = useState<Record<string, boolean>>({});
  const [isValidating, setIsValidating] = useState(false);

  const currentStep = SETUP_STEPS[currentStepIndex];
  const progress = ((currentStepIndex + 1) / SETUP_STEPS.length) * 100;

  const handleNext = async () => {
    if (currentStep.validation) {
      setIsValidating(true);
      try {
        const isValid = await currentStep.validation();
        if (!isValid) {
          setIsValidating(false);
          return;
        }
      } catch (error) {
        console.error('Validation error:', error);
        setIsValidating(false);
        return;
      }
      setIsValidating(false);
    }

    setCompletedSteps(prev => new Set([...prev, currentStep.id]));
    
    if (currentStepIndex < SETUP_STEPS.length - 1) {
      setCurrentStepIndex(prev => prev + 1);
    } else {
      onComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(prev => prev - 1);
    }
  };

  const handleValidationChange = (isValid: boolean) => {
    setStepValidation(prev => ({
      ...prev,
      [currentStep.id]: isValid,
    }));
  };

  const isCurrentStepValid = stepValidation[currentStep.id] !== false;
  const canProceed = isCurrentStepValid && !isValidating;

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">Setup Metamorphic Reactor</CardTitle>
          <CardDescription>
            Let's get you set up with everything you need to start transforming code
          </CardDescription>
          <div className="mt-4">
            <Progress value={progress} className="w-full" />
            <p className="text-sm text-muted-foreground mt-2">
              Step {currentStepIndex + 1} of {SETUP_STEPS.length}
            </p>
          </div>
        </CardHeader>

        <CardContent>
          {/* Step Navigation */}
          <div className="flex items-center justify-between mb-8">
            {SETUP_STEPS.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className="flex flex-col items-center">
                  <div
                    className={cn(
                      "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",
                      index < currentStepIndex || completedSteps.has(step.id)
                        ? "bg-primary text-primary-foreground"
                        : index === currentStepIndex
                        ? "bg-primary/20 text-primary border-2 border-primary"
                        : "bg-muted text-muted-foreground"
                    )}
                  >
                    {completedSteps.has(step.id) ? (
                      <CheckCircle className="w-4 h-4" />
                    ) : (
                      index + 1
                    )}
                  </div>
                  <span className="text-xs mt-1 text-center max-w-20">
                    {step.title}
                  </span>
                </div>
                {index < SETUP_STEPS.length - 1 && (
                  <div className="w-12 h-px bg-border mx-2" />
                )}
              </div>
            ))}
          </div>

          {/* Current Step Content */}
          <div className="min-h-[400px]">
            <currentStep.component
              onNext={handleNext}
              onPrevious={handlePrevious}
              onValidationChange={handleValidationChange}
              isActive={true}
            />
          </div>

          {/* Navigation Buttons */}
          <div className="flex items-center justify-between mt-8">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStepIndex === 0}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Previous
            </Button>

            <div className="flex items-center space-x-2">
              {onSkip && currentStepIndex < SETUP_STEPS.length - 1 && (
                <Button variant="ghost" onClick={onSkip}>
                  Skip Setup
                </Button>
              )}
              <Button
                onClick={handleNext}
                disabled={!canProceed}
              >
                {isValidating ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : currentStepIndex === SETUP_STEPS.length - 1 ? (
                  'Complete Setup'
                ) : (
                  <>
                    Next
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Step Components
function WelcomeStep({ onValidationChange }: StepProps) {
  useEffect(() => {
    onValidationChange(true);
  }, [onValidationChange]);

  return (
    <div className="text-center space-y-6">
      <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto">
        <CheckCircle className="w-8 h-8 text-primary" />
      </div>
      <div>
        <h3 className="text-xl font-semibold mb-2">Welcome to Metamorphic Reactor</h3>
        <p className="text-muted-foreground">
          Transform your code with the power of dual-AI agents. This setup wizard will help you
          configure everything you need to get started.
        </p>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <Circle className="w-4 h-4 text-blue-600" />
            </div>
            <h4 className="font-medium">Supabase</h4>
            <p className="text-sm text-muted-foreground">Real-time database</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <Circle className="w-4 h-4 text-green-600" />
            </div>
            <h4 className="font-medium">GitHub</h4>
            <p className="text-sm text-muted-foreground">Code repository</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <Circle className="w-4 h-4 text-purple-600" />
            </div>
            <h4 className="font-medium">AI Models</h4>
            <p className="text-sm text-muted-foreground">Transformation engine</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function SupabaseStep({ onValidationChange }: StepProps) {
  // TODO: Implement Supabase configuration step
  useEffect(() => {
    onValidationChange(true);
  }, [onValidationChange]);

  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold">Configure Supabase</h3>
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Supabase configuration step will be implemented here.
        </AlertDescription>
      </Alert>
    </div>
  );
}

function GitHubStep({ onValidationChange }: StepProps) {
  // TODO: Implement GitHub OAuth step
  useEffect(() => {
    onValidationChange(true);
  }, [onValidationChange]);

  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold">Connect GitHub</h3>
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          GitHub OAuth configuration step will be implemented here.
        </AlertDescription>
      </Alert>
    </div>
  );
}

function ModelsStep({ onValidationChange }: StepProps) {
  // TODO: Implement AI models configuration step
  useEffect(() => {
    onValidationChange(true);
  }, [onValidationChange]);

  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold">Configure AI Models</h3>
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          AI models configuration step will be implemented here.
        </AlertDescription>
      </Alert>
    </div>
  );
}

function CompleteStep({ onValidationChange }: StepProps) {
  useEffect(() => {
    onValidationChange(true);
  }, [onValidationChange]);

  return (
    <div className="text-center space-y-6">
      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
        <CheckCircle className="w-8 h-8 text-green-600" />
      </div>
      <div>
        <h3 className="text-xl font-semibold mb-2">Setup Complete!</h3>
        <p className="text-muted-foreground">
          You're all set to start transforming code with Metamorphic Reactor.
        </p>
      </div>
      <Badge variant="secondary" className="bg-green-100 text-green-800">
        Ready to Transform Code
      </Badge>
    </div>
  );
}

export default SetupWizard;
