import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { EnhancedSettingsPage } from '@/components/settings/EnhancedSettingsPage';
import { SettingsAPI } from '@/lib/api/settings';
import { SettingsMigration } from '@/lib/migration/settingsMigration';
import { 
  DEFAULT_PROVIDER_CONFIGS,
  DEFAULT_COST_GUARD_CONFIG,
  DEFAULT_TOKEN_MONITOR_CONFIG,
  DEFAULT_FAILOVER_CONFIG,
  DEFAULT_RETRY_CONFIG,
  DEFAULT_PERFORMANCE_CONFIG
} from '@/types/settings';

// Mock the API modules
vi.mock('@/lib/api/settings');
vi.mock('@/lib/migration/settingsMigration');
vi.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getUser: vi.fn().mockResolvedValue({
        data: { user: { id: 'test-user-id' } }
      })
    },
    from: vi.fn().mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({
            data: null,
            error: { code: 'PGRST116' }
          })
        })
      }),
      upsert: vi.fn().mockResolvedValue({ data: {}, error: null })
    })
  }
}));

const mockSettings = {
  id: 'test-id',
  user_id: 'test-user-id',
  planner_model: 'gpt-4o',
  critic_model: 'gpt-4o',
  default_max_iterations: 10,
  default_score_threshold: 0.95,
  telemetry_enabled: true,
  auto_create_pr: false,
  provider_configs: DEFAULT_PROVIDER_CONFIGS,
  cost_guard_config: DEFAULT_COST_GUARD_CONFIG,
  token_monitor_config: DEFAULT_TOKEN_MONITOR_CONFIG,
  failover_config: DEFAULT_FAILOVER_CONFIG,
  retry_config: DEFAULT_RETRY_CONFIG,
  performance_config: DEFAULT_PERFORMANCE_CONFIG,
  config_version: 2,
  validation_errors: [],
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
};

const mockCurrentUsage = {
  currentCost: 0.45,
  tokensUsed: 12500,
  requestsToday: 8,
  costToday: 1.23,
};

const mockProviderHealth = {
  'openai': { isHealthy: true, lastCheck: new Date(), responseTime: 245 },
  'vertex-ai': { isHealthy: true, lastCheck: new Date(), responseTime: 312 },
  'anthropic': { isHealthy: false, lastCheck: new Date(), responseTime: 1200 },
};

describe('Enhanced Settings Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock SettingsAPI methods
    vi.mocked(SettingsAPI.loadSettings).mockResolvedValue({
      success: true,
      data: mockSettings
    });
    
    vi.mocked(SettingsAPI.saveSettings).mockResolvedValue({
      success: true,
      data: mockSettings
    });
    
    vi.mocked(SettingsAPI.getCurrentUsage).mockResolvedValue({
      success: true,
      data: mockCurrentUsage
    });
    
    vi.mocked(SettingsAPI.getProviderHealth).mockResolvedValue({
      success: true,
      data: mockProviderHealth
    });
    
    vi.mocked(SettingsAPI.testProviderConnection).mockResolvedValue({
      success: true,
      data: { isValid: true, responseTime: 250 }
    });
    
    // Mock SettingsMigration methods
    vi.mocked(SettingsMigration.checkMigrationStatus).mockResolvedValue({
      needsMigration: false,
      currentVersion: 2,
      targetVersion: 2,
      affectedUsers: 0
    });
  });

  it('should render enhanced settings page with all tabs', async () => {
    const mockOnSave = vi.fn();
    const mockOnTest = vi.fn().mockResolvedValue(true);

    render(
      <EnhancedSettingsPage
        initialSettings={mockSettings}
        onSave={mockOnSave}
        onTest={mockOnTest}
        currentUsage={mockCurrentUsage}
        providerHealth={mockProviderHealth}
      />
    );

    // Check if main tabs are present
    expect(screen.getByText('Providers')).toBeInTheDocument();
    expect(screen.getByText('Cost & Tokens')).toBeInTheDocument();
    expect(screen.getByText('Reliability')).toBeInTheDocument();
    expect(screen.getByText('Monitoring')).toBeInTheDocument();

    // Check if the configuration version is displayed
    expect(screen.getByText('v2')).toBeInTheDocument();
  });

  it('should handle provider configuration changes', async () => {
    const mockOnSave = vi.fn();
    const mockOnTest = vi.fn().mockResolvedValue(true);

    render(
      <EnhancedSettingsPage
        initialSettings={mockSettings}
        onSave={mockOnSave}
        onTest={mockOnTest}
        currentUsage={mockCurrentUsage}
        providerHealth={mockProviderHealth}
      />
    );

    // Switch to providers tab (should be default)
    const providersTab = screen.getByText('Providers');
    fireEvent.click(providersTab);

    // Look for provider configuration elements
    await waitFor(() => {
      expect(screen.getByText('Planner Agent')).toBeInTheDocument();
      expect(screen.getByText('Critic Agent')).toBeInTheDocument();
    });
  });

  it('should handle cost management settings', async () => {
    const mockOnSave = vi.fn();

    render(
      <EnhancedSettingsPage
        initialSettings={mockSettings}
        onSave={mockOnSave}
        currentUsage={mockCurrentUsage}
        providerHealth={mockProviderHealth}
      />
    );

    // Switch to cost & tokens tab
    const costTab = screen.getByText('Cost & Tokens');
    fireEvent.click(costTab);

    await waitFor(() => {
      // Check if cost management elements are present
      expect(screen.getByText('Cost Guard')).toBeInTheDocument();
      expect(screen.getByText('Token Monitor')).toBeInTheDocument();
    });
  });

  it('should handle reliability and performance settings', async () => {
    const mockOnSave = vi.fn();

    render(
      <EnhancedSettingsPage
        initialSettings={mockSettings}
        onSave={mockOnSave}
        currentUsage={mockCurrentUsage}
        providerHealth={mockProviderHealth}
      />
    );

    // Switch to reliability tab
    const reliabilityTab = screen.getByText('Reliability');
    fireEvent.click(reliabilityTab);

    await waitFor(() => {
      // Check if reliability elements are present
      expect(screen.getByText('Failover Configuration')).toBeInTheDocument();
      expect(screen.getByText('Retry Settings')).toBeInTheDocument();
    });
  });

  it('should save settings successfully', async () => {
    const mockOnSave = vi.fn().mockResolvedValue(undefined);

    render(
      <EnhancedSettingsPage
        initialSettings={mockSettings}
        onSave={mockOnSave}
        currentUsage={mockCurrentUsage}
        providerHealth={mockProviderHealth}
      />
    );

    // Find and click save button
    const saveButton = screen.getByText('Save Settings');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalledWith(expect.objectContaining({
        config_version: 2,
        provider_configs: expect.any(Object),
        cost_guard_config: expect.any(Object),
      }));
    });
  });

  it('should handle validation errors', async () => {
    const invalidSettings = {
      ...mockSettings,
      cost_guard_config: {
        ...mockSettings.cost_guard_config,
        maxCostPerLoop: -1, // Invalid negative value
      },
    };

    const mockOnSave = vi.fn();

    render(
      <EnhancedSettingsPage
        initialSettings={invalidSettings}
        onSave={mockOnSave}
        currentUsage={mockCurrentUsage}
        providerHealth={mockProviderHealth}
      />
    );

    // Should show validation errors
    await waitFor(() => {
      expect(screen.getByText('Configuration Issues:')).toBeInTheDocument();
    });
  });

  it('should handle import/export functionality', async () => {
    const mockOnSave = vi.fn();

    render(
      <EnhancedSettingsPage
        initialSettings={mockSettings}
        onSave={mockOnSave}
        currentUsage={mockCurrentUsage}
        providerHealth={mockProviderHealth}
      />
    );

    // Find export button
    const exportButton = screen.getByText('Export');
    expect(exportButton).toBeInTheDocument();

    // Find import button
    const importButton = screen.getByText('Import');
    expect(importButton).toBeInTheDocument();
  });

  it('should test provider connections', async () => {
    const mockOnTest = vi.fn().mockResolvedValue(true);

    render(
      <EnhancedSettingsPage
        initialSettings={mockSettings}
        onTest={mockOnTest}
        currentUsage={mockCurrentUsage}
        providerHealth={mockProviderHealth}
      />
    );

    // Switch to providers tab
    const providersTab = screen.getByText('Providers');
    fireEvent.click(providersTab);

    // Look for test connection functionality
    await waitFor(() => {
      const testButtons = screen.getAllByText(/test/i);
      expect(testButtons.length).toBeGreaterThan(0);
    });
  });

  it('should display real-time usage data', async () => {
    const mockOnSave = vi.fn();

    render(
      <EnhancedSettingsPage
        initialSettings={mockSettings}
        onSave={mockOnSave}
        currentUsage={mockCurrentUsage}
        providerHealth={mockProviderHealth}
      />
    );

    // Switch to monitoring tab
    const monitoringTab = screen.getByText('Monitoring');
    fireEvent.click(monitoringTab);

    await waitFor(() => {
      expect(screen.getByText('Real-time monitoring dashboard will be implemented here')).toBeInTheDocument();
    });
  });

  it('should handle migration from legacy settings', async () => {
    const legacySettings = {
      id: 'test-id',
      user_id: 'test-user-id',
      planner_model: 'gpt-4-turbo',
      critic_model: 'claude-3-sonnet',
      default_max_iterations: 10,
      default_score_threshold: 0.95,
      telemetry_enabled: true,
      auto_create_pr: false,
      // No enhanced fields
    };

    vi.mocked(SettingsAPI.loadSettings).mockResolvedValue({
      success: true,
      data: legacySettings as any
    });

    vi.mocked(SettingsMigration.checkMigrationStatus).mockResolvedValue({
      needsMigration: true,
      currentVersion: 1,
      targetVersion: 2,
      affectedUsers: 1
    });

    const mockOnSave = vi.fn();

    render(
      <EnhancedSettingsPage
        initialSettings={legacySettings as any}
        onSave={mockOnSave}
        currentUsage={mockCurrentUsage}
        providerHealth={mockProviderHealth}
      />
    );

    // Should still render the enhanced interface
    expect(screen.getByText('Enhanced Settings')).toBeInTheDocument();
  });

  it('should handle API errors gracefully', async () => {
    vi.mocked(SettingsAPI.saveSettings).mockResolvedValue({
      success: false,
      error: 'Network error'
    });

    const mockOnSave = vi.fn().mockRejectedValue(new Error('Network error'));

    render(
      <EnhancedSettingsPage
        initialSettings={mockSettings}
        onSave={mockOnSave}
        currentUsage={mockCurrentUsage}
        providerHealth={mockProviderHealth}
      />
    );

    // Try to save settings
    const saveButton = screen.getByText('Save Settings');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(screen.getByText('Failed to save settings. Please check your configuration and try again.')).toBeInTheDocument();
    });
  });

  it('should validate configuration in real-time', async () => {
    const mockOnSave = vi.fn();

    render(
      <EnhancedSettingsPage
        initialSettings={mockSettings}
        onSave={mockOnSave}
        currentUsage={mockCurrentUsage}
        providerHealth={mockProviderHealth}
      />
    );

    // Should show validation status
    await waitFor(() => {
      // Look for validation indicators
      const validationElements = screen.getAllByTestId(/validation|check|error/i);
      expect(validationElements.length).toBeGreaterThanOrEqual(0);
    });
  });
});
});
