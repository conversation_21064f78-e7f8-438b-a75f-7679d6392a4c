import { JSONPatch } from '@metamorphic-reactor/agents';

export interface GitHubConfig {
  token: string;
  owner: string;
  repo: string;
}

export interface PullRequestResult {
  url: string;
  number: number;
  branch: string;
  title: string;
}

export class GitHubService {
  private config: GitHubConfig;
  private baseUrl = 'https://api.github.com';

  constructor(config: GitHubConfig) {
    this.config = config;
  }

  private async makeRequest(endpoint: string, options: RequestInit = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    const response = await fetch(url, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.config.token}`,
        'Accept': 'application/vnd.github.v3+json',
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`GitHub API error: ${response.status} ${response.statusText} - ${error}`);
    }

    return response.json();
  }

  async createReactorBranch(sessionId: string): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const branchName = `reactor/${timestamp}-${sessionId.slice(0, 8)}`;

    // Get the default branch SHA
    const repo = await this.makeRequest(`/repos/${this.config.owner}/${this.config.repo}`);
    const defaultBranch = repo.default_branch;

    const branchRef = await this.makeRequest(
      `/repos/${this.config.owner}/${this.config.repo}/git/refs/heads/${defaultBranch}`
    );

    // Create new branch
    await this.makeRequest(`/repos/${this.config.owner}/${this.config.repo}/git/refs`, {
      method: 'POST',
      body: JSON.stringify({
        ref: `refs/heads/${branchName}`,
        sha: branchRef.object.sha,
      }),
    });

    return branchName;
  }

  async commitPatch(
    branchName: string,
    patch: JSONPatch,
    sessionId: string,
    originalPrompt: string
  ): Promise<void> {
    // Create patch file content
    const patchContent = {
      sessionId,
      timestamp: new Date().toISOString(),
      originalPrompt,
      patch,
      metadata: {
        confidence: patch.confidence,
        description: patch.description,
        operationsCount: patch.operations.length,
      },
    };

    const fileName = `reactor-patches/${sessionId}.json`;
    const content = JSON.stringify(patchContent, null, 2);
    const encodedContent = Buffer.from(content).toString('base64');

    // Check if file exists
    let fileSha: string | undefined;
    try {
      const existingFile = await this.makeRequest(
        `/repos/${this.config.owner}/${this.config.repo}/contents/${fileName}?ref=${branchName}`
      );
      fileSha = existingFile.sha;
    } catch (error) {
      // File doesn't exist, which is fine for new files
    }

    // Create or update file
    const commitData: any = {
      message: `feat: reactor auto patch\n\nGenerated by Metamorphic Reactor\nSession: ${sessionId}\nPrompt: ${originalPrompt.slice(0, 100)}${originalPrompt.length > 100 ? '...' : ''}\nConfidence: ${(patch.confidence * 100).toFixed(1)}%`,
      content: encodedContent,
      branch: branchName,
    };

    if (fileSha) {
      commitData.sha = fileSha;
    }

    await this.makeRequest(
      `/repos/${this.config.owner}/${this.config.repo}/contents/${fileName}`,
      {
        method: 'PUT',
        body: JSON.stringify(commitData),
      }
    );
  }

  async createDraftPR(
    branchName: string,
    sessionId: string,
    originalPrompt: string,
    score: number,
    iterations: number
  ): Promise<PullRequestResult> {
    const title = `feat: reactor auto patch`;
    const body = `# 🔮 Metamorphic Reactor Auto Patch

## Session Details
- **Session ID**: \`${sessionId}\`
- **Final Score**: ${(score * 100).toFixed(1)}%
- **Iterations**: ${iterations}
- **Generated**: ${new Date().toISOString()}

## Original Prompt
\`\`\`
${originalPrompt}
\`\`\`

## Summary
This PR contains an automatically generated patch from the Metamorphic Reactor dual-agent system. The patch has been analyzed and refined through ${iterations} iterations to achieve a quality score of ${(score * 100).toFixed(1)}%.

## Files Changed
- \`reactor-patches/${sessionId}.json\` - Generated patch file

## Review Notes
- This is a draft PR generated automatically
- Please review the patch file for the specific changes
- The patch can be applied using the JSON Patch format
- Consider testing the changes before merging

---
*Generated by Metamorphic Reactor v1.0.0*`;

    const prData = {
      title,
      body,
      head: branchName,
      base: 'main', // Assuming main is the default branch
      draft: true,
    };

    const pr = await this.makeRequest(
      `/repos/${this.config.owner}/${this.config.repo}/pulls`,
      {
        method: 'POST',
        body: JSON.stringify(prData),
      }
    );

    return {
      url: pr.html_url,
      number: pr.number,
      branch: branchName,
      title,
    };
  }

  async createReactorPR(
    sessionId: string,
    patch: JSONPatch,
    originalPrompt: string,
    score: number,
    iterations: number
  ): Promise<PullRequestResult> {
    try {
      console.log(`🔀 Creating reactor PR for session ${sessionId}`);

      // Step 1: Create branch
      const branchName = await this.createReactorBranch(sessionId);
      console.log(`📝 Created branch: ${branchName}`);

      // Step 2: Commit patch
      await this.commitPatch(branchName, patch, sessionId, originalPrompt);
      console.log(`💾 Committed patch to branch`);

      // Step 3: Create draft PR
      const prResult = await this.createDraftPR(
        branchName,
        sessionId,
        originalPrompt,
        score,
        iterations
      );
      console.log(`🎉 Created draft PR: ${prResult.url}`);

      return prResult;
    } catch (error) {
      console.error('❌ Failed to create reactor PR:', error);
      throw error;
    }
  }

  // Additional methods for autonomous PR creation
  async getRepository(owner: string, repo: string): Promise<any> {
    return this.makeRequest(`/repos/${owner}/${repo}`);
  }

  async getRepositoryFiles(owner: string, repo: string, branch: string = 'main'): Promise<any[]> {
    const response = await this.makeRequest(`/repos/${owner}/${repo}/git/trees/${branch}?recursive=1`);
    return response.tree || [];
  }

  async getFileContent(owner: string, repo: string, path: string, branch: string = 'main'): Promise<string> {
    const response = await this.makeRequest(`/repos/${owner}/${repo}/contents/${path}?ref=${branch}`);
    return Buffer.from(response.content, 'base64').toString('utf-8');
  }

  async getBranch(owner: string, repo: string, branch: string): Promise<any> {
    return this.makeRequest(`/repos/${owner}/${repo}/branches/${branch}`);
  }

  async createBranch(owner: string, repo: string, branchName: string, baseSha: string): Promise<any> {
    return this.makeRequest(`/repos/${owner}/${repo}/git/refs`, {
      method: 'POST',
      body: JSON.stringify({
        ref: `refs/heads/${branchName}`,
        sha: baseSha,
      }),
    });
  }

  async updateFile(
    owner: string,
    repo: string,
    path: string,
    content: string,
    message: string,
    branch: string,
    sha?: string
  ): Promise<any> {
    const encodedContent = Buffer.from(content).toString('base64');

    const body: any = {
      message,
      content: encodedContent,
      branch,
    };

    if (sha) {
      body.sha = sha;
    }

    return this.makeRequest(`/repos/${owner}/${repo}/contents/${path}`, {
      method: 'PUT',
      body: JSON.stringify(body),
    });
  }

  async createPullRequest(
    owner: string,
    repo: string,
    title: string,
    body: string,
    head: string,
    base: string,
    isDraft: boolean = true
  ): Promise<any> {
    return this.makeRequest(`/repos/${owner}/${repo}/pulls`, {
      method: 'POST',
      body: JSON.stringify({
        title,
        body,
        head,
        base,
        draft: isDraft,
      }),
    });
  }
}

// Factory function to create GitHub service from environment
export function createGitHubService(): GitHubService | null {
  const token = process.env.GITHUB_TOKEN;
  const owner = process.env.GITHUB_REPO_OWNER;
  const repo = process.env.GITHUB_REPO_NAME;

  if (!token || !owner || !repo) {
    console.warn('⚠️  GitHub integration disabled: missing environment variables');
    return null;
  }

  return new GitHubService({ token, owner, repo });
}

// Singleton instance for autonomous operations
let githubServiceInstance: GitHubService | null = null;

export const githubService = {
  getInstance(): GitHubService {
    if (!githubServiceInstance) {
      githubServiceInstance = createGitHubService();
      if (!githubServiceInstance) {
        throw new Error('GitHub service not configured. Please set GITHUB_TOKEN, GITHUB_REPO_OWNER, and GITHUB_REPO_NAME environment variables.');
      }
    }
    return githubServiceInstance;
  },

  // Proxy methods for autonomous operations
  async getRepository(owner: string, repo: string): Promise<any> {
    return this.getInstance().getRepository(owner, repo);
  },

  async getRepositoryFiles(owner: string, repo: string, branch?: string): Promise<any[]> {
    return this.getInstance().getRepositoryFiles(owner, repo, branch);
  },

  async getFileContent(owner: string, repo: string, path: string, branch?: string): Promise<string> {
    return this.getInstance().getFileContent(owner, repo, path, branch);
  },

  async getBranch(owner: string, repo: string, branch: string): Promise<any> {
    return this.getInstance().getBranch(owner, repo, branch);
  },

  async createBranch(owner: string, repo: string, branchName: string, baseSha: string): Promise<any> {
    return this.getInstance().createBranch(owner, repo, branchName, baseSha);
  },

  async updateFile(
    owner: string,
    repo: string,
    path: string,
    content: string,
    message: string,
    branch: string,
    sha?: string
  ): Promise<any> {
    return this.getInstance().updateFile(owner, repo, path, content, message, branch, sha);
  },

  async createPullRequest(
    owner: string,
    repo: string,
    title: string,
    body: string,
    head: string,
    base: string,
    isDraft?: boolean
  ): Promise<any> {
    return this.getInstance().createPullRequest(owner, repo, title, body, head, base, isDraft);
  }
};
