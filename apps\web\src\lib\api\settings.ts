import { supabase } from '@/lib/supabase';
import { 
  EnhancedUserSettings, 
  DatabaseSettings, 
  validateEnhancedUserSettings,
  migrateFromLegacySettings 
} from '@/types/settings';

/**
 * Enhanced Settings API
 * Handles CRUD operations for the new enhanced settings structure
 */

export interface SettingsAPIResponse<T = any> {
  data?: T;
  error?: string;
  success: boolean;
}

export interface SettingsValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export class SettingsAPI {
  /**
   * Load user settings with automatic migration from legacy format
   */
  static async loadSettings(): Promise<SettingsAPIResponse<EnhancedUserSettings>> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      const { data, error } = await supabase
        .from('settings')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') { // Not found error
        console.error('Error loading settings:', error);
        return { success: false, error: 'Failed to load settings' };
      }

      if (data) {
        // Check if we have enhanced settings or need to migrate
        if (data.provider_configs && data.config_version >= 2) {
          // Already enhanced settings
          const enhancedSettings = this.mapDatabaseToEnhanced(data);
          return { success: true, data: enhancedSettings };
        } else {
          // Legacy settings - migrate to new format
          const migratedSettings = migrateFromLegacySettings(data);
          return { success: true, data: migratedSettings as EnhancedUserSettings };
        }
      } else {
        // No settings found - return default enhanced settings
        const defaultSettings = migrateFromLegacySettings({
          planner_model: "gpt-4o",
          critic_model: "gpt-4o",
          default_max_iterations: 10,
          default_score_threshold: 0.95,
          telemetry_enabled: true,
          auto_create_pr: false,
        });
        return { success: true, data: defaultSettings as EnhancedUserSettings };
      }
    } catch (error) {
      console.error('Error in loadSettings:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Save enhanced user settings with validation and encryption
   */
  static async saveSettings(settings: EnhancedUserSettings): Promise<SettingsAPIResponse<EnhancedUserSettings>> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      // Validate settings before saving
      const validation = await this.validateSettings(settings);
      if (!validation.isValid) {
        return { 
          success: false, 
          error: `Validation failed: ${validation.errors.join(', ')}` 
        };
      }

      // Encrypt API keys
      const encryptedKeys = await this.encryptAPIKeys(settings.api_keys || {});

      // Prepare database settings
      const databaseSettings: Partial<DatabaseSettings> = {
        user_id: user.id,
        
        // Legacy fields for backward compatibility
        planner_model: settings.planner_model,
        critic_model: settings.critic_model,
        default_max_iterations: settings.default_max_iterations,
        default_score_threshold: settings.default_score_threshold,
        telemetry_enabled: settings.telemetry_enabled,
        auto_create_pr: settings.auto_create_pr,
        github_repo_owner: settings.github_repo_owner || null,
        github_repo_name: settings.github_repo_name || null,
        
        // Enhanced configuration (JSONB fields)
        provider_configs: settings.provider_configs,
        cost_guard_config: settings.cost_guard_config,
        token_monitor_config: settings.token_monitor_config,
        failover_config: settings.failover_config,
        retry_config: settings.retry_config,
        performance_config: settings.performance_config,
        
        // Encrypted API keys
        openai_api_key_encrypted: encryptedKeys.openai || null,
        anthropic_api_key_encrypted: encryptedKeys.anthropic || null,
        google_api_key_encrypted: encryptedKeys.google || null,
        
        // Vertex AI configuration
        vertex_ai_project_id: settings.vertex_ai_project_id || null,
        vertex_ai_location: settings.vertex_ai_location || 'us-central1',
        vertex_ai_credentials_encrypted: encryptedKeys.vertex_ai_credentials || null,
        
        // Metadata
        config_version: settings.config_version || 2,
        updated_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from('settings')
        .upsert(databaseSettings, { onConflict: 'user_id' })
        .select()
        .single();

      if (error) {
        console.error('Error saving settings:', error);
        return { success: false, error: 'Failed to save settings' };
      }

      // Return the enhanced settings
      const savedSettings = this.mapDatabaseToEnhanced(data);
      return { success: true, data: savedSettings };

    } catch (error) {
      console.error('Error in saveSettings:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Validate settings configuration
   */
  static async validateSettings(settings: EnhancedUserSettings): Promise<SettingsValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Use Zod validation
      validateEnhancedUserSettings(settings);
    } catch (error: any) {
      if (error.errors) {
        errors.push(...error.errors.map((e: any) => e.message));
      } else {
        errors.push(error.message);
      }
    }

    // Additional business logic validation
    if (settings.cost_guard_config.enabled && settings.cost_guard_config.maxCostPerLoop > 20) {
      warnings.push('Cost limit is very high ($20+). Consider lowering for safety.');
    }

    if (settings.provider_configs.fallback_providers.length === 0 && settings.failover_config.enabled) {
      errors.push('Failover is enabled but no fallback providers are configured.');
    }

    if (settings.performance_config.parallelExecution && settings.performance_config.maxConcurrency > 5) {
      warnings.push('High concurrency may increase costs significantly.');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Test provider connection
   */
  static async testProviderConnection(
    providerType: string,
    credentials: any
  ): Promise<SettingsAPIResponse<{ isValid: boolean; responseTime: number }>> {
    try {
      const startTime = Date.now();

      // For now, simulate the test since we don't have the edge function yet
      // TODO: Replace with actual edge function call when implemented
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000));

      const responseTime = Date.now() - startTime;
      const isValid = Math.random() > 0.2; // 80% success rate for demo

      return {
        success: true,
        data: {
          isValid,
          responseTime
        }
      };
    } catch (error) {
      console.error('Error testing provider connection:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Connection test failed'
      };
    }
  }

  /**
   * Get current usage statistics
   */
  static async getCurrentUsage(): Promise<SettingsAPIResponse<{
    currentCost: number;
    tokensUsed: number;
    requestsToday: number;
    costToday: number;
  }>> {
    try {
      // For now, return simulated data
      // TODO: Replace with actual usage tracking when implemented
      const data = {
        currentCost: 0.45 + Math.random() * 0.5,
        tokensUsed: Math.floor(10000 + Math.random() * 20000),
        requestsToday: Math.floor(5 + Math.random() * 15),
        costToday: 1.23 + Math.random() * 2,
      };

      return { success: true, data };
    } catch (error) {
      console.error('Error getting usage stats:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get usage stats'
      };
    }
  }

  /**
   * Get provider health status
   */
  static async getProviderHealth(): Promise<SettingsAPIResponse<Record<string, {
    isHealthy: boolean;
    lastCheck: Date;
    responseTime: number;
  }>>> {
    try {
      // For now, return simulated health data
      // TODO: Replace with actual health monitoring when implemented
      const healthData = {
        'openai': {
          isHealthy: Math.random() > 0.1,
          lastCheck: new Date(),
          responseTime: 200 + Math.random() * 300
        },
        'vertex-ai': {
          isHealthy: Math.random() > 0.15,
          lastCheck: new Date(),
          responseTime: 300 + Math.random() * 400
        },
        'anthropic': {
          isHealthy: Math.random() > 0.2,
          lastCheck: new Date(),
          responseTime: 250 + Math.random() * 350
        },
      };

      return { success: true, data: healthData };
    } catch (error) {
      console.error('Error getting provider health:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get provider health'
      };
    }
  }

  /**
   * Export settings configuration
   */
  static async exportSettings(): Promise<SettingsAPIResponse<string>> {
    try {
      const settingsResult = await this.loadSettings();
      if (!settingsResult.success || !settingsResult.data) {
        return { success: false, error: 'Failed to load settings for export' };
      }

      const exportData = {
        settings: {
          ...settingsResult.data,
          // Mask sensitive data
          api_keys: settingsResult.data.api_keys ? {
            openai: settingsResult.data.api_keys.openai ? '***MASKED***' : undefined,
            anthropic: settingsResult.data.api_keys.anthropic ? '***MASKED***' : undefined,
            vertex_ai_credentials: settingsResult.data.api_keys.vertex_ai_credentials ? '***MASKED***' : undefined,
          } : undefined,
        },
        exportedAt: new Date().toISOString(),
        version: settingsResult.data.config_version,
      };

      const jsonString = JSON.stringify(exportData, null, 2);
      return { success: true, data: jsonString };
    } catch (error) {
      console.error('Error exporting settings:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Export failed' 
      };
    }
  }

  /**
   * Private helper methods
   */
  private static async encryptAPIKeys(apiKeys: any): Promise<Record<string, string | null>> {
    const encrypted: Record<string, string | null> = {};

    if (apiKeys.openai) {
      const { data } = await supabase.rpc('encrypt_secret', { secret: apiKeys.openai });
      encrypted.openai = data;
    }

    if (apiKeys.anthropic) {
      const { data } = await supabase.rpc('encrypt_secret', { secret: apiKeys.anthropic });
      encrypted.anthropic = data;
    }

    if (apiKeys.google) {
      const { data } = await supabase.rpc('encrypt_secret', { secret: apiKeys.google });
      encrypted.google = data;
    }

    if (apiKeys.vertex_ai_credentials) {
      const credentialsString = JSON.stringify(apiKeys.vertex_ai_credentials);
      const { data } = await supabase.rpc('encrypt_secret', { secret: credentialsString });
      encrypted.vertex_ai_credentials = data;
    }

    return encrypted;
  }

  private static mapDatabaseToEnhanced(dbSettings: DatabaseSettings): EnhancedUserSettings {
    return {
      id: dbSettings.id,
      user_id: dbSettings.user_id,
      
      // Legacy fields
      planner_model: dbSettings.planner_model,
      critic_model: dbSettings.critic_model,
      default_max_iterations: dbSettings.default_max_iterations,
      default_score_threshold: dbSettings.default_score_threshold,
      telemetry_enabled: dbSettings.telemetry_enabled,
      auto_create_pr: dbSettings.auto_create_pr,
      github_repo_owner: dbSettings.github_repo_owner || undefined,
      github_repo_name: dbSettings.github_repo_name || undefined,
      
      // Enhanced configuration
      provider_configs: dbSettings.provider_configs,
      cost_guard_config: dbSettings.cost_guard_config,
      token_monitor_config: dbSettings.token_monitor_config,
      failover_config: dbSettings.failover_config,
      retry_config: dbSettings.retry_config,
      performance_config: dbSettings.performance_config,
      
      // Vertex AI configuration
      vertex_ai_project_id: dbSettings.vertex_ai_project_id || undefined,
      vertex_ai_location: dbSettings.vertex_ai_location || undefined,
      
      // Metadata
      config_version: dbSettings.config_version,
      last_validated_at: dbSettings.last_validated_at || undefined,
      validation_errors: Array.isArray(dbSettings.validation_errors) ? dbSettings.validation_errors : [],
      
      // Timestamps
      created_at: dbSettings.created_at,
      updated_at: dbSettings.updated_at,
    };
  }
}
