import { supabase } from '@/lib/supabase';
import { 
  EnhancedUserSettings, 
  DatabaseSettings,
  migrateFromLegacySettings,
  validateEnhancedUserSettings,
  DEFAULT_PROVIDER_CONFIGS,
  DEFAULT_COST_GUARD_CONFIG,
  DEFAULT_TOKEN_MONITOR_CONFIG,
  DEFAULT_FAILOVER_CONFIG,
  DEFAULT_RETRY_CONFIG,
  DEFAULT_PERFORMANCE_CONFIG
} from '@/types/settings';

/**
 * Settings Migration System
 * Handles backward compatibility and data migration between settings versions
 */

export interface MigrationResult {
  success: boolean;
  migratedCount: number;
  errors: string[];
  warnings: string[];
}

export interface MigrationStatus {
  needsMigration: boolean;
  currentVersion: number;
  targetVersion: number;
  affectedUsers: number;
}

export class SettingsMigration {
  private static readonly CURRENT_VERSION = 2;
  private static readonly LEGACY_VERSION = 1;

  /**
   * Check if migration is needed for the current user
   */
  static async checkMigrationStatus(): Promise<MigrationStatus> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('settings')
        .select('config_version')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      const currentVersion = data?.config_version || this.LEGACY_VERSION;
      const needsMigration = currentVersion < this.CURRENT_VERSION;

      return {
        needsMigration,
        currentVersion,
        targetVersion: this.CURRENT_VERSION,
        affectedUsers: needsMigration ? 1 : 0,
      };
    } catch (error) {
      console.error('Error checking migration status:', error);
      return {
        needsMigration: false,
        currentVersion: this.CURRENT_VERSION,
        targetVersion: this.CURRENT_VERSION,
        affectedUsers: 0,
      };
    }
  }

  /**
   * Migrate user settings from legacy to enhanced format
   */
  static async migrateUserSettings(): Promise<MigrationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    let migratedCount = 0;

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Load current settings
      const { data: currentSettings, error: loadError } = await supabase
        .from('settings')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (loadError && loadError.code !== 'PGRST116') {
        errors.push(`Failed to load settings: ${loadError.message}`);
        return { success: false, migratedCount: 0, errors, warnings };
      }

      if (!currentSettings) {
        // No settings exist - create default enhanced settings
        const defaultSettings = this.createDefaultEnhancedSettings(user.id);
        const { error: createError } = await supabase
          .from('settings')
          .insert(defaultSettings);

        if (createError) {
          errors.push(`Failed to create default settings: ${createError.message}`);
          return { success: false, migratedCount: 0, errors, warnings };
        }

        migratedCount = 1;
        warnings.push('Created default enhanced settings');
      } else if ((currentSettings.config_version || 1) < this.CURRENT_VERSION) {
        // Migrate existing legacy settings
        const migrationResult = await this.performMigration(currentSettings);
        
        if (!migrationResult.success) {
          errors.push(...migrationResult.errors);
          return { success: false, migratedCount: 0, errors, warnings };
        }

        migratedCount = 1;
        warnings.push(...migrationResult.warnings);
      }

      return {
        success: true,
        migratedCount,
        errors,
        warnings,
      };
    } catch (error) {
      errors.push(error instanceof Error ? error.message : 'Unknown migration error');
      return { success: false, migratedCount: 0, errors, warnings };
    }
  }

  /**
   * Perform the actual migration of settings data
   */
  private static async performMigration(legacySettings: any): Promise<{
    success: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Migrate legacy settings to enhanced format
      const enhancedSettings = migrateFromLegacySettings(legacySettings);

      // Validate the migrated settings
      try {
        validateEnhancedUserSettings(enhancedSettings);
      } catch (validationError: any) {
        warnings.push('Some migrated settings failed validation and were reset to defaults');
        
        // Reset invalid configurations to defaults
        if (validationError.errors?.some((e: any) => e.path.includes('provider_configs'))) {
          enhancedSettings.provider_configs = DEFAULT_PROVIDER_CONFIGS;
          warnings.push('Provider configurations reset to defaults');
        }
      }

      // Prepare database update
      const databaseSettings: Partial<DatabaseSettings> = {
        // Legacy fields (preserve existing values)
        planner_model: enhancedSettings.planner_model,
        critic_model: enhancedSettings.critic_model,
        default_max_iterations: enhancedSettings.default_max_iterations,
        default_score_threshold: enhancedSettings.default_score_threshold,
        telemetry_enabled: enhancedSettings.telemetry_enabled,
        auto_create_pr: enhancedSettings.auto_create_pr,
        github_repo_owner: enhancedSettings.github_repo_owner || null,
        github_repo_name: enhancedSettings.github_repo_name || null,

        // Enhanced configuration (new JSONB fields)
        provider_configs: enhancedSettings.provider_configs,
        cost_guard_config: enhancedSettings.cost_guard_config,
        token_monitor_config: enhancedSettings.token_monitor_config,
        failover_config: enhancedSettings.failover_config,
        retry_config: enhancedSettings.retry_config,
        performance_config: enhancedSettings.performance_config,

        // Metadata
        config_version: this.CURRENT_VERSION,
        updated_at: new Date().toISOString(),
      };

      // Update the database
      const { error: updateError } = await supabase
        .from('settings')
        .update(databaseSettings)
        .eq('user_id', legacySettings.user_id);

      if (updateError) {
        errors.push(`Failed to update settings: ${updateError.message}`);
        return { success: false, errors, warnings };
      }

      warnings.push(`Successfully migrated settings from v${legacySettings.config_version || 1} to v${this.CURRENT_VERSION}`);
      
      return { success: true, errors, warnings };
    } catch (error) {
      errors.push(error instanceof Error ? error.message : 'Migration failed');
      return { success: false, errors, warnings };
    }
  }

  /**
   * Create default enhanced settings for new users
   */
  private static createDefaultEnhancedSettings(userId: string): Partial<DatabaseSettings> {
    return {
      user_id: userId,
      
      // Legacy defaults
      planner_model: 'gpt-4o',
      critic_model: 'gpt-4o',
      default_max_iterations: 10,
      default_score_threshold: 0.95,
      telemetry_enabled: true,
      auto_create_pr: false,
      
      // Enhanced configuration defaults
      provider_configs: DEFAULT_PROVIDER_CONFIGS,
      cost_guard_config: DEFAULT_COST_GUARD_CONFIG,
      token_monitor_config: DEFAULT_TOKEN_MONITOR_CONFIG,
      failover_config: DEFAULT_FAILOVER_CONFIG,
      retry_config: DEFAULT_RETRY_CONFIG,
      performance_config: DEFAULT_PERFORMANCE_CONFIG,
      
      // Metadata
      config_version: this.CURRENT_VERSION,
      validation_errors: [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
  }

  /**
   * Rollback migration (emergency use only)
   */
  static async rollbackMigration(): Promise<MigrationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Load current enhanced settings
      const { data: currentSettings, error: loadError } = await supabase
        .from('settings')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (loadError) {
        errors.push(`Failed to load settings for rollback: ${loadError.message}`);
        return { success: false, migratedCount: 0, errors, warnings };
      }

      if (!currentSettings || currentSettings.config_version < this.CURRENT_VERSION) {
        warnings.push('No enhanced settings found to rollback');
        return { success: true, migratedCount: 0, errors, warnings };
      }

      // Create legacy-compatible settings
      const legacySettings = {
        planner_model: currentSettings.planner_model,
        critic_model: currentSettings.critic_model,
        default_max_iterations: currentSettings.default_max_iterations,
        default_score_threshold: currentSettings.default_score_threshold,
        telemetry_enabled: currentSettings.telemetry_enabled,
        auto_create_pr: currentSettings.auto_create_pr,
        github_repo_owner: currentSettings.github_repo_owner,
        github_repo_name: currentSettings.github_repo_name,
        
        // Clear enhanced fields
        provider_configs: null,
        cost_guard_config: null,
        token_monitor_config: null,
        failover_config: null,
        retry_config: null,
        performance_config: null,
        
        config_version: this.LEGACY_VERSION,
        updated_at: new Date().toISOString(),
      };

      const { error: updateError } = await supabase
        .from('settings')
        .update(legacySettings)
        .eq('user_id', user.id);

      if (updateError) {
        errors.push(`Failed to rollback settings: ${updateError.message}`);
        return { success: false, migratedCount: 0, errors, warnings };
      }

      warnings.push('Settings rolled back to legacy format');
      return { success: true, migratedCount: 1, errors, warnings };
    } catch (error) {
      errors.push(error instanceof Error ? error.message : 'Rollback failed');
      return { success: false, migratedCount: 0, errors, warnings };
    }
  }

  /**
   * Validate data integrity after migration
   */
  static async validateMigration(): Promise<{
    isValid: boolean;
    issues: string[];
  }> {
    const issues: string[] = [];

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        issues.push('User not authenticated');
        return { isValid: false, issues };
      }

      const { data: settings, error } = await supabase
        .from('settings')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error) {
        issues.push(`Failed to load settings: ${error.message}`);
        return { isValid: false, issues };
      }

      if (!settings) {
        issues.push('No settings found');
        return { isValid: false, issues };
      }

      // Check version
      if (settings.config_version !== this.CURRENT_VERSION) {
        issues.push(`Incorrect version: expected ${this.CURRENT_VERSION}, got ${settings.config_version}`);
      }

      // Check required enhanced fields
      if (!settings.provider_configs) {
        issues.push('Missing provider_configs');
      }

      if (!settings.cost_guard_config) {
        issues.push('Missing cost_guard_config');
      }

      // Validate enhanced settings structure
      try {
        const enhancedSettings = this.mapDatabaseToEnhanced(settings);
        validateEnhancedUserSettings(enhancedSettings);
      } catch (validationError: any) {
        issues.push(`Settings validation failed: ${validationError.message}`);
      }

      return { isValid: issues.length === 0, issues };
    } catch (error) {
      issues.push(error instanceof Error ? error.message : 'Validation failed');
      return { isValid: false, issues };
    }
  }

  /**
   * Helper method to map database settings to enhanced format
   */
  private static mapDatabaseToEnhanced(dbSettings: any): EnhancedUserSettings {
    return {
      id: dbSettings.id,
      user_id: dbSettings.user_id,
      planner_model: dbSettings.planner_model,
      critic_model: dbSettings.critic_model,
      default_max_iterations: dbSettings.default_max_iterations,
      default_score_threshold: dbSettings.default_score_threshold,
      telemetry_enabled: dbSettings.telemetry_enabled,
      auto_create_pr: dbSettings.auto_create_pr,
      github_repo_owner: dbSettings.github_repo_owner,
      github_repo_name: dbSettings.github_repo_name,
      provider_configs: dbSettings.provider_configs || DEFAULT_PROVIDER_CONFIGS,
      cost_guard_config: dbSettings.cost_guard_config || DEFAULT_COST_GUARD_CONFIG,
      token_monitor_config: dbSettings.token_monitor_config || DEFAULT_TOKEN_MONITOR_CONFIG,
      failover_config: dbSettings.failover_config || DEFAULT_FAILOVER_CONFIG,
      retry_config: dbSettings.retry_config || DEFAULT_RETRY_CONFIG,
      performance_config: dbSettings.performance_config || DEFAULT_PERFORMANCE_CONFIG,
      vertex_ai_project_id: dbSettings.vertex_ai_project_id,
      vertex_ai_location: dbSettings.vertex_ai_location,
      config_version: dbSettings.config_version || this.CURRENT_VERSION,
      last_validated_at: dbSettings.last_validated_at,
      validation_errors: dbSettings.validation_errors || [],
      created_at: dbSettings.created_at,
      updated_at: dbSettings.updated_at,
    };
  }
}
