import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Code, 
  Plus, 
  Search, 
  Copy, 
  Edit, 
  Trash2, 
  Star, 
  StarOff, 
  Tag, 
  FileText,
  Zap,
  Download,
  Upload
} from 'lucide-react';

interface CodeSnippet {
  id: string;
  name: string;
  description: string;
  code: string;
  language: string;
  tags: string[];
  isFavorite: boolean;
  category: string;
  createdAt: Date;
  updatedAt: Date;
  usageCount: number;
}

interface CodeTemplate {
  id: string;
  name: string;
  description: string;
  files: {
    name: string;
    content: string;
    language: string;
  }[];
  category: string;
  tags: string[];
  variables: {
    name: string;
    description: string;
    defaultValue: string;
    type: 'string' | 'number' | 'boolean';
  }[];
}

interface CodeSnippetsProps {
  onInsertSnippet: (code: string) => void;
  onCreateFromTemplate: (template: CodeTemplate, variables: Record<string, any>) => void;
  language?: string;
}

const defaultSnippets: CodeSnippet[] = [
  {
    id: '1',
    name: 'React Functional Component',
    description: 'Basic React functional component with TypeScript',
    code: `import React from 'react';

interface {{ComponentName}}Props {
  // Add props here
}

export const {{ComponentName}}: React.FC<{{ComponentName}}Props> = () => {
  return (
    <div>
      <h1>{{ComponentName}}</h1>
    </div>
  );
};`,
    language: 'typescript',
    tags: ['react', 'component', 'typescript'],
    isFavorite: true,
    category: 'React',
    createdAt: new Date(),
    updatedAt: new Date(),
    usageCount: 15
  },
  {
    id: '2',
    name: 'useState Hook',
    description: 'React useState hook with TypeScript',
    code: `const [{{stateName}}, set{{StateName}}] = useState<{{StateType}}>({{initialValue}});`,
    language: 'typescript',
    tags: ['react', 'hooks', 'state'],
    isFavorite: true,
    category: 'React Hooks',
    createdAt: new Date(),
    updatedAt: new Date(),
    usageCount: 32
  },
  {
    id: '3',
    name: 'useEffect Hook',
    description: 'React useEffect hook with cleanup',
    code: `useEffect(() => {
  // Effect logic here
  {{effectLogic}}

  return () => {
    // Cleanup logic here
    {{cleanupLogic}}
  };
}, [{{dependencies}}]);`,
    language: 'typescript',
    tags: ['react', 'hooks', 'effect'],
    isFavorite: false,
    category: 'React Hooks',
    createdAt: new Date(),
    updatedAt: new Date(),
    usageCount: 28
  },
  {
    id: '4',
    name: 'Express Route Handler',
    description: 'Express.js route handler with error handling',
    code: `app.{{method}}('{{route}}', async (req, res) => {
  try {
    // Route logic here
    {{routeLogic}}
    
    res.json({ success: true, data: result });
  } catch (error) {
    console.error('Error in {{route}}:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});`,
    language: 'javascript',
    tags: ['express', 'node', 'api'],
    isFavorite: false,
    category: 'Backend',
    createdAt: new Date(),
    updatedAt: new Date(),
    usageCount: 12
  },
  {
    id: '5',
    name: 'Async Function',
    description: 'Async function with error handling',
    code: `async function {{functionName}}({{parameters}}) {
  try {
    {{functionBody}}
    return result;
  } catch (error) {
    console.error('Error in {{functionName}}:', error);
    throw error;
  }
}`,
    language: 'typescript',
    tags: ['async', 'function', 'error-handling'],
    isFavorite: false,
    category: 'Functions',
    createdAt: new Date(),
    updatedAt: new Date(),
    usageCount: 8
  }
];

const defaultTemplates: CodeTemplate[] = [
  {
    id: '1',
    name: 'React Component with Tests',
    description: 'Complete React component with TypeScript and Jest tests',
    category: 'React',
    tags: ['react', 'typescript', 'testing'],
    variables: [
      { name: 'componentName', description: 'Component name', defaultValue: 'MyComponent', type: 'string' },
      { name: 'hasProps', description: 'Include props interface', defaultValue: 'true', type: 'boolean' }
    ],
    files: [
      {
        name: '{{componentName}}.tsx',
        language: 'typescript',
        content: `import React from 'react';

{{#if hasProps}}
interface {{componentName}}Props {
  // Add props here
}

export const {{componentName}}: React.FC<{{componentName}}Props> = (props) => {
{{else}}
export const {{componentName}}: React.FC = () => {
{{/if}}
  return (
    <div>
      <h1>{{componentName}}</h1>
    </div>
  );
};`
      },
      {
        name: '{{componentName}}.test.tsx',
        language: 'typescript',
        content: `import { render, screen } from '@testing-library/react';
import { {{componentName}} } from './{{componentName}}';

describe('{{componentName}}', () => {
  it('renders correctly', () => {
    render(<{{componentName}} />);
    expect(screen.getByText('{{componentName}}')).toBeInTheDocument();
  });
});`
      }
    ]
  }
];

export const CodeSnippets = ({ onInsertSnippet, onCreateFromTemplate, language }: CodeSnippetsProps) => {
  const [snippets, setSnippets] = useState<CodeSnippet[]>(defaultSnippets);
  const [templates, setTemplates] = useState<CodeTemplate[]>(defaultTemplates);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedLanguage, setSelectedLanguage] = useState<string>(language || 'all');
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingSnippet, setEditingSnippet] = useState<CodeSnippet | null>(null);

  // Filter snippets based on search and filters
  const filteredSnippets = snippets.filter(snippet => {
    const matchesSearch = snippet.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         snippet.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         snippet.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || snippet.category === selectedCategory;
    const matchesLanguage = selectedLanguage === 'all' || snippet.language === selectedLanguage;
    const matchesFavorites = !showFavoritesOnly || snippet.isFavorite;

    return matchesSearch && matchesCategory && matchesLanguage && matchesFavorites;
  });

  // Get unique categories and languages
  const categories = ['all', ...Array.from(new Set(snippets.map(s => s.category)))];
  const languages = ['all', ...Array.from(new Set(snippets.map(s => s.language)))];

  const handleInsertSnippet = (snippet: CodeSnippet) => {
    // Process template variables in snippet code
    let processedCode = snippet.code;
    
    // Simple template variable replacement ({{variableName}})
    const variables = processedCode.match(/\{\{(\w+)\}\}/g);
    if (variables) {
      variables.forEach(variable => {
        const varName = variable.replace(/[{}]/g, '');
        const userInput = prompt(`Enter value for ${varName}:`);
        if (userInput !== null) {
          processedCode = processedCode.replace(new RegExp(variable, 'g'), userInput);
        }
      });
    }

    onInsertSnippet(processedCode);
    
    // Update usage count
    setSnippets(prev => prev.map(s => 
      s.id === snippet.id 
        ? { ...s, usageCount: s.usageCount + 1 }
        : s
    ));
  };

  const toggleFavorite = (snippetId: string) => {
    setSnippets(prev => prev.map(s => 
      s.id === snippetId 
        ? { ...s, isFavorite: !s.isFavorite }
        : s
    ));
  };

  const deleteSnippet = (snippetId: string) => {
    setSnippets(prev => prev.filter(s => s.id !== snippetId));
  };

  const exportSnippets = () => {
    const dataStr = JSON.stringify(snippets, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'code-snippets.json';
    link.click();
    URL.revokeObjectURL(url);
  };

  const importSnippets = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const imported = JSON.parse(e.target?.result as string);
          setSnippets(prev => [...prev, ...imported]);
        } catch (error) {
          console.error('Failed to import snippets:', error);
        }
      };
      reader.readAsText(file);
    }
  };

  return (
    <div className="w-full h-full flex flex-col space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Code className="w-5 h-5" />
          <h2 className="text-xl font-semibold">Code Snippets & Templates</h2>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={exportSnippets}>
            <Download className="w-4 h-4 mr-1" />
            Export
          </Button>
          <Button variant="outline" size="sm" onClick={() => document.getElementById('import-input')?.click()}>
            <Upload className="w-4 h-4 mr-1" />
            Import
          </Button>
          <input
            id="import-input"
            type="file"
            accept=".json"
            onChange={importSnippets}
            className="hidden"
          />
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="w-4 h-4 mr-1" />
                New Snippet
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New Snippet</DialogTitle>
                <DialogDescription>
                  Create a reusable code snippet with template variables
                </DialogDescription>
              </DialogHeader>
              {/* Create snippet form will be added */}
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="snippets" className="w-full">
        <TabsList>
          <TabsTrigger value="snippets">Snippets</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="snippets" className="space-y-4">
          {/* Search and filters */}
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search snippets..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>
                    {category === 'all' ? 'All Categories' : category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Language" />
              </SelectTrigger>
              <SelectContent>
                {languages.map(lang => (
                  <SelectItem key={lang} value={lang}>
                    {lang === 'all' ? 'All Languages' : lang}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button
              variant={showFavoritesOnly ? "default" : "outline"}
              size="sm"
              onClick={() => setShowFavoritesOnly(!showFavoritesOnly)}
            >
              <Star className="w-4 h-4 mr-1" />
              Favorites
            </Button>
          </div>

          {/* Snippets grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredSnippets.map(snippet => (
              <Card key={snippet.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-sm font-medium">{snippet.name}</CardTitle>
                      <CardDescription className="text-xs mt-1">
                        {snippet.description}
                      </CardDescription>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleFavorite(snippet.id)}
                      className="p-1 h-auto"
                    >
                      {snippet.isFavorite ? (
                        <Star className="w-4 h-4 fill-current text-yellow-500" />
                      ) : (
                        <StarOff className="w-4 h-4" />
                      )}
                    </Button>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="text-xs">
                      {snippet.language}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {snippet.category}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      Used {snippet.usageCount} times
                    </span>
                  </div>

                  <div className="flex flex-wrap gap-1">
                    {snippet.tags.map(tag => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        <Tag className="w-3 h-3 mr-1" />
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      onClick={() => handleInsertSnippet(snippet)}
                      className="flex-1"
                    >
                      <Zap className="w-4 h-4 mr-1" />
                      Insert
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => navigator.clipboard.writeText(snippet.code)}
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditingSnippet(snippet)}
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => deleteSnippet(snippet.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredSnippets.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Code className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No snippets found matching your criteria</p>
              <p className="text-sm">Try adjusting your search or filters</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          {/* Templates will be implemented similarly */}
          <div className="text-center py-8 text-muted-foreground">
            <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Templates feature coming soon</p>
            <p className="text-sm">Create multi-file project templates</p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
