name: 🎭 Browser MCP UI Tests

on:
  push:
    branches: [ main, develop, 'ui/**' ]
    paths:
      - 'apps/web/**'
      - 'src/**'
      - 'e2e/**'
      - 'playwright.config.ts'
      - '.github/workflows/browser-mcp-ui-tests.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'apps/web/**'
      - 'src/**'
      - 'e2e/**'
      - 'playwright.config.ts'

env:
  NODE_VERSION: '18'
  PLAYWRIGHT_BROWSERS_PATH: ${{ github.workspace }}/ms-playwright

jobs:
  ui-tests:
    name: 🎯 UI Tests & Accessibility Audit
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    strategy:
      fail-fast: false
      matrix:
        browser: [chromium, firefox, webkit]
        
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 🔧 Install dependencies
        run: |
          npm ci
          npm run build --if-present
          
      - name: 🎭 Install Playwright browsers
        run: |
          npx playwright install ${{ matrix.browser }} --with-deps
          
      - name: 🚀 Start development server
        run: |
          npm run dev &
          sleep 10
          curl -f http://localhost:8080 || exit 1
        timeout-minutes: 3
        
      - name: 🎯 Run Playwright tests (${{ matrix.browser }})
        run: |
          npx playwright test --project=${{ matrix.browser }} --reporter=html,json,junit
        env:
          CI: true
          
      - name: 🔍 Run accessibility audit
        if: matrix.browser == 'chromium'
        run: |
          node scripts/accessibility-audit.js
          
      - name: 📊 Bundle size check
        if: matrix.browser == 'chromium'
        run: |
          npm run build
          npx bundlesize
        env:
          BUNDLESIZE_GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          
      - name: 📈 Coverage check
        if: matrix.browser == 'chromium'
        run: |
          npm run test:coverage
          npx nyc check-coverage --lines 90 --functions 90 --branches 90 --statements 90
          
      - name: 📸 Upload screenshots
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: screenshots-${{ matrix.browser }}
          path: artifacts/test-results/
          retention-days: 7
          
      - name: 📊 Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results-${{ matrix.browser }}
          path: |
            artifacts/playwright-report/
            artifacts/test-results.json
            artifacts/junit-results.xml
          retention-days: 30
          
      - name: 🎬 Upload traces
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: traces-${{ matrix.browser }}
          path: artifacts/test-results/
          retention-days: 7

  accessibility-report:
    name: ♿ Accessibility Report
    runs-on: ubuntu-latest
    needs: ui-tests
    if: always()
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 🔧 Install dependencies
        run: npm ci
        
      - name: 🎭 Install Playwright browsers
        run: npx playwright install chromium --with-deps
        
      - name: 🚀 Start development server
        run: |
          npm run dev &
          sleep 10
          curl -f http://localhost:8080 || exit 1
        timeout-minutes: 3
        
      - name: ♿ Run comprehensive accessibility audit
        run: |
          # Run axe-core audit on all pages
          npx playwright test e2e/accessibility.spec.ts --project=chromium
          
          # Generate accessibility report
          node scripts/generate-accessibility-report.js
          
      - name: 📊 Check accessibility score
        run: |
          SCORE=$(node -e "
            const report = require('./artifacts/accessibility-summary.json');
            const avgScore = Object.values(report.page_results)
              .reduce((sum, page) => sum + page.score, 0) / Object.keys(report.page_results).length;
            console.log(Math.round(avgScore));
          ")
          echo "Average accessibility score: $SCORE"
          if [ "$SCORE" -lt 97 ]; then
            echo "❌ Accessibility score $SCORE is below target of 97"
            exit 1
          else
            echo "✅ Accessibility score $SCORE meets target of 97+"
          fi
          
      - name: 📊 Upload accessibility report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: accessibility-report
          path: |
            artifacts/accessibility-*.json
            artifacts/axe-violations-*.json
          retention-days: 30

  bundle-analysis:
    name: 📦 Bundle Size Analysis
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 🔧 Install dependencies
        run: npm ci
        
      - name: 🏗️ Build application
        run: npm run build
        
      - name: 📊 Analyze bundle size
        run: |
          # Check main bundle size (should be ≤ 900KB gzipped)
          BUNDLE_SIZE=$(find dist -name "*.js" -exec gzip -c {} \; | wc -c)
          BUNDLE_SIZE_KB=$((BUNDLE_SIZE / 1024))
          
          echo "Bundle size: ${BUNDLE_SIZE_KB}KB (gzipped)"
          
          if [ "$BUNDLE_SIZE_KB" -gt 900 ]; then
            echo "❌ Bundle size ${BUNDLE_SIZE_KB}KB exceeds limit of 900KB"
            exit 1
          else
            echo "✅ Bundle size ${BUNDLE_SIZE_KB}KB is within limit"
          fi
          
      - name: 📈 Bundle size report
        run: |
          npx webpack-bundle-analyzer dist/stats.json --mode static --report artifacts/bundle-report.html --no-open
          
      - name: 📊 Upload bundle analysis
        uses: actions/upload-artifact@v4
        with:
          name: bundle-analysis
          path: artifacts/bundle-report.html
          retention-days: 30

  performance-audit:
    name: ⚡ Performance Audit
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 🔧 Install dependencies
        run: npm ci
        
      - name: 🚀 Start development server
        run: |
          npm run dev &
          sleep 10
          curl -f http://localhost:8080 || exit 1
        timeout-minutes: 3
        
      - name: ⚡ Run Lighthouse audit
        run: |
          npm install -g lighthouse
          lighthouse http://localhost:8080/dashboard \
            --output=json \
            --output-path=artifacts/lighthouse-dashboard.json \
            --chrome-flags="--headless --no-sandbox"
            
          lighthouse http://localhost:8080/settings \
            --output=json \
            --output-path=artifacts/lighthouse-settings.json \
            --chrome-flags="--headless --no-sandbox"
            
      - name: 📊 Check performance scores
        run: |
          node -e "
            const dashboard = require('./artifacts/lighthouse-dashboard.json');
            const settings = require('./artifacts/lighthouse-settings.json');
            
            const dashboardScore = Math.round(dashboard.lhr.categories.performance.score * 100);
            const settingsScore = Math.round(settings.lhr.categories.performance.score * 100);
            
            console.log('Dashboard performance score:', dashboardScore);
            console.log('Settings performance score:', settingsScore);
            
            if (dashboardScore < 90 || settingsScore < 90) {
              console.log('❌ Performance scores below 90');
              process.exit(1);
            } else {
              console.log('✅ Performance scores meet target');
            }
          "
          
      - name: 📊 Upload performance reports
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: performance-reports
          path: artifacts/lighthouse-*.json
          retention-days: 30

  summary:
    name: 📋 Test Summary
    runs-on: ubuntu-latest
    needs: [ui-tests, accessibility-report, bundle-analysis, performance-audit]
    if: always()
    
    steps:
      - name: 📥 Download all artifacts
        uses: actions/download-artifact@v4
        
      - name: 📊 Generate summary report
        run: |
          echo "# 🎭 Browser MCP UI Tests Summary" > summary.md
          echo "" >> summary.md
          echo "## 🎯 Test Results" >> summary.md
          echo "- **Browsers Tested**: Chromium, Firefox, WebKit" >> summary.md
          echo "- **Responsive Breakpoints**: 320px, 768px, 1280px" >> summary.md
          echo "- **Accessibility Target**: ≥97 score" >> summary.md
          echo "- **Bundle Size Limit**: ≤900KB gzipped" >> summary.md
          echo "- **Performance Target**: ≥90 score" >> summary.md
          echo "" >> summary.md
          
          # Add test status
          if [ "${{ needs.ui-tests.result }}" = "success" ]; then
            echo "✅ **UI Tests**: PASSED" >> summary.md
          else
            echo "❌ **UI Tests**: FAILED" >> summary.md
          fi
          
          if [ "${{ needs.accessibility-report.result }}" = "success" ]; then
            echo "✅ **Accessibility**: PASSED" >> summary.md
          else
            echo "❌ **Accessibility**: FAILED" >> summary.md
          fi
          
          if [ "${{ needs.bundle-analysis.result }}" = "success" ]; then
            echo "✅ **Bundle Size**: PASSED" >> summary.md
          else
            echo "❌ **Bundle Size**: FAILED" >> summary.md
          fi
          
          if [ "${{ needs.performance-audit.result }}" = "success" ]; then
            echo "✅ **Performance**: PASSED" >> summary.md
          else
            echo "❌ **Performance**: FAILED" >> summary.md
          fi
          
          cat summary.md
          
      - name: 💬 Comment PR
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const summary = fs.readFileSync('summary.md', 'utf8');
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: summary
            });
