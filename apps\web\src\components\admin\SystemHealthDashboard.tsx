import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Activity, 
  Server, 
  Database, 
  Cpu, 
  MemoryStick,
  HardDrive,
  Wifi,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Clock,
  TrendingUp,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ServiceStatus {
  name: string;
  status: 'healthy' | 'degraded' | 'down' | 'unknown';
  responseTime: number;
  uptime: number;
  lastCheck: string;
  errorRate: number;
  description: string;
}

interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
    load: number[];
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  disk: {
    used: number;
    total: number;
    percentage: number;
  };
  network: {
    inbound: number;
    outbound: number;
  };
}

interface HealthData {
  overall: 'healthy' | 'degraded' | 'down';
  uptime: number;
  services: ServiceStatus[];
  metrics: SystemMetrics;
  incidents: Array<{
    id: string;
    title: string;
    status: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    timestamp: string;
    description: string;
  }>;
}

interface SystemHealthDashboardProps {
  className?: string;
}

export const SystemHealthDashboard: React.FC<SystemHealthDashboardProps> = ({ className }) => {
  const [healthData, setHealthData] = useState<HealthData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  const fetchHealthData = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Fetch health status
      const healthResponse = await fetch('/health');
      const metricsResponse = await fetch('/api/logs/metrics');
      const servicesResponse = await fetch('/api/ai/performance');
      
      const [health, metrics, services] = await Promise.all([
        healthResponse.ok ? healthResponse.json() : null,
        metricsResponse.ok ? metricsResponse.json() : null,
        servicesResponse.ok ? servicesResponse.json() : null
      ]);

      // Mock data for demonstration - in real app this would come from actual monitoring
      const mockHealthData: HealthData = {
        overall: health?.status === 'ok' ? 'healthy' : 'degraded',
        uptime: 99.9,
        services: [
          {
            name: 'API Server',
            status: health?.status === 'ok' ? 'healthy' : 'degraded',
            responseTime: 45,
            uptime: 99.9,
            lastCheck: new Date().toISOString(),
            errorRate: 0.1,
            description: 'Main application API'
          },
          {
            name: 'Database',
            status: 'healthy',
            responseTime: 12,
            uptime: 99.95,
            lastCheck: new Date().toISOString(),
            errorRate: 0.05,
            description: 'Supabase PostgreSQL database'
          },
          {
            name: 'AI Services',
            status: services?.data ? 'healthy' : 'degraded',
            responseTime: 1200,
            uptime: 99.8,
            lastCheck: new Date().toISOString(),
            errorRate: 0.2,
            description: 'OpenAI, Anthropic, Google AI providers'
          },
          {
            name: 'Queue System',
            status: 'healthy',
            responseTime: 25,
            uptime: 99.9,
            lastCheck: new Date().toISOString(),
            errorRate: 0.1,
            description: 'Transformation job queue'
          },
          {
            name: 'GitHub Integration',
            status: 'healthy',
            responseTime: 180,
            uptime: 99.7,
            lastCheck: new Date().toISOString(),
            errorRate: 0.3,
            description: 'GitHub API and PR creation'
          }
        ],
        metrics: {
          cpu: {
            usage: 35,
            cores: 4,
            load: [0.8, 1.2, 0.9]
          },
          memory: {
            used: 2.1,
            total: 8.0,
            percentage: 26
          },
          disk: {
            used: 45,
            total: 100,
            percentage: 45
          },
          network: {
            inbound: 1.2,
            outbound: 0.8
          }
        },
        incidents: []
      };

      setHealthData(mockHealthData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load health data');
    } finally {
      setIsLoading(false);
      setLastUpdated(new Date());
    }
  };

  // Auto-refresh functionality
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(fetchHealthData, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  // Initial load
  useEffect(() => {
    fetchHealthData();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'degraded':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'down':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'default';
      case 'degraded':
        return 'secondary';
      case 'down':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const formatUptime = (uptime: number) => {
    return `${uptime.toFixed(2)}%`;
  };

  if (isLoading && !healthData) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="w-5 h-5" />
            <span>System Health</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="w-6 h-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="w-5 h-5" />
            <span>System Health</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertTriangle className="w-4 h-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Activity className="w-5 h-5 text-primary" />
            <CardTitle>System Health Dashboard</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            {getStatusIcon(healthData?.overall || 'unknown')}
            <Badge variant={getStatusBadgeVariant(healthData?.overall || 'unknown')}>
              {healthData?.overall?.charAt(0).toUpperCase() + healthData?.overall?.slice(1)}
            </Badge>
            <Button
              variant={autoRefresh ? "default" : "outline"}
              size="sm"
              onClick={() => setAutoRefresh(!autoRefresh)}
              className="h-8"
            >
              <RefreshCw className={cn("w-4 h-4 mr-2", autoRefresh && "animate-spin")} />
              Auto
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={fetchHealthData}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={cn("w-4 h-4", isLoading && "animate-spin")} />
            </Button>
          </div>
        </div>
        <CardDescription>
          Real-time system monitoring and health status • Last updated: {lastUpdated.toLocaleTimeString()}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="services">Services</TabsTrigger>
            <TabsTrigger value="metrics">Metrics</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-4">
            {/* Overall Status */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <Activity className="w-4 h-4" />
                    <span>Status</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(healthData?.overall || 'unknown')}
                    <span className="font-medium capitalize">{healthData?.overall}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <Clock className="w-4 h-4" />
                    <span>Uptime</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">
                    {formatUptime(healthData?.uptime || 0)}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <Server className="w-4 h-4" />
                    <span>Services</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {healthData?.services.filter(s => s.status === 'healthy').length || 0}/
                    {healthData?.services.length || 0}
                  </div>
                  <div className="text-xs text-muted-foreground">Healthy</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <TrendingUp className="w-4 h-4" />
                    <span>Performance</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600">Good</div>
                  <div className="text-xs text-muted-foreground">All systems optimal</div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="services" className="space-y-4">
            <div className="space-y-3">
              {healthData?.services.map((service, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(service.status)}
                        <div>
                          <div className="font-medium">{service.name}</div>
                          <div className="text-sm text-muted-foreground">{service.description}</div>
                        </div>
                      </div>
                      <div className="text-right space-y-1">
                        <Badge variant={getStatusBadgeVariant(service.status) as any}>
                          {service.status}
                        </Badge>
                        <div className="text-xs text-muted-foreground">
                          {service.responseTime}ms • {formatUptime(service.uptime)}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="metrics" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* CPU Usage */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <Cpu className="w-4 h-4" />
                    <span>CPU Usage</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Usage</span>
                      <span>{healthData?.metrics.cpu.usage}%</span>
                    </div>
                    <Progress value={healthData?.metrics.cpu.usage || 0} className="h-2" />
                    <div className="text-xs text-muted-foreground">
                      {healthData?.metrics.cpu.cores} cores • Load: {healthData?.metrics.cpu.load.join(', ')}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Memory Usage */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <MemoryStick className="w-4 h-4" />
                    <span>Memory Usage</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Usage</span>
                      <span>{healthData?.metrics.memory.percentage}%</span>
                    </div>
                    <Progress value={healthData?.metrics.memory.percentage || 0} className="h-2" />
                    <div className="text-xs text-muted-foreground">
                      {healthData?.metrics.memory.used}GB / {healthData?.metrics.memory.total}GB
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Disk Usage */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <HardDrive className="w-4 h-4" />
                    <span>Disk Usage</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Usage</span>
                      <span>{healthData?.metrics.disk.percentage}%</span>
                    </div>
                    <Progress value={healthData?.metrics.disk.percentage || 0} className="h-2" />
                    <div className="text-xs text-muted-foreground">
                      {healthData?.metrics.disk.used}GB / {healthData?.metrics.disk.total}GB
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Network */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <Wifi className="w-4 h-4" />
                    <span>Network</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Inbound</span>
                      <span>{healthData?.metrics.network.inbound} MB/s</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Outbound</span>
                      <span>{healthData?.metrics.network.outbound} MB/s</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default SystemHealthDashboard;
