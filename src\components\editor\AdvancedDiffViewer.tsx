import { useState, useRef, useEffect } from 'react';
import { MonacoDiffEditor } from '@monaco-editor/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { 
  GitCompare, 
  Download, 
  Copy, 
  Check, 
  X, 
  RotateCcw, 
  ZoomIn, 
  ZoomOut, 
  Eye, 
  EyeOff,
  ArrowLeft,
  ArrowRight,
  FileText,
  Split,
  Layers
} from 'lucide-react';

interface DiffChange {
  type: 'added' | 'removed' | 'modified';
  lineNumber: number;
  content: string;
  originalLineNumber?: number;
}

interface DiffViewerProps {
  original: string;
  modified: string;
  language?: string;
  fileName?: string;
  onAcceptChange?: (change: DiffChange) => void;
  onRejectChange?: (change: DiffChange) => void;
  onAcceptAll?: () => void;
  onRejectAll?: () => void;
  readOnly?: boolean;
  showInlineView?: boolean;
  enableConflictResolution?: boolean;
}

export const AdvancedDiffViewer = ({
  original,
  modified,
  language = 'typescript',
  fileName = 'untitled',
  onAcceptChange,
  onRejectChange,
  onAcceptAll,
  onRejectAll,
  readOnly = false,
  showInlineView = false,
  enableConflictResolution = false
}: DiffViewerProps) => {
  const [viewMode, setViewMode] = useState<'side-by-side' | 'inline'>('side-by-side');
  const [showWhitespace, setShowWhitespace] = useState(false);
  const [ignoreWhitespace, setIgnoreWhitespace] = useState(false);
  const [wordWrap, setWordWrap] = useState(true);
  const [fontSize, setFontSize] = useState(14);
  const [changes, setChanges] = useState<DiffChange[]>([]);
  const [currentChangeIndex, setCurrentChangeIndex] = useState(0);
  const diffEditorRef = useRef<any>(null);

  // Calculate diff statistics
  const diffStats = {
    additions: changes.filter(c => c.type === 'added').length,
    deletions: changes.filter(c => c.type === 'removed').length,
    modifications: changes.filter(c => c.type === 'modified').length,
    total: changes.length
  };

  const handleEditorDidMount = (editor: any, monaco: any) => {
    diffEditorRef.current = editor;
    
    // Configure diff editor options
    editor.updateOptions({
      fontSize,
      wordWrap: wordWrap ? 'on' : 'off',
      renderWhitespace: showWhitespace ? 'all' : 'none',
      ignoreTrimWhitespace: ignoreWhitespace,
      renderSideBySide: viewMode === 'side-by-side',
      readOnly,
      originalEditable: !readOnly,
      automaticLayout: true,
      scrollBeyondLastLine: false,
      minimap: { enabled: false },
      lineNumbers: 'on',
      glyphMargin: true,
      folding: true,
      selectOnLineNumbers: true,
      matchBrackets: 'always',
      theme: 'vs-dark'
    });

    // Extract changes from diff
    const model = editor.getModel();
    if (model) {
      const originalModel = model.original;
      const modifiedModel = model.modified;
      
      // Get line changes
      const lineChanges = editor.getLineChanges();
      const extractedChanges: DiffChange[] = [];
      
      lineChanges?.forEach((change: any) => {
        if (change.originalStartLineNumber === 0) {
          // Addition
          for (let i = change.modifiedStartLineNumber; i <= change.modifiedEndLineNumber; i++) {
            extractedChanges.push({
              type: 'added',
              lineNumber: i,
              content: modifiedModel.getLineContent(i)
            });
          }
        } else if (change.modifiedStartLineNumber === 0) {
          // Deletion
          for (let i = change.originalStartLineNumber; i <= change.originalEndLineNumber; i++) {
            extractedChanges.push({
              type: 'removed',
              lineNumber: i,
              content: originalModel.getLineContent(i),
              originalLineNumber: i
            });
          }
        } else {
          // Modification
          for (let i = change.modifiedStartLineNumber; i <= change.modifiedEndLineNumber; i++) {
            extractedChanges.push({
              type: 'modified',
              lineNumber: i,
              content: modifiedModel.getLineContent(i),
              originalLineNumber: change.originalStartLineNumber + (i - change.modifiedStartLineNumber)
            });
          }
        }
      });
      
      setChanges(extractedChanges);
    }
  };

  const navigateToChange = (index: number) => {
    if (diffEditorRef.current && changes[index]) {
      const change = changes[index];
      diffEditorRef.current.revealLineInCenter(change.lineNumber);
      setCurrentChangeIndex(index);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const downloadDiff = () => {
    const diffContent = `--- ${fileName} (original)\n+++ ${fileName} (modified)\n${modified}`;
    const blob = new Blob([diffContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${fileName}.diff`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="w-full h-full flex flex-col space-y-4">
      {/* Header with controls */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <GitCompare className="w-5 h-5" />
              <span>Diff Viewer</span>
              {fileName && <Badge variant="outline">{fileName}</Badge>}
            </CardTitle>
            
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={() => copyToClipboard(modified)}>
                <Copy className="w-4 h-4 mr-1" />
                Copy
              </Button>
              <Button variant="outline" size="sm" onClick={downloadDiff}>
                <Download className="w-4 h-4 mr-1" />
                Download
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Diff statistics */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                +{diffStats.additions}
              </Badge>
              <Badge variant="secondary" className="bg-red-100 text-red-800">
                -{diffStats.deletions}
              </Badge>
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                ~{diffStats.modifications}
              </Badge>
            </div>
            
            {changes.length > 0 && (
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigateToChange(Math.max(0, currentChangeIndex - 1))}
                  disabled={currentChangeIndex === 0}
                >
                  <ArrowLeft className="w-4 h-4" />
                </Button>
                <span className="text-sm text-muted-foreground">
                  {currentChangeIndex + 1} of {changes.length}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigateToChange(Math.min(changes.length - 1, currentChangeIndex + 1))}
                  disabled={currentChangeIndex === changes.length - 1}
                >
                  <ArrowRight className="w-4 h-4" />
                </Button>
              </div>
            )}
          </div>

          {/* Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Label htmlFor="viewMode">View:</Label>
                <Select value={viewMode} onValueChange={(value: any) => setViewMode(value)}>
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="side-by-side">
                      <div className="flex items-center space-x-2">
                        <Split className="w-4 h-4" />
                        <span>Side by Side</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="inline">
                      <div className="flex items-center space-x-2">
                        <Layers className="w-4 h-4" />
                        <span>Inline</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Label htmlFor="fontSize">Size:</Label>
                <Select value={fontSize.toString()} onValueChange={(value) => setFontSize(parseInt(value))}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="12">12px</SelectItem>
                    <SelectItem value="14">14px</SelectItem>
                    <SelectItem value="16">16px</SelectItem>
                    <SelectItem value="18">18px</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Switch
                  checked={showWhitespace}
                  onCheckedChange={setShowWhitespace}
                  id="whitespace"
                />
                <Label htmlFor="whitespace">Whitespace</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={ignoreWhitespace}
                  onCheckedChange={setIgnoreWhitespace}
                  id="ignore-whitespace"
                />
                <Label htmlFor="ignore-whitespace">Ignore Whitespace</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={wordWrap}
                  onCheckedChange={setWordWrap}
                  id="word-wrap"
                />
                <Label htmlFor="word-wrap">Word Wrap</Label>
              </div>
            </div>
          </div>

          {/* Action buttons for conflict resolution */}
          {enableConflictResolution && !readOnly && (
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={onAcceptAll}>
                <Check className="w-4 h-4 mr-1" />
                Accept All
              </Button>
              <Button variant="outline" size="sm" onClick={onRejectAll}>
                <X className="w-4 h-4 mr-1" />
                Reject All
              </Button>
              <Separator orientation="vertical" className="h-6" />
              <Button variant="outline" size="sm">
                <RotateCcw className="w-4 h-4 mr-1" />
                Reset
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Diff editor */}
      <div className="flex-1 border rounded-lg overflow-hidden">
        <MonacoDiffEditor
          height="100%"
          language={language}
          original={original}
          modified={modified}
          onMount={handleEditorDidMount}
          options={{
            fontSize,
            wordWrap: wordWrap ? 'on' : 'off',
            renderWhitespace: showWhitespace ? 'all' : 'none',
            ignoreTrimWhitespace: ignoreWhitespace,
            renderSideBySide: viewMode === 'side-by-side',
            readOnly,
            originalEditable: !readOnly,
            automaticLayout: true,
            scrollBeyondLastLine: false,
            minimap: { enabled: false },
            lineNumbers: 'on',
            glyphMargin: true,
            folding: true,
            selectOnLineNumbers: true,
            matchBrackets: 'always',
            theme: 'vs-dark'
          }}
        />
      </div>
    </div>
  );
};
