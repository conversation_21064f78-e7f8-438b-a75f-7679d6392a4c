import { z } from 'zod';

// Cost Entry Schema
export const costEntrySchema = z.object({
  id: z.string(),
  timestamp: z.string().transform(str => new Date(str)),
  provider: z.enum(['openai', 'anthropic', 'google']),
  model: z.string(),
  operation: z.enum(['transformation', 'validation', 'analysis']),
  promptTokens: z.number(),
  completionTokens: z.number(),
  totalTokens: z.number(),
  cost: z.number(),
  transformationId: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

export type CostEntry = z.infer<typeof costEntrySchema>;

// Budget Configuration Schema
export const budgetConfigSchema = z.object({
  dailyLimit: z.number().min(0),
  monthlyLimit: z.number().min(0),
  perTransformationLimit: z.number().min(0),
  alertThresholds: z.object({
    daily: z.number().min(0).max(1), // Percentage (0.8 = 80%)
    monthly: z.number().min(0).max(1),
    perTransformation: z.number().min(0).max(1),
  }),
  enableAlerts: z.boolean(),
});

export type BudgetConfig = z.infer<typeof budgetConfigSchema>;

// Usage Summary
export interface UsageSummary {
  today: {
    cost: number;
    transformations: number;
    tokens: number;
  };
  thisMonth: {
    cost: number;
    transformations: number;
    tokens: number;
  };
  allTime: {
    cost: number;
    transformations: number;
    tokens: number;
  };
}

// Cost Alert
export interface CostAlert {
  id: string;
  type: 'daily' | 'monthly' | 'per_transformation' | 'budget_exceeded';
  message: string;
  threshold: number;
  current: number;
  timestamp: Date;
  acknowledged: boolean;
}

// Cost Tracker Class
export class CostTracker {
  private static readonly STORAGE_KEY = 'metamorphic_reactor_cost_data';
  private static readonly ALERTS_KEY = 'metamorphic_reactor_cost_alerts';
  
  private costEntries: CostEntry[] = [];
  private budgetConfig: BudgetConfig;
  private alerts: CostAlert[] = [];

  constructor(budgetConfig?: Partial<BudgetConfig>) {
    this.budgetConfig = {
      dailyLimit: 10.0,
      monthlyLimit: 100.0,
      perTransformationLimit: 3.0,
      alertThresholds: {
        daily: 0.8,
        monthly: 0.8,
        perTransformation: 0.9,
      },
      enableAlerts: true,
      ...budgetConfig,
    };

    this.loadData();
  }

  // Record a cost entry
  recordCost(entry: Omit<CostEntry, 'id' | 'timestamp'>): string {
    const costEntry: CostEntry = {
      id: this.generateId(),
      timestamp: new Date(),
      ...entry,
    };

    this.costEntries.push(costEntry);
    this.saveData();
    
    // Check for budget alerts
    if (this.budgetConfig.enableAlerts) {
      this.checkBudgetAlerts(costEntry);
    }

    return costEntry.id;
  }

  // Get usage summary
  getUsageSummary(): UsageSummary {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const todayEntries = this.costEntries.filter(entry => entry.timestamp >= today);
    const monthEntries = this.costEntries.filter(entry => entry.timestamp >= thisMonth);
    const allEntries = this.costEntries;

    return {
      today: this.calculateSummary(todayEntries),
      thisMonth: this.calculateSummary(monthEntries),
      allTime: this.calculateSummary(allEntries),
    };
  }

  // Get cost by date range
  getCostByDateRange(startDate: Date, endDate: Date): CostEntry[] {
    return this.costEntries.filter(
      entry => entry.timestamp >= startDate && entry.timestamp <= endDate
    );
  }

  // Get cost by transformation
  getCostByTransformation(transformationId: string): CostEntry[] {
    return this.costEntries.filter(entry => entry.transformationId === transformationId);
  }

  // Get cost breakdown by provider
  getCostBreakdownByProvider(): Record<string, number> {
    const breakdown: Record<string, number> = {};
    
    this.costEntries.forEach(entry => {
      breakdown[entry.provider] = (breakdown[entry.provider] || 0) + entry.cost;
    });

    return breakdown;
  }

  // Get cost breakdown by model
  getCostBreakdownByModel(): Record<string, number> {
    const breakdown: Record<string, number> = {};
    
    this.costEntries.forEach(entry => {
      breakdown[entry.model] = (breakdown[entry.model] || 0) + entry.cost;
    });

    return breakdown;
  }

  // Check if operation is within budget
  canAffordOperation(estimatedCost: number): {
    allowed: boolean;
    reason?: string;
    limits: {
      daily: { current: number; limit: number; remaining: number };
      monthly: { current: number; limit: number; remaining: number };
      perTransformation: { limit: number };
    };
  } {
    const summary = this.getUsageSummary();
    
    const limits = {
      daily: {
        current: summary.today.cost,
        limit: this.budgetConfig.dailyLimit,
        remaining: this.budgetConfig.dailyLimit - summary.today.cost,
      },
      monthly: {
        current: summary.thisMonth.cost,
        limit: this.budgetConfig.monthlyLimit,
        remaining: this.budgetConfig.monthlyLimit - summary.thisMonth.cost,
      },
      perTransformation: {
        limit: this.budgetConfig.perTransformationLimit,
      },
    };

    // Check per-transformation limit
    if (estimatedCost > this.budgetConfig.perTransformationLimit) {
      return {
        allowed: false,
        reason: `Estimated cost ($${estimatedCost.toFixed(4)}) exceeds per-transformation limit ($${this.budgetConfig.perTransformationLimit})`,
        limits,
      };
    }

    // Check daily limit
    if (summary.today.cost + estimatedCost > this.budgetConfig.dailyLimit) {
      return {
        allowed: false,
        reason: `Operation would exceed daily budget limit ($${this.budgetConfig.dailyLimit})`,
        limits,
      };
    }

    // Check monthly limit
    if (summary.thisMonth.cost + estimatedCost > this.budgetConfig.monthlyLimit) {
      return {
        allowed: false,
        reason: `Operation would exceed monthly budget limit ($${this.budgetConfig.monthlyLimit})`,
        limits,
      };
    }

    return { allowed: true, limits };
  }

  // Get active alerts
  getActiveAlerts(): CostAlert[] {
    return this.alerts.filter(alert => !alert.acknowledged);
  }

  // Acknowledge alert
  acknowledgeAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      this.saveAlerts();
      return true;
    }
    return false;
  }

  // Clear old alerts
  clearOldAlerts(olderThanDays: number = 7): number {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
    
    const initialCount = this.alerts.length;
    this.alerts = this.alerts.filter(alert => alert.timestamp > cutoffDate);
    this.saveAlerts();
    
    return initialCount - this.alerts.length;
  }

  // Update budget configuration
  updateBudgetConfig(config: Partial<BudgetConfig>): void {
    this.budgetConfig = { ...this.budgetConfig, ...config };
    this.saveData();
  }

  // Export cost data
  exportCostData(): string {
    return JSON.stringify({
      costEntries: this.costEntries.map(entry => ({
        ...entry,
        timestamp: entry.timestamp.toISOString(),
      })),
      budgetConfig: this.budgetConfig,
      alerts: this.alerts.map(alert => ({
        ...alert,
        timestamp: alert.timestamp.toISOString(),
      })),
    }, null, 2);
  }

  // Private helper methods
  private calculateSummary(entries: CostEntry[]): {
    cost: number;
    transformations: number;
    tokens: number;
  } {
    const transformationIds = new Set(
      entries.filter(e => e.transformationId).map(e => e.transformationId)
    );

    return {
      cost: entries.reduce((sum, entry) => sum + entry.cost, 0),
      transformations: transformationIds.size,
      tokens: entries.reduce((sum, entry) => sum + entry.totalTokens, 0),
    };
  }

  private checkBudgetAlerts(newEntry: CostEntry): void {
    const summary = this.getUsageSummary();

    // Check daily threshold
    const dailyUsage = summary.today.cost / this.budgetConfig.dailyLimit;
    if (dailyUsage >= this.budgetConfig.alertThresholds.daily) {
      this.createAlert(
        'daily',
        `Daily budget usage at ${(dailyUsage * 100).toFixed(1)}%`,
        this.budgetConfig.alertThresholds.daily,
        dailyUsage
      );
    }

    // Check monthly threshold
    const monthlyUsage = summary.thisMonth.cost / this.budgetConfig.monthlyLimit;
    if (monthlyUsage >= this.budgetConfig.alertThresholds.monthly) {
      this.createAlert(
        'monthly',
        `Monthly budget usage at ${(monthlyUsage * 100).toFixed(1)}%`,
        this.budgetConfig.alertThresholds.monthly,
        monthlyUsage
      );
    }

    // Check per-transformation threshold
    const transformationUsage = newEntry.cost / this.budgetConfig.perTransformationLimit;
    if (transformationUsage >= this.budgetConfig.alertThresholds.perTransformation) {
      this.createAlert(
        'per_transformation',
        `Transformation cost at ${(transformationUsage * 100).toFixed(1)}% of limit`,
        this.budgetConfig.alertThresholds.perTransformation,
        transformationUsage
      );
    }
  }

  private createAlert(
    type: CostAlert['type'],
    message: string,
    threshold: number,
    current: number
  ): void {
    // Don't create duplicate alerts
    const existingAlert = this.alerts.find(
      alert => alert.type === type && !alert.acknowledged
    );
    
    if (existingAlert) return;

    const alert: CostAlert = {
      id: this.generateId(),
      type,
      message,
      threshold,
      current,
      timestamp: new Date(),
      acknowledged: false,
    };

    this.alerts.push(alert);
    this.saveAlerts();
  }

  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private saveData(): void {
    try {
      const data = {
        costEntries: this.costEntries.map(entry => ({
          ...entry,
          timestamp: entry.timestamp.toISOString(),
        })),
        budgetConfig: this.budgetConfig,
      };
      localStorage.setItem(CostTracker.STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('Failed to save cost data:', error);
    }
  }

  private saveAlerts(): void {
    try {
      const alerts = this.alerts.map(alert => ({
        ...alert,
        timestamp: alert.timestamp.toISOString(),
      }));
      localStorage.setItem(CostTracker.ALERTS_KEY, JSON.stringify(alerts));
    } catch (error) {
      console.error('Failed to save cost alerts:', error);
    }
  }

  private loadData(): void {
    try {
      // Load cost entries and budget config
      const stored = localStorage.getItem(CostTracker.STORAGE_KEY);
      if (stored) {
        const data = JSON.parse(stored);
        this.costEntries = data.costEntries.map((entry: any) => ({
          ...entry,
          timestamp: new Date(entry.timestamp),
        }));
        if (data.budgetConfig) {
          this.budgetConfig = { ...this.budgetConfig, ...data.budgetConfig };
        }
      }

      // Load alerts
      const alertsStored = localStorage.getItem(CostTracker.ALERTS_KEY);
      if (alertsStored) {
        this.alerts = JSON.parse(alertsStored).map((alert: any) => ({
          ...alert,
          timestamp: new Date(alert.timestamp),
        }));
      }
    } catch (error) {
      console.error('Failed to load cost data:', error);
    }
  }
}
