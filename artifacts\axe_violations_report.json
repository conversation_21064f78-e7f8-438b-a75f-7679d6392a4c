{"summary": {"violations": 4, "passes": 35, "incomplete": 1, "inapplicable": 53, "score": 88, "url": "http://localhost:8080/dashboard", "timestamp": "2024-01-18T10:30:00Z"}, "violations": [{"id": "button-name", "impact": "critical", "description": "Ensure buttons have discernible text", "help": "Buttons must have discernible text", "nodes": [{"target": "button[data-component-line=\"73\"]", "html": "<button class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground rounded-md h-8 w-8 p-0 shrink-0\">", "failureSummary": "Element does not have inner text that is visible to screen readers"}, {"target": "button[data-lov-id=\"src\\\\pages\\\\Dashboard.tsx:569:14\"]", "html": "<button class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground rounded-md h-8 w-8 p-0\">", "failureSummary": "Element does not have inner text that is visible to screen readers"}]}, {"id": "color-contrast", "impact": "serious", "description": "Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds", "help": "Elements must meet minimum color contrast ratio thresholds", "nodes": [{"target": "p[data-component-line=\"91\"]", "contrastRatio": 3.72, "expectedRatio": 4.5, "fgColor": "#64748b", "bgColor": "#111827"}, {"target": ".text-primary-foreground", "contrastRatio": 4.46, "expectedRatio": 4.5, "fgColor": "#ffffff", "bgColor": "#6366f1"}, {"target": "p[data-lov-name=\"CardDescription\"]", "contrastRatio": 3.58, "expectedRatio": 4.5, "fgColor": "#64748b", "bgColor": "#1b1b25"}]}, {"id": "page-has-heading-one", "impact": "moderate", "description": "Ensure that the page, or at least one of its frames contains a level-one heading", "help": "Page should contain a level-one heading", "nodes": [{"target": "html", "failureSummary": "Page must have a level-one heading"}]}, {"id": "region", "impact": "moderate", "description": "Ensure all page content is contained by landmarks", "help": "All page content should be contained by landmarks", "nodes": [{"target": "h3", "failureSummary": "Some page content is not contained by landmarks"}]}]}