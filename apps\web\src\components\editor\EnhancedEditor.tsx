import { useState, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { CodeEditor } from '../CodeEditor';
import { useBreakpoint } from '@/components/ui/mobile-layout';
import { DiffViewer } from '../DiffViewer';
import { 
  Settings, 
  Search, 
  Code, 
  GitCompare, 
  Save, 
  Play, 
  Square, 
  RotateCcw,
  Zap,
  Eye,
  FileText,
  Layers,
  Terminal,
  Bug,
  Plus,
  X
} from 'lucide-react';

interface EditorFile {
  id: string;
  name: string;
  path: string;
  content: string;
  language: string;
  isDirty: boolean;
  isReadOnly: boolean;
  lastModified: Date;
}

interface EnhancedEditorProps {
  initialCode?: string;
  transformedCode?: string;
  isTransforming?: boolean;
  onCodeChange?: (code: string) => void;
  onRunTransformation?: () => void;
  onStopTransformation?: () => void;
  onApplyChanges?: () => void;
  onResetChanges?: () => void;
}

const getLanguageFromExtension = (filename: string): string => {
  const ext = filename.split('.').pop()?.toLowerCase();
  const languageMap: Record<string, string> = {
    'js': 'javascript',
    'jsx': 'javascript',
    'ts': 'typescript',
    'tsx': 'typescript',
    'py': 'python',
    'java': 'java',
    'cpp': 'cpp',
    'c': 'c',
    'cs': 'csharp',
    'php': 'php',
    'rb': 'ruby',
    'go': 'go',
    'rs': 'rust',
    'html': 'html',
    'css': 'css',
    'scss': 'scss',
    'sass': 'sass',
    'json': 'json',
    'xml': 'xml',
    'yaml': 'yaml',
    'yml': 'yaml',
    'md': 'markdown',
    'sql': 'sql',
    'sh': 'shell',
    'bash': 'shell'
  };
  return languageMap[ext || ''] || 'plaintext';
};

export const EnhancedEditor = ({
  initialCode = '',
  transformedCode = '',
  isTransforming = false,
  onCodeChange,
  onRunTransformation,
  onStopTransformation,
  onApplyChanges,
  onResetChanges
}: EnhancedEditorProps) => {
  const { isMobile, isTablet } = useBreakpoint();
  const [activeView, setActiveView] = useState<'editor' | 'diff' | 'snippets'>('editor');
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [currentCode, setCurrentCode] = useState(initialCode);
  
  // Enhanced editor state
  const [editorFiles, setEditorFiles] = useState<EditorFile[]>([
    {
      id: '1',
      name: 'main.js',
      path: '/main.js',
      content: initialCode,
      language: 'javascript',
      isDirty: false,
      isReadOnly: false,
      lastModified: new Date()
    }
  ]);

  const [activeFileId, setActiveFileId] = useState<string>('1');

  useEffect(() => {
    setCurrentCode(initialCode);
    setEditorFiles(prev => prev.map(file => 
      file.id === '1' 
        ? { ...file, content: initialCode }
        : file
    ));
  }, [initialCode]);

  const handleFileChange = useCallback((fileId: string, content: string) => {
    setEditorFiles(prev => prev.map(file => 
      file.id === fileId 
        ? { ...file, content, isDirty: true, lastModified: new Date() }
        : file
    ));
    
    // Update main code if it's the main file
    if (fileId === '1') {
      setCurrentCode(content);
      onCodeChange?.(content);
    }
  }, [onCodeChange]);

  const handleFileCreate = useCallback((name: string, path: string, language: string) => {
    const newFile: EditorFile = {
      id: Date.now().toString(),
      name,
      path,
      content: '',
      language,
      isDirty: false,
      isReadOnly: false,
      lastModified: new Date()
    };
    setEditorFiles(prev => [...prev, newFile]);
  }, []);

  const handleFileDelete = useCallback((fileId: string) => {
    if (fileId === '1') return; // Don't delete main file
    setEditorFiles(prev => prev.filter(file => file.id !== fileId));
  }, []);

  const handleFileRename = useCallback((fileId: string, newName: string) => {
    setEditorFiles(prev => prev.map(file => 
      file.id === fileId 
        ? { ...file, name: newName, isDirty: true }
        : file
    ));
  }, []);

  const handleFileSave = useCallback((fileId: string) => {
    setEditorFiles(prev => prev.map(file => 
      file.id === fileId 
        ? { ...file, isDirty: false }
        : file
    ));
  }, []);

  const handleFileSaveAll = useCallback(() => {
    setEditorFiles(prev => prev.map(file => ({ ...file, isDirty: false })));
  }, []);

  const getStatusInfo = () => {
    const totalFiles = editorFiles.length;
    const dirtyFiles = editorFiles.filter(f => f.isDirty).length;
    const totalLines = editorFiles.reduce((acc, file) => acc + file.content.split('\n').length, 0);

    return { totalFiles, dirtyFiles, totalLines };
  };

  const status = getStatusInfo();
  const activeFile = editorFiles.find(f => f.id === activeFileId);

  return (
    <div className="w-full h-full flex flex-col">
      {/* Toolbar */}
      <div className={`border-b border-border bg-background ${isMobile ? 'p-1' : 'p-2'}`}>
        <div className={isMobile ? "space-y-2" : "flex items-center justify-between"}>
          <div className="flex items-center space-x-2">
            <Tabs value={activeView} onValueChange={(value: any) => setActiveView(value)}>
              <TabsList className={isMobile ? "h-7 w-full" : "h-8"}>
                <TabsTrigger value="editor" className={isMobile ? "text-xs flex-1" : "text-xs"}>
                  <FileText className="w-3 h-3 mr-1" />
                  {isMobile ? "Edit" : "Editor"}
                </TabsTrigger>
                <TabsTrigger value="diff" className={isMobile ? "text-xs flex-1" : "text-xs"}>
                  <GitCompare className="w-3 h-3 mr-1" />
                  Diff
                </TabsTrigger>
                <TabsTrigger value="snippets" className={isMobile ? "text-xs flex-1" : "text-xs"}>
                  <Code className="w-3 h-3 mr-1" />
                  {isMobile ? "Snip" : "Snippets"}
                </TabsTrigger>
              </TabsList>
            </Tabs>

            <Separator orientation="vertical" className="h-6" />

            {/* Action buttons */}
            <div className="flex items-center space-x-1">
              {onRunTransformation && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={isTransforming ? onStopTransformation : onRunTransformation}
                  disabled={editorFiles.length === 0}
                >
                  {isTransforming ? (
                    <>
                      <Square className="w-3 h-3 mr-1" />
                      Stop
                    </>
                  ) : (
                    <>
                      <Play className="w-3 h-3 mr-1" />
                      Transform
                    </>
                  )}
                </Button>
              )}

              <Button variant="outline" size="sm" onClick={handleFileSaveAll}>
                <Save className="w-3 h-3 mr-1" />
                Save All
              </Button>

              {onApplyChanges && transformedCode && (
                <Button variant="outline" size="sm" onClick={onApplyChanges}>
                  <Zap className="w-3 h-3 mr-1" />
                  Apply
                </Button>
              )}

              {onResetChanges && (
                <Button variant="outline" size="sm" onClick={onResetChanges}>
                  <RotateCcw className="w-3 h-3 mr-1" />
                  Reset
                </Button>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {/* Status indicators */}
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <Badge variant="outline" className="text-xs">
                {status.totalFiles} files
              </Badge>
              {status.dirtyFiles > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {status.dirtyFiles} unsaved
                </Badge>
              )}
              <Badge variant="outline" className="text-xs">
                {status.totalLines} lines
              </Badge>
            </div>

            <Separator orientation="vertical" className="h-6" />

            {/* Tool buttons */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsSearchOpen(true)}
            >
              <Search className="w-4 h-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsSettingsOpen(true)}
            >
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1">
        {activeView === 'editor' && (
          <div className="h-full flex">
            {/* File tabs */}
            <div className={isMobile ? "w-full border-b border-border bg-muted/30" : "w-64 border-r border-border bg-muted/30"}>
              <div className={isMobile ? "p-2 border-b border-border" : "p-3 border-b border-border"}>
                <div className="flex items-center justify-between mb-2">
                  <h3 className={isMobile ? "font-medium text-xs" : "font-medium text-sm"}>Files</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      const name = prompt('File name:');
                      if (name) {
                        const language = getLanguageFromExtension(name);
                        handleFileCreate(name, `/${name}`, language);
                      }
                    }}
                    className={isMobile ? "h-5 w-5 p-0" : "h-6 w-6 p-0"}
                  >
                    <Plus className="w-3 h-3" />
                  </Button>
                </div>
              </div>
              
              <div className="p-2 space-y-1">
                {editorFiles.map(file => (
                  <div
                    key={file.id}
                    className={`flex items-center justify-between p-2 hover:bg-muted rounded cursor-pointer ${
                      file.id === activeFileId ? 'bg-muted' : ''
                    }`}
                    onClick={() => setActiveFileId(file.id)}
                  >
                    <div className="flex items-center space-x-2">
                      <FileText className="w-4 h-4" />
                      <span className="text-sm truncate">{file.name}</span>
                      {file.isDirty && (
                        <div className="w-2 h-2 bg-orange-500 rounded-full" />
                      )}
                    </div>
                    {file.id !== '1' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleFileDelete(file.id);
                        }}
                        className="h-4 w-4 p-0"
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Editor */}
            <div className="flex-1">
              {activeFile ? (
                <CodeEditor
                  value={activeFile.content}
                  onChange={(content) => handleFileChange(activeFile.id, content)}
                  language={activeFile.language}
                  readOnly={activeFile.isReadOnly}
                  enableIntelliSense={true}
                  enableErrorDetection={true}
                  enableCodeLens={true}
                  onSave={() => handleFileSave(activeFile.id)}
                />
              ) : (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                  <div className="text-center">
                    <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No file selected</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {activeView === 'diff' && (
          <div className="h-full">
            <DiffViewer 
              diffContent={transformedCode ? `--- Original\n+++ Transformed\n${transformedCode}` : ''}
            />
          </div>
        )}

        {activeView === 'snippets' && (
          <div className="h-full p-4">
            <div className="text-center py-8 text-muted-foreground">
              <Code className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>Code snippets feature</p>
              <p className="text-sm">Coming soon...</p>
            </div>
          </div>
        )}
      </div>

      {/* Status bar */}
      <div className="border-t border-border bg-muted/30 px-3 py-1">
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center space-x-4">
            <span>Ready</span>
            {isTransforming && (
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                <span>Transforming...</span>
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-4">
            <span>Ln 1, Col 1</span>
            <span>14px</span>
            <span>2 spaces</span>
            <span>dark</span>
          </div>
        </div>
      </div>
    </div>
  );
};
