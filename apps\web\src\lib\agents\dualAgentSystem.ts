import { z } from 'zod';

// Agent Types
export enum AgentType {
  PLANNER = 'planner',
  CRITIC = 'critic',
}

export enum AgentStatus {
  IDLE = 'idle',
  THINKING = 'thinking',
  GENERATING = 'generating',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

// Transformation Request Schema
export const transformationRequestSchema = z.object({
  id: z.string(),
  code: z.string(),
  prompt: z.string(),
  language: z.string().optional(),
  context: z.record(z.any()).optional(),
  constraints: z.array(z.string()).optional(),
  preferences: z.object({
    plannerModel: z.string(),
    criticModel: z.string(),
    temperature: z.number(),
    maxTokens: z.number(),
    maxIterations: z.number(),
    scoreThreshold: z.number(),
  }),
});

export type TransformationRequest = z.infer<typeof transformationRequestSchema>;

// Agent Response Schema
export const agentResponseSchema = z.object({
  agentType: z.nativeEnum(AgentType),
  status: z.nativeEnum(AgentStatus),
  iteration: z.number(),
  timestamp: z.string().transform(str => new Date(str)),
  content: z.string(),
  reasoning: z.string().optional(),
  score: z.number().optional(),
  suggestions: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional(),
});

export type AgentResponse = z.infer<typeof agentResponseSchema>;

// Transformation Result Schema
export const transformationResultSchema = z.object({
  id: z.string(),
  status: z.enum(['in_progress', 'completed', 'failed', 'cancelled']),
  originalCode: z.string(),
  transformedCode: z.string().optional(),
  iterations: z.array(z.object({
    plannerResponse: agentResponseSchema,
    criticResponse: agentResponseSchema.optional(),
    score: z.number(),
    accepted: z.boolean(),
  })),
  finalScore: z.number().optional(),
  totalCost: z.number().optional(),
  duration: z.number().optional(),
  error: z.string().optional(),
});

export type TransformationResult = z.infer<typeof transformationResultSchema>;

// Abstract Agent Base Class
export abstract class Agent {
  protected agentType: AgentType;
  protected model: string;
  protected temperature: number;
  protected maxTokens: number;

  constructor(
    agentType: AgentType,
    model: string,
    temperature: number = 0.7,
    maxTokens: number = 4000
  ) {
    this.agentType = agentType;
    this.model = model;
    this.temperature = temperature;
    this.maxTokens = maxTokens;
  }

  abstract process(
    request: TransformationRequest,
    iteration: number,
    previousResponse?: AgentResponse
  ): Promise<AgentResponse>;

  protected createResponse(
    status: AgentStatus,
    iteration: number,
    content: string,
    reasoning?: string,
    score?: number,
    suggestions?: string[]
  ): AgentResponse {
    return {
      agentType: this.agentType,
      status,
      iteration,
      timestamp: new Date(),
      content,
      reasoning,
      score,
      suggestions,
      metadata: {
        model: this.model,
        temperature: this.temperature,
        maxTokens: this.maxTokens,
      },
    };
  }
}

// Planner Agent
export class PlannerAgent extends Agent {
  constructor(model: string, temperature: number = 0.7, maxTokens: number = 4000) {
    super(AgentType.PLANNER, model, temperature, maxTokens);
  }

  async process(
    request: TransformationRequest,
    iteration: number,
    criticFeedback?: AgentResponse
  ): Promise<AgentResponse> {
    try {
      const prompt = this.buildPlannerPrompt(request, iteration, criticFeedback);
      
      // Simulate AI model call
      const response = await this.callAIModel(prompt);
      
      return this.createResponse(
        AgentStatus.COMPLETED,
        iteration,
        response.code,
        response.reasoning,
        undefined, // Planner doesn't score, only generates
        response.suggestions
      );
    } catch (error) {
      return this.createResponse(
        AgentStatus.FAILED,
        iteration,
        '',
        `Failed to generate transformation: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private buildPlannerPrompt(
    request: TransformationRequest,
    iteration: number,
    criticFeedback?: AgentResponse
  ): string {
    let prompt = `You are a code transformation planner. Your task is to transform the given code according to the user's requirements.

Original Code:
\`\`\`${request.language || 'javascript'}
${request.code}
\`\`\`

Transformation Request:
${request.prompt}

${request.constraints?.length ? `Constraints:
${request.constraints.map(c => `- ${c}`).join('\n')}` : ''}

${request.context ? `Additional Context:
${JSON.stringify(request.context, null, 2)}` : ''}`;

    if (iteration > 1 && criticFeedback) {
      prompt += `

Previous Iteration Feedback:
${criticFeedback.reasoning}

Suggestions for improvement:
${criticFeedback.suggestions?.map(s => `- ${s}`).join('\n') || 'None provided'}`;
    }

    prompt += `

Please provide:
1. The transformed code
2. Your reasoning for the changes
3. Any suggestions for further improvements

Format your response as JSON:
{
  "code": "transformed code here",
  "reasoning": "explanation of changes",
  "suggestions": ["suggestion 1", "suggestion 2"]
}`;

    return prompt;
  }

  private async callAIModel(prompt: string): Promise<{
    code: string;
    reasoning: string;
    suggestions: string[];
  }> {
    // Simulate AI model call with delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Mock response for demonstration
    return {
      code: `// Optimized version with memoization
const fibonacci = (() => {
  const cache = new Map();
  
  return function fib(n) {
    if (n <= 1) return n;
    
    if (cache.has(n)) {
      return cache.get(n);
    }
    
    const result = fib(n - 1) + fib(n - 2);
    cache.set(n, result);
    return result;
  };
})();

console.log(fibonacci(10));`,
      reasoning: 'Added memoization using a closure and Map to cache previously computed values, significantly improving performance for larger inputs.',
      suggestions: [
        'Consider adding input validation for negative numbers',
        'Could add TypeScript types for better type safety',
        'Consider using BigInt for very large Fibonacci numbers'
      ]
    };
  }
}

// Critic Agent
export class CriticAgent extends Agent {
  constructor(model: string, temperature: number = 0.3, maxTokens: number = 2000) {
    super(AgentType.CRITIC, model, temperature, maxTokens);
  }

  async process(
    request: TransformationRequest,
    iteration: number,
    plannerResponse: AgentResponse
  ): Promise<AgentResponse> {
    try {
      const prompt = this.buildCriticPrompt(request, plannerResponse);
      
      // Simulate AI model call
      const response = await this.callAIModel(prompt);
      
      return this.createResponse(
        AgentStatus.COMPLETED,
        iteration,
        plannerResponse.content, // Critic doesn't modify code, just evaluates
        response.reasoning,
        response.score,
        response.suggestions
      );
    } catch (error) {
      return this.createResponse(
        AgentStatus.FAILED,
        iteration,
        '',
        `Failed to evaluate transformation: ${error instanceof Error ? error.message : 'Unknown error'}`,
        0
      );
    }
  }

  private buildCriticPrompt(
    request: TransformationRequest,
    plannerResponse: AgentResponse
  ): string {
    return `You are a code transformation critic. Evaluate the quality of the proposed transformation.

Original Code:
\`\`\`${request.language || 'javascript'}
${request.code}
\`\`\`

Transformation Request:
${request.prompt}

Proposed Transformation:
\`\`\`${request.language || 'javascript'}
${plannerResponse.content}
\`\`\`

Planner's Reasoning:
${plannerResponse.reasoning}

Evaluate the transformation on these criteria:
1. Correctness: Does the code work as intended?
2. Completeness: Does it fully address the request?
3. Quality: Is the code well-written and maintainable?
4. Performance: Are there performance improvements?
5. Best Practices: Does it follow coding best practices?

Provide a score from 0.0 to 1.0 and detailed feedback.

Format your response as JSON:
{
  "score": 0.85,
  "reasoning": "detailed evaluation",
  "suggestions": ["improvement 1", "improvement 2"]
}`;
  }

  private async callAIModel(prompt: string): Promise<{
    score: number;
    reasoning: string;
    suggestions: string[];
  }> {
    // Simulate AI model call with delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Mock response for demonstration
    return {
      score: 0.85,
      reasoning: 'The transformation successfully adds memoization which dramatically improves performance. The implementation is clean and follows good practices. However, it could benefit from input validation and TypeScript types.',
      suggestions: [
        'Add input validation for negative numbers',
        'Consider using TypeScript for better type safety',
        'Add JSDoc comments for better documentation'
      ]
    };
  }
}

// Dual Agent System
export class DualAgentSystem {
  private planner: PlannerAgent;
  private critic: CriticAgent;

  constructor(
    plannerModel: string,
    criticModel: string,
    plannerTemp: number = 0.7,
    criticTemp: number = 0.3,
    maxTokens: number = 4000
  ) {
    this.planner = new PlannerAgent(plannerModel, plannerTemp, maxTokens);
    this.critic = new CriticAgent(criticModel, criticTemp, maxTokens);
  }

  async transform(request: TransformationRequest): Promise<TransformationResult> {
    const result: TransformationResult = {
      id: request.id,
      status: 'in_progress',
      originalCode: request.code,
      iterations: [],
      totalCost: 0,
    };

    const startTime = Date.now();

    try {
      for (let iteration = 1; iteration <= request.preferences.maxIterations; iteration++) {
        // Get previous critic feedback if available
        const previousCritic = result.iterations[iteration - 2]?.criticResponse;
        
        // Planner generates transformation
        const plannerResponse = await this.planner.process(request, iteration, previousCritic);
        
        if (plannerResponse.status === AgentStatus.FAILED) {
          result.status = 'failed';
          result.error = plannerResponse.reasoning;
          break;
        }

        // Critic evaluates transformation
        const criticResponse = await this.critic.process(request, iteration, plannerResponse);
        
        if (criticResponse.status === AgentStatus.FAILED) {
          result.status = 'failed';
          result.error = criticResponse.reasoning;
          break;
        }

        const score = criticResponse.score || 0;
        const accepted = score >= request.preferences.scoreThreshold;

        result.iterations.push({
          plannerResponse,
          criticResponse,
          score,
          accepted,
        });

        // If score meets threshold, we're done
        if (accepted) {
          result.status = 'completed';
          result.transformedCode = plannerResponse.content;
          result.finalScore = score;
          break;
        }

        // If this was the last iteration, mark as completed with best attempt
        if (iteration === request.preferences.maxIterations) {
          result.status = 'completed';
          result.transformedCode = plannerResponse.content;
          result.finalScore = score;
        }
      }
    } catch (error) {
      result.status = 'failed';
      result.error = error instanceof Error ? error.message : 'Unknown error';
    }

    result.duration = Date.now() - startTime;
    return result;
  }
}
