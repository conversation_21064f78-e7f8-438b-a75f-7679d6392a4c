{"metadata": {"timestamp": "2025-01-19T00:00:00.000Z", "url": "http://localhost:8080/dashboard", "discoveryMethod": "static_analysis", "totalControls": 47, "version": "1.0.0", "notes": "Discovered through static code analysis of React components. Missing data-testid attributes on most controls."}, "summary": {"controlsByType": {"button": 18, "slider": 1, "input": 6, "select": 3, "checkbox": 1, "tab": 7, "badge": 11}, "controlsByComponent": {"QuickSettingsPanel": 12, "NotificationCenter": 10, "SystemHealthIndicators": 2, "PerformanceMetricsWidget": 1, "CostTrackingWidget": 2, "DashboardIntegration": 0, "MainDashboard": 20}, "missingDataTestIds": 47, "accessibilityIssues": ["Most controls lack data-testid attributes for testing", "Some buttons lack descriptive aria-labels", "Form inputs could benefit from better labeling"]}, "controls": [{"id": "main-back-button", "type": "button", "component": "MainDashboard", "text": "Back", "data-testid": null, "aria-label": "Go back to home page", "functionality": "Navigate to home page", "selector": "button[aria-label='Go back to home page']", "location": "header"}, {"id": "examples-toggle", "type": "button", "component": "MainDashboard", "text": "Examples", "data-testid": null, "aria-label": "Show code examples", "functionality": "Toggle code examples panel", "selector": "button[aria-label*='code examples']", "location": "header"}, {"id": "help-toggle", "type": "button", "component": "MainDashboard", "text": "Help", "data-testid": null, "aria-label": "Show help panel", "functionality": "Toggle help panel", "selector": "button[aria-label*='help panel']", "location": "header"}, {"id": "apply-changes-button", "type": "button", "component": "MainDashboard", "text": "Apply Changes", "data-testid": null, "aria-label": "Apply the transformed code changes to the editor", "functionality": "Apply code transformations", "selector": "button[aria-label*='Apply the transformed code changes']", "location": "header"}, {"id": "download-button", "type": "button", "component": "MainDashboard", "text": "Download", "data-testid": null, "aria-label": "Download current code as a file", "functionality": "Download code file", "selector": "button[aria-label*='Download current code']", "location": "header"}, {"id": "history-button", "type": "button", "component": "MainDashboard", "text": "History", "data-testid": null, "aria-label": "View transformation history", "functionality": "Navigate to history page", "selector": "button[aria-label*='transformation history']", "location": "header"}, {"id": "settings-button", "type": "button", "component": "MainDashboard", "text": "Settings", "data-testid": null, "aria-label": "Open settings page", "functionality": "Navigate to settings page", "selector": "button[aria-label*='settings page']", "location": "header"}, {"id": "tab-code-editor", "type": "tab", "component": "MainDashboard", "text": "Editor", "data-testid": null, "aria-controls": "code-editor-panel", "functionality": "Switch to code editor tab", "selector": "[role='tab'][aria-controls='code-editor-panel']", "location": "tab-bar"}, {"id": "tab-enhanced-editor", "type": "tab", "component": "MainDashboard", "text": "Enhanced", "data-testid": null, "aria-controls": "enhanced-editor-panel", "functionality": "Switch to enhanced editor tab", "selector": "[role='tab'][aria-controls='enhanced-editor-panel']", "location": "tab-bar"}, {"id": "tab-overview", "type": "tab", "component": "MainDashboard", "text": "Overview", "data-testid": null, "aria-controls": "overview-panel", "functionality": "Switch to dashboard overview tab", "selector": "[role='tab'][aria-controls='overview-panel']", "location": "tab-bar"}, {"id": "tab-analytics", "type": "tab", "component": "MainDashboard", "text": "Analytics", "data-testid": null, "aria-controls": "analytics-panel", "functionality": "Switch to analytics tab", "selector": "[role='tab'][aria-controls='analytics-panel']", "location": "tab-bar"}, {"id": "tab-admin", "type": "tab", "component": "MainDashboard", "text": "Admin", "data-testid": null, "aria-controls": "admin-panel", "functionality": "Switch to admin tab", "selector": "[role='tab'][aria-controls='admin-panel']", "location": "tab-bar"}, {"id": "tab-workspace", "type": "tab", "component": "MainDashboard", "text": "Workspace", "data-testid": null, "aria-controls": "workspace-panel", "functionality": "Switch to workspace tab", "selector": "[role='tab'][aria-controls='workspace-panel']", "location": "tab-bar"}, {"id": "tab-notifications", "type": "tab", "component": "MainDashboard", "text": "<PERSON><PERSON><PERSON>", "data-testid": null, "aria-controls": "notifications-panel", "functionality": "Switch to notifications tab", "selector": "[role='tab'][aria-controls='notifications-panel']", "location": "tab-bar"}, {"id": "dashboard-badge", "type": "badge", "component": "MainDashboard", "text": "Dashboard", "data-testid": null, "functionality": "Display current page indicator", "selector": ".bg-agent-planner\\/20", "location": "header"}, {"id": "performance-refresh-button", "type": "button", "component": "PerformanceMetricsWidget", "text": "", "data-testid": null, "aria-label": "Refresh performance metrics", "functionality": "Refresh performance data", "selector": "button.h-8.w-8.p-0", "location": "widget-header"}, {"id": "cost-refresh-button", "type": "button", "component": "CostTrackingWidget", "text": "", "data-testid": null, "aria-label": "Refresh cost data", "functionality": "Refresh cost tracking data", "selector": "button.h-8.w-8.p-0", "location": "widget-header"}, {"id": "cost-view-details-button", "type": "button", "component": "CostTrackingWidget", "text": "View Details", "data-testid": null, "functionality": "View detailed cost information", "selector": "button.h-7.px-2.text-xs", "location": "widget-footer"}, {"id": "system-health-refresh-button", "type": "button", "component": "SystemHealthIndicators", "text": "", "data-testid": null, "aria-label": "Refresh system health", "functionality": "Refresh system health data", "selector": "button.h-8.w-8.p-0", "location": "widget-header"}, {"id": "system-health-badge", "type": "badge", "component": "SystemHealthIndicators", "text": "Healthy", "data-testid": null, "functionality": "Display system health status", "selector": "Badge[variant]", "location": "widget-header"}, {"id": "quick-settings-planner-select", "type": "select", "component": "QuickSettingsPanel", "text": "Planner Model", "data-testid": null, "functionality": "Select AI planner model", "selector": "select.w-full.px-3.py-2", "location": "widget-body"}, {"id": "quick-settings-critic-select", "type": "select", "component": "QuickSettingsPanel", "text": "Critic Model", "data-testid": null, "functionality": "Select AI critic model", "selector": "select.w-full.px-3.py-2", "location": "widget-body"}, {"id": "quick-settings-temperature-slider", "type": "slider", "component": "QuickSettingsPanel", "text": "Temperature", "data-testid": null, "functionality": "Adjust AI temperature parameter", "selector": "Slider.w-full", "location": "widget-body"}, {"id": "quick-settings-temperature-badge", "type": "badge", "component": "QuickSettingsPanel", "text": "0.70", "data-testid": null, "functionality": "Display current temperature value", "selector": "Badge.font-mono.text-xs", "location": "widget-body"}, {"id": "quick-settings-max-tokens-input", "type": "input", "component": "QuickSettingsPanel", "text": "<PERSON>", "data-testid": null, "functionality": "Set maximum token limit", "selector": "Input[type='number'].font-mono", "location": "widget-body"}, {"id": "quick-settings-max-iterations-input", "type": "input", "component": "QuickSettingsPanel", "text": "Max Iterations", "data-testid": null, "functionality": "Set maximum loop iterations", "selector": "Input[type='number'].font-mono", "location": "widget-body"}, {"id": "quick-settings-score-threshold-input", "type": "input", "component": "QuickSettingsPanel", "text": "Score Threshold", "data-testid": null, "functionality": "Set quality score threshold", "selector": "Input[type='number'].font-mono", "location": "widget-body"}, {"id": "quick-settings-cost-limit-input", "type": "input", "component": "QuickSettingsPanel", "text": "Cost Limit ($)", "data-testid": null, "functionality": "Set cost limit", "selector": "Input[type='number'].font-mono", "location": "widget-body"}, {"id": "quick-settings-timeout-input", "type": "input", "component": "QuickSettingsPanel", "text": "Timeout (seconds)", "data-testid": null, "functionality": "Set timeout duration", "selector": "Input[type='number'].font-mono", "location": "widget-body"}, {"id": "quick-settings-cost-badge", "type": "badge", "component": "QuickSettingsPanel", "text": "$0.0234", "data-testid": null, "functionality": "Display estimated cost", "selector": "Badge[variant]", "location": "widget-body"}, {"id": "quick-settings-reset-button", "type": "button", "component": "QuickSettingsPanel", "text": "Reset", "data-testid": null, "functionality": "Reset settings to defaults", "selector": "Button[variant='outline'].size-sm", "location": "widget-footer"}, {"id": "quick-settings-save-button", "type": "button", "component": "QuickSettingsPanel", "text": "Save Settings", "data-testid": null, "functionality": "Save current settings", "selector": "Button.size-sm", "location": "widget-footer"}, {"id": "notifications-unread-badge", "type": "badge", "component": "NotificationCenter", "text": "5 unread", "data-testid": null, "functionality": "Display unread notification count", "selector": "Badge[variant='destructive']", "location": "widget-header"}, {"id": "notifications-mark-all-read-button", "type": "button", "component": "NotificationCenter", "text": "<PERSON>", "data-testid": null, "functionality": "Mark all notifications as read", "selector": "But<PERSON>[variant='ghost'].text-xs", "location": "widget-header"}, {"id": "notifications-clear-all-button", "type": "button", "component": "NotificationCenter", "text": "Clear All", "data-testid": null, "functionality": "Clear all notifications", "selector": "But<PERSON>[variant='ghost'].text-xs", "location": "widget-header"}, {"id": "notifications-filter-select", "type": "select", "component": "NotificationCenter", "text": "Filter notifications", "data-testid": null, "functionality": "Filter notifications by type", "selector": "select.px-3.py-1.text-sm", "location": "widget-body"}, {"id": "notifications-unread-only-checkbox", "type": "checkbox", "component": "NotificationCenter", "text": "Unread only", "data-testid": null, "functionality": "Show only unread notifications", "selector": "input[type='checkbox'].rounded", "location": "widget-body"}, {"id": "notification-priority-badge", "type": "badge", "component": "NotificationCenter", "text": "high", "data-testid": null, "functionality": "Display notification priority", "selector": "Badge[variant='outline'].text-xs", "location": "notification-item"}, {"id": "notification-action-button", "type": "button", "component": "NotificationCenter", "text": "Review Settings", "data-testid": null, "functionality": "Execute notification action", "selector": "Button[variant='outline'].text-xs.h-6", "location": "notification-item"}, {"id": "notification-mark-read-button", "type": "button", "component": "NotificationCenter", "text": "<PERSON>", "data-testid": null, "functionality": "Mark individual notification as read", "selector": "<PERSON><PERSON>[variant='ghost'].text-xs.h-6", "location": "notification-item"}, {"id": "notification-dismiss-button", "type": "button", "component": "NotificationCenter", "text": "", "data-testid": null, "functionality": "Dismiss individual notification", "selector": "<PERSON>ton[variant='ghost'].h-6.w-6.p-0", "location": "notification-item"}]}