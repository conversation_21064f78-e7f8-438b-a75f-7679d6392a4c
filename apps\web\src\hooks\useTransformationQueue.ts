import { useState, useEffect, useCallback } from 'react';
import { TransformationQueue, JobPriority, JobStatus, QueueEvent } from '@/lib/queue/transformationQueue';
import { TransformationRequest } from '@/lib/agents/dualAgentSystem';

// Global queue instance
let queueInstance: TransformationQueue | null = null;

const getQueueInstance = () => {
  if (!queueInstance) {
    queueInstance = new TransformationQueue({
      maxConcurrentJobs: 2,
      maxRetries: 3,
      retryDelay: 5000,
      jobTimeout: 300000, // 5 minutes
      enablePersistence: true,
    });
  }
  return queueInstance;
};

export const useTransformationQueue = () => {
  const [queue] = useState(() => getQueueInstance());
  const [stats, setStats] = useState(queue.getStats());
  const [jobs, setJobs] = useState(queue.getAllJobs());
  const [isProcessing, setIsProcessing] = useState(false);

  // Update stats and jobs when queue changes
  const updateQueueState = useCallback(() => {
    setStats(queue.getStats());
    setJobs(queue.getAllJobs());
    setIsProcessing(queue.getStats().running > 0);
  }, [queue]);

  // Set up event listeners
  useEffect(() => {
    const handleQueueEvent = (event: QueueEvent) => {
      updateQueueState();
      
      // You can add specific event handling here
      switch (event.type) {
        case 'job_completed':
          console.log(`Job ${event.jobId} completed`);
          break;
        case 'job_failed':
          console.log(`Job ${event.jobId} failed`);
          break;
        case 'job_started':
          console.log(`Job ${event.jobId} started`);
          break;
      }
    };

    // Subscribe to all queue events
    const eventTypes = [
      'job_added',
      'job_started', 
      'job_completed',
      'job_failed',
      'job_cancelled',
      'job_retrying',
      'queue_empty',
      'queue_full'
    ] as const;

    eventTypes.forEach(eventType => {
      queue.on(eventType, handleQueueEvent);
    });

    // Initial state update
    updateQueueState();

    // Cleanup
    return () => {
      eventTypes.forEach(eventType => {
        queue.off(eventType, handleQueueEvent);
      });
    };
  }, [queue, updateQueueState]);

  // Add transformation to queue
  const addTransformation = useCallback((
    code: string,
    prompt: string,
    priority: JobPriority = JobPriority.NORMAL,
    options?: {
      language?: string;
      plannerModel?: string;
      criticModel?: string;
      temperature?: number;
      maxTokens?: number;
      maxIterations?: number;
      scoreThreshold?: number;
    }
  ) => {
    const request: TransformationRequest = {
      id: `transform_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      code,
      prompt,
      language: options?.language || 'javascript',
      preferences: {
        plannerModel: options?.plannerModel || 'gpt-4-turbo',
        criticModel: options?.criticModel || 'claude-3-sonnet',
        temperature: options?.temperature || 0.7,
        maxTokens: options?.maxTokens || 4000,
        maxIterations: options?.maxIterations || 10,
        scoreThreshold: options?.scoreThreshold || 0.8,
      },
    };

    const jobId = queue.addJob(request, priority, {
      addedAt: new Date().toISOString(),
      userInitiated: true,
    });

    return jobId;
  }, [queue]);

  // Cancel job
  const cancelJob = useCallback((jobId: string) => {
    return queue.cancelJob(jobId);
  }, [queue]);

  // Remove job
  const removeJob = useCallback((jobId: string) => {
    return queue.removeJob(jobId);
  }, [queue]);

  // Clear completed jobs
  const clearCompleted = useCallback(() => {
    return queue.clearCompleted();
  }, [queue]);

  // Get job by ID
  const getJob = useCallback((jobId: string) => {
    return queue.getJob(jobId);
  }, [queue]);

  // Get jobs by status
  const getJobsByStatus = useCallback((status: JobStatus) => {
    return queue.getJobsByStatus(status);
  }, [queue]);

  // Get pending jobs
  const getPendingJobs = useCallback(() => {
    return jobs.filter(job => job.status === JobStatus.PENDING);
  }, [jobs]);

  // Get running jobs
  const getRunningJobs = useCallback(() => {
    return jobs.filter(job => job.status === JobStatus.RUNNING);
  }, [jobs]);

  // Get completed jobs
  const getCompletedJobs = useCallback(() => {
    return jobs.filter(job => job.status === JobStatus.COMPLETED);
  }, [jobs]);

  // Get failed jobs
  const getFailedJobs = useCallback(() => {
    return jobs.filter(job => job.status === JobStatus.FAILED);
  }, [jobs]);

  // Get recent jobs (last 10)
  const getRecentJobs = useCallback(() => {
    return [...jobs]
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, 10);
  }, [jobs]);

  return {
    // Queue state
    stats,
    jobs,
    isProcessing,
    
    // Job management
    addTransformation,
    cancelJob,
    removeJob,
    clearCompleted,
    getJob,
    
    // Job queries
    getJobsByStatus,
    getPendingJobs,
    getRunningJobs,
    getCompletedJobs,
    getFailedJobs,
    getRecentJobs,
    
    // Queue instance (for advanced usage)
    queue,
  };
};

// Hook for monitoring a specific job
export const useJobMonitor = (jobId: string | null) => {
  const { getJob } = useTransformationQueue();
  const [job, setJob] = useState(jobId ? getJob(jobId) : null);

  useEffect(() => {
    if (!jobId) {
      setJob(null);
      return;
    }

    // Update job state periodically
    const interval = setInterval(() => {
      const currentJob = getJob(jobId);
      setJob(currentJob);
    }, 1000);

    return () => clearInterval(interval);
  }, [jobId, getJob]);

  return job;
};

// Hook for queue statistics
export const useQueueStats = () => {
  const { stats } = useTransformationQueue();
  
  const getQueueHealth = () => {
    const { total, failed, completed } = stats;
    if (total === 0) return 'idle';
    
    const failureRate = failed / total;
    const successRate = completed / total;
    
    if (failureRate > 0.3) return 'unhealthy';
    if (successRate > 0.8) return 'healthy';
    return 'moderate';
  };

  const getEstimatedWaitTime = () => {
    const { pending, running } = stats;
    if (pending === 0) return 0;
    
    // Rough estimate: 2 minutes per job, considering concurrent processing
    const avgJobTime = 2 * 60 * 1000; // 2 minutes in ms
    const concurrentJobs = Math.max(running, 1);
    
    return Math.ceil((pending * avgJobTime) / concurrentJobs);
  };

  return {
    ...stats,
    health: getQueueHealth(),
    estimatedWaitTime: getEstimatedWaitTime(),
  };
};
