# Enhanced Settings System Guide

## Overview

The Enhanced Settings System provides comprehensive configuration management for the dual-agent SDK with enterprise-grade controls, real-time monitoring, and advanced provider management.

## Features

### 🔧 Multi-Provider Configuration
- **15+ AI Models** across OpenAI, Vertex AI, and Anthropic
- **Dynamic Model Selection** with provider-specific settings
- **API Key Management** with secure encryption and validation
- **Connection Testing** with real-time health monitoring

### 💰 Cost Management
- **Real-time Budget Monitoring** with configurable limits
- **Usage Tracking** with detailed analytics
- **Alert Thresholds** for cost and token consumption
- **Efficiency Metrics** and optimization recommendations

### 🛡️ Reliability & Performance
- **Automatic Failover** with health-based routing
- **Exponential Backoff** retry logic with jitter
- **Performance Optimization** for streaming and parallel execution
- **Provider Health Monitoring** with response time tracking

### 📊 Real-time Dashboard
- **Live Usage Metrics** with automatic updates
- **Provider Status** monitoring
- **Cost Tracking** with trend analysis
- **Performance Analytics** and optimization insights

## Getting Started

### Basic Configuration

1. **Navigate to Settings**
   ```
   Dashboard → Settings → Enhanced Settings
   ```

2. **Configure Providers**
   - Go to the "Providers" tab
   - Set up your Planner and Critic agents
   - Add API keys for each provider
   - Test connections to verify setup

3. **Set Cost Limits**
   - Switch to "Cost & Tokens" tab
   - Configure budget limits and alerts
   - Set token usage thresholds
   - Enable cost guard protection

4. **Configure Reliability**
   - Open "Reliability" tab
   - Set up failover providers
   - Configure retry settings
   - Enable performance optimizations

### Provider Configuration

#### OpenAI Setup
```typescript
{
  type: 'openai',
  model: 'gpt-4o',
  temperature: 0.7,
  maxTokens: 4000,
  organizationId: 'org-xxx', // Optional
  projectId: 'proj-xxx'      // Optional
}
```

#### Vertex AI Setup
```typescript
{
  type: 'vertex-ai',
  model: 'gemini-2.0-flash-exp',
  temperature: 0.7,
  maxTokens: 4000,
  projectId: 'my-gcp-project',
  location: 'us-central1'
}
```

#### Anthropic Setup
```typescript
{
  type: 'anthropic',
  model: 'claude-3-5-sonnet-20241022',
  temperature: 0.7,
  maxTokens: 4000,
  version: '2023-06-01'
}
```

### Cost Guard Configuration

```typescript
{
  enabled: true,
  maxCostPerLoop: 3.0,        // $3 per execution
  maxCostPerHour: 10.0,       // $10 per hour
  maxCostPerDay: 50.0,        // $50 per day
  alertThresholds: {
    warning: 0.8,             // 80% of limit
    critical: 0.95            // 95% of limit
  }
}
```

### Token Monitor Configuration

```typescript
{
  enabled: true,
  maxTokensPerHour: 50000,
  maxTokensPerDay: 200000,
  trackEfficiency: true,
  alertThresholds: {
    warning: 0.8,
    critical: 0.95
  }
}
```

## Advanced Features

### Failover Configuration

Set up automatic failover to backup providers:

```typescript
{
  enabled: true,
  maxRetries: 3,
  healthCheckInterval: 30000,  // 30 seconds
  fallbackProviders: [
    {
      type: 'anthropic',
      model: 'claude-3-5-sonnet-20241022',
      // ... other config
    },
    {
      type: 'vertex-ai',
      model: 'gemini-2.0-flash-exp',
      // ... other config
    }
  ]
}
```

### Retry Configuration

Configure exponential backoff with jitter:

```typescript
{
  maxRetries: 5,
  baseDelayMs: 1000,
  maxDelayMs: 30000,
  exponentialBase: 2,
  jitter: true,
  timeoutMs: 60000
}
```

### Performance Configuration

Optimize for speed and efficiency:

```typescript
{
  streamingEnabled: true,
  parallelExecution: true,
  maxConcurrency: 3,
  cacheEnabled: true,
  compressionEnabled: true
}
```

## API Integration

### Loading Settings

```typescript
import { SettingsAPI } from '@/lib/api/settings';

const loadUserSettings = async () => {
  const result = await SettingsAPI.loadSettings();
  if (result.success) {
    return result.data;
  } else {
    throw new Error(result.error);
  }
};
```

### Saving Settings

```typescript
const saveUserSettings = async (settings: EnhancedUserSettings) => {
  const result = await SettingsAPI.saveSettings(settings);
  if (!result.success) {
    throw new Error(result.error);
  }
  return result.data;
};
```

### Testing Connections

```typescript
const testProvider = async (provider: string, config: ProviderConfig) => {
  const result = await SettingsAPI.testProviderConnection(provider, config);
  return result.success && result.data?.isValid;
};
```

## Migration Guide

### From Legacy Settings

The system automatically migrates legacy settings to the enhanced format:

1. **Automatic Detection** - Legacy settings are detected on load
2. **Safe Migration** - Original data is preserved during migration
3. **Validation** - Migrated settings are validated for correctness
4. **Rollback Support** - Emergency rollback available if needed

### Manual Migration

```typescript
import { SettingsMigration } from '@/lib/migration/settingsMigration';

// Check if migration is needed
const status = await SettingsMigration.checkMigrationStatus();

if (status.needsMigration) {
  // Perform migration
  const result = await SettingsMigration.migrateUserSettings();
  
  if (result.success) {
    console.log(`Migrated ${result.migratedCount} settings`);
  }
}
```

## Import/Export

### Exporting Settings

```typescript
// Export with API keys masked (recommended)
const exportData = await SettingsAPI.exportSettings();

// Save to file
const blob = new Blob([exportData.data], { type: 'application/json' });
const url = URL.createObjectURL(blob);
// ... download logic
```

### Importing Settings

```typescript
// Validate import data
const importData = JSON.parse(fileContent);
validateEnhancedUserSettings(importData.settings);

// Import settings
await SettingsAPI.saveSettings(importData.settings);
```

## Security Considerations

### API Key Management
- **Encryption at Rest** - All API keys are encrypted before storage
- **Secure Transmission** - Keys are never transmitted in plain text
- **Masked Exports** - API keys are masked in configuration exports
- **Validation** - Connection testing validates key authenticity

### Access Control
- **User Isolation** - Settings are scoped to individual users
- **Row Level Security** - Database policies enforce access control
- **Audit Logging** - All changes are logged for security review

## Troubleshooting

### Common Issues

#### Provider Connection Failures
1. Verify API keys are correct and active
2. Check provider-specific settings (org ID, project ID)
3. Ensure network connectivity to provider endpoints
4. Review rate limits and quotas

#### Cost Guard Triggers
1. Review current usage in the monitoring dashboard
2. Adjust cost limits if legitimate usage
3. Check for runaway processes or loops
4. Optimize model selection for cost efficiency

#### Migration Issues
1. Backup current settings before migration
2. Check validation errors in migration logs
3. Use rollback if migration fails
4. Contact support for complex migration scenarios

### Debug Mode

Enable debug logging for troubleshooting:

```typescript
// In development environment
localStorage.setItem('debug-enhanced-settings', 'true');
```

## Best Practices

### Configuration
- **Start Conservative** - Begin with lower cost limits and increase as needed
- **Test Thoroughly** - Always test provider connections after configuration
- **Monitor Regularly** - Check the dashboard for usage patterns and issues
- **Backup Settings** - Export configurations before major changes

### Security
- **Rotate Keys** - Regularly rotate API keys for security
- **Limit Access** - Only share configurations with trusted team members
- **Mask Sensitive Data** - Always export with API keys masked
- **Review Permissions** - Regularly audit provider permissions and quotas

### Performance
- **Use Streaming** - Enable streaming for faster response times
- **Optimize Models** - Choose appropriate models for your use case
- **Monitor Costs** - Track efficiency metrics and optimize accordingly
- **Cache Results** - Enable caching for repeated operations

## Support

For additional help:
- Check the troubleshooting section above
- Review the integration tests for usage examples
- Consult the API documentation for detailed method signatures
- Contact support for complex configuration scenarios
