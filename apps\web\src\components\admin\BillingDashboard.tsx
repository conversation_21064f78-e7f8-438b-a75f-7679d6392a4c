import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  CreditCard, 
  DollarSign, 
  TrendingUp, 
  Calendar, 
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Download,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface BillingData {
  currentPlan: {
    name: string;
    price: number;
    currency: string;
    interval: string;
    features: string[];
  };
  usage: {
    current: number;
    limit: number;
    percentage: number;
  };
  billing: {
    nextBillingDate: string;
    lastPayment: {
      amount: number;
      date: string;
      status: string;
    };
  };
  costs: {
    thisMonth: number;
    lastMonth: number;
    breakdown: Array<{
      service: string;
      cost: number;
      usage: number;
    }>;
  };
}

interface BillingDashboardProps {
  className?: string;
}

export const BillingDashboard: React.FC<BillingDashboardProps> = ({ className }) => {
  const [billingData, setBillingData] = useState<BillingData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  const fetchBillingData = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Fetch subscription info
      const subscriptionResponse = await fetch('/api/billing/subscription');
      const usageResponse = await fetch('/api/billing/usage');
      const costsResponse = await fetch('/api/billing/costs?period=current');
      
      const [subscription, usage, costs] = await Promise.all([
        subscriptionResponse.ok ? subscriptionResponse.json() : null,
        usageResponse.ok ? usageResponse.json() : null,
        costsResponse.ok ? costsResponse.json() : null
      ]);

      setBillingData({
        currentPlan: subscription?.data || {
          name: 'Free Tier',
          price: 0,
          currency: 'USD',
          interval: 'month',
          features: ['Basic transformations', 'Community support']
        },
        usage: usage?.data || {
          current: 0,
          limit: 1000,
          percentage: 0
        },
        billing: {
          nextBillingDate: subscription?.data?.nextBillingDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          lastPayment: subscription?.data?.lastPayment || {
            amount: 0,
            date: new Date().toISOString(),
            status: 'succeeded'
          }
        },
        costs: costs?.data || {
          thisMonth: 0,
          lastMonth: 0,
          breakdown: []
        }
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load billing data');
    } finally {
      setIsLoading(false);
      setLastUpdated(new Date());
    }
  };

  useEffect(() => {
    fetchBillingData();
  }, []);

  const handleDownloadInvoice = async () => {
    try {
      const response = await fetch('/api/billing/invoice/latest');
      if (response.ok) {
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `invoice-${new Date().toISOString().split('T')[0]}.pdf`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    } catch (err) {
      console.error('Failed to download invoice:', err);
    }
  };

  if (isLoading) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CreditCard className="w-5 h-5" />
            <span>Billing Dashboard</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="w-6 h-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CreditCard className="w-5 h-5" />
            <span>Billing Dashboard</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertTriangle className="w-4 h-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <CreditCard className="w-5 h-5 text-primary" />
            <CardTitle>Billing Dashboard</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={fetchBillingData}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={cn("w-4 h-4", isLoading && "animate-spin")} />
            </Button>
          </div>
        </div>
        <CardDescription>
          Manage your subscription, usage, and billing information
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="usage">Usage</TabsTrigger>
            <TabsTrigger value="costs">Costs</TabsTrigger>
            <TabsTrigger value="billing">Billing</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-4">
            {/* Current Plan */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Current Plan</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{billingData?.currentPlan.name}</span>
                      <Badge variant="secondary">Active</Badge>
                    </div>
                    <div className="text-2xl font-bold">
                      ${billingData?.currentPlan.price}
                      <span className="text-sm font-normal text-muted-foreground">
                        /{billingData?.currentPlan.interval}
                      </span>
                    </div>
                    <div className="space-y-1">
                      {billingData?.currentPlan.features.map((feature, index) => (
                        <div key={index} className="flex items-center space-x-2 text-sm">
                          <CheckCircle className="w-3 h-3 text-green-500" />
                          <span>{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Usage This Month</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span>API Requests</span>
                      <span>{billingData?.usage.current} / {billingData?.usage.limit}</span>
                    </div>
                    <Progress value={billingData?.usage.percentage || 0} className="h-2" />
                    <div className="text-xs text-muted-foreground">
                      {billingData?.usage.percentage}% of monthly limit used
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="usage" className="space-y-4">
            <Alert>
              <TrendingUp className="w-4 h-4" />
              <AlertDescription>
                Usage tracking helps you monitor your API consumption and avoid overage charges.
              </AlertDescription>
            </Alert>
            {/* Usage details would go here */}
          </TabsContent>

          <TabsContent value="costs" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">This Month</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">${billingData?.costs.thisMonth.toFixed(2)}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Last Month</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">${billingData?.costs.lastMonth.toFixed(2)}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Change</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">
                    {billingData?.costs.thisMonth && billingData?.costs.lastMonth
                      ? `${((billingData.costs.thisMonth - billingData.costs.lastMonth) / billingData.costs.lastMonth * 100).toFixed(1)}%`
                      : '0%'
                    }
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="billing" className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Billing Information</h3>
              <div className="space-x-2">
                <Button variant="outline" size="sm" onClick={handleDownloadInvoice}>
                  <Download className="w-4 h-4 mr-2" />
                  Download Invoice
                </Button>
                <Button variant="outline" size="sm">
                  <Settings className="w-4 h-4 mr-2" />
                  Manage Billing
                </Button>
              </div>
            </div>
            {/* Billing details would go here */}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default BillingDashboard;
