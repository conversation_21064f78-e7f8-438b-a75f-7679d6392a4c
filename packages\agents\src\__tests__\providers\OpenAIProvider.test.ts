import { jest } from '@jest/globals';
import { OpenAIProvider } from '../../providers/OpenAIProvider.js';
import { ProviderConfig, PlanRequest, CritiqueRequest } from '../../types.js';

// Mock OpenAI SDK
jest.mock('openai', () => {
  return {
    default: jest.fn().mockImplementation(() => ({
      chat: {
        completions: {
          create: jest.fn(),
        },
      },
      models: {
        list: jest.fn().mockResolvedValue({ data: [] }),
      },
    })),
    APIError: class APIError extends Error {
      constructor(message: string, public status?: number, public code?: string, public headers?: Record<string, string>) {
        super(message);
        this.name = 'APIError';
      }
    },
  };
});

describe('OpenAIProvider', () => {
  let provider: OpenAIProvider;
  let mockOpenAI: any;
  let config: ProviderConfig;

  beforeEach(() => {
    config = {
      type: 'openai',
      model: 'gpt-4o',
      temperature: 0.7,
      maxTokens: 2000,
      apiKey: 'test-api-key',
    };

    // Reset mocks
    jest.clearAllMocks();
    
    // Get the mocked OpenAI constructor
    const OpenAI = require('openai').default;
    mockOpenAI = {
      chat: {
        completions: {
          create: jest.fn(),
        },
      },
      models: {
        list: jest.fn().mockResolvedValue({ data: [] }),
      },
    };
    
    OpenAI.mockReturnValue(mockOpenAI);
    
    provider = new OpenAIProvider(config);
  });

  describe('constructor', () => {
    it('should create provider with valid config', () => {
      expect(provider).toBeInstanceOf(OpenAIProvider);
      expect(provider.getType()).toBe('openai');
      expect(provider.getModel()).toBe('gpt-4o');
    });

    it('should throw error without API key', () => {
      const invalidConfig = { ...config };
      delete invalidConfig.apiKey;
      delete process.env.OPENAI_API_KEY;

      expect(() => new OpenAIProvider(invalidConfig)).toThrow('OpenAI API key is required');
    });

    it('should use environment variable for API key', () => {
      process.env.OPENAI_API_KEY = 'env-api-key';
      const configWithoutKey = { ...config };
      delete configWithoutKey.apiKey;

      expect(() => new OpenAIProvider(configWithoutKey)).not.toThrow();
      delete process.env.OPENAI_API_KEY;
    });
  });

  describe('validateConfig', () => {
    it('should validate config successfully', async () => {
      mockOpenAI.models.list.mockResolvedValue({ data: [] });
      
      const isValid = await provider.validateConfig();
      expect(isValid).toBe(true);
      expect(mockOpenAI.models.list).toHaveBeenCalled();
    });

    it('should return false on API error', async () => {
      mockOpenAI.models.list.mockRejectedValue(new Error('API Error'));
      
      const isValid = await provider.validateConfig();
      expect(isValid).toBe(false);
    });
  });

  describe('generatePatch', () => {
    const mockRequest: PlanRequest = {
      prompt: 'Add a new user registration feature',
      context: { framework: 'React' },
    };

    const mockResponse = {
      id: 'test-response-id',
      choices: [{
        message: {
          content: JSON.stringify({
            operations: [
              { op: 'add', path: '/components/UserRegistration', value: { component: 'UserRegistration' } }
            ],
            description: 'Added user registration component',
            confidence: 0.9,
          }),
        },
      }],
      usage: {
        prompt_tokens: 100,
        completion_tokens: 200,
        total_tokens: 300,
      },
    };

    it('should generate patch successfully', async () => {
      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse);

      const result = await provider.generatePatch(mockRequest);

      expect(result.data).toEqual({
        operations: [
          { op: 'add', path: '/components/UserRegistration', value: { component: 'UserRegistration' } }
        ],
        description: 'Added user registration component',
        confidence: 0.9,
      });
      expect(result.usage.totalTokens).toBe(300);
      expect(result.requestId).toBe('test-response-id');
    });

    it('should handle API errors', async () => {
      const OpenAI = require('openai');
      const apiError = new OpenAI.APIError('Rate limit exceeded', 429);
      mockOpenAI.chat.completions.create.mockRejectedValue(apiError);

      await expect(provider.generatePatch(mockRequest)).rejects.toThrow('Rate limit exceeded');
    });

    it('should handle invalid JSON response', async () => {
      const invalidResponse = {
        ...mockResponse,
        choices: [{ message: { content: 'invalid json' } }],
      };
      mockOpenAI.chat.completions.create.mockResolvedValue(invalidResponse);

      await expect(provider.generatePatch(mockRequest)).rejects.toThrow('Failed to parse OpenAI response as JSON');
    });

    it('should handle empty response', async () => {
      const emptyResponse = {
        ...mockResponse,
        choices: [{ message: { content: null } }],
      };
      mockOpenAI.chat.completions.create.mockResolvedValue(emptyResponse);

      await expect(provider.generatePatch(mockRequest)).rejects.toThrow('No content in OpenAI response');
    });
  });

  describe('scorePatch', () => {
    const mockRequest: CritiqueRequest = {
      patch: {
        operations: [{ op: 'add', path: '/test', value: 'test' }],
        description: 'Test patch',
        confidence: 0.8,
      },
      originalPrompt: 'Add test feature',
    };

    const mockResponse = {
      id: 'test-critique-id',
      choices: [{
        message: {
          content: JSON.stringify({
            score: 0.85,
            feedback: 'Good implementation with minor improvements needed',
            suggestions: ['Add error handling', 'Include tests'],
            isAcceptable: false,
          }),
        },
      }],
      usage: {
        prompt_tokens: 150,
        completion_tokens: 100,
        total_tokens: 250,
      },
    };

    it('should score patch successfully', async () => {
      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse);

      const result = await provider.scorePatch(mockRequest);

      expect(result.data).toEqual({
        score: 0.85,
        feedback: 'Good implementation with minor improvements needed',
        suggestions: ['Add error handling', 'Include tests'],
        isAcceptable: false,
      });
      expect(result.usage.totalTokens).toBe(250);
    });
  });

  describe('supportsStreaming', () => {
    it('should return true for streaming support', () => {
      expect(provider.supportsStreaming()).toBe(true);
    });
  });

  describe('cost calculation', () => {
    it('should calculate cost correctly for known model', () => {
      const usage = { inputTokens: 1000, outputTokens: 500 };
      // gpt-4o: input $0.0025, output $0.01 per 1k tokens
      const expectedCost = (1000 * 0.0025 + 500 * 0.01) / 1000;
      
      // Access private method through any cast for testing
      const cost = (provider as any).calculateCost(usage);
      expect(cost).toBeCloseTo(expectedCost, 6);
    });

    it('should use default rates for unknown model', () => {
      const unknownConfig = { ...config, model: 'unknown-model' };
      const unknownProvider = new OpenAIProvider(unknownConfig);
      
      const usage = { inputTokens: 1000, outputTokens: 500 };
      const cost = (unknownProvider as any).calculateCost(usage);
      
      // Should use default rates
      expect(cost).toBeGreaterThan(0);
    });
  });

  describe('token usage tracking', () => {
    it('should track token usage correctly', () => {
      const initialUsage = provider.getTokenUsage();
      expect(initialUsage.totalTokens).toBe(0);
      expect(initialUsage.cost).toBe(0);
    });

    it('should reset metrics', () => {
      provider.resetMetrics();
      const usage = provider.getTokenUsage();
      expect(usage.totalTokens).toBe(0);
      expect(usage.cost).toBe(0);
    });
  });

  describe('configuration updates', () => {
    it('should update configuration', () => {
      provider.updateConfig({ temperature: 0.5 });
      // Configuration should be updated (private property, so we test behavior)
      expect(provider.getModel()).toBe('gpt-4o'); // Should remain the same
    });
  });
});
