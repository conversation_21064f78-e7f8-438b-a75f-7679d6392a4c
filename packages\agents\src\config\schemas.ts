import { z } from 'zod';

/**
 * Zod schemas for provider configuration validation
 */

// Base provider schema
export const ProviderTypeSchema = z.enum(['openai', 'vertex-ai', 'anthropic']);

// OpenAI specific configuration
export const OpenAIConfigSchema = z.object({
  baseURL: z.string().url().optional(),
  organization: z.string().optional(),
  project: z.string().optional(),
}).optional();

// Vertex AI specific configuration
export const VertexAIConfigSchema = z.object({
  projectId: z.string().min(1, 'Project ID is required'),
  location: z.string().min(1, 'Location is required'),
  credentials: z.any().optional(), // JSON credentials object
}).optional();

// Anthropic specific configuration
export const AnthropicConfigSchema = z.object({
  baseURL: z.string().url().optional(),
  version: z.string().optional(),
}).optional();

// Main provider configuration schema
export const ProviderConfigSchema = z.object({
  type: ProviderTypeSchema,
  apiKey: z.string().optional(),
  model: z.string().min(1, 'Model is required'),
  temperature: z.number().min(0).max(2).default(0.7),
  maxTokens: z.number().positive().default(2000),
  systemPrompt: z.string().optional(),
  tools: z.array(z.any()).optional(),
  
  // Provider-specific configurations
  openai: OpenAIConfigSchema,
  vertexAI: VertexAIConfigSchema,
  anthropic: AnthropicConfigSchema,
}).refine((data) => {
  // Validate provider-specific requirements
  switch (data.type) {
    case 'openai':
      return data.apiKey || process.env.OPENAI_API_KEY;
    case 'vertex-ai':
      return data.vertexAI?.projectId && data.vertexAI?.location;
    case 'anthropic':
      return data.apiKey || process.env.ANTHROPIC_API_KEY;
    default:
      return false;
  }
}, {
  message: "Provider-specific configuration is invalid or missing required fields",
});

// Token usage schema
export const TokenUsageSchema = z.object({
  inputTokens: z.number().nonnegative(),
  outputTokens: z.number().nonnegative(),
  totalTokens: z.number().nonnegative(),
  cost: z.number().nonnegative(),
  provider: ProviderTypeSchema,
  model: z.string(),
  timestamp: z.date(),
});

// Retry configuration schema
export const RetryConfigSchema = z.object({
  maxAttempts: z.number().min(1).max(10).default(5),
  baseDelay: z.number().positive().default(1000),
  maxDelay: z.number().positive().default(30000),
  jitter: z.boolean().default(true),
});

// Cost guard schema
export const CostGuardSchema = z.object({
  maxCostPerLoop: z.number().positive().default(3.0),
  currentCost: z.number().nonnegative().default(0),
  enabled: z.boolean().default(true),
});

// Agent configuration schema (updated)
export const AgentConfigSchema = z.object({
  provider: ProviderConfigSchema.optional(),
  // Legacy fields for backward compatibility
  model: z.enum(['gemini-2.5', 'gpt-4-turbo']).optional(),
  temperature: z.number().min(0).max(2).optional(),
  maxTokens: z.number().positive().optional(),
}).refine((data) => {
  // Either new provider config or legacy config must be present
  return data.provider || (data.model && data.temperature !== undefined && data.maxTokens !== undefined);
}, {
  message: "Either provider configuration or legacy configuration (model, temperature, maxTokens) must be provided",
});

// Dual agent configuration schema
export const DualAgentConfigSchema = z.object({
  planner: z.object({
    provider: ProviderConfigSchema,
    systemPrompt: z.string().optional(),
  }),
  critic: z.object({
    provider: ProviderConfigSchema,
    systemPrompt: z.string().optional(),
  }),
  retry: RetryConfigSchema.optional(),
  costGuard: CostGuardSchema.optional(),
  parallelExecution: z.boolean().default(false),
  streaming: z.boolean().default(false),
});

// Model definitions for each provider
export const OpenAIModels = z.enum([
  'gpt-4o',
  'gpt-4o-mini',
  'gpt-4-turbo',
  'gpt-4',
  'gpt-3.5-turbo',
  'computer-use-preview', // For computer use tool
]);

export const VertexAIModels = z.enum([
  'gemini-2.5-flash',
  'gemini-2.5-pro',
  'gemini-2.0-flash',
  'gemini-1.5-pro',
  'gemini-1.5-flash',
]);

export const AnthropicModels = z.enum([
  'claude-opus-4-20250514',
  'claude-sonnet-4-20250514',
  'claude-3-7-sonnet-20241022',
  'claude-3-5-sonnet-20241022',
  'claude-3-5-haiku-20241022',
]);

// Provider-specific model validation
export const ProviderModelSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('openai'),
    model: OpenAIModels,
  }),
  z.object({
    type: z.literal('vertex-ai'),
    model: VertexAIModels,
  }),
  z.object({
    type: z.literal('anthropic'),
    model: AnthropicModels,
  }),
]);

// Environment configuration schema
export const EnvironmentConfigSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  OPENAI_API_KEY: z.string().optional(),
  ANTHROPIC_API_KEY: z.string().optional(),
  GOOGLE_APPLICATION_CREDENTIALS: z.string().optional(),
  VERTEX_AI_PROJECT_ID: z.string().optional(),
  VERTEX_AI_LOCATION: z.string().optional(),
  MAX_COST_PER_LOOP: z.string().transform(Number).pipe(z.number().positive()).optional(),
  ENABLE_STREAMING: z.string().transform(Boolean).optional(),
  ENABLE_PARALLEL_EXECUTION: z.string().transform(Boolean).optional(),
});

// Type exports
export type ProviderType = z.infer<typeof ProviderTypeSchema>;
export type ProviderConfig = z.infer<typeof ProviderConfigSchema>;
export type TokenUsage = z.infer<typeof TokenUsageSchema>;
export type RetryConfig = z.infer<typeof RetryConfigSchema>;
export type CostGuard = z.infer<typeof CostGuardSchema>;
export type AgentConfig = z.infer<typeof AgentConfigSchema>;
export type DualAgentConfig = z.infer<typeof DualAgentConfigSchema>;
export type OpenAIModel = z.infer<typeof OpenAIModels>;
export type VertexAIModel = z.infer<typeof VertexAIModels>;
export type AnthropicModel = z.infer<typeof AnthropicModels>;
export type EnvironmentConfig = z.infer<typeof EnvironmentConfigSchema>;

/**
 * Validation helper functions
 */
export function validateProviderConfig(config: unknown): ProviderConfig {
  return ProviderConfigSchema.parse(config);
}

export function validateDualAgentConfig(config: unknown): DualAgentConfig {
  return DualAgentConfigSchema.parse(config);
}

export function validateEnvironmentConfig(env: Record<string, string | undefined> = process.env): EnvironmentConfig {
  return EnvironmentConfigSchema.parse(env);
}
