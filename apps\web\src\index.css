
@tailwind base;
@tailwind components;
@tailwind utilities;

/*
 * Metamorphic Reactor Design System
 * Consistent indigo-500/slate-800 theme with comprehensive color palette
 * WCAG AA compliant contrast ratios
 */

@layer base {
  :root {
    /* Light theme - Base colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    /* Card components */
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    /* Popover components */
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Primary brand colors - Indigo theme */
    --primary: 238.7 83.5% 66.7%; /* indigo-500 */
    --primary-foreground: 0 0% 100%;

    /* Secondary colors - Slate theme */
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    /* Muted colors */
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    /* Accent colors */
    --accent: 238.7 83.5% 66.7%; /* indigo-500 */
    --accent-foreground: 0 0% 100%;

    /* Destructive colors */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    /* Border and input colors */
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 238.7 83.5% 66.7%; /* indigo-500 */

    /* Border radius */
    --radius: 0.5rem;

    /* Sidebar colors */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 238.7 83.5% 66.7%; /* indigo-500 */
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 238.7 83.5% 66.7%; /* indigo-500 */

    /* Status colors */
    --success: 142.1 76.2% 36.3%; /* green-600 */
    --success-foreground: 0 0% 100%;
    --warning: 32.1 94.6% 43.7%; /* amber-500 */
    --warning-foreground: 0 0% 100%;
    --info: 221.2 83.2% 53.3%; /* blue-500 */
    --info-foreground: 0 0% 100%;

    /* Transformation status colors */
    --status-queued: 32.1 94.6% 43.7%; /* amber-500 */
    --status-queued-foreground: 0 0% 100%;
    --status-running: 238.7 83.5% 66.7%; /* indigo-500 */
    --status-running-foreground: 0 0% 100%;
    --status-completed: 142.1 76.2% 36.3%; /* green-600 */
    --status-completed-foreground: 0 0% 100%;
    --status-failed: 0 84.2% 60.2%; /* red-500 */
    --status-failed-foreground: 0 0% 100%;

    /* Agent colors */
    --agent-planner: 238.7 83.5% 66.7%; /* indigo-500 */
    --agent-planner-foreground: 0 0% 100%;
    --agent-critic: 262.1 83.3% 57.8%; /* purple-500 */
    --agent-critic-foreground: 0 0% 100%;
    --agent-system: 220.9 39.3% 11%; /* slate-800 */
    --agent-system-foreground: 0 0% 100%;
  }

  .dark {
    /* Dark theme - Base colors with slate-800 foundation */
    --background: 220.9 39.3% 11%; /* slate-800 */
    --foreground: 210 40% 98%;

    /* Card components */
    --card: 220.9 39.3% 11%; /* slate-800 */
    --card-foreground: 210 40% 98%;

    /* Popover components */
    --popover: 220.9 39.3% 11%; /* slate-800 */
    --popover-foreground: 210 40% 98%;

    /* Primary brand colors - Indigo theme */
    --primary: 238.7 83.5% 66.7%; /* indigo-500 */
    --primary-foreground: 0 0% 100%;

    /* Secondary colors - Darker slate */
    --secondary: 215 27.9% 16.9%; /* slate-700 */
    --secondary-foreground: 210 40% 98%;

    /* Muted colors */
    --muted: 215 27.9% 16.9%; /* slate-700 */
    --muted-foreground: 215.4 16.3% 46.9%;

    /* Accent colors */
    --accent: 238.7 83.5% 66.7%; /* indigo-500 */
    --accent-foreground: 0 0% 100%;

    /* Destructive colors */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    /* Border and input colors */
    --border: 215 27.9% 16.9%; /* slate-700 */
    --input: 215 27.9% 16.9%; /* slate-700 */
    --ring: 238.7 83.5% 66.7%; /* indigo-500 */

    /* Sidebar colors */
    --sidebar-background: 222.2 84% 4.9%; /* slate-900 */
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 238.7 83.5% 66.7%; /* indigo-500 */
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 215 27.9% 16.9%; /* slate-700 */
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 215 27.9% 16.9%; /* slate-700 */
    --sidebar-ring: 238.7 83.5% 66.7%; /* indigo-500 */

    /* Status colors - Adjusted for dark theme */
    --success: 142.1 70.6% 45.3%; /* green-500 */
    --success-foreground: 0 0% 100%;
    --warning: 32.1 94.6% 43.7%; /* amber-500 */
    --warning-foreground: 0 0% 100%;
    --info: 221.2 83.2% 53.3%; /* blue-500 */
    --info-foreground: 0 0% 100%;

    /* Transformation status colors */
    --status-queued: 32.1 94.6% 43.7%; /* amber-500 */
    --status-queued-foreground: 0 0% 100%;
    --status-running: 238.7 83.5% 66.7%; /* indigo-500 */
    --status-running-foreground: 0 0% 100%;
    --status-completed: 142.1 70.6% 45.3%; /* green-500 */
    --status-completed-foreground: 0 0% 100%;
    --status-failed: 0 84.2% 60.2%; /* red-500 */
    --status-failed-foreground: 0 0% 100%;

    /* Agent colors */
    --agent-planner: 238.7 83.5% 66.7%; /* indigo-500 */
    --agent-planner-foreground: 0 0% 100%;
    --agent-critic: 262.1 83.3% 57.8%; /* purple-500 */
    --agent-critic-foreground: 0 0% 100%;
    --agent-system: 215.4 16.3% 46.9%; /* slate-500 */
    --agent-system-foreground: 0 0% 100%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    font-variation-settings: normal;
    line-height: 1.6;
    letter-spacing: -0.011em;
  }

  code, pre, .font-mono {
    font-family: 'JetBrains Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
    font-feature-settings: 'liga' 1, 'calt' 1;
  }

  /* Typography Scale - Consistent hierarchy */
  h1, .text-h1 {
    @apply text-4xl font-bold tracking-tight;
    line-height: 1.1;
  }

  h2, .text-h2 {
    @apply text-3xl font-semibold tracking-tight;
    line-height: 1.2;
  }

  h3, .text-h3 {
    @apply text-2xl font-semibold tracking-tight;
    line-height: 1.25;
  }

  h4, .text-h4 {
    @apply text-xl font-semibold tracking-tight;
    line-height: 1.3;
  }

  h5, .text-h5 {
    @apply text-lg font-medium;
    line-height: 1.4;
  }

  h6, .text-h6 {
    @apply text-base font-medium;
    line-height: 1.5;
  }

  /* Body text variants */
  .text-body-lg {
    @apply text-lg;
    line-height: 1.6;
  }

  .text-body {
    @apply text-base;
    line-height: 1.6;
  }

  .text-body-sm {
    @apply text-sm;
    line-height: 1.5;
  }

  .text-caption {
    @apply text-xs;
    line-height: 1.4;
  }

  /* Interactive text */
  .text-link {
    @apply text-primary hover:text-primary/80 transition-colors;
  }

  /* Code text */
  .text-code {
    @apply font-mono text-sm bg-muted px-1.5 py-0.5 rounded;
  }
}

/* Enhanced scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

::-webkit-scrollbar-corner {
  background: hsl(var(--muted));
}

/* Firefox scrollbar styling */
* {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground) / 0.3) hsl(var(--muted));
}

/* Enhanced transitions */
.transition-all {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-colors {
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}

.transition-transform {
  transition: transform 0.15s ease-in-out;
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background;
}

/* Gradient animations */
@keyframes gradient-x {
  0%, 100% {
    background-size: 200% 200%;
    background-position: left center;
  }
  50% {
    background-size: 200% 200%;
    background-position: right center;
  }
}

@keyframes gradient-y {
  0%, 100% {
    background-size: 200% 200%;
    background-position: center top;
  }
  50% {
    background-size: 200% 200%;
    background-position: center bottom;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 hsl(var(--primary) / 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px hsl(var(--primary) / 0);
  }
}

.animate-gradient-x {
  animation: gradient-x 3s ease infinite;
}

.animate-gradient-y {
  animation: gradient-y 3s ease infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Utility classes for consistent spacing */
.space-y-section > * + * {
  margin-top: 2rem;
}

.space-y-component > * + * {
  margin-top: 1rem;
}

.space-y-element > * + * {
  margin-top: 0.5rem;
}

/* Enhanced glass morphism effects with better dark mode support */
.glass {
  background: hsl(var(--background) / 0.8);
  backdrop-filter: blur(12px) saturate(180%);
  border: 1px solid hsl(var(--border) / 0.5);
  box-shadow:
    0 4px 6px -1px hsl(var(--foreground) / 0.1),
    0 2px 4px -1px hsl(var(--foreground) / 0.06);
}

.glass-strong {
  background: hsl(var(--background) / 0.95);
  backdrop-filter: blur(16px) saturate(180%);
  border: 1px solid hsl(var(--border) / 0.8);
  box-shadow:
    0 10px 15px -3px hsl(var(--foreground) / 0.1),
    0 4px 6px -2px hsl(var(--foreground) / 0.05);
}

/* Dark mode specific enhancements */
.dark .glass {
  background: hsl(var(--background) / 0.7);
  border: 1px solid hsl(var(--border) / 0.3);
  box-shadow:
    0 4px 6px -1px hsl(0 0% 0% / 0.3),
    0 2px 4px -1px hsl(0 0% 0% / 0.2),
    inset 0 1px 0 hsl(var(--foreground) / 0.1);
}

.dark .glass-strong {
  background: hsl(var(--background) / 0.9);
  border: 1px solid hsl(var(--border) / 0.5);
  box-shadow:
    0 10px 15px -3px hsl(0 0% 0% / 0.4),
    0 4px 6px -2px hsl(0 0% 0% / 0.3),
    inset 0 1px 0 hsl(var(--foreground) / 0.1);
}

/* Enhanced depth and layering */
.depth-1 {
  box-shadow:
    0 1px 3px 0 hsl(var(--foreground) / 0.1),
    0 1px 2px 0 hsl(var(--foreground) / 0.06);
}

.depth-2 {
  box-shadow:
    0 4px 6px -1px hsl(var(--foreground) / 0.1),
    0 2px 4px -1px hsl(var(--foreground) / 0.06);
}

.depth-3 {
  box-shadow:
    0 10px 15px -3px hsl(var(--foreground) / 0.1),
    0 4px 6px -2px hsl(var(--foreground) / 0.05);
}

.depth-4 {
  box-shadow:
    0 20px 25px -5px hsl(var(--foreground) / 0.1),
    0 10px 10px -5px hsl(var(--foreground) / 0.04);
}

/* Dark mode depth adjustments */
.dark .depth-1 {
  box-shadow:
    0 1px 3px 0 hsl(0 0% 0% / 0.3),
    0 1px 2px 0 hsl(0 0% 0% / 0.2);
}

.dark .depth-2 {
  box-shadow:
    0 4px 6px -1px hsl(0 0% 0% / 0.3),
    0 2px 4px -1px hsl(0 0% 0% / 0.2);
}

.dark .depth-3 {
  box-shadow:
    0 10px 15px -3px hsl(0 0% 0% / 0.4),
    0 4px 6px -2px hsl(0 0% 0% / 0.3);
}

.dark .depth-4 {
  box-shadow:
    0 20px 25px -5px hsl(0 0% 0% / 0.5),
    0 10px 10px -5px hsl(0 0% 0% / 0.4);
}

/* Loading skeleton */
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.skeleton {
  background: linear-gradient(90deg, hsl(var(--muted)) 25%, hsl(var(--muted-foreground) / 0.1) 50%, hsl(var(--muted)) 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
