import { chromium, FullConfig } from '@playwright/test';

/**
 * Global setup for Playwright tests
 * Prepares test environment and validates server availability
 */

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup for dashboard tests...');
  
  const { baseURL } = config.projects[0].use;
  
  if (!baseURL) {
    throw new Error('baseURL is not configured');
  }

  // Launch browser to check if server is ready
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {
    console.log(`📡 Checking server availability at ${baseURL}...`);
    
    // Wait for server to be ready
    let retries = 30; // 30 seconds max wait
    while (retries > 0) {
      try {
        await page.goto(baseURL, { timeout: 5000 });
        console.log('✅ Server is ready!');
        break;
      } catch (error) {
        retries--;
        if (retries === 0) {
          throw new Error(`Server at ${baseURL} is not responding after 30 seconds`);
        }
        console.log(`⏳ Server not ready, retrying... (${retries} attempts left)`);
        await page.waitForTimeout(1000);
      }
    }

    // Verify dashboard page loads
    console.log('🔍 Verifying dashboard page loads...');
    await page.goto(`${baseURL}/dashboard`);
    await page.waitForSelector('main', { timeout: 10000 });
    console.log('✅ Dashboard page verified!');

    // Verify settings page loads
    console.log('🔍 Verifying settings page loads...');
    await page.goto(`${baseURL}/settings`);
    await page.waitForSelector('h1', { timeout: 10000 });
    console.log('✅ Settings page verified!');

    // Create test artifacts directory
    const fs = require('fs');
    const path = require('path');
    const artifactsDir = path.join(process.cwd(), 'artifacts');
    
    if (!fs.existsSync(artifactsDir)) {
      fs.mkdirSync(artifactsDir, { recursive: true });
      console.log('📁 Created artifacts directory');
    }

    console.log('🎉 Global setup completed successfully!');
    
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

export default globalSetup;
