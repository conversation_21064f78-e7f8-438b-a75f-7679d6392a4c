import { jest } from '@jest/globals';

// Global test setup
beforeAll(() => {
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  
  // Mock console methods to reduce noise in tests
  jest.spyOn(console, 'log').mockImplementation(() => {});
  jest.spyOn(console, 'warn').mockImplementation(() => {});
  jest.spyOn(console, 'info').mockImplementation(() => {});
  
  // Keep console.error for debugging
  const originalError = console.error;
  jest.spyOn(console, 'error').mockImplementation((...args) => {
    // Only show errors in verbose mode
    if (process.env.JEST_VERBOSE === 'true') {
      originalError(...args);
    }
  });
});

afterAll(() => {
  // Restore console methods
  jest.restoreAllMocks();
});

beforeEach(() => {
  // Clear all mocks before each test
  jest.clearAllMocks();
  
  // Reset environment variables
  delete process.env.OPENAI_API_KEY;
  delete process.env.ANTHROPIC_API_KEY;
  delete process.env.GOOGLE_APPLICATION_CREDENTIALS;
  delete process.env.VERTEX_AI_PROJECT_ID;
  delete process.env.VERTEX_AI_LOCATION;
});

afterEach(() => {
  // Clean up after each test
  jest.clearAllTimers();
  jest.useRealTimers();
});

// Global test utilities
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeWithinRange(floor: number, ceiling: number): R;
      toBeValidJSON(): R;
      toHaveValidTokenUsage(): R;
    }
  }
}

// Custom Jest matchers
expect.extend({
  toBeWithinRange(received: number, floor: number, ceiling: number) {
    const pass = received >= floor && received <= ceiling;
    if (pass) {
      return {
        message: () => `expected ${received} not to be within range ${floor} - ${ceiling}`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be within range ${floor} - ${ceiling}`,
        pass: false,
      };
    }
  },

  toBeValidJSON(received: string) {
    try {
      JSON.parse(received);
      return {
        message: () => `expected ${received} not to be valid JSON`,
        pass: true,
      };
    } catch (error) {
      return {
        message: () => `expected ${received} to be valid JSON, but got error: ${error}`,
        pass: false,
      };
    }
  },

  toHaveValidTokenUsage(received: any) {
    const requiredFields = ['inputTokens', 'outputTokens', 'totalTokens', 'cost', 'provider', 'model', 'timestamp'];
    const missingFields = requiredFields.filter(field => !(field in received));
    
    if (missingFields.length === 0) {
      const validTypes = 
        typeof received.inputTokens === 'number' &&
        typeof received.outputTokens === 'number' &&
        typeof received.totalTokens === 'number' &&
        typeof received.cost === 'number' &&
        typeof received.provider === 'string' &&
        typeof received.model === 'string' &&
        received.timestamp instanceof Date;

      if (validTypes) {
        return {
          message: () => `expected token usage object to be invalid`,
          pass: true,
        };
      } else {
        return {
          message: () => `expected token usage object to have correct field types`,
          pass: false,
        };
      }
    } else {
      return {
        message: () => `expected token usage object to have fields: ${missingFields.join(', ')}`,
        pass: false,
      };
    }
  },
});

// Mock implementations for external dependencies
export const mockProviderResponse = {
  data: {
    operations: [{ op: 'add', path: '/test', value: 'test' }],
    description: 'Test patch',
    confidence: 0.9,
  },
  usage: {
    inputTokens: 100,
    outputTokens: 200,
    totalTokens: 300,
    cost: 0.01,
    provider: 'openai' as const,
    model: 'gpt-4o',
    timestamp: new Date(),
  },
  requestId: 'test-request-id',
  latency: 1000,
};

export const mockCritiqueResponse = {
  data: {
    score: 0.85,
    feedback: 'Good implementation',
    suggestions: ['Add tests', 'Improve error handling'],
    isAcceptable: false,
  },
  usage: {
    inputTokens: 150,
    outputTokens: 100,
    totalTokens: 250,
    cost: 0.008,
    provider: 'openai' as const,
    model: 'gpt-4o',
    timestamp: new Date(),
  },
  requestId: 'test-critique-id',
  latency: 800,
};

// Test data factories
export const createMockTokenUsage = (overrides: Partial<any> = {}) => ({
  inputTokens: 100,
  outputTokens: 200,
  totalTokens: 300,
  cost: 0.01,
  provider: 'openai' as const,
  model: 'gpt-4o',
  timestamp: new Date(),
  ...overrides,
});

export const createMockProviderConfig = (type: 'openai' | 'vertex-ai' | 'anthropic' = 'openai') => {
  const baseConfig = {
    temperature: 0.7,
    maxTokens: 2000,
  };

  switch (type) {
    case 'openai':
      return {
        ...baseConfig,
        type: 'openai' as const,
        model: 'gpt-4o',
        apiKey: 'mock-openai-key',
      };
    case 'vertex-ai':
      return {
        ...baseConfig,
        type: 'vertex-ai' as const,
        model: 'gemini-2.5-flash',
        vertexAI: {
          projectId: 'mock-project',
          location: 'us-central1',
        },
      };
    case 'anthropic':
      return {
        ...baseConfig,
        type: 'anthropic' as const,
        model: 'claude-opus-4-20250514',
        apiKey: 'mock-anthropic-key',
      };
  }
};

// Async test helpers
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const waitForCondition = async (
  condition: () => boolean | Promise<boolean>,
  timeout: number = 5000,
  interval: number = 100
): Promise<void> => {
  const start = Date.now();
  
  while (Date.now() - start < timeout) {
    if (await condition()) {
      return;
    }
    await waitFor(interval);
  }
  
  throw new Error(`Condition not met within ${timeout}ms`);
};

// Error simulation helpers
export const createNetworkError = (message: string = 'Network error') => {
  const error = new Error(message);
  (error as any).code = 'ECONNRESET';
  return error;
};

export const createTimeoutError = (message: string = 'Request timeout') => {
  const error = new Error(message);
  (error as any).code = 'ETIMEDOUT';
  return error;
};

// Performance testing helpers
export const measurePerformance = async <T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> => {
  const start = performance.now();
  const result = await fn();
  const duration = performance.now() - start;
  return { result, duration };
};

// Memory leak detection helpers
export const checkMemoryLeaks = () => {
  if (global.gc) {
    global.gc();
    const memUsage = process.memoryUsage();
    return {
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      external: memUsage.external,
      rss: memUsage.rss,
    };
  }
  return null;
};
