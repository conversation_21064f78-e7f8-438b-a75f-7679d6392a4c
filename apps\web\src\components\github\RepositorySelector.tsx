import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Search, 
  Star, 
  GitFork, 
  Calendar, 
  Lock, 
  Unlock,
  ExternalLink,
  RefreshCw,
  Filter,
  Check
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface Repository {
  id: number;
  name: string;
  full_name: string;
  description: string;
  private: boolean;
  fork: boolean;
  stargazers_count: number;
  forks_count: number;
  language: string;
  updated_at: string;
  html_url: string;
  clone_url: string;
  default_branch: string;
}

interface RepositorySelectorProps {
  onSelect: (repository: Repository) => void;
  selectedRepository?: Repository;
  className?: string;
}

export const RepositorySelector: React.FC<RepositorySelectorProps> = ({
  onSelect,
  selectedRepository,
  className
}) => {
  const [repositories, setRepositories] = useState<Repository[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState<'all' | 'private' | 'public' | 'forks'>('all');

  useEffect(() => {
    loadRepositories();
  }, []);

  const loadRepositories = async () => {
    setLoading(true);
    try {
      // Mock data for demonstration
      const mockRepos: Repository[] = [
        {
          id: 1,
          name: 'my-awesome-project',
          full_name: 'user/my-awesome-project',
          description: 'An awesome project built with React and TypeScript',
          private: false,
          fork: false,
          stargazers_count: 42,
          forks_count: 8,
          language: 'TypeScript',
          updated_at: '2024-01-15T10:30:00Z',
          html_url: 'https://github.com/user/my-awesome-project',
          clone_url: 'https://github.com/user/my-awesome-project.git',
          default_branch: 'main',
        },
        {
          id: 2,
          name: 'api-server',
          full_name: 'user/api-server',
          description: 'RESTful API server with Node.js and Express',
          private: true,
          fork: false,
          stargazers_count: 15,
          forks_count: 3,
          language: 'JavaScript',
          updated_at: '2024-01-10T14:20:00Z',
          html_url: 'https://github.com/user/api-server',
          clone_url: 'https://github.com/user/api-server.git',
          default_branch: 'main',
        },
        {
          id: 3,
          name: 'react-components',
          full_name: 'user/react-components',
          description: 'Reusable React components library',
          private: false,
          fork: true,
          stargazers_count: 128,
          forks_count: 24,
          language: 'TypeScript',
          updated_at: '2024-01-12T09:15:00Z',
          html_url: 'https://github.com/user/react-components',
          clone_url: 'https://github.com/user/react-components.git',
          default_branch: 'main',
        },
      ];
      
      setRepositories(mockRepos);
    } catch (error) {
      console.error('Failed to load repositories:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredRepositories = repositories.filter(repo => {
    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      if (!repo.name.toLowerCase().includes(searchLower) &&
          !repo.description?.toLowerCase().includes(searchLower)) {
        return false;
      }
    }

    // Apply type filter
    switch (filter) {
      case 'private':
        return repo.private;
      case 'public':
        return !repo.private;
      case 'forks':
        return repo.fork;
      default:
        return true;
    }
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getLanguageColor = (language: string) => {
    const colors: Record<string, string> = {
      'TypeScript': 'bg-blue-500',
      'JavaScript': 'bg-yellow-500',
      'Python': 'bg-green-500',
      'Java': 'bg-red-500',
      'Go': 'bg-cyan-500',
      'Rust': 'bg-orange-500',
    };
    return colors[language] || 'bg-gray-500';
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Select Repository</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={loadRepositories}
            disabled={loading}
          >
            <RefreshCw className={cn("w-4 h-4", loading && "animate-spin")} />
          </Button>
        </CardTitle>
        <CardDescription>
          Choose a repository to work with for code transformations
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Search repositories..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
          
          <div className="flex gap-1">
            {['all', 'private', 'public', 'forks'].map((filterType) => (
              <Button
                key={filterType}
                variant={filter === filterType ? "default" : "outline"}
                size="sm"
                onClick={() => setFilter(filterType as any)}
                className="capitalize"
              >
                {filterType}
              </Button>
            ))}
          </div>
        </div>

        {/* Repository List */}
        <ScrollArea className="h-96">
          <div className="space-y-2">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="w-6 h-6 animate-spin" />
                <span className="ml-2">Loading repositories...</span>
              </div>
            ) : filteredRepositories.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Search className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>No repositories found</p>
                <p className="text-sm">Try adjusting your search or filters</p>
              </div>
            ) : (
              filteredRepositories.map((repo) => (
                <div
                  key={repo.id}
                  className={cn(
                    "p-4 border rounded-lg cursor-pointer transition-colors hover:bg-muted/50",
                    selectedRepository?.id === repo.id && "border-primary bg-primary/5"
                  )}
                  onClick={() => onSelect(repo)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="font-medium truncate">{repo.name}</h3>
                        {repo.private ? (
                          <Lock className="w-4 h-4 text-muted-foreground" />
                        ) : (
                          <Unlock className="w-4 h-4 text-muted-foreground" />
                        )}
                        {repo.fork && (
                          <GitFork className="w-4 h-4 text-muted-foreground" />
                        )}
                        {selectedRepository?.id === repo.id && (
                          <Check className="w-4 h-4 text-primary" />
                        )}
                      </div>
                      
                      <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                        {repo.description || 'No description available'}
                      </p>
                      
                      <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                        {repo.language && (
                          <div className="flex items-center space-x-1">
                            <div className={cn("w-2 h-2 rounded-full", getLanguageColor(repo.language))} />
                            <span>{repo.language}</span>
                          </div>
                        )}
                        
                        <div className="flex items-center space-x-1">
                          <Star className="w-3 h-3" />
                          <span>{repo.stargazers_count}</span>
                        </div>
                        
                        <div className="flex items-center space-x-1">
                          <GitFork className="w-3 h-3" />
                          <span>{repo.forks_count}</span>
                        </div>
                        
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-3 h-3" />
                          <span>Updated {formatDate(repo.updated_at)}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                      <Badge variant={repo.private ? "secondary" : "outline"} className="text-xs">
                        {repo.private ? 'Private' : 'Public'}
                      </Badge>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          window.open(repo.html_url, '_blank');
                        }}
                        className="h-8 w-8 p-0"
                      >
                        <ExternalLink className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>

        {/* Selected Repository Info */}
        {selectedRepository && (
          <div className="p-3 bg-primary/5 border border-primary/20 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">Selected Repository</p>
                <p className="text-xs text-muted-foreground">{selectedRepository.full_name}</p>
              </div>
              <Badge variant="default" className="text-xs">
                {selectedRepository.default_branch}
              </Badge>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default RepositorySelector;
