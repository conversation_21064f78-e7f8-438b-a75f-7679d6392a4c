import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  Activity,
  BarChart3,
  Settings,
  Bell,
  Shield,
  Users,
  Zap,
  Monitor,
  GitBranch,
  Server,
  Database,
  Clock,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Import existing components
import { OnboardingWizard } from '@/components/app/onboarding/OnboardingWizard';
import { QueueStatus } from '@/components/app/queue/QueueStatus';
import { QueueManager } from '@/components/app/queue/QueueManager';
import { LogsViewer } from '@/components/app/admin/LogsViewer';
import { SubscriptionPlans } from '@/components/app/billing/SubscriptionPlans';

interface DashboardIntegrationProps {
  className?: string;
  showOnboarding?: boolean;
  onOnboardingComplete?: () => void;
}

// Simple widget components for missing functionality
const PerformanceMetricsWidget = () => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium">Performance</CardTitle>
      <TrendingUp className="h-4 w-4 text-muted-foreground" />
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">98.5%</div>
      <p className="text-xs text-muted-foreground">
        +2.1% from last month
      </p>
      <div className="mt-4 space-y-2">
        <div className="flex items-center justify-between text-sm">
          <span>Response Time</span>
          <span className="text-green-600">245ms</span>
        </div>
        <div className="flex items-center justify-between text-sm">
          <span>Success Rate</span>
          <span className="text-green-600">99.2%</span>
        </div>
      </div>
    </CardContent>
  </Card>
);

const CostTrackingWidget = () => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium">Cost Tracking</CardTitle>
      <DollarSign className="h-4 w-4 text-muted-foreground" />
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">$24.50</div>
      <p className="text-xs text-muted-foreground">
        $75.50 remaining this month
      </p>
      <div className="mt-4">
        <div className="flex items-center justify-between text-sm mb-2">
          <span>Budget Usage</span>
          <span>24.5%</span>
        </div>
        <div className="w-full bg-secondary rounded-full h-2">
          <div className="bg-primary h-2 rounded-full" style={{ width: '24.5%' }}></div>
        </div>
      </div>
    </CardContent>
  </Card>
);

const SystemHealthIndicators = () => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium">System Health</CardTitle>
      <Server className="h-4 w-4 text-muted-foreground" />
    </CardHeader>
    <CardContent>
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm">API Server</span>
          <Badge variant="default" className="bg-success text-success-foreground">
            <CheckCircle className="w-3 h-3 mr-1" />
            Online
          </Badge>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm">Database</span>
          <Badge variant="default" className="bg-success text-success-foreground">
            <CheckCircle className="w-3 h-3 mr-1" />
            Online
          </Badge>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm">Queue Service</span>
          <Badge variant="secondary" className="bg-warning text-warning-foreground">
            <AlertTriangle className="w-3 h-3 mr-1" />
            Warning
          </Badge>
        </div>
      </div>
    </CardContent>
  </Card>
);

const RecentActivityTimeline = () => (
  <Card>
    <CardHeader>
      <CardTitle className="text-sm font-medium">Recent Activity</CardTitle>
    </CardHeader>
    <CardContent>
      <div className="space-y-4">
        <div className="flex items-start space-x-3">
          <div className="w-2 h-2 bg-success rounded-full mt-2"></div>
          <div className="flex-1">
            <p className="text-sm">Code transformation completed</p>
            <p className="text-xs text-muted-foreground">2 minutes ago</p>
          </div>
        </div>
        <div className="flex items-start space-x-3">
          <div className="w-2 h-2 bg-info rounded-full mt-2"></div>
          <div className="flex-1">
            <p className="text-sm">New user onboarded</p>
            <p className="text-xs text-muted-foreground">15 minutes ago</p>
          </div>
        </div>
        <div className="flex items-start space-x-3">
          <div className="w-2 h-2 bg-agent-planner rounded-full mt-2"></div>
          <div className="flex-1">
            <p className="text-sm">System backup completed</p>
            <p className="text-xs text-muted-foreground">1 hour ago</p>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
);

const NotificationCenter = () => (
  <Card>
    <CardHeader>
      <CardTitle className="text-sm font-medium">Notifications</CardTitle>
    </CardHeader>
    <CardContent>
      <div className="space-y-3">
        <div className="flex items-center space-x-3 p-2 bg-warning/10 rounded-lg">
          <AlertTriangle className="w-4 h-4 text-warning" />
          <div className="flex-1">
            <p className="text-sm font-medium">Queue Warning</p>
            <p className="text-xs text-muted-foreground">High queue volume detected</p>
          </div>
        </div>
        <div className="flex items-center space-x-3 p-2 bg-success/10 rounded-lg">
          <CheckCircle className="w-4 h-4 text-success" />
          <div className="flex-1">
            <p className="text-sm font-medium">Backup Complete</p>
            <p className="text-xs text-muted-foreground">Daily backup successful</p>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
);

export const DashboardIntegration: React.FC<DashboardIntegrationProps> = ({
  className,
  showOnboarding = false,
  onOnboardingComplete
}) => {
  const [activeTab, setActiveTab] = useState('overview');

  // Show onboarding wizard if needed
  if (showOnboarding) {
    return (
      <div className={cn("min-h-screen bg-background p-4", className)}>
        <OnboardingWizard onComplete={onOnboardingComplete || (() => {})} />
      </div>
    );
  }

  return (
    <div className={cn("w-full space-y-6", className)}>
      <header className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
          <p className="text-muted-foreground">
            Monitor your code transformations, system health, and usage analytics
          </p>
        </div>
      </header>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 lg:grid-cols-5" role="tablist" aria-label="Dashboard sections">
          <TabsTrigger value="overview" className="flex items-center justify-center sm:justify-start space-x-0 sm:space-x-2" role="tab" aria-controls="overview-content">
            <Monitor className="w-4 h-4" aria-hidden="true" />
            <span className="hidden sm:inline">Overview</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center justify-center sm:justify-start space-x-0 sm:space-x-2" role="tab" aria-controls="analytics-content">
            <BarChart3 className="w-4 h-4" aria-hidden="true" />
            <span className="hidden sm:inline">Analytics</span>
          </TabsTrigger>
          <TabsTrigger value="admin" className="flex items-center justify-center sm:justify-start space-x-0 sm:space-x-2 lg:flex" role="tab" aria-controls="admin-content">
            <Shield className="w-4 h-4" aria-hidden="true" />
            <span className="hidden sm:inline">Admin</span>
          </TabsTrigger>
          <TabsTrigger value="billing" className="flex items-center justify-center sm:justify-start space-x-0 sm:space-x-2 lg:flex" role="tab" aria-controls="billing-content">
            <DollarSign className="w-4 h-4" aria-hidden="true" />
            <span className="hidden sm:inline">Billing</span>
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center justify-center sm:justify-start space-x-0 sm:space-x-2 lg:flex" role="tab" aria-controls="notifications-content">
            <Bell className="w-4 h-4" aria-hidden="true" />
            <span className="hidden sm:inline">Alerts</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6" id="overview-content" role="tabpanel" aria-labelledby="overview-tab">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-3 sm:gap-4 lg:gap-6" role="region" aria-label="Dashboard overview widgets">
            <PerformanceMetricsWidget />
            <CostTrackingWidget />
            <SystemHealthIndicators />
            <div className="sm:col-span-2 lg:col-span-2">
              <RecentActivityTimeline />
            </div>
            <NotificationCenter />
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Usage Analytics</CardTitle>
                <CardDescription>Track API usage and performance metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Detailed analytics dashboard coming soon. This will include transformation success rates,
                  API usage patterns, and performance benchmarks.
                </p>
              </CardContent>
            </Card>
            <QueueStatus />
          </div>
        </TabsContent>

        <TabsContent value="admin" className="space-y-6">
          <Tabs defaultValue="queue" className="w-full">
            <TabsList>
              <TabsTrigger value="queue">Queue</TabsTrigger>
              <TabsTrigger value="logs">Logs</TabsTrigger>
            </TabsList>
            
            <TabsContent value="queue">
              <QueueManager />
            </TabsContent>
            
            <TabsContent value="logs">
              <LogsViewer />
            </TabsContent>
          </Tabs>
        </TabsContent>

        <TabsContent value="billing" className="space-y-6">
          <SubscriptionPlans />
        </TabsContent>

        <TabsContent value="notifications" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <NotificationCenter />
            <Card>
              <CardHeader>
                <CardTitle>Alert Settings</CardTitle>
                <CardDescription>Configure notification preferences</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Notification settings panel coming soon. Configure alerts for cost limits,
                  system health, and transformation completion.
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DashboardIntegration;
