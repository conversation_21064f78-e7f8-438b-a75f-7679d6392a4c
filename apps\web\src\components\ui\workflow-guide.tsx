import * as React from "react"
import { cn } from "@/lib/utils"
import { CheckIcon, PlayIcon, InfoIcon, ChevronRightIcon } from "@/components/ui/icon"
import { Button } from "@/components/ui/button"
import { VStack, HStack } from "@/components/ui/layout"
import { Typography } from "@/components/ui/typography"
import { Badge } from "@/components/ui/badge"

interface WorkflowStep {
  id: string
  title: string
  description: string
  action?: () => void
  completed?: boolean
  optional?: boolean
  estimatedTime?: string
}

interface WorkflowGuideProps {
  title: string
  description?: string
  steps: WorkflowStep[]
  currentStep?: string
  onStepClick?: (stepId: string) => void
  className?: string
}

export function WorkflowGuide({
  title,
  description,
  steps,
  currentStep,
  onStepClick,
  className
}: WorkflowGuideProps) {
  const currentStepIndex = steps.findIndex(step => step.id === currentStep)
  
  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <VStack spacing="xs">
        <Typography variant="h5" className="text-foreground">
          {title}
        </Typography>
        {description && (
          <Typography variant="body-sm" className="text-muted-foreground">
            {description}
          </Typography>
        )}
      </VStack>
      
      {/* Progress indicator */}
      <div className="flex items-center space-x-2">
        <div className="flex-1 bg-muted rounded-full h-2">
          <div
            className="bg-primary h-2 rounded-full transition-all duration-300"
            style={{
              width: `${(steps.filter(s => s.completed).length / steps.length) * 100}%`
            }}
          />
        </div>
        <Typography variant="caption" className="text-muted-foreground">
          {steps.filter(s => s.completed).length} of {steps.length}
        </Typography>
      </div>
      
      {/* Steps */}
      <VStack spacing="xs">
        {steps.map((step, index) => (
          <WorkflowStepItem
            key={step.id}
            step={step}
            isActive={step.id === currentStep}
            isCompleted={step.completed || false}
            isNext={index === currentStepIndex + 1}
            onClick={() => onStepClick?.(step.id)}
          />
        ))}
      </VStack>
    </div>
  )
}

interface WorkflowStepItemProps {
  step: WorkflowStep
  isActive: boolean
  isCompleted: boolean
  isNext: boolean
  onClick?: () => void
}

function WorkflowStepItem({ step, isActive, isCompleted, isNext, onClick }: WorkflowStepItemProps) {
  return (
    <div
      className={cn(
        "p-3 rounded-lg border transition-all cursor-pointer",
        {
          "border-primary bg-primary/5": isActive,
          "border-success bg-success/5": isCompleted,
          "border-border hover:border-border/80": !isActive && !isCompleted,
          "border-warning bg-warning/5": isNext && !isActive && !isCompleted,
        }
      )}
      onClick={onClick}
    >
      <HStack spacing="sm" align="start">
        {/* Status indicator */}
        <div
          className={cn(
            "flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium",
            {
              "bg-primary text-primary-foreground": isActive,
              "bg-success text-success-foreground": isCompleted,
              "bg-muted text-muted-foreground": !isActive && !isCompleted && !isNext,
              "bg-warning text-warning-foreground": isNext && !isActive && !isCompleted,
            }
          )}
        >
          {isCompleted ? (
            <CheckIcon size="xs" />
          ) : isActive ? (
            <PlayIcon size="xs" />
          ) : (
            <span>{steps.findIndex(s => s.id === step.id) + 1}</span>
          )}
        </div>
        
        {/* Content */}
        <VStack spacing="xs" className="flex-1">
          <HStack spacing="sm" className="justify-between items-start">
            <Typography
              variant="body-sm"
              className={cn(
                "font-medium",
                {
                  "text-primary": isActive,
                  "text-success": isCompleted,
                  "text-foreground": !isActive && !isCompleted,
                }
              )}
            >
              {step.title}
            </Typography>
            
            <HStack spacing="xs">
              {step.optional && (
                <Badge variant="secondary" className="text-xs">
                  Optional
                </Badge>
              )}
              {step.estimatedTime && (
                <Typography variant="caption" className="text-muted-foreground">
                  {step.estimatedTime}
                </Typography>
              )}
            </HStack>
          </HStack>
          
          <Typography variant="body-sm" className="text-muted-foreground">
            {step.description}
          </Typography>
          
          {step.action && (isActive || isNext) && (
            <Button
              size="sm"
              variant={isActive ? "default" : "outline"}
              onClick={(e) => {
                e.stopPropagation()
                step.action?.()
              }}
              className="self-start"
            >
              {isActive ? "Continue" : "Start"}
              <ChevronRightIcon size="xs" className="ml-1" />
            </Button>
          )}
        </VStack>
      </HStack>
    </div>
  )
}

// Quick action buttons for common workflows
interface QuickActionsProps {
  actions: Array<{
    label: string
    description: string
    icon?: React.ReactNode
    action: () => void
    variant?: "default" | "outline" | "secondary"
  }>
  className?: string
}

export function QuickActions({ actions, className }: QuickActionsProps) {
  return (
    <div className={cn("grid grid-cols-1 sm:grid-cols-2 gap-3", className)}>
      {actions.map((action, index) => (
        <Button
          key={index}
          variant={action.variant || "outline"}
          className="h-auto p-4 justify-start"
          onClick={action.action}
        >
          <VStack spacing="xs" align="start" className="text-left">
            <HStack spacing="sm" align="center">
              {action.icon}
              <Typography variant="body-sm" className="font-medium">
                {action.label}
              </Typography>
            </HStack>
            <Typography variant="caption" className="text-muted-foreground">
              {action.description}
            </Typography>
          </VStack>
        </Button>
      ))}
    </div>
  )
}

// Workflow context provider
interface WorkflowContextValue {
  currentWorkflow: string | null
  setCurrentWorkflow: (workflow: string | null) => void
  completeStep: (stepId: string) => void
  resetWorkflow: () => void
  completedSteps: Set<string>
}

const WorkflowContext = React.createContext<WorkflowContextValue | null>(null)

export function WorkflowProvider({ children }: { children: React.ReactNode }) {
  const [currentWorkflow, setCurrentWorkflow] = React.useState<string | null>(null)
  const [completedSteps, setCompletedSteps] = React.useState<Set<string>>(new Set())
  
  const completeStep = React.useCallback((stepId: string) => {
    setCompletedSteps(prev => new Set([...prev, stepId]))
  }, [])
  
  const resetWorkflow = React.useCallback(() => {
    setCurrentWorkflow(null)
    setCompletedSteps(new Set())
  }, [])
  
  const value = React.useMemo(() => ({
    currentWorkflow,
    setCurrentWorkflow,
    completeStep,
    resetWorkflow,
    completedSteps
  }), [currentWorkflow, setCurrentWorkflow, completeStep, resetWorkflow, completedSteps])
  
  return (
    <WorkflowContext.Provider value={value}>
      {children}
    </WorkflowContext.Provider>
  )
}

export function useWorkflow() {
  const context = React.useContext(WorkflowContext)
  if (!context) {
    throw new Error('useWorkflow must be used within a WorkflowProvider')
  }
  return context
}

// Predefined workflows for common tasks
export const reactorWorkflows = {
  firstTime: {
    id: 'first-time',
    title: 'Getting Started with Metamorphic Reactor',
    description: 'Learn the basics of code transformation with our dual-agent system',
    steps: [
      {
        id: 'write-prompt',
        title: 'Write Your Transformation Prompt',
        description: 'Describe what you want to transform or improve in your code',
        estimatedTime: '2 min'
      },
      {
        id: 'run-reactor',
        title: 'Run the Reactor Loop',
        description: 'Let our AI agents analyze and transform your code',
        estimatedTime: '1-3 min'
      },
      {
        id: 'review-changes',
        title: 'Review the Changes',
        description: 'Examine the proposed transformations in the diff viewer',
        estimatedTime: '2 min'
      },
      {
        id: 'apply-changes',
        title: 'Apply Changes',
        description: 'Accept the transformations and update your code',
        estimatedTime: '30 sec'
      },
      {
        id: 'create-pr',
        title: 'Create Pull Request',
        description: 'Generate a PR with your transformations (optional)',
        estimatedTime: '1 min',
        optional: true
      }
    ]
  },
  optimization: {
    id: 'optimization',
    title: 'Code Optimization Workflow',
    description: 'Optimize your code for better performance and maintainability',
    steps: [
      {
        id: 'analyze-code',
        title: 'Analyze Current Code',
        description: 'Paste or write the code you want to optimize',
        estimatedTime: '1 min'
      },
      {
        id: 'set-goals',
        title: 'Set Optimization Goals',
        description: 'Specify what aspects to optimize (performance, readability, etc.)',
        estimatedTime: '1 min'
      },
      {
        id: 'run-optimization',
        title: 'Run Optimization',
        description: 'Execute the reactor loop with optimization focus',
        estimatedTime: '2-4 min'
      },
      {
        id: 'benchmark',
        title: 'Review Benchmarks',
        description: 'Compare before and after performance metrics',
        estimatedTime: '2 min'
      },
      {
        id: 'finalize',
        title: 'Finalize Changes',
        description: 'Apply optimizations and save results',
        estimatedTime: '1 min'
      }
    ]
  }
}
