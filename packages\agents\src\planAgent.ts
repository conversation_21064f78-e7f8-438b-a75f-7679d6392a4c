import { <PERSON><PERSON><PERSON>atch, PlanRequest, AgentConfig, ProviderConfig } from './types.js';
import { AgentProvider } from './providers/AgentProvider.js';
import { ProviderFactory } from './providers/ProviderFactory.js';
import { ProviderFailover } from './utils/ProviderFailover.js';
import { CostGuard } from './utils/CostGuard.js';
import { TokenMonitor } from './utils/TokenMonitor.js';
import { StreamingManager } from './streaming/StreamingManager.js';

export class PlanAgent {
  private config: AgentConfig;
  private provider: AgentProvider;
  private failover?: ProviderFailover;
  private costGuard: CostGuard;
  private tokenMonitor: TokenMonitor;
  private streamingManager: StreamingManager;

  constructor(
    config: AgentConfig = {
      model: 'gemini-2.5',
      temperature: 0.7,
      maxTokens: 2000
    },
    costGuard?: CostGuard,
    tokenMonitor?: TokenMonitor,
    streamingManager?: StreamingManager
  ) {
    this.config = config;
    this.costGuard = costGuard || new CostGuard();
    this.tokenMonitor = tokenMonitor || new TokenMonitor();
    this.streamingManager = streamingManager || new StreamingManager();

    // Initialize provider
    if (config.provider) {
      this.provider = ProviderFactory.createProvider(config.provider);
    } else {
      // Legacy support - convert old config to new provider config
      const legacyConfig = this.convertLegacyConfig(config);
      this.provider = ProviderFactory.createProvider(legacyConfig);
    }
  }

  /**
   * Initialize with failover providers
   */
  initializeFailover(fallbackConfigs: ProviderConfig[]): void {
    if (this.config.provider) {
      this.failover = new ProviderFailover(
        this.config.provider,
        fallbackConfigs
      );
    }
  }

  async generatePatch(request: PlanRequest): Promise<JSONPatch> {
    console.log(`[PlanAgent] Generating patch with ${this.provider.getType()}:${this.provider.getModel()}`);
    console.log(`[PlanAgent] Request: ${request.prompt}`);

    // Validate request
    if (!request.prompt || request.prompt.trim().length === 0) {
      throw new Error('Prompt cannot be empty');
    }

    // Check cost limits
    this.costGuard.checkCostLimit(0.25); // Estimate $0.25 for planning

    try {
      let result;

      if (this.failover) {
        // Use failover for enhanced reliability
        result = await this.failover.executeWithFailover(
          (provider) => provider.generatePatch(request),
          'generatePatch'
        );
      } else {
        // Direct provider execution
        result = await this.provider.generatePatch(request);
      }

      // Record metrics
      this.costGuard.recordCost(
        this.provider.getType(),
        this.provider.getModel(),
        'generatePatch',
        result.usage
      );

      this.tokenMonitor.recordUsage(
        this.provider.getType(),
        this.provider.getModel(),
        'generatePatch',
        result.usage,
        result.requestId
      );

      console.log(`[PlanAgent] Generated patch with confidence: ${result.data.confidence}`);
      console.log(`[PlanAgent] Cost: $${result.usage.cost.toFixed(4)}, Tokens: ${result.usage.totalTokens}`);

      return result.data;
    } catch (error) {
      console.error(`[PlanAgent] Error generating patch:`, error);
      throw error;
    }
  }

  /**
   * Generate patch with streaming
   */
  async *generatePatchStream(request: PlanRequest): AsyncGenerator<Partial<JSONPatch>, JSONPatch, unknown> {
    console.log(`[PlanAgent] Starting streaming patch generation`);

    this.costGuard.checkCostLimit(0.25);

    const requestId = `plan_${Date.now()}`;
    const streamGenerator = this.streamingManager.streamPatchGeneration(
      this.provider,
      request,
      requestId
    );

    try {
      for await (const chunk of streamGenerator) {
        yield {
          operations: chunk.operations,
          description: chunk.description,
          confidence: chunk.confidence,
        };
      }

      const finalResult = await streamGenerator.return(undefined);
      return finalResult.value;
    } catch (error) {
      console.error(`[PlanAgent] Streaming error:`, error);
      throw error;
    }
  }

  /**
   * Get current provider information
   */
  getProviderInfo(): {
    type: string;
    model: string;
    cost: number;
    supportsStreaming: boolean;
  } {
    return {
      type: this.provider.getType(),
      model: this.provider.getModel(),
      cost: this.provider.getCost(),
      supportsStreaming: this.provider.supportsStreaming(),
    };
  }

  /**
   * Get cost and token statistics
   */
  getStats(): {
    totalCost: number;
    totalTokens: number;
    requestCount: number;
    averageCost: number;
  } {
    const costStats = this.costGuard.getStats();
    const tokenStats = this.tokenMonitor.getStats();

    return {
      totalCost: costStats.totalCost,
      totalTokens: tokenStats.totalTokens,
      requestCount: tokenStats.requestCount,
      averageCost: tokenStats.averageCost,
    };
  }

  private convertLegacyConfig(config: AgentConfig): ProviderConfig {
    // Convert legacy config to new provider config
    const modelMapping: Record<string, { type: 'openai' | 'vertex-ai'; model: string }> = {
      'gpt-4-turbo': { type: 'openai', model: 'gpt-4-turbo' },
      'gemini-2.5': { type: 'vertex-ai', model: 'gemini-2.5-flash' },
    };

    const mapping = modelMapping[config.model || 'gpt-4-turbo'];

    return {
      type: mapping.type,
      model: mapping.model,
      temperature: config.temperature || 0.7,
      maxTokens: config.maxTokens || 2000,
      systemPrompt: 'You are a code planning agent. Generate precise JSON patches to implement requested changes.',
    };
  }

  async validatePatch(patch: JSONPatch): Promise<boolean> {
    // Basic validation
    if (!patch.operations || !Array.isArray(patch.operations)) {
      return false;
    }
    
    if (!patch.description || typeof patch.description !== 'string') {
      return false;
    }
    
    if (typeof patch.confidence !== 'number' || patch.confidence < 0 || patch.confidence > 1) {
      return false;
    }
    
    // Validate each operation
    for (const op of patch.operations) {
      if (!op.op || !op.path) {
        return false;
      }
      
      if (!['add', 'remove', 'replace', 'move', 'copy', 'test'].includes(op.op)) {
        return false;
      }
    }
    
    return true;
  }
}
