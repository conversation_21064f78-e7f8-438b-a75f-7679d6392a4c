import * as React from "react"
import { cn } from "@/lib/utils"
import { Card } from "@/components/ui/card"
import { VStack, HStack } from "@/components/ui/layout"
import { Typography } from "@/components/ui/typography"
import { ProgressBar, CircularProgress } from "@/components/ui/loading-states"
import { 
  TrendingUpIcon, 
  TrendingDownIcon, 
  ActivityIcon,
  ClockIcon,
  CodeIcon,
  CheckIcon,
  ErrorIcon
} from "@/components/ui/icon"

// Analytics data interfaces
interface MetricData {
  value: number
  change: number
  trend: 'up' | 'down' | 'stable'
  label: string
  format?: 'number' | 'percentage' | 'duration' | 'bytes'
}

interface UsageMetrics {
  totalTransformations: MetricData
  successRate: MetricData
  averageIterations: MetricData
  averageTime: MetricData
  codeQualityImprovement: MetricData
  activeUsers: MetricData
}

interface PerformanceMetrics {
  responseTime: MetricData
  throughput: MetricData
  errorRate: MetricData
  uptime: MetricData
}

// Metric card component
interface MetricCardProps {
  metric: MetricData
  className?: string
}

function MetricCard({ metric, className }: MetricCardProps) {
  const formatValue = (value: number, format?: string) => {
    switch (format) {
      case 'percentage':
        return `${value.toFixed(1)}%`
      case 'duration':
        return `${value.toFixed(1)}s`
      case 'bytes':
        return `${(value / 1024 / 1024).toFixed(1)}MB`
      default:
        return value.toLocaleString()
    }
  }

  const getTrendIcon = () => {
    switch (metric.trend) {
      case 'up':
        return <TrendingUpIcon className="w-4 h-4 text-success" />
      case 'down':
        return <TrendingDownIcon className="w-4 h-4 text-destructive" />
      default:
        return <ActivityIcon className="w-4 h-4 text-muted-foreground" />
    }
  }

  const getTrendColor = () => {
    switch (metric.trend) {
      case 'up':
        return 'text-success'
      case 'down':
        return 'text-destructive'
      default:
        return 'text-muted-foreground'
    }
  }

  return (
    <Card className={cn("p-4", className)}>
      <VStack spacing="sm">
        <HStack spacing="sm" className="justify-between items-start">
          <Typography variant="caption" className="text-muted-foreground">
            {metric.label}
          </Typography>
          {getTrendIcon()}
        </HStack>
        
        <Typography variant="h4" className="text-foreground">
          {formatValue(metric.value, metric.format)}
        </Typography>
        
        <HStack spacing="xs" align="center">
          <Typography variant="caption" className={getTrendColor()}>
            {metric.change > 0 ? '+' : ''}{metric.change.toFixed(1)}%
          </Typography>
          <Typography variant="caption" className="text-muted-foreground">
            vs last period
          </Typography>
        </HStack>
      </VStack>
    </Card>
  )
}

// Usage analytics component
interface UsageAnalyticsProps {
  metrics: UsageMetrics
  className?: string
}

export function UsageAnalytics({ metrics, className }: UsageAnalyticsProps) {
  return (
    <div className={cn("space-y-6", className)}>
      <VStack spacing="sm">
        <Typography variant="h5" className="text-foreground">
          Usage Analytics
        </Typography>
        <Typography variant="body-sm" className="text-muted-foreground">
          Track transformation usage and success metrics
        </Typography>
      </VStack>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <MetricCard metric={metrics.totalTransformations} />
        <MetricCard metric={metrics.successRate} />
        <MetricCard metric={metrics.averageIterations} />
        <MetricCard metric={metrics.averageTime} />
        <MetricCard metric={metrics.codeQualityImprovement} />
        <MetricCard metric={metrics.activeUsers} />
      </div>

      {/* Success rate visualization */}
      <Card className="p-6">
        <VStack spacing="md">
          <Typography variant="h6" className="text-foreground">
            Transformation Success Rate
          </Typography>
          
          <HStack spacing="lg" align="center">
            <CircularProgress
              value={metrics.successRate.value}
              size={120}
              strokeWidth={8}
              variant="success"
              showPercentage
              label="Overall Success"
            />
            
            <VStack spacing="sm" className="flex-1">
              <HStack spacing="sm" className="justify-between w-full">
                <HStack spacing="xs" align="center">
                  <CheckIcon className="w-4 h-4 text-success" />
                  <Typography variant="body-sm">Successful</Typography>
                </HStack>
                <Typography variant="body-sm" className="font-medium">
                  {((metrics.successRate.value / 100) * metrics.totalTransformations.value).toFixed(0)}
                </Typography>
              </HStack>
              
              <HStack spacing="sm" className="justify-between w-full">
                <HStack spacing="xs" align="center">
                  <ErrorIcon className="w-4 h-4 text-destructive" />
                  <Typography variant="body-sm">Failed</Typography>
                </HStack>
                <Typography variant="body-sm" className="font-medium">
                  {(((100 - metrics.successRate.value) / 100) * metrics.totalTransformations.value).toFixed(0)}
                </Typography>
              </HStack>
              
              <ProgressBar
                value={metrics.successRate.value}
                variant="success"
                className="w-full"
              />
            </VStack>
          </HStack>
        </VStack>
      </Card>
    </div>
  )
}

// Performance analytics component
interface PerformanceAnalyticsProps {
  metrics: PerformanceMetrics
  className?: string
}

export function PerformanceAnalytics({ metrics, className }: PerformanceAnalyticsProps) {
  return (
    <div className={cn("space-y-6", className)}>
      <VStack spacing="sm">
        <Typography variant="h5" className="text-foreground">
          Performance Analytics
        </Typography>
        <Typography variant="body-sm" className="text-muted-foreground">
          Monitor system performance and reliability
        </Typography>
      </VStack>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard metric={metrics.responseTime} />
        <MetricCard metric={metrics.throughput} />
        <MetricCard metric={metrics.errorRate} />
        <MetricCard metric={metrics.uptime} />
      </div>

      {/* Performance trends */}
      <Card className="p-6">
        <VStack spacing="md">
          <Typography variant="h6" className="text-foreground">
            System Health Overview
          </Typography>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <VStack spacing="sm">
              <Typography variant="body-sm" className="text-muted-foreground">
                Response Time
              </Typography>
              <HStack spacing="sm" align="center">
                <CircularProgress
                  value={Math.max(0, 100 - (metrics.responseTime.value / 10))}
                  size={80}
                  variant={metrics.responseTime.value < 2 ? 'success' : metrics.responseTime.value < 5 ? 'warning' : 'error'}
                />
                <VStack spacing="xs">
                  <Typography variant="h6">
                    {metrics.responseTime.value.toFixed(2)}s
                  </Typography>
                  <Typography variant="caption" className="text-muted-foreground">
                    Average
                  </Typography>
                </VStack>
              </HStack>
            </VStack>
            
            <VStack spacing="sm">
              <Typography variant="body-sm" className="text-muted-foreground">
                System Uptime
              </Typography>
              <HStack spacing="sm" align="center">
                <CircularProgress
                  value={metrics.uptime.value}
                  size={80}
                  variant="success"
                  showPercentage
                />
                <VStack spacing="xs">
                  <Typography variant="h6">
                    {metrics.uptime.value.toFixed(2)}%
                  </Typography>
                  <Typography variant="caption" className="text-muted-foreground">
                    Last 30 days
                  </Typography>
                </VStack>
              </HStack>
            </VStack>
          </div>
        </VStack>
      </Card>
    </div>
  )
}

// Real-time activity feed
interface ActivityItem {
  id: string
  type: 'transformation' | 'error' | 'success' | 'user_action'
  message: string
  timestamp: Date
  metadata?: Record<string, any>
}

interface ActivityFeedProps {
  activities: ActivityItem[]
  className?: string
}

export function ActivityFeed({ activities, className }: ActivityFeedProps) {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'transformation':
        return <CodeIcon className="w-4 h-4 text-primary" />
      case 'success':
        return <CheckIcon className="w-4 h-4 text-success" />
      case 'error':
        return <ErrorIcon className="w-4 h-4 text-destructive" />
      default:
        return <ActivityIcon className="w-4 h-4 text-muted-foreground" />
    }
  }

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date()
    const diff = now.getTime() - timestamp.getTime()
    const minutes = Math.floor(diff / 60000)
    
    if (minutes < 1) return 'Just now'
    if (minutes < 60) return `${minutes}m ago`
    if (minutes < 1440) return `${Math.floor(minutes / 60)}h ago`
    return timestamp.toLocaleDateString()
  }

  return (
    <Card className={cn("p-6", className)}>
      <VStack spacing="md">
        <HStack spacing="sm" className="justify-between items-center">
          <Typography variant="h6" className="text-foreground">
            Recent Activity
          </Typography>
          <ClockIcon className="w-4 h-4 text-muted-foreground" />
        </HStack>
        
        <VStack spacing="sm" className="max-h-80 overflow-y-auto">
          {activities.length === 0 ? (
            <Typography variant="body-sm" className="text-muted-foreground text-center py-8">
              No recent activity
            </Typography>
          ) : (
            activities.map((activity) => (
              <HStack key={activity.id} spacing="sm" align="start" className="p-2 rounded hover:bg-muted/50">
                <div className="mt-1">
                  {getActivityIcon(activity.type)}
                </div>
                <VStack spacing="xs" className="flex-1 min-w-0">
                  <Typography variant="body-sm" className="text-foreground">
                    {activity.message}
                  </Typography>
                  <Typography variant="caption" className="text-muted-foreground">
                    {formatTimestamp(activity.timestamp)}
                  </Typography>
                </VStack>
              </HStack>
            ))
          )}
        </VStack>
      </VStack>
    </Card>
  )
}

// Main analytics dashboard
interface AnalyticsDashboardProps {
  usageMetrics: UsageMetrics
  performanceMetrics: PerformanceMetrics
  activities: ActivityItem[]
  className?: string
}

export function AnalyticsDashboard({
  usageMetrics,
  performanceMetrics,
  activities,
  className
}: AnalyticsDashboardProps) {
  return (
    <div className={cn("space-y-8", className)}>
      <VStack spacing="sm">
        <Typography variant="h4" className="text-foreground">
          Analytics Dashboard
        </Typography>
        <Typography variant="body-sm" className="text-muted-foreground">
          Monitor usage patterns, performance metrics, and system health
        </Typography>
      </VStack>

      <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
        <div className="xl:col-span-2 space-y-8">
          <UsageAnalytics metrics={usageMetrics} />
          <PerformanceAnalytics metrics={performanceMetrics} />
        </div>
        
        <div className="space-y-6">
          <ActivityFeed activities={activities} />
        </div>
      </div>
    </div>
  )
}
