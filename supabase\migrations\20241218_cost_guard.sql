-- Migration: Cost Guard System
-- Description: Tables for cost tracking, limits, alerts, and predictions

-- Cost limits table
CREATE TABLE IF NOT EXISTS cost_limits (
    repository_id TEXT PRIMARY KEY REFERENCES autonomous_repositories(id) ON DELETE CASCADE,
    daily_limit DECIMAL(10,4) NOT NULL DEFAULT 3.00,
    weekly_limit DECIMAL(10,4) NOT NULL DEFAULT 20.00,
    monthly_limit DECIMAL(10,4) NOT NULL DEFAULT 80.00,
    per_operation_limit DECIMAL(10,4) NOT NULL DEFAULT 0.50,
    alert_threshold DECIMAL(3,2) NOT NULL DEFAULT 0.80 CHECK (alert_threshold >= 0 AND alert_threshold <= 1),
    enabled BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Cost alerts table
CREATE TABLE IF NOT EXISTS cost_alerts (
    id TEXT PRIMARY KEY,
    repository_id TEXT NOT NULL REFERENCES autonomous_repositories(id) ON DELETE CASCADE,
    alert_type TEXT NOT NULL CHECK (alert_type IN ('approaching_limit', 'limit_exceeded', 'budget_exhausted', 'unusual_spending')),
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    message TEXT NOT NULL,
    current_cost DECIMAL(10,4) NOT NULL,
    limit_value DECIMAL(10,4) NOT NULL,
    percentage DECIMAL(5,2) NOT NULL,
    acknowledged BOOLEAN DEFAULT false,
    acknowledged_at TIMESTAMPTZ,
    acknowledged_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Cost predictions table
CREATE TABLE IF NOT EXISTS cost_predictions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    repository_id TEXT NOT NULL REFERENCES autonomous_repositories(id) ON DELETE CASCADE,
    timeframe TEXT NOT NULL CHECK (timeframe IN ('daily', 'weekly', 'monthly')),
    current_cost DECIMAL(10,4) NOT NULL,
    predicted_cost DECIMAL(10,4) NOT NULL,
    confidence DECIMAL(3,2) NOT NULL CHECK (confidence >= 0 AND confidence <= 1),
    trend TEXT NOT NULL CHECK (trend IN ('increasing', 'stable', 'decreasing')),
    projected_limit_reach TIMESTAMPTZ,
    recommendations TEXT[] DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(repository_id, timeframe)
);

-- Cost budget tracking (for real-time monitoring)
CREATE TABLE IF NOT EXISTS cost_budgets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    repository_id TEXT NOT NULL REFERENCES autonomous_repositories(id) ON DELETE CASCADE,
    budget_type TEXT NOT NULL CHECK (budget_type IN ('daily', 'weekly', 'monthly')),
    budget_date DATE NOT NULL,
    allocated_amount DECIMAL(10,4) NOT NULL,
    spent_amount DECIMAL(10,4) DEFAULT 0,
    remaining_amount DECIMAL(10,4) GENERATED ALWAYS AS (allocated_amount - spent_amount) STORED,
    utilization_percentage DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE 
            WHEN allocated_amount > 0 THEN (spent_amount / allocated_amount * 100)
            ELSE 0 
        END
    ) STORED,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'exceeded', 'exhausted')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(repository_id, budget_type, budget_date)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_cost_limits_enabled ON cost_limits(enabled);
CREATE INDEX IF NOT EXISTS idx_cost_alerts_repository_id ON cost_alerts(repository_id);
CREATE INDEX IF NOT EXISTS idx_cost_alerts_severity ON cost_alerts(severity);
CREATE INDEX IF NOT EXISTS idx_cost_alerts_acknowledged ON cost_alerts(acknowledged);
CREATE INDEX IF NOT EXISTS idx_cost_alerts_created_at ON cost_alerts(created_at);
CREATE INDEX IF NOT EXISTS idx_cost_predictions_repository_id ON cost_predictions(repository_id);
CREATE INDEX IF NOT EXISTS idx_cost_predictions_timeframe ON cost_predictions(timeframe);
CREATE INDEX IF NOT EXISTS idx_cost_budgets_repository_date ON cost_budgets(repository_id, budget_date);
CREATE INDEX IF NOT EXISTS idx_cost_budgets_status ON cost_budgets(status);

-- Row Level Security (RLS)
ALTER TABLE cost_limits ENABLE ROW LEVEL SECURITY;
ALTER TABLE cost_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE cost_predictions ENABLE ROW LEVEL SECURITY;
ALTER TABLE cost_budgets ENABLE ROW LEVEL SECURITY;

-- RLS Policies for cost_limits
CREATE POLICY "Users can view cost limits for their repositories" ON cost_limits
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM autonomous_repositories ar 
            WHERE ar.id = cost_limits.repository_id 
            AND ar.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update cost limits for their repositories" ON cost_limits
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM autonomous_repositories ar 
            WHERE ar.id = cost_limits.repository_id 
            AND ar.user_id = auth.uid()
        )
    );

-- RLS Policies for cost_alerts
CREATE POLICY "Users can view alerts for their repositories" ON cost_alerts
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM autonomous_repositories ar 
            WHERE ar.id = cost_alerts.repository_id 
            AND ar.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can acknowledge their alerts" ON cost_alerts
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM autonomous_repositories ar 
            WHERE ar.id = cost_alerts.repository_id 
            AND ar.user_id = auth.uid()
        )
    );

CREATE POLICY "System can insert alerts" ON cost_alerts
    FOR INSERT WITH CHECK (true); -- Allow system to insert

-- RLS Policies for cost_predictions
CREATE POLICY "Users can view predictions for their repositories" ON cost_predictions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM autonomous_repositories ar 
            WHERE ar.id = cost_predictions.repository_id 
            AND ar.user_id = auth.uid()
        )
    );

CREATE POLICY "System can manage predictions" ON cost_predictions
    FOR ALL USING (true); -- Allow system full access

-- RLS Policies for cost_budgets
CREATE POLICY "Users can view budgets for their repositories" ON cost_budgets
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM autonomous_repositories ar 
            WHERE ar.id = cost_budgets.repository_id 
            AND ar.user_id = auth.uid()
        )
    );

CREATE POLICY "System can manage budgets" ON cost_budgets
    FOR ALL USING (true); -- Allow system full access

-- Functions for cost management
CREATE OR REPLACE FUNCTION update_cost_budget(
    repo_id TEXT,
    budget_type TEXT,
    amount DECIMAL(10,4)
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO cost_budgets (
        repository_id,
        budget_type,
        budget_date,
        allocated_amount,
        spent_amount
    )
    VALUES (
        repo_id,
        budget_type,
        CASE 
            WHEN budget_type = 'daily' THEN CURRENT_DATE
            WHEN budget_type = 'weekly' THEN DATE_TRUNC('week', CURRENT_DATE)::DATE
            WHEN budget_type = 'monthly' THEN DATE_TRUNC('month', CURRENT_DATE)::DATE
        END,
        amount,
        0
    )
    ON CONFLICT (repository_id, budget_type, budget_date)
    DO UPDATE SET
        allocated_amount = EXCLUDED.allocated_amount,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION add_cost_to_budget(
    repo_id TEXT,
    cost_amount DECIMAL(10,4)
)
RETURNS VOID AS $$
BEGIN
    -- Update daily budget
    UPDATE cost_budgets 
    SET spent_amount = spent_amount + cost_amount,
        updated_at = NOW(),
        status = CASE 
            WHEN spent_amount + cost_amount >= allocated_amount THEN 'exhausted'
            WHEN spent_amount + cost_amount >= allocated_amount * 0.9 THEN 'exceeded'
            ELSE 'active'
        END
    WHERE repository_id = repo_id 
        AND budget_type = 'daily' 
        AND budget_date = CURRENT_DATE;

    -- Update weekly budget
    UPDATE cost_budgets 
    SET spent_amount = spent_amount + cost_amount,
        updated_at = NOW(),
        status = CASE 
            WHEN spent_amount + cost_amount >= allocated_amount THEN 'exhausted'
            WHEN spent_amount + cost_amount >= allocated_amount * 0.9 THEN 'exceeded'
            ELSE 'active'
        END
    WHERE repository_id = repo_id 
        AND budget_type = 'weekly' 
        AND budget_date = DATE_TRUNC('week', CURRENT_DATE)::DATE;

    -- Update monthly budget
    UPDATE cost_budgets 
    SET spent_amount = spent_amount + cost_amount,
        updated_at = NOW(),
        status = CASE 
            WHEN spent_amount + cost_amount >= allocated_amount THEN 'exhausted'
            WHEN spent_amount + cost_amount >= allocated_amount * 0.9 THEN 'exceeded'
            ELSE 'active'
        END
    WHERE repository_id = repo_id 
        AND budget_type = 'monthly' 
        AND budget_date = DATE_TRUNC('month', CURRENT_DATE)::DATE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION get_cost_summary(
    repo_id TEXT,
    timeframe TEXT DEFAULT 'daily'
)
RETURNS TABLE (
    total_cost DECIMAL(10,4),
    operation_count BIGINT,
    avg_cost_per_operation DECIMAL(10,4),
    limit_amount DECIMAL(10,4),
    utilization_percentage DECIMAL(5,2),
    status TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(SUM(ac.cost_usd), 0) as total_cost,
        COUNT(ac.id) as operation_count,
        CASE 
            WHEN COUNT(ac.id) > 0 THEN COALESCE(SUM(ac.cost_usd), 0) / COUNT(ac.id)
            ELSE 0
        END as avg_cost_per_operation,
        CASE 
            WHEN timeframe = 'daily' THEN cl.daily_limit
            WHEN timeframe = 'weekly' THEN cl.weekly_limit
            WHEN timeframe = 'monthly' THEN cl.monthly_limit
            ELSE cl.daily_limit
        END as limit_amount,
        CASE 
            WHEN cl.daily_limit > 0 AND timeframe = 'daily' THEN (COALESCE(SUM(ac.cost_usd), 0) / cl.daily_limit * 100)
            WHEN cl.weekly_limit > 0 AND timeframe = 'weekly' THEN (COALESCE(SUM(ac.cost_usd), 0) / cl.weekly_limit * 100)
            WHEN cl.monthly_limit > 0 AND timeframe = 'monthly' THEN (COALESCE(SUM(ac.cost_usd), 0) / cl.monthly_limit * 100)
            ELSE 0
        END as utilization_percentage,
        CASE 
            WHEN COALESCE(SUM(ac.cost_usd), 0) >= 
                CASE 
                    WHEN timeframe = 'daily' THEN cl.daily_limit
                    WHEN timeframe = 'weekly' THEN cl.weekly_limit
                    WHEN timeframe = 'monthly' THEN cl.monthly_limit
                    ELSE cl.daily_limit
                END 
            THEN 'exhausted'
            WHEN COALESCE(SUM(ac.cost_usd), 0) >= 
                CASE 
                    WHEN timeframe = 'daily' THEN cl.daily_limit * 0.9
                    WHEN timeframe = 'weekly' THEN cl.weekly_limit * 0.9
                    WHEN timeframe = 'monthly' THEN cl.monthly_limit * 0.9
                    ELSE cl.daily_limit * 0.9
                END 
            THEN 'warning'
            ELSE 'normal'
        END as status
    FROM cost_limits cl
    LEFT JOIN autonomous_costs ac ON ac.repository_id = cl.repository_id
        AND ac.timestamp >= 
            CASE 
                WHEN timeframe = 'daily' THEN CURRENT_DATE
                WHEN timeframe = 'weekly' THEN DATE_TRUNC('week', CURRENT_DATE)
                WHEN timeframe = 'monthly' THEN DATE_TRUNC('month', CURRENT_DATE)
                ELSE CURRENT_DATE
            END
    WHERE cl.repository_id = repo_id
    GROUP BY cl.daily_limit, cl.weekly_limit, cl.monthly_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update updated_at timestamp
CREATE TRIGGER update_cost_limits_updated_at
    BEFORE UPDATE ON cost_limits
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cost_budgets_updated_at
    BEFORE UPDATE ON cost_budgets
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON cost_limits TO authenticated;
GRANT SELECT ON cost_alerts TO authenticated;
GRANT SELECT ON cost_predictions TO authenticated;
GRANT SELECT ON cost_budgets TO authenticated;
GRANT EXECUTE ON FUNCTION get_cost_summary(TEXT, TEXT) TO authenticated;
