import { jest } from '@jest/globals';
import { PlanAgent } from '../../planAgent.js';
import { CritiqueAgent } from '../../critiqueAgent.js';
import { ParallelOrchestrator } from '../../orchestration/ParallelOrchestrator.js';
import { StreamingManager } from '../../streaming/StreamingManager.js';
import { CostGuard } from '../../utils/CostGuard.js';
import { TokenMonitor } from '../../utils/TokenMonitor.js';
import { ProviderFactory } from '../../providers/ProviderFactory.js';
import { ProviderConfig, PlanRequest } from '../../types.js';

// Mock all provider implementations for integration testing
jest.mock('../../providers/OpenAIProvider.js');
jest.mock('../../providers/VertexAIProvider.js');
jest.mock('../../providers/AnthropicProvider.js');

describe('Dual Agent Integration Tests', () => {
  let planAgent: PlanAgent;
  let critiqueAgent: CritiqueAgent;
  let orchestrator: ParallelOrchestrator;
  let streamingManager: StreamingManager;
  let costGuard: CostGuard;
  let tokenMonitor: TokenMonitor;

  const mockProviderConfigs: ProviderConfig[] = [
    {
      type: 'openai',
      model: 'gpt-4o',
      temperature: 0.7,
      maxTokens: 2000,
      apiKey: 'mock-openai-key',
    },
    {
      type: 'vertex-ai',
      model: 'gemini-2.5-flash',
      temperature: 0.7,
      maxTokens: 2000,
      vertexAI: {
        projectId: 'mock-project',
        location: 'us-central1',
      },
    },
    {
      type: 'anthropic',
      model: 'claude-opus-4-20250514',
      temperature: 0.7,
      maxTokens: 2000,
      apiKey: 'mock-anthropic-key',
    },
  ];

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Initialize shared utilities
    costGuard = new CostGuard({ maxCostPerLoop: 5.0 });
    tokenMonitor = new TokenMonitor();
    streamingManager = new StreamingManager();

    // Mock provider responses
    const mockPatchResponse = {
      data: {
        operations: [
          { op: 'add', path: '/components/TestComponent', value: { name: 'TestComponent' } }
        ],
        description: 'Added test component',
        confidence: 0.9,
      },
      usage: {
        inputTokens: 100,
        outputTokens: 200,
        totalTokens: 300,
        cost: 0.01,
        provider: 'openai' as const,
        model: 'gpt-4o',
        timestamp: new Date(),
      },
      requestId: 'mock-request-id',
      latency: 1000,
    };

    const mockCritiqueResponse = {
      data: {
        score: 0.85,
        feedback: 'Good implementation with minor improvements needed',
        suggestions: ['Add error handling', 'Include tests'],
        isAcceptable: false,
      },
      usage: {
        inputTokens: 150,
        outputTokens: 100,
        totalTokens: 250,
        cost: 0.008,
        provider: 'openai' as const,
        model: 'gpt-4o',
        timestamp: new Date(),
      },
      requestId: 'mock-critique-id',
      latency: 800,
    };

    // Mock provider factory to return mock providers
    jest.spyOn(ProviderFactory, 'createProvider').mockImplementation((config) => {
      const mockProvider = {
        getType: () => config.type,
        getModel: () => config.model,
        getCost: () => 0,
        supportsStreaming: () => true,
        validateConfig: jest.fn().mockResolvedValue(true),
        generatePatch: jest.fn().mockResolvedValue(mockPatchResponse),
        scorePatch: jest.fn().mockResolvedValue(mockCritiqueResponse),
        streamGeneratePatch: jest.fn().mockImplementation(async function* () {
          yield { operations: [], description: 'Processing...', confidence: 0 };
          return mockPatchResponse;
        }),
        streamScorePatch: jest.fn().mockImplementation(async function* () {
          yield { score: 0, feedback: 'Analyzing...', suggestions: [], isAcceptable: false };
          return mockCritiqueResponse;
        }),
        getTokenUsage: () => ({
          inputTokens: 0,
          outputTokens: 0,
          totalTokens: 0,
          cost: 0,
          provider: config.type,
          model: config.model,
          timestamp: new Date(),
        }),
        resetMetrics: jest.fn(),
        updateConfig: jest.fn(),
      };
      return mockProvider as any;
    });

    // Initialize agents
    planAgent = new PlanAgent(
      { provider: mockProviderConfigs[0] },
      costGuard,
      tokenMonitor,
      streamingManager
    );

    critiqueAgent = new CritiqueAgent(
      { provider: mockProviderConfigs[0] },
      costGuard,
      tokenMonitor,
      streamingManager
    );

    // Initialize orchestrator
    orchestrator = new ParallelOrchestrator(
      streamingManager,
      costGuard,
      tokenMonitor
    );
  });

  describe('Basic Dual Agent Flow', () => {
    const testRequest: PlanRequest = {
      prompt: 'Create a user authentication system',
      context: { framework: 'React', database: 'PostgreSQL' },
    };

    it('should complete a basic plan-critique cycle', async () => {
      // Generate patch
      const patch = await planAgent.generatePatch(testRequest);
      expect(patch).toBeDefined();
      expect(patch.operations).toHaveLength(1);
      expect(patch.confidence).toBe(0.9);

      // Critique patch
      const critique = await critiqueAgent.scorePatch({
        patch,
        originalPrompt: testRequest.prompt,
        context: testRequest.context,
      });

      expect(critique).toBeDefined();
      expect(critique.score).toBe(0.85);
      expect(critique.isAcceptable).toBe(false);
      expect(critique.suggestions).toHaveLength(2);
    });

    it('should track costs and tokens correctly', async () => {
      const initialCost = costGuard.getCurrentCost();
      const initialTokens = tokenMonitor.getStats().totalTokens;

      await planAgent.generatePatch(testRequest);
      await critiqueAgent.scorePatch({
        patch: {
          operations: [{ op: 'add', path: '/test', value: 'test' }],
          description: 'Test patch',
          confidence: 0.8,
        },
        originalPrompt: testRequest.prompt,
      });

      const finalCost = costGuard.getCurrentCost();
      const finalTokens = tokenMonitor.getStats().totalTokens;

      expect(finalCost).toBeGreaterThan(initialCost);
      expect(finalTokens).toBeGreaterThan(initialTokens);
    });

    it('should provide agent statistics', () => {
      const planStats = planAgent.getStats();
      const critiqueStats = critiqueAgent.getStats();

      expect(planStats).toHaveProperty('totalCost');
      expect(planStats).toHaveProperty('totalTokens');
      expect(planStats).toHaveProperty('requestCount');
      expect(planStats).toHaveProperty('averageCost');

      expect(critiqueStats).toHaveProperty('totalCost');
      expect(critiqueStats).toHaveProperty('totalTokens');
      expect(critiqueStats).toHaveProperty('requestCount');
      expect(critiqueStats).toHaveProperty('averageCost');
    });
  });

  describe('Streaming Integration', () => {
    const testRequest: PlanRequest = {
      prompt: 'Implement real-time notifications',
      context: { technology: 'WebSockets' },
    };

    it('should stream patch generation', async () => {
      const chunks: any[] = [];
      const streamGenerator = planAgent.generatePatchStream(testRequest);

      for await (const chunk of streamGenerator) {
        chunks.push(chunk);
      }

      expect(chunks.length).toBeGreaterThan(0);
      const finalChunk = chunks[chunks.length - 1];
      expect(finalChunk).toHaveProperty('operations');
      expect(finalChunk).toHaveProperty('description');
      expect(finalChunk).toHaveProperty('confidence');
    });

    it('should stream critique generation', async () => {
      const chunks: any[] = [];
      const streamGenerator = critiqueAgent.scorePatchStream({
        patch: {
          operations: [{ op: 'add', path: '/notifications', value: { system: 'WebSocket' } }],
          description: 'Added notification system',
          confidence: 0.8,
        },
        originalPrompt: testRequest.prompt,
      });

      for await (const chunk of streamGenerator) {
        chunks.push(chunk);
      }

      expect(chunks.length).toBeGreaterThan(0);
      const finalChunk = chunks[chunks.length - 1];
      expect(finalChunk).toHaveProperty('score');
      expect(finalChunk).toHaveProperty('feedback');
      expect(finalChunk).toHaveProperty('suggestions');
    });
  });

  describe('Parallel Orchestration', () => {
    const testRequest: PlanRequest = {
      prompt: 'Build a REST API with authentication',
      context: { framework: 'Express.js', auth: 'JWT' },
    };

    it('should execute dual agent loop with orchestrator', async () => {
      const providers = mockProviderConfigs.map(config => 
        ProviderFactory.createProvider(config)
      );

      const result = await orchestrator.executeDualAgentLoop(
        providers,
        providers,
        testRequest,
        2 // max iterations
      );

      expect(result).toBeDefined();
      expect(result.finalPatch).toBeDefined();
      expect(result.iterations).toHaveLength(1); // Should stop after first acceptable result
      expect(result.totalLatency).toBeGreaterThan(0);
      expect(result.totalCost).toBeGreaterThan(0);
    });

    it('should handle parallel execution strategies', async () => {
      const providers = mockProviderConfigs.map(config => 
        ProviderFactory.createProvider(config)
      );

      const sequentialResult = await orchestrator.generatePatch(
        providers,
        testRequest,
        'sequential'
      );

      const parallelResult = await orchestrator.generatePatch(
        providers,
        testRequest,
        'parallel'
      );

      expect(sequentialResult.result).toBeDefined();
      expect(parallelResult.result).toBeDefined();
      expect(sequentialResult.wasParallel).toBe(false);
      expect(parallelResult.wasParallel).toBe(true);
    });

    it('should provide execution statistics', () => {
      const stats = orchestrator.getExecutionStats();

      expect(stats).toHaveProperty('totalExecutions');
      expect(stats).toHaveProperty('strategyDistribution');
      expect(stats).toHaveProperty('averageLatency');
      expect(stats).toHaveProperty('averageCost');
      expect(stats).toHaveProperty('successRate');
      expect(stats).toHaveProperty('parallelEfficiency');
    });
  });

  describe('Cost Guard Integration', () => {
    it('should enforce cost limits', async () => {
      // Set a very low cost limit
      const restrictiveCostGuard = new CostGuard({ maxCostPerLoop: 0.001 });
      
      const restrictedAgent = new PlanAgent(
        { provider: mockProviderConfigs[0] },
        restrictiveCostGuard
      );

      // First request should work
      await expect(restrictedAgent.generatePatch({
        prompt: 'Simple test',
      })).resolves.toBeDefined();

      // Second request should fail due to cost limit
      await expect(restrictedAgent.generatePatch({
        prompt: 'Another test',
      })).rejects.toThrow('Cost limit exceeded');
    });

    it('should track cost by provider', () => {
      const stats = costGuard.getStats();
      
      expect(stats.costByProvider).toBeInstanceOf(Map);
      expect(stats.totalCost).toBeGreaterThanOrEqual(0);
      expect(stats.remainingBudget).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Token Monitor Integration', () => {
    it('should track token usage across operations', async () => {
      const initialStats = tokenMonitor.getStats();
      
      await planAgent.generatePatch({
        prompt: 'Test prompt for token tracking',
      });

      const finalStats = tokenMonitor.getStats();
      
      expect(finalStats.totalTokens).toBeGreaterThan(initialStats.totalTokens);
      expect(finalStats.requestCount).toBeGreaterThan(initialStats.requestCount);
    });

    it('should provide usage by provider', () => {
      const usageByProvider = tokenMonitor.getUsageByProvider();
      
      expect(usageByProvider).toBeInstanceOf(Map);
    });

    it('should calculate efficiency metrics', () => {
      const efficiency = tokenMonitor.getEfficiencyMetrics();
      
      expect(efficiency).toHaveProperty('tokensPerDollar');
      expect(efficiency).toHaveProperty('costPerToken');
      expect(efficiency).toHaveProperty('averageRequestSize');
    });
  });

  describe('Error Handling', () => {
    it('should handle provider failures gracefully', async () => {
      // Mock a provider that fails
      const failingProvider = {
        getType: () => 'openai' as const,
        getModel: () => 'gpt-4o',
        getCost: () => 0,
        supportsStreaming: () => false,
        validateConfig: jest.fn().mockResolvedValue(false),
        generatePatch: jest.fn().mockRejectedValue(new Error('Provider failed')),
        scorePatch: jest.fn().mockRejectedValue(new Error('Provider failed')),
        getTokenUsage: () => ({
          inputTokens: 0,
          outputTokens: 0,
          totalTokens: 0,
          cost: 0,
          provider: 'openai' as const,
          model: 'gpt-4o',
          timestamp: new Date(),
        }),
        resetMetrics: jest.fn(),
        updateConfig: jest.fn(),
      };

      jest.spyOn(ProviderFactory, 'createProvider').mockReturnValue(failingProvider as any);

      const failingAgent = new PlanAgent({ provider: mockProviderConfigs[0] });

      await expect(failingAgent.generatePatch({
        prompt: 'This should fail',
      })).rejects.toThrow('Provider failed');
    });

    it('should validate agent configurations', () => {
      expect(() => new PlanAgent({
        provider: {
          type: 'openai',
          model: 'gpt-4o',
          temperature: 0.7,
          maxTokens: 2000,
          apiKey: 'test-key',
        }
      })).not.toThrow();
    });
  });
});
