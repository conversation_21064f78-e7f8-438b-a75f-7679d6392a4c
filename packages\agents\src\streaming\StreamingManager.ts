import { EventEmitter } from 'events';
import { AgentProvider } from '../providers/AgentProvider.js';
import { JSONPatch, CritiqueResult, ProviderType } from '../types.js';

/**
 * Streaming event types
 */
export type StreamingEventType = 
  | 'stream_start'
  | 'stream_chunk'
  | 'stream_complete'
  | 'stream_error'
  | 'provider_switch'
  | 'cost_update'
  | 'token_update';

/**
 * Streaming event data
 */
export interface StreamingEvent {
  type: StreamingEventType;
  provider: ProviderType;
  operation: 'generatePatch' | 'scorePatch';
  data?: any;
  error?: Error;
  timestamp: Date;
  requestId?: string;
  metadata?: Record<string, any>;
}

/**
 * Streaming chunk for patch generation
 */
export interface PatchStreamChunk {
  operations?: any[];
  description?: string;
  confidence?: number;
  partial: boolean;
  complete: boolean;
}

/**
 * Streaming chunk for critique
 */
export interface CritiqueStreamChunk {
  score?: number;
  feedback?: string;
  suggestions?: string[];
  isAcceptable?: boolean;
  partial: boolean;
  complete: boolean;
}

/**
 * Server-Sent Events (SSE) formatter
 */
export class SSEFormatter {
  /**
   * Format streaming event as SSE
   */
  static formatEvent(event: StreamingEvent): string {
    const data = {
      type: event.type,
      provider: event.provider,
      operation: event.operation,
      data: event.data,
      error: event.error ? {
        message: event.error.message,
        name: event.error.name,
        stack: event.error.stack,
      } : undefined,
      timestamp: event.timestamp.toISOString(),
      requestId: event.requestId,
      metadata: event.metadata,
    };

    return `data: ${JSON.stringify(data)}\n\n`;
  }

  /**
   * Format multiple events as SSE stream
   */
  static formatEvents(events: StreamingEvent[]): string {
    return events.map(event => this.formatEvent(event)).join('');
  }

  /**
   * Create SSE headers
   */
  static getSSEHeaders(): Record<string, string> {
    return {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
    };
  }
}

/**
 * Streaming manager for agent operations
 */
export class StreamingManager extends EventEmitter {
  private activeStreams: Map<string, AbortController> = new Map();
  private streamHistory: Map<string, StreamingEvent[]> = new Map();

  constructor() {
    super();
    this.setMaxListeners(100); // Allow many concurrent streams
  }

  /**
   * Start streaming patch generation
   */
  async *streamPatchGeneration(
    provider: AgentProvider,
    request: any,
    requestId: string = this.generateRequestId()
  ): AsyncGenerator<PatchStreamChunk, JSONPatch, unknown> {
    const abortController = new AbortController();
    this.activeStreams.set(requestId, abortController);

    try {
      // Emit stream start event
      this.emitEvent({
        type: 'stream_start',
        provider: provider.getType(),
        operation: 'generatePatch',
        requestId,
        timestamp: new Date(),
      });

      if (provider.supportsStreaming()) {
        // Use provider's streaming capability
        const streamGenerator = provider.streamGeneratePatch(request);
        let accumulated: Partial<JSONPatch> = {};

        for await (const chunk of streamGenerator) {
          if (abortController.signal.aborted) {
            throw new Error('Stream aborted');
          }

          // Merge chunk with accumulated data
          accumulated = { ...accumulated, ...chunk };

          const streamChunk: PatchStreamChunk = {
            operations: chunk.operations,
            description: chunk.description,
            confidence: chunk.confidence,
            partial: true,
            complete: false,
          };

          // Emit chunk event
          this.emitEvent({
            type: 'stream_chunk',
            provider: provider.getType(),
            operation: 'generatePatch',
            data: streamChunk,
            requestId,
            timestamp: new Date(),
          });

          yield streamChunk;
        }

        // Get final result
        const finalResult = await streamGenerator.return(undefined);
        const finalPatch = finalResult.value?.data || accumulated as JSONPatch;

        // Emit completion event
        this.emitEvent({
          type: 'stream_complete',
          provider: provider.getType(),
          operation: 'generatePatch',
          data: finalPatch,
          requestId,
          timestamp: new Date(),
        });

        return finalPatch;
      } else {
        // Fallback to non-streaming with simulated chunks
        const result = await provider.generatePatch(request);
        
        // Simulate streaming by yielding partial data
        const patch = result.data;
        const totalOps = patch.operations.length;
        
        for (let i = 0; i < totalOps; i++) {
          if (abortController.signal.aborted) {
            throw new Error('Stream aborted');
          }

          const partialChunk: PatchStreamChunk = {
            operations: patch.operations.slice(0, i + 1),
            description: i === totalOps - 1 ? patch.description : `Processing operation ${i + 1}/${totalOps}...`,
            confidence: i === totalOps - 1 ? patch.confidence : undefined,
            partial: i < totalOps - 1,
            complete: i === totalOps - 1,
          };

          this.emitEvent({
            type: 'stream_chunk',
            provider: provider.getType(),
            operation: 'generatePatch',
            data: partialChunk,
            requestId,
            timestamp: new Date(),
          });

          yield partialChunk;

          // Add small delay to simulate streaming
          await this.sleep(100);
        }

        this.emitEvent({
          type: 'stream_complete',
          provider: provider.getType(),
          operation: 'generatePatch',
          data: patch,
          requestId,
          timestamp: new Date(),
        });

        return patch;
      }
    } catch (error) {
      const streamError = error instanceof Error ? error : new Error(String(error));
      
      this.emitEvent({
        type: 'stream_error',
        provider: provider.getType(),
        operation: 'generatePatch',
        error: streamError,
        requestId,
        timestamp: new Date(),
      });

      throw streamError;
    } finally {
      this.activeStreams.delete(requestId);
    }
  }

  /**
   * Start streaming critique
   */
  async *streamCritique(
    provider: AgentProvider,
    request: any,
    requestId: string = this.generateRequestId()
  ): AsyncGenerator<CritiqueStreamChunk, CritiqueResult, unknown> {
    const abortController = new AbortController();
    this.activeStreams.set(requestId, abortController);

    try {
      this.emitEvent({
        type: 'stream_start',
        provider: provider.getType(),
        operation: 'scorePatch',
        requestId,
        timestamp: new Date(),
      });

      if (provider.supportsStreaming()) {
        const streamGenerator = provider.streamScorePatch(request);
        let accumulated: Partial<CritiqueResult> = {};

        for await (const chunk of streamGenerator) {
          if (abortController.signal.aborted) {
            throw new Error('Stream aborted');
          }

          accumulated = { ...accumulated, ...chunk };

          const streamChunk: CritiqueStreamChunk = {
            score: chunk.score,
            feedback: chunk.feedback,
            suggestions: chunk.suggestions,
            isAcceptable: chunk.isAcceptable,
            partial: true,
            complete: false,
          };

          this.emitEvent({
            type: 'stream_chunk',
            provider: provider.getType(),
            operation: 'scorePatch',
            data: streamChunk,
            requestId,
            timestamp: new Date(),
          });

          yield streamChunk;
        }

        const finalResult = await streamGenerator.return(undefined);
        const finalCritique = finalResult.value?.data || accumulated as CritiqueResult;

        this.emitEvent({
          type: 'stream_complete',
          provider: provider.getType(),
          operation: 'scorePatch',
          data: finalCritique,
          requestId,
          timestamp: new Date(),
        });

        return finalCritique;
      } else {
        // Fallback to non-streaming with simulated progress
        const result = await provider.scorePatch(request);
        const critique = result.data;

        // Simulate streaming critique analysis
        const steps = [
          { feedback: 'Analyzing correctness...', partial: true },
          { feedback: 'Evaluating completeness...', partial: true },
          { feedback: 'Checking quality and best practices...', partial: true },
          { feedback: 'Assessing safety considerations...', partial: true },
          { ...critique, partial: false, complete: true },
        ];

        for (let i = 0; i < steps.length; i++) {
          if (abortController.signal.aborted) {
            throw new Error('Stream aborted');
          }

          const step = steps[i];
          const streamChunk: CritiqueStreamChunk = {
            score: step.partial ? undefined : critique.score,
            feedback: step.feedback,
            suggestions: step.partial ? [] : critique.suggestions,
            isAcceptable: step.partial ? undefined : critique.isAcceptable,
            partial: step.partial,
            complete: !step.partial,
          };

          this.emitEvent({
            type: 'stream_chunk',
            provider: provider.getType(),
            operation: 'scorePatch',
            data: streamChunk,
            requestId,
            timestamp: new Date(),
          });

          yield streamChunk;

          if (!step.partial) break;
          await this.sleep(200);
        }

        this.emitEvent({
          type: 'stream_complete',
          provider: provider.getType(),
          operation: 'scorePatch',
          data: critique,
          requestId,
          timestamp: new Date(),
        });

        return critique;
      }
    } catch (error) {
      const streamError = error instanceof Error ? error : new Error(String(error));
      
      this.emitEvent({
        type: 'stream_error',
        provider: provider.getType(),
        operation: 'scorePatch',
        error: streamError,
        requestId,
        timestamp: new Date(),
      });

      throw streamError;
    } finally {
      this.activeStreams.delete(requestId);
    }
  }

  /**
   * Abort a streaming operation
   */
  abortStream(requestId: string): boolean {
    const controller = this.activeStreams.get(requestId);
    if (controller) {
      controller.abort();
      this.activeStreams.delete(requestId);
      return true;
    }
    return false;
  }

  /**
   * Get active stream count
   */
  getActiveStreamCount(): number {
    return this.activeStreams.size;
  }

  /**
   * Get stream history
   */
  getStreamHistory(requestId: string): StreamingEvent[] {
    return this.streamHistory.get(requestId) || [];
  }

  /**
   * Clear stream history
   */
  clearHistory(): void {
    this.streamHistory.clear();
  }

  /**
   * Create SSE response stream
   */
  createSSEStream(): {
    stream: ReadableStream<Uint8Array>;
    controller: ReadableStreamDefaultController<Uint8Array>;
  } {
    let controller: ReadableStreamDefaultController<Uint8Array>;

    const stream = new ReadableStream<Uint8Array>({
      start(ctrl) {
        controller = ctrl;
        
        // Send initial connection event
        const initialEvent = SSEFormatter.formatEvent({
          type: 'stream_start',
          provider: 'system' as ProviderType,
          operation: 'generatePatch',
          data: { message: 'SSE connection established' },
          timestamp: new Date(),
        });
        
        controller.enqueue(new TextEncoder().encode(initialEvent));
      },
      cancel() {
        // Cleanup when stream is cancelled
      }
    });

    // Listen for streaming events and forward to SSE
    this.on('streaming_event', (event: StreamingEvent) => {
      if (controller) {
        const sseData = SSEFormatter.formatEvent(event);
        controller.enqueue(new TextEncoder().encode(sseData));
      }
    });

    return { stream, controller: controller! };
  }

  private emitEvent(event: StreamingEvent): void {
    // Store in history
    const history = this.streamHistory.get(event.requestId || 'unknown') || [];
    history.push(event);
    this.streamHistory.set(event.requestId || 'unknown', history);

    // Emit to listeners
    this.emit('streaming_event', event);
    this.emit(event.type, event);
  }

  private generateRequestId(): string {
    return `stream_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
