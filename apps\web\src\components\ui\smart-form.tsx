import * as React from "react"
import { cn } from "@/lib/utils"
import { CheckIcon, ErrorIcon, WarningIcon, InfoIcon, LoadingIcon, ChevronDownIcon, UploadIcon } from "@/components/ui/icon"
import { Button } from "@/components/ui/button"
import { VStack, HStack } from "@/components/ui/layout"
import { Typography } from "@/components/ui/typography"

// Form validation types
type ValidationRule = {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: any) => string | null
}

type ValidationResult = {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

// Smart input component with validation
interface SmartInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  description?: string
  validation?: ValidationRule
  showValidation?: boolean
  onValidationChange?: (result: ValidationResult) => void
}

export function SmartInput({
  label,
  description,
  validation,
  showValidation = true,
  onValidationChange,
  className,
  ...props
}: SmartInputProps) {
  const [value, setValue] = React.useState(props.value || "")
  const [touched, setTouched] = React.useState(false)
  const [validationResult, setValidationResult] = React.useState<ValidationResult>({
    isValid: true,
    errors: [],
    warnings: []
  })

  const validateValue = React.useCallback((val: string) => {
    if (!validation) return { isValid: true, errors: [], warnings: [] }

    const errors: string[] = []
    const warnings: string[] = []

    // Required validation
    if (validation.required && !val.trim()) {
      errors.push("This field is required")
    }

    // Length validations
    if (validation.minLength && val.length < validation.minLength) {
      errors.push(`Minimum length is ${validation.minLength} characters`)
    }

    if (validation.maxLength && val.length > validation.maxLength) {
      errors.push(`Maximum length is ${validation.maxLength} characters`)
    }

    // Pattern validation
    if (validation.pattern && val && !validation.pattern.test(val)) {
      errors.push("Invalid format")
    }

    // Custom validation
    if (validation.custom && val) {
      const customError = validation.custom(val)
      if (customError) {
        errors.push(customError)
      }
    }

    // Warnings for length approaching limit
    if (validation.maxLength && val.length > validation.maxLength * 0.8) {
      warnings.push(`Approaching character limit (${val.length}/${validation.maxLength})`)
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }, [validation])

  React.useEffect(() => {
    const result = validateValue(value as string)
    setValidationResult(result)
    onValidationChange?.(result)
  }, [value, validateValue, onValidationChange])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setValue(newValue)
    props.onChange?.(e)
  }

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setTouched(true)
    props.onBlur?.(e)
  }

  const showErrors = touched && showValidation && validationResult.errors.length > 0
  const showWarnings = showValidation && validationResult.warnings.length > 0

  return (
    <VStack spacing="xs" className="w-full">
      {label && (
        <label className="text-body-sm font-medium text-foreground">
          {label}
          {validation?.required && <span className="text-destructive ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        <input
          {...props}
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          className={cn(
            "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
            {
              "border-destructive focus-visible:ring-destructive": showErrors,
              "border-warning focus-visible:ring-warning": showWarnings && !showErrors,
              "border-success focus-visible:ring-success": touched && validationResult.isValid && value,
            },
            className
          )}
        />
        
        {/* Validation indicator */}
        {touched && showValidation && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {validationResult.errors.length > 0 ? (
              <ErrorIcon size="sm" className="text-destructive" />
            ) : validationResult.warnings.length > 0 ? (
              <WarningIcon size="sm" className="text-warning" />
            ) : value ? (
              <CheckIcon size="sm" className="text-success" />
            ) : null}
          </div>
        )}
      </div>

      {description && !showErrors && !showWarnings && (
        <Typography variant="caption" className="text-muted-foreground">
          {description}
        </Typography>
      )}

      {/* Validation messages */}
      {showErrors && (
        <VStack spacing="xs">
          {validationResult.errors.map((error, index) => (
            <HStack key={index} spacing="xs" align="center">
              <ErrorIcon size="xs" className="text-destructive flex-shrink-0" />
              <Typography variant="caption" className="text-destructive">
                {error}
              </Typography>
            </HStack>
          ))}
        </VStack>
      )}

      {showWarnings && !showErrors && (
        <VStack spacing="xs">
          {validationResult.warnings.map((warning, index) => (
            <HStack key={index} spacing="xs" align="center">
              <WarningIcon size="xs" className="text-warning flex-shrink-0" />
              <Typography variant="caption" className="text-warning">
                {warning}
              </Typography>
            </HStack>
          ))}
        </VStack>
      )}
    </VStack>
  )
}

// Smart textarea with auto-resize and validation
interface SmartTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string
  description?: string
  validation?: ValidationRule
  showValidation?: boolean
  autoResize?: boolean
  onValidationChange?: (result: ValidationResult) => void
}

export function SmartTextarea({
  label,
  description,
  validation,
  showValidation = true,
  autoResize = true,
  onValidationChange,
  className,
  ...props
}: SmartTextareaProps) {
  const textareaRef = React.useRef<HTMLTextAreaElement>(null)
  const [value, setValue] = React.useState(props.value || "")
  const [touched, setTouched] = React.useState(false)
  const [validationResult, setValidationResult] = React.useState<ValidationResult>({
    isValid: true,
    errors: [],
    warnings: []
  })

  const validateValue = React.useCallback((val: string) => {
    if (!validation) return { isValid: true, errors: [], warnings: [] }

    const errors: string[] = []
    const warnings: string[] = []

    if (validation.required && !val.trim()) {
      errors.push("This field is required")
    }

    if (validation.minLength && val.length < validation.minLength) {
      errors.push(`Minimum length is ${validation.minLength} characters`)
    }

    if (validation.maxLength && val.length > validation.maxLength) {
      errors.push(`Maximum length is ${validation.maxLength} characters`)
    }

    if (validation.custom && val) {
      const customError = validation.custom(val)
      if (customError) {
        errors.push(customError)
      }
    }

    if (validation.maxLength && val.length > validation.maxLength * 0.8) {
      warnings.push(`${val.length}/${validation.maxLength} characters`)
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }, [validation])

  React.useEffect(() => {
    const result = validateValue(value as string)
    setValidationResult(result)
    onValidationChange?.(result)
  }, [value, validateValue, onValidationChange])

  React.useEffect(() => {
    if (autoResize && textareaRef.current) {
      const textarea = textareaRef.current
      textarea.style.height = 'auto'
      textarea.style.height = `${textarea.scrollHeight}px`
    }
  }, [value, autoResize])

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value
    setValue(newValue)
    props.onChange?.(e)
  }

  const handleBlur = (e: React.FocusEvent<HTMLTextAreaElement>) => {
    setTouched(true)
    props.onBlur?.(e)
  }

  const showErrors = touched && showValidation && validationResult.errors.length > 0
  const showWarnings = showValidation && validationResult.warnings.length > 0

  return (
    <VStack spacing="xs" className="w-full">
      {label && (
        <label className="text-body-sm font-medium text-foreground">
          {label}
          {validation?.required && <span className="text-destructive ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        <textarea
          ref={textareaRef}
          {...props}
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          className={cn(
            "flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
            {
              "border-destructive focus-visible:ring-destructive": showErrors,
              "border-warning focus-visible:ring-warning": showWarnings && !showErrors,
              "border-success focus-visible:ring-success": touched && validationResult.isValid && value,
              "resize-none": autoResize,
            },
            className
          )}
        />
        
        {/* Character count */}
        {validation?.maxLength && (
          <div className="absolute bottom-2 right-2 text-xs text-muted-foreground bg-background px-1 rounded">
            {(value as string).length}/{validation.maxLength}
          </div>
        )}
      </div>

      {description && !showErrors && !showWarnings && (
        <Typography variant="caption" className="text-muted-foreground">
          {description}
        </Typography>
      )}

      {/* Validation messages */}
      {showErrors && (
        <VStack spacing="xs">
          {validationResult.errors.map((error, index) => (
            <HStack key={index} spacing="xs" align="center">
              <ErrorIcon size="xs" className="text-destructive flex-shrink-0" />
              <Typography variant="caption" className="text-destructive">
                {error}
              </Typography>
            </HStack>
          ))}
        </VStack>
      )}

      {showWarnings && !showErrors && (
        <VStack spacing="xs">
          {validationResult.warnings.map((warning, index) => (
            <HStack key={index} spacing="xs" align="center">
              <WarningIcon size="xs" className="text-warning flex-shrink-0" />
              <Typography variant="caption" className="text-warning">
                {warning}
              </Typography>
            </HStack>
          ))}
        </VStack>
      )}
    </VStack>
  )
}

// Multi-select component
interface MultiSelectOption {
  value: string
  label: string
  disabled?: boolean
}

interface MultiSelectProps {
  options: MultiSelectOption[]
  value: string[]
  onChange: (value: string[]) => void
  placeholder?: string
  label?: string
  description?: string
  className?: string
}

export function MultiSelect({
  options,
  value,
  onChange,
  placeholder = "Select options...",
  label,
  description,
  className
}: MultiSelectProps) {
  const [isOpen, setIsOpen] = React.useState(false)

  const toggleOption = (optionValue: string) => {
    const newValue = value.includes(optionValue)
      ? value.filter(v => v !== optionValue)
      : [...value, optionValue]
    onChange(newValue)
  }

  const selectedLabels = options
    .filter(option => value.includes(option.value))
    .map(option => option.label)

  return (
    <VStack spacing="xs" className={className}>
      {label && (
        <label className="text-body-sm font-medium text-foreground">
          {label}
        </label>
      )}

      <div className="relative">
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
        >
          <span className={selectedLabels.length > 0 ? "text-foreground" : "text-muted-foreground"}>
            {selectedLabels.length > 0
              ? selectedLabels.length === 1
                ? selectedLabels[0]
                : `${selectedLabels.length} selected`
              : placeholder
            }
          </span>
          <ChevronDownIcon className="h-4 w-4 opacity-50" />
        </button>

        {isOpen && (
          <div className="absolute top-full left-0 right-0 z-50 mt-1 max-h-60 overflow-auto rounded-md border bg-popover shadow-md">
            {options.map((option) => (
              <div
                key={option.value}
                className={cn(
                  "flex items-center space-x-2 px-3 py-2 cursor-pointer hover:bg-accent",
                  option.disabled && "opacity-50 cursor-not-allowed"
                )}
                onClick={() => !option.disabled && toggleOption(option.value)}
              >
                <div className={cn(
                  "w-4 h-4 border rounded flex items-center justify-center",
                  value.includes(option.value) && "bg-primary border-primary"
                )}>
                  {value.includes(option.value) && (
                    <CheckIcon size="xs" className="text-primary-foreground" />
                  )}
                </div>
                <span className="text-sm">{option.label}</span>
              </div>
            ))}
          </div>
        )}
      </div>

      {description && (
        <Typography variant="caption" className="text-muted-foreground">
          {description}
        </Typography>
      )}
    </VStack>
  )
}

// File upload component
interface FileUploadProps {
  onFileSelect: (files: File[]) => void
  accept?: string
  multiple?: boolean
  maxSize?: number // in bytes
  label?: string
  description?: string
  className?: string
}

export function FileUpload({
  onFileSelect,
  accept,
  multiple = false,
  maxSize,
  label,
  description,
  className
}: FileUploadProps) {
  const [dragActive, setDragActive] = React.useState(false)
  const inputRef = React.useRef<HTMLInputElement>(null)

  const handleFiles = (files: FileList | null) => {
    if (!files) return

    const fileArray = Array.from(files)
    const validFiles = fileArray.filter(file => {
      if (maxSize && file.size > maxSize) {
        console.warn(`File ${file.name} exceeds maximum size`)
        return false
      }
      return true
    })

    onFileSelect(validFiles)
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    handleFiles(e.dataTransfer.files)
  }

  return (
    <VStack spacing="xs" className={className}>
      {label && (
        <label className="text-body-sm font-medium text-foreground">
          {label}
        </label>
      )}

      <div
        className={cn(
          "border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors",
          dragActive ? "border-primary bg-primary/5" : "border-border hover:border-border/80"
        )}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={() => inputRef.current?.click()}
      >
        <VStack spacing="sm" align="center">
          <UploadIcon className="text-muted-foreground" />
          <VStack spacing="xs" align="center">
            <Typography variant="body-sm" className="font-medium">
              Drop files here or click to browse
            </Typography>
            <Typography variant="caption" className="text-muted-foreground">
              {accept && `Accepted formats: ${accept}`}
              {maxSize && ` • Max size: ${(maxSize / 1024 / 1024).toFixed(1)}MB`}
            </Typography>
          </VStack>
        </VStack>

        <input
          ref={inputRef}
          type="file"
          accept={accept}
          multiple={multiple}
          onChange={(e) => handleFiles(e.target.files)}
          className="hidden"
        />
      </div>

      {description && (
        <Typography variant="caption" className="text-muted-foreground">
          {description}
        </Typography>
      )}
    </VStack>
  )
}

export { type ValidationRule, type ValidationResult }
