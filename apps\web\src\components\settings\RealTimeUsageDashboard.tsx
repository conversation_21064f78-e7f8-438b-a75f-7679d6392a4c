import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { 
  Activity, 
  DollarSign, 
  Zap, 
  TrendingUp, 
  TrendingDown,
  Clock,
  BarChart3,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Wifi,
  WifiOff
} from 'lucide-react';

interface UsageData {
  currentCost: number;
  tokensUsed: number;
  requestsToday: number;
  costToday: number;
  hourlyTokens: number;
  requestsPerMinute: number;
}

interface ProviderHealth {
  isHealthy: boolean;
  lastCheck: Date;
  responseTime: number;
}

interface RealTimeUsageDashboardProps {
  costLimit: number;
  tokenLimit: number;
  refreshInterval?: number;
  onAlert?: (alert: { type: 'cost' | 'tokens' | 'health'; message: string }) => void;
}

export const RealTimeUsageDashboard: React.FC<RealTimeUsageDashboardProps> = ({
  costLimit,
  tokenLimit,
  refreshInterval = 30000, // 30 seconds
  onAlert,
}) => {
  const [usageData, setUsageData] = useState<UsageData>({
    currentCost: 0,
    tokensUsed: 0,
    requestsToday: 0,
    costToday: 0,
    hourlyTokens: 0,
    requestsPerMinute: 0,
  });

  const [providerHealth, setProviderHealth] = useState<Record<string, ProviderHealth>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [isConnected, setIsConnected] = useState(true);

  // Simulate real-time data updates
  useEffect(() => {
    const updateData = () => {
      setUsageData(prev => ({
        currentCost: Math.max(0, prev.currentCost + (Math.random() - 0.5) * 0.1),
        tokensUsed: prev.tokensUsed + Math.floor(Math.random() * 100),
        requestsToday: prev.requestsToday + (Math.random() > 0.8 ? 1 : 0),
        costToday: Math.max(0, prev.costToday + (Math.random() - 0.3) * 0.05),
        hourlyTokens: Math.floor(Math.random() * 5000),
        requestsPerMinute: Math.floor(Math.random() * 10),
      }));

      setProviderHealth({
        'openai': {
          isHealthy: Math.random() > 0.1,
          lastCheck: new Date(),
          responseTime: 200 + Math.random() * 300,
        },
        'vertex-ai': {
          isHealthy: Math.random() > 0.15,
          lastCheck: new Date(),
          responseTime: 300 + Math.random() * 400,
        },
        'anthropic': {
          isHealthy: Math.random() > 0.2,
          lastCheck: new Date(),
          responseTime: 250 + Math.random() * 350,
        },
      });

      setLastUpdate(new Date());
      setIsLoading(false);
    };

    // Initial load
    updateData();

    // Set up interval
    const interval = setInterval(updateData, refreshInterval);

    return () => clearInterval(interval);
  }, [refreshInterval]);

  // Check for alerts
  useEffect(() => {
    if (!onAlert) return;

    const costPercentage = (usageData.currentCost / costLimit) * 100;
    const tokenPercentage = (usageData.hourlyTokens / tokenLimit) * 100;

    if (costPercentage > 80) {
      onAlert({
        type: 'cost',
        message: `Cost usage at ${costPercentage.toFixed(1)}% of limit`,
      });
    }

    if (tokenPercentage > 80) {
      onAlert({
        type: 'tokens',
        message: `Token usage at ${tokenPercentage.toFixed(1)}% of hourly limit`,
      });
    }

    const unhealthyProviders = Object.entries(providerHealth)
      .filter(([_, health]) => !health.isHealthy)
      .map(([provider]) => provider);

    if (unhealthyProviders.length > 0) {
      onAlert({
        type: 'health',
        message: `Providers unhealthy: ${unhealthyProviders.join(', ')}`,
      });
    }
  }, [usageData, providerHealth, costLimit, tokenLimit, onAlert]);

  const costPercentage = Math.min((usageData.currentCost / costLimit) * 100, 100);
  const tokenPercentage = Math.min((usageData.hourlyTokens / tokenLimit) * 100, 100);
  const isNearCostLimit = costPercentage > 80;
  const isNearTokenLimit = tokenPercentage > 80;

  const getHealthIcon = (isHealthy: boolean) => {
    return isHealthy ? (
      <CheckCircle className="w-4 h-4 text-green-400" />
    ) : (
      <AlertTriangle className="w-4 h-4 text-red-400" />
    );
  };

  const formatCurrency = (amount: number) => `$${amount.toFixed(4)}`;
  const formatNumber = (num: number) => num.toLocaleString();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Activity className="w-5 h-5 text-blue-400" />
          <h3 className="text-lg font-semibold text-white">Real-time Usage Dashboard</h3>
          <Badge variant={isConnected ? "default" : "destructive"} className="text-xs">
            {isConnected ? (
              <>
                <Wifi className="w-3 h-3 mr-1" />
                Live
              </>
            ) : (
              <>
                <WifiOff className="w-3 h-3 mr-1" />
                Offline
              </>
            )}
          </Badge>
        </div>
        <div className="flex items-center gap-2 text-sm text-slate-400">
          <Clock className="w-4 h-4" />
          Last updated: {lastUpdate.toLocaleTimeString()}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {/* Current Cost */}
        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-2">
              <DollarSign className="w-5 h-5 text-green-400" />
              <Badge variant={isNearCostLimit ? "destructive" : "secondary"} className="text-xs">
                {costPercentage.toFixed(1)}%
              </Badge>
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold text-white">
                {formatCurrency(usageData.currentCost)}
              </p>
              <p className="text-xs text-slate-400">Current Loop Cost</p>
              <Progress 
                value={costPercentage} 
                className={`h-1 ${isNearCostLimit ? 'bg-red-900' : 'bg-slate-700'}`}
              />
            </div>
          </CardContent>
        </Card>

        {/* Tokens Used */}
        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-2">
              <Zap className="w-5 h-5 text-blue-400" />
              <Badge variant={isNearTokenLimit ? "destructive" : "secondary"} className="text-xs">
                {tokenPercentage.toFixed(1)}%
              </Badge>
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold text-white">
                {formatNumber(usageData.hourlyTokens)}
              </p>
              <p className="text-xs text-slate-400">Tokens/Hour</p>
              <Progress 
                value={tokenPercentage} 
                className={`h-1 ${isNearTokenLimit ? 'bg-red-900' : 'bg-slate-700'}`}
              />
            </div>
          </CardContent>
        </Card>

        {/* Requests Today */}
        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-2">
              <BarChart3 className="w-5 h-5 text-purple-400" />
              <TrendingUp className="w-4 h-4 text-green-400" />
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold text-white">
                {formatNumber(usageData.requestsToday)}
              </p>
              <p className="text-xs text-slate-400">Requests Today</p>
              <p className="text-xs text-green-400">
                {usageData.requestsPerMinute}/min current
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Daily Cost */}
        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-2">
              <TrendingUp className="w-5 h-5 text-orange-400" />
              <Badge variant="outline" className="text-xs">
                24h
              </Badge>
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold text-white">
                {formatCurrency(usageData.costToday)}
              </p>
              <p className="text-xs text-slate-400">Total Today</p>
              <p className="text-xs text-slate-400">
                Avg: {formatCurrency(usageData.costToday / Math.max(1, usageData.requestsToday))} per request
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Provider Health Status */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Activity className="w-5 h-5 text-blue-400" />
            Provider Health Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {Object.entries(providerHealth).map(([provider, health]) => (
              <div key={provider} className="p-3 bg-slate-900/50 rounded-lg border border-slate-700">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getHealthIcon(health.isHealthy)}
                    <span className="text-white font-medium capitalize">{provider}</span>
                  </div>
                  <Badge variant={health.isHealthy ? "default" : "destructive"} className="text-xs">
                    {health.isHealthy ? "Healthy" : "Unhealthy"}
                  </Badge>
                </div>
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">Response Time:</span>
                    <span className="text-white">{Math.round(health.responseTime)}ms</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">Last Check:</span>
                    <span className="text-white">{health.lastCheck.toLocaleTimeString()}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Usage Trends */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-green-400" />
            Usage Trends
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-400">+12%</p>
              <p className="text-sm text-slate-400">Cost Efficiency</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-400">-8%</p>
              <p className="text-sm text-slate-400">Avg Response Time</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-400">+25%</p>
              <p className="text-sm text-slate-400">Success Rate</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-orange-400">99.2%</p>
              <p className="text-sm text-slate-400">Uptime</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Alerts Section */}
      {(isNearCostLimit || isNearTokenLimit) && (
        <Card className="bg-yellow-500/10 border-yellow-500/20">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="w-5 h-5 text-yellow-500" />
              <span className="text-yellow-200 font-medium">Usage Alerts</span>
            </div>
            <div className="space-y-1">
              {isNearCostLimit && (
                <p className="text-yellow-200 text-sm">
                  ⚠️ Cost usage is at {costPercentage.toFixed(1)}% of limit
                </p>
              )}
              {isNearTokenLimit && (
                <p className="text-yellow-200 text-sm">
                  ⚠️ Token usage is at {tokenPercentage.toFixed(1)}% of hourly limit
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Refresh Button */}
      <div className="flex justify-center">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setLastUpdate(new Date())}
          disabled={isLoading}
          className="border-slate-600 text-slate-300 hover:text-white"
        >
          {isLoading ? (
            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <RefreshCw className="w-4 h-4 mr-2" />
          )}
          Refresh Data
        </Button>
      </div>
    </div>
  );
};
