// Advanced caching utilities for Metamorphic Reactor

// Memory cache with TTL and size limits
class MemoryCache<T = any> {
  private cache = new Map<string, { value: T; expires: number; size: number }>()
  private maxSize: number
  private currentSize = 0

  constructor(maxSize: number = 50 * 1024 * 1024) { // 50MB default
    this.maxSize = maxSize
  }

  set(key: string, value: T, ttl: number = 5 * 60 * 1000): void { // 5 minutes default
    const size = this.calculateSize(value)
    const expires = Date.now() + ttl

    // Remove expired entries
    this.cleanup()

    // Make room if needed
    while (this.currentSize + size > this.maxSize && this.cache.size > 0) {
      this.evictLRU()
    }

    // Remove existing entry if updating
    if (this.cache.has(key)) {
      this.currentSize -= this.cache.get(key)!.size
    }

    this.cache.set(key, { value, expires, size })
    this.currentSize += size
  }

  get(key: string): T | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    if (Date.now() > entry.expires) {
      this.delete(key)
      return null
    }

    // Move to end (LRU)
    this.cache.delete(key)
    this.cache.set(key, entry)
    
    return entry.value
  }

  delete(key: string): boolean {
    const entry = this.cache.get(key)
    if (entry) {
      this.currentSize -= entry.size
      return this.cache.delete(key)
    }
    return false
  }

  clear(): void {
    this.cache.clear()
    this.currentSize = 0
  }

  has(key: string): boolean {
    const entry = this.cache.get(key)
    if (!entry) return false
    
    if (Date.now() > entry.expires) {
      this.delete(key)
      return false
    }
    
    return true
  }

  private cleanup(): void {
    const now = Date.now()
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expires) {
        this.delete(key)
      }
    }
  }

  private evictLRU(): void {
    const firstKey = this.cache.keys().next().value
    if (firstKey) {
      this.delete(firstKey)
    }
  }

  private calculateSize(value: any): number {
    try {
      return new Blob([JSON.stringify(value)]).size
    } catch {
      return 1024 // Default size if calculation fails
    }
  }

  getStats() {
    return {
      size: this.cache.size,
      currentSize: this.currentSize,
      maxSize: this.maxSize,
      hitRate: 0 // Could be implemented with counters
    }
  }
}

// Persistent cache using IndexedDB
class PersistentCache {
  private dbName = 'MetamorphicReactorCache'
  private version = 1
  private storeName = 'cache'

  async set(key: string, value: any, ttl: number = 24 * 60 * 60 * 1000): Promise<void> {
    const db = await this.openDB()
    const transaction = db.transaction([this.storeName], 'readwrite')
    const store = transaction.objectStore(this.storeName)
    
    const expires = Date.now() + ttl
    await store.put({ key, value, expires })
  }

  async get(key: string): Promise<any | null> {
    const db = await this.openDB()
    const transaction = db.transaction([this.storeName], 'readonly')
    const store = transaction.objectStore(this.storeName)
    
    const result = await store.get(key)
    if (!result) return null

    if (Date.now() > result.expires) {
      await this.delete(key)
      return null
    }

    return result.value
  }

  async delete(key: string): Promise<void> {
    const db = await this.openDB()
    const transaction = db.transaction([this.storeName], 'readwrite')
    const store = transaction.objectStore(this.storeName)
    
    await store.delete(key)
  }

  async clear(): Promise<void> {
    const db = await this.openDB()
    const transaction = db.transaction([this.storeName], 'readwrite')
    const store = transaction.objectStore(this.storeName)
    
    await store.clear()
  }

  async cleanup(): Promise<void> {
    const db = await this.openDB()
    const transaction = db.transaction([this.storeName], 'readwrite')
    const store = transaction.objectStore(this.storeName)
    
    const now = Date.now()
    const cursor = await store.openCursor()
    
    while (cursor) {
      if (cursor.value.expires < now) {
        await cursor.delete()
      }
      await cursor.continue()
    }
  }

  private async openDB(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version)
      
      request.onerror = () => reject(request.error)
      request.onsuccess = () => resolve(request.result)
      
      request.onupgradeneeded = () => {
        const db = request.result
        if (!db.objectStoreNames.contains(this.storeName)) {
          db.createObjectStore(this.storeName, { keyPath: 'key' })
        }
      }
    })
  }
}

// Cache manager with multiple strategies
export class CacheManager {
  private memoryCache = new MemoryCache()
  private persistentCache = new PersistentCache()

  async get(key: string, strategy: 'memory' | 'persistent' | 'both' = 'both'): Promise<any | null> {
    if (strategy === 'memory' || strategy === 'both') {
      const memoryResult = this.memoryCache.get(key)
      if (memoryResult !== null) return memoryResult
    }

    if (strategy === 'persistent' || strategy === 'both') {
      const persistentResult = await this.persistentCache.get(key)
      if (persistentResult !== null) {
        // Populate memory cache
        this.memoryCache.set(key, persistentResult)
        return persistentResult
      }
    }

    return null
  }

  async set(
    key: string, 
    value: any, 
    options: {
      memoryTTL?: number
      persistentTTL?: number
      strategy?: 'memory' | 'persistent' | 'both'
    } = {}
  ): Promise<void> {
    const { 
      memoryTTL = 5 * 60 * 1000, 
      persistentTTL = 24 * 60 * 60 * 1000,
      strategy = 'both'
    } = options

    if (strategy === 'memory' || strategy === 'both') {
      this.memoryCache.set(key, value, memoryTTL)
    }

    if (strategy === 'persistent' || strategy === 'both') {
      await this.persistentCache.set(key, value, persistentTTL)
    }
  }

  async delete(key: string): Promise<void> {
    this.memoryCache.delete(key)
    await this.persistentCache.delete(key)
  }

  async clear(): Promise<void> {
    this.memoryCache.clear()
    await this.persistentCache.clear()
  }

  async cleanup(): Promise<void> {
    await this.persistentCache.cleanup()
  }

  getStats() {
    return {
      memory: this.memoryCache.getStats()
    }
  }
}

// Global cache instance
export const cache = new CacheManager()

// Cache decorators and utilities
export function cached(
  key: string | ((args: any[]) => string),
  ttl: number = 5 * 60 * 1000
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const cacheKey = typeof key === 'string' ? key : key(args)
      
      const cached = await cache.get(cacheKey)
      if (cached !== null) {
        return cached
      }

      const result = await method.apply(this, args)
      await cache.set(cacheKey, result, { memoryTTL: ttl })
      
      return result
    }

    return descriptor
  }
}

// React hook for cached data
export function useCachedData<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: {
    ttl?: number
    strategy?: 'memory' | 'persistent' | 'both'
    refreshInterval?: number
  } = {}
) {
  const [data, setData] = React.useState<T | null>(null)
  const [loading, setLoading] = React.useState(true)
  const [error, setError] = React.useState<Error | null>(null)

  const { ttl = 5 * 60 * 1000, strategy = 'both', refreshInterval } = options

  React.useEffect(() => {
    let mounted = true

    async function loadData() {
      try {
        setLoading(true)
        setError(null)

        // Try cache first
        const cached = await cache.get(key, strategy)
        if (cached !== null && mounted) {
          setData(cached)
          setLoading(false)
          return
        }

        // Fetch fresh data
        const fresh = await fetcher()
        if (mounted) {
          setData(fresh)
          await cache.set(key, fresh, { memoryTTL: ttl, strategy })
        }
      } catch (err) {
        if (mounted) {
          setError(err as Error)
        }
      } finally {
        if (mounted) {
          setLoading(false)
        }
      }
    }

    loadData()

    // Set up refresh interval if specified
    let interval: NodeJS.Timeout | undefined
    if (refreshInterval) {
      interval = setInterval(loadData, refreshInterval)
    }

    return () => {
      mounted = false
      if (interval) {
        clearInterval(interval)
      }
    }
  }, [key, ttl, strategy, refreshInterval])

  const refresh = React.useCallback(async () => {
    await cache.delete(key)
    setLoading(true)
    
    try {
      const fresh = await fetcher()
      setData(fresh)
      await cache.set(key, fresh, { memoryTTL: ttl, strategy })
    } catch (err) {
      setError(err as Error)
    } finally {
      setLoading(false)
    }
  }, [key, fetcher, ttl, strategy])

  return { data, loading, error, refresh }
}

// Import React for hooks
import * as React from 'react'
