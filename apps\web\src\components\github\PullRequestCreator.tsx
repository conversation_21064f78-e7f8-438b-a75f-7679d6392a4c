import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { GitPullRequest, Send, FileText, Users, Tag } from 'lucide-react';

interface PullRequestCreatorProps {
  repository: string;
  sourceBranch: string;
  onCreatePR: (prData: any) => void;
}

export const PullRequestCreator: React.FC<PullRequestCreatorProps> = ({
  repository,
  sourceBranch,
  onCreatePR
}) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [targetBranch, setTargetBranch] = useState('main');
  const [isDraft, setIsDraft] = useState(false);
  const [assignees, setAssignees] = useState<string[]>([]);
  const [labels, setLabels] = useState<string[]>([]);

  const availableBranches = ['main', 'develop', 'staging'];
  const availableAssignees = ['developer1', 'developer2', 'reviewer1'];
  const availableLabels = ['enhancement', 'bug', 'documentation', 'refactor'];

  const generateTitle = () => {
    const suggestions = [
      'feat: Add new transformation feature',
      'fix: Resolve code generation issue',
      'refactor: Improve code structure',
      'docs: Update documentation',
    ];
    setTitle(suggestions[Math.floor(Math.random() * suggestions.length)]);
  };

  const generateDescription = () => {
    const template = `## Description
Brief description of the changes made.

## Changes
- [ ] Added new feature
- [ ] Fixed bug
- [ ] Updated documentation
- [ ] Refactored code

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed

## Screenshots (if applicable)
Add screenshots here if relevant.

## Additional Notes
Any additional information or context.`;
    setDescription(template);
  };

  const handleCreatePR = () => {
    const prData = {
      title,
      description,
      sourceBranch,
      targetBranch,
      isDraft,
      assignees,
      labels,
    };
    onCreatePR(prData);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <GitPullRequest className="w-5 h-5" />
          <span>Create Pull Request</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Branch Info */}
        <div className="p-3 bg-muted/50 rounded-lg">
          <div className="flex items-center justify-between text-sm">
            <span>From: <Badge variant="outline">{sourceBranch}</Badge></span>
            <span>→</span>
            <span>To: <Badge variant="outline">{targetBranch}</Badge></span>
          </div>
        </div>

        {/* Title */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">Title</label>
            <Button variant="ghost" size="sm" onClick={generateTitle}>
              <FileText className="w-4 h-4 mr-1" />
              Suggest
            </Button>
          </div>
          <Input
            placeholder="Enter PR title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
          />
        </div>

        {/* Target Branch */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Target Branch</label>
          <Select value={targetBranch} onValueChange={setTargetBranch}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {availableBranches.map(branch => (
                <SelectItem key={branch} value={branch}>
                  {branch}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Description */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">Description</label>
            <Button variant="ghost" size="sm" onClick={generateDescription}>
              <FileText className="w-4 h-4 mr-1" />
              Template
            </Button>
          </div>
          <Textarea
            placeholder="Describe your changes"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            rows={8}
          />
        </div>

        {/* Options */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="draft"
              checked={isDraft}
              onCheckedChange={(checked) => setIsDraft(checked as boolean)}
            />
            <label htmlFor="draft" className="text-sm">
              Create as draft
            </label>
          </div>

          {/* Assignees */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center">
              <Users className="w-4 h-4 mr-1" />
              Assignees
            </label>
            <div className="flex flex-wrap gap-2">
              {availableAssignees.map(assignee => (
                <Badge
                  key={assignee}
                  variant={assignees.includes(assignee) ? "default" : "outline"}
                  className="cursor-pointer"
                  onClick={() => {
                    setAssignees(prev =>
                      prev.includes(assignee)
                        ? prev.filter(a => a !== assignee)
                        : [...prev, assignee]
                    );
                  }}
                >
                  {assignee}
                </Badge>
              ))}
            </div>
          </div>

          {/* Labels */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center">
              <Tag className="w-4 h-4 mr-1" />
              Labels
            </label>
            <div className="flex flex-wrap gap-2">
              {availableLabels.map(label => (
                <Badge
                  key={label}
                  variant={labels.includes(label) ? "default" : "outline"}
                  className="cursor-pointer"
                  onClick={() => {
                    setLabels(prev =>
                      prev.includes(label)
                        ? prev.filter(l => l !== label)
                        : [...prev, label]
                    );
                  }}
                >
                  {label}
                </Badge>
              ))}
            </div>
          </div>
        </div>

        {/* Create Button */}
        <Button
          onClick={handleCreatePR}
          disabled={!title.trim() || !description.trim()}
          className="w-full"
        >
          <Send className="w-4 h-4 mr-2" />
          Create Pull Request
        </Button>
      </CardContent>
    </Card>
  );
};

export default PullRequestCreator;
