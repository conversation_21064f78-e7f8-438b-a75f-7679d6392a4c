import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Clock, 
  Search,
  Filter,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Zap,
  GitBranch,
  User,
  Settings,
  Activity,
  Code,
  Database
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { HStack, VStack } from '@/components/ui/layout';
import { Typography } from '@/components/ui/typography';
import { formatDistanceToNow } from 'date-fns';

interface ActivityEvent {
  id: string;
  type: 'transformation' | 'error' | 'system' | 'user_action' | 'github' | 'billing';
  title: string;
  description: string;
  timestamp: string;
  status: 'success' | 'error' | 'warning' | 'info';
  metadata?: {
    user_id?: string;
    session_id?: string;
    cost?: number;
    score?: number;
    iterations?: number;
    error_code?: string;
    [key: string]: any;
  };
}

interface RecentActivityTimelineProps {
  className?: string;
  refreshInterval?: number;
  maxEvents?: number;
}

export const RecentActivityTimeline: React.FC<RecentActivityTimelineProps> = ({
  className,
  refreshInterval = 30000, // 30 seconds
  maxEvents = 50
}) => {
  const [events, setEvents] = useState<ActivityEvent[]>([]);
  const [filteredEvents, setFilteredEvents] = useState<ActivityEvent[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Fetch recent activity events
  const fetchRecentActivity = async () => {
    setIsLoading(true);
    
    try {
      const activities: ActivityEvent[] = [];

      // Fetch transformation history
      try {
        const transformationsResponse = await fetch('/api/transformations?limit=20');
        if (transformationsResponse.ok) {
          const transformationsData = await transformationsResponse.json();
          transformationsData.data?.forEach((transformation: any) => {
            activities.push({
              id: `transformation-${transformation.id}`,
              type: 'transformation',
              title: 'Code Transformation',
              description: transformation.prompt?.substring(0, 100) + '...' || 'Code transformation completed',
              timestamp: transformation.created_at,
              status: transformation.status === 'completed' ? 'success' : 
                      transformation.status === 'failed' ? 'error' : 'info',
              metadata: {
                session_id: transformation.id,
                cost: transformation.cost,
                score: transformation.final_score,
                iterations: transformation.iterations
              }
            });
          });
        }
      } catch (error) {
        console.warn('Failed to fetch transformations:', error);
      }

      // Fetch system logs
      try {
        const logsResponse = await fetch('/api/logs?level=error&limit=10');
        if (logsResponse.ok) {
          const logsData = await logsResponse.json();
          logsData.data?.forEach((log: any) => {
            activities.push({
              id: `log-${log.id}`,
              type: 'error',
              title: 'System Error',
              description: log.message,
              timestamp: log.created_at,
              status: 'error',
              metadata: {
                service: log.service,
                error_code: log.metadata?.error_code
              }
            });
          });
        }
      } catch (error) {
        console.warn('Failed to fetch logs:', error);
      }

      // Fetch billing events
      try {
        const billingResponse = await fetch('/api/billing/usage');
        if (billingResponse.ok) {
          const billingData = await billingResponse.json();
          if (billingData.data?.usage_by_type) {
            Object.entries(billingData.data.usage_by_type).forEach(([type, count]) => {
              activities.push({
                id: `billing-${type}`,
                type: 'billing',
                title: 'Usage Recorded',
                description: `${count} ${type} operations in current period`,
                timestamp: new Date().toISOString(),
                status: 'info',
                metadata: {
                  usage_type: type,
                  quantity: count
                }
              });
            });
          }
        }
      } catch (error) {
        console.warn('Failed to fetch billing data:', error);
      }

      // Add some mock system events for demonstration
      activities.push(
        {
          id: 'system-startup',
          type: 'system',
          title: 'System Health Check',
          description: 'All services are operational',
          timestamp: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
          status: 'success'
        },
        {
          id: 'user-login',
          type: 'user_action',
          title: 'User Session',
          description: 'User authenticated successfully',
          timestamp: new Date(Date.now() - 600000).toISOString(), // 10 minutes ago
          status: 'success'
        }
      );

      // Sort by timestamp (newest first) and limit
      const sortedEvents = activities
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, maxEvents);

      setEvents(sortedEvents);
      setFilteredEvents(sortedEvents);

    } catch (error) {
      console.error('Failed to fetch recent activity:', error);
    } finally {
      setIsLoading(false);
      setLastUpdated(new Date());
    }
  };

  // Auto-refresh activity
  useEffect(() => {
    fetchRecentActivity();
    const interval = setInterval(fetchRecentActivity, refreshInterval);
    return () => clearInterval(interval);
  }, [refreshInterval, maxEvents]);

  // Filter and search events
  useEffect(() => {
    let filtered = events;

    // Apply type filter
    if (selectedFilter !== 'all') {
      filtered = filtered.filter(event => event.type === selectedFilter);
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(event => 
        event.title.toLowerCase().includes(query) ||
        event.description.toLowerCase().includes(query)
      );
    }

    setFilteredEvents(filtered);
  }, [events, selectedFilter, searchQuery]);

  const getEventIcon = (type: string, status: string) => {
    switch (type) {
      case 'transformation':
        return status === 'success' ? 
          <CheckCircle className="w-4 h-4 text-green-500" /> :
          status === 'error' ? 
          <XCircle className="w-4 h-4 text-red-500" /> :
          <Zap className="w-4 h-4 text-blue-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'system':
        return <Settings className="w-4 h-4 text-purple-500" />;
      case 'user_action':
        return <User className="w-4 h-4 text-blue-500" />;
      case 'github':
        return <GitBranch className="w-4 h-4 text-orange-500" />;
      case 'billing':
        return <Database className="w-4 h-4 text-green-500" />;
      default:
        return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'success': return 'default';
      case 'error': return 'destructive';
      case 'warning': return 'secondary';
      case 'info': return 'outline';
      default: return 'outline';
    }
  };

  const filterOptions = [
    { value: 'all', label: 'All Events' },
    { value: 'transformation', label: 'Transformations' },
    { value: 'error', label: 'Errors' },
    { value: 'system', label: 'System' },
    { value: 'user_action', label: 'User Actions' },
    { value: 'github', label: 'GitHub' },
    { value: 'billing', label: 'Billing' }
  ];

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Clock className="w-5 h-5 text-primary" />
            <CardTitle className="text-lg">Recent Activity</CardTitle>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={fetchRecentActivity}
            disabled={isLoading}
            className="h-8 w-8 p-0"
          >
            <RefreshCw className={cn("w-4 h-4", isLoading && "animate-spin")} />
          </Button>
        </div>
        <CardDescription>
          Timeline of recent transformations, errors, and system events
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Search and Filter Controls */}
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Search activity..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 text-muted-foreground" />
            <select
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value)}
              className="px-3 py-2 text-sm border border-border rounded-md bg-background"
            >
              {filterOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Activity Timeline */}
        <ScrollArea className="h-96">
          <div className="space-y-4">
            {filteredEvents.length === 0 ? (
              <div className="text-center py-8">
                <Activity className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                <Typography variant="body-sm" className="text-muted-foreground">
                  {searchQuery || selectedFilter !== 'all' ? 'No matching activity found' : 'No recent activity'}
                </Typography>
              </div>
            ) : (
              filteredEvents.map((event, index) => (
                <div key={event.id} className="flex items-start space-x-3 group">
                  {/* Timeline connector */}
                  <div className="flex flex-col items-center">
                    <div className="flex-shrink-0">
                      {getEventIcon(event.type, event.status)}
                    </div>
                    {index < filteredEvents.length - 1 && (
                      <div className="w-px h-8 bg-border mt-2" />
                    )}
                  </div>

                  {/* Event content */}
                  <div className="flex-1 min-w-0 pb-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <Typography variant="body-sm" className="font-medium">
                            {event.title}
                          </Typography>
                          <Badge variant={getStatusBadgeVariant(event.status)} className="text-xs">
                            {event.status}
                          </Badge>
                        </div>
                        <Typography variant="caption" className="text-muted-foreground line-clamp-2">
                          {event.description}
                        </Typography>
                        
                        {/* Metadata */}
                        {event.metadata && (
                          <div className="flex flex-wrap gap-2 mt-2">
                            {event.metadata.cost && (
                              <Badge variant="outline" className="text-xs">
                                ${event.metadata.cost.toFixed(4)}
                              </Badge>
                            )}
                            {event.metadata.score && (
                              <Badge variant="outline" className="text-xs">
                                Score: {event.metadata.score.toFixed(2)}
                              </Badge>
                            )}
                            {event.metadata.iterations && (
                              <Badge variant="outline" className="text-xs">
                                {event.metadata.iterations} iterations
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                      
                      <Typography variant="caption" className="text-muted-foreground whitespace-nowrap ml-2">
                        {formatDistanceToNow(new Date(event.timestamp), { addSuffix: true })}
                      </Typography>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>

        {/* Footer */}
        <div className="pt-2 border-t border-border flex justify-between items-center">
          <Typography variant="caption" className="text-muted-foreground">
            Showing {filteredEvents.length} of {events.length} events
          </Typography>
          <Typography variant="caption" className="text-muted-foreground">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </Typography>
        </div>
      </CardContent>
    </Card>
  );
};

export default RecentActivityTimeline;
