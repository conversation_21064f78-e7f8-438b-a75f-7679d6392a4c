import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  DollarSign, 
  AlertTriangle, 
  TrendingUp, 
  Clock,
  Activity,
  Shield,
  BarChart3,
  Zap
} from 'lucide-react';
import { 
  CostGuardConfig, 
  TokenMonitorConfig,
  DEFAULT_COST_GUARD_CONFIG,
  DEFAULT_TOKEN_MONITOR_CONFIG
} from '@/types/settings';

interface CostManagementSettingsProps {
  costGuardConfig: CostGuardConfig;
  tokenMonitorConfig: TokenMonitorConfig;
  onCostGuardChange: (config: CostGuardConfig) => void;
  onTokenMonitorChange: (config: TokenMonitorConfig) => void;
  currentUsage?: {
    currentCost: number;
    tokensUsed: number;
    requestsToday: number;
    costToday: number;
  };
}

export const CostManagementSettings: React.FC<CostManagementSettingsProps> = ({
  costGuardConfig,
  tokenMonitorConfig,
  onCostGuardChange,
  onTokenMonitorChange,
  currentUsage = {
    currentCost: 0,
    tokensUsed: 0,
    requestsToday: 0,
    costToday: 0,
  },
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Calculate usage percentages
  const costPercentage = Math.min((currentUsage.currentCost / costGuardConfig.maxCostPerLoop) * 100, 100);
  const isNearLimit = costPercentage >= (costGuardConfig.alertThresholds.nearLimitWarning * 100);
  const hourlyTokensPercentage = Math.min((currentUsage.tokensUsed / tokenMonitorConfig.alertThresholds.tokensPerHour) * 100, 100);

  const handleCostGuardUpdate = (updates: Partial<CostGuardConfig>) => {
    onCostGuardChange({ ...costGuardConfig, ...updates });
  };

  const handleTokenMonitorUpdate = (updates: Partial<TokenMonitorConfig>) => {
    onTokenMonitorChange({ ...tokenMonitorConfig, ...updates });
  };

  const handleAlertThresholdUpdate = (
    type: 'costGuard' | 'tokenMonitor',
    field: string,
    value: number
  ) => {
    if (type === 'costGuard') {
      handleCostGuardUpdate({
        alertThresholds: {
          ...costGuardConfig.alertThresholds,
          [field]: value,
        },
      });
    } else {
      handleTokenMonitorUpdate({
        alertThresholds: {
          ...tokenMonitorConfig.alertThresholds,
          [field]: value,
        },
      });
    }
  };

  const resetToDefaults = () => {
    onCostGuardChange(DEFAULT_COST_GUARD_CONFIG);
    onTokenMonitorChange(DEFAULT_TOKEN_MONITOR_CONFIG);
  };

  return (
    <div className="space-y-6">
      {/* Cost Guard Configuration */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <DollarSign className="w-5 h-5 text-green-400" />
            Cost Guard
            <Badge variant={costGuardConfig.enabled ? "default" : "secondary"}>
              {costGuardConfig.enabled ? "Enabled" : "Disabled"}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Enable/Disable Cost Guard */}
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-slate-300 font-medium">Enable Cost Guard</Label>
              <p className="text-sm text-slate-400">
                Automatically abort operations when cost limits are exceeded
              </p>
            </div>
            <Switch
              checked={costGuardConfig.enabled}
              onCheckedChange={(enabled) => handleCostGuardUpdate({ enabled })}
            />
          </div>

          {/* Current Usage Display */}
          <div className="p-4 bg-slate-900/50 rounded-lg border border-slate-700">
            <div className="flex items-center justify-between mb-2">
              <Label className="text-slate-300">Current Loop Cost</Label>
              <span className="text-white font-mono">
                ${currentUsage.currentCost.toFixed(4)} / ${costGuardConfig.maxCostPerLoop.toFixed(2)}
              </span>
            </div>
            <Progress 
              value={costPercentage} 
              className={`h-2 ${isNearLimit ? 'bg-red-900' : 'bg-slate-700'}`}
            />
            {isNearLimit && (
              <Alert className="mt-3 border-yellow-500/20 bg-yellow-500/10">
                <AlertTriangle className="h-4 w-4 text-yellow-500" />
                <AlertDescription className="text-yellow-200">
                  Approaching cost limit ({costPercentage.toFixed(1)}% used)
                </AlertDescription>
              </Alert>
            )}
          </div>

          {/* Maximum Cost Per Loop */}
          <div className="space-y-3">
            <Label className="text-slate-300 font-medium">Maximum Cost Per Loop</Label>
            <div className="flex items-center gap-4">
              <span className="text-slate-400 text-sm">$0.50</span>
              <Slider
                value={[costGuardConfig.maxCostPerLoop]}
                onValueChange={([value]) => handleCostGuardUpdate({ maxCostPerLoop: value })}
                min={0.5}
                max={20}
                step={0.5}
                className="flex-1"
                disabled={!costGuardConfig.enabled}
              />
              <span className="text-slate-400 text-sm">$20.00</span>
            </div>
            <div className="text-center">
              <span className="text-white font-mono text-lg">
                ${costGuardConfig.maxCostPerLoop.toFixed(2)}
              </span>
            </div>
          </div>

          {/* Near Limit Warning Threshold */}
          <div className="space-y-3">
            <Label className="text-slate-300 font-medium">Near Limit Warning</Label>
            <div className="flex items-center gap-4">
              <span className="text-slate-400 text-sm">50%</span>
              <Slider
                value={[costGuardConfig.alertThresholds.nearLimitWarning * 100]}
                onValueChange={([value]) => handleAlertThresholdUpdate('costGuard', 'nearLimitWarning', value / 100)}
                min={50}
                max={95}
                step={5}
                className="flex-1"
                disabled={!costGuardConfig.enabled}
              />
              <span className="text-slate-400 text-sm">95%</span>
            </div>
            <div className="text-center">
              <span className="text-white font-mono">
                {(costGuardConfig.alertThresholds.nearLimitWarning * 100).toFixed(0)}%
              </span>
            </div>
          </div>

          {/* Hourly Cost Alert */}
          <div className="space-y-3">
            <Label className="text-slate-300 font-medium">Hourly Cost Alert</Label>
            <div className="flex items-center gap-2">
              <span className="text-slate-400">$</span>
              <Input
                type="number"
                value={costGuardConfig.alertThresholds.costPerHour}
                onChange={(e) => handleAlertThresholdUpdate('costGuard', 'costPerHour', parseFloat(e.target.value) || 0)}
                min={1}
                max={100}
                step={1}
                className="bg-slate-700 border-slate-600 text-white"
                disabled={!costGuardConfig.enabled}
              />
              <span className="text-slate-400">per hour</span>
            </div>
            <p className="text-sm text-slate-400">
              Alert when hourly spending exceeds this amount
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Token Monitor Configuration */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Activity className="w-5 h-5 text-blue-400" />
            Token Monitor
            <Badge variant={tokenMonitorConfig.enabled ? "default" : "secondary"}>
              {tokenMonitorConfig.enabled ? "Enabled" : "Disabled"}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Enable/Disable Token Monitor */}
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-slate-300 font-medium">Enable Token Monitoring</Label>
              <p className="text-sm text-slate-400">
                Track token usage and provide detailed analytics
              </p>
            </div>
            <Switch
              checked={tokenMonitorConfig.enabled}
              onCheckedChange={(enabled) => handleTokenMonitorUpdate({ enabled })}
            />
          </div>

          {/* Current Token Usage */}
          <div className="grid grid-cols-3 gap-4">
            <div className="p-3 bg-slate-900/50 rounded-lg border border-slate-700">
              <div className="flex items-center gap-2 mb-1">
                <Zap className="w-4 h-4 text-blue-400" />
                <span className="text-slate-300 text-sm">Tokens Today</span>
              </div>
              <span className="text-white font-mono text-lg">
                {currentUsage.tokensUsed.toLocaleString()}
              </span>
            </div>
            <div className="p-3 bg-slate-900/50 rounded-lg border border-slate-700">
              <div className="flex items-center gap-2 mb-1">
                <BarChart3 className="w-4 h-4 text-green-400" />
                <span className="text-slate-300 text-sm">Requests</span>
              </div>
              <span className="text-white font-mono text-lg">
                {currentUsage.requestsToday}
              </span>
            </div>
            <div className="p-3 bg-slate-900/50 rounded-lg border border-slate-700">
              <div className="flex items-center gap-2 mb-1">
                <TrendingUp className="w-4 h-4 text-purple-400" />
                <span className="text-slate-300 text-sm">Cost Today</span>
              </div>
              <span className="text-white font-mono text-lg">
                ${currentUsage.costToday.toFixed(4)}
              </span>
            </div>
          </div>

          {/* Log Level */}
          <div className="space-y-2">
            <Label className="text-slate-300 font-medium">Log Level</Label>
            <div className="grid grid-cols-3 gap-2">
              {(['none', 'basic', 'detailed'] as const).map((level) => (
                <Button
                  key={level}
                  variant={tokenMonitorConfig.logLevel === level ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleTokenMonitorUpdate({ logLevel: level })}
                  disabled={!tokenMonitorConfig.enabled}
                  className="capitalize"
                >
                  {level}
                </Button>
              ))}
            </div>
          </div>

          {/* Advanced Settings Toggle */}
          <div className="flex items-center justify-between pt-2 border-t border-slate-700">
            <Label className="text-slate-300 font-medium">Advanced Alert Settings</Label>
            <Switch
              checked={showAdvanced}
              onCheckedChange={setShowAdvanced}
            />
          </div>

          {/* Advanced Alert Settings */}
          {showAdvanced && (
            <div className="space-y-4 p-4 bg-slate-900/50 rounded-lg border border-slate-700">
              {/* Tokens Per Hour Alert */}
              <div className="space-y-2">
                <Label className="text-slate-400">Tokens Per Hour Alert</Label>
                <Input
                  type="number"
                  value={tokenMonitorConfig.alertThresholds.tokensPerHour}
                  onChange={(e) => handleAlertThresholdUpdate('tokenMonitor', 'tokensPerHour', parseInt(e.target.value) || 0)}
                  min={10000}
                  max={10000000}
                  step={10000}
                  className="bg-slate-700 border-slate-600 text-white"
                  disabled={!tokenMonitorConfig.enabled}
                />
              </div>

              {/* Requests Per Minute Alert */}
              <div className="space-y-2">
                <Label className="text-slate-400">Requests Per Minute Alert</Label>
                <Input
                  type="number"
                  value={tokenMonitorConfig.alertThresholds.requestsPerMinute}
                  onChange={(e) => handleAlertThresholdUpdate('tokenMonitor', 'requestsPerMinute', parseInt(e.target.value) || 0)}
                  min={1}
                  max={1000}
                  step={1}
                  className="bg-slate-700 border-slate-600 text-white"
                  disabled={!tokenMonitorConfig.enabled}
                />
              </div>

              {/* Max Events Storage */}
              <div className="space-y-2">
                <Label className="text-slate-400">Max Events to Store</Label>
                <Input
                  type="number"
                  value={tokenMonitorConfig.maxEvents}
                  onChange={(e) => handleTokenMonitorUpdate({ maxEvents: parseInt(e.target.value) || 10000 })}
                  min={1000}
                  max={100000}
                  step={1000}
                  className="bg-slate-700 border-slate-600 text-white"
                  disabled={!tokenMonitorConfig.enabled}
                />
                <p className="text-xs text-slate-400">
                  Higher values provide more history but use more memory
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Reset to Defaults */}
      <div className="flex justify-end">
        <Button
          variant="outline"
          onClick={resetToDefaults}
          className="border-slate-600 text-slate-300 hover:text-white"
        >
          Reset to Defaults
        </Button>
      </div>
    </div>
  );
};
