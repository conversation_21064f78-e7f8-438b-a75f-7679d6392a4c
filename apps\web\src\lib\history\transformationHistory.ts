import { z } from 'zod';
import { TransformationRequest, TransformationResult } from '../agents/dualAgentSystem';

// History Entry Schema
export const historyEntrySchema = z.object({
  id: z.string(),
  version: z.number(),
  timestamp: z.string().transform(str => new Date(str)),
  request: z.any(), // TransformationRequest
  result: z.any(), // TransformationResult
  tags: z.array(z.string()).default([]),
  favorite: z.boolean().default(false),
  notes: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

export type HistoryEntry = z.infer<typeof historyEntrySchema>;

// Search Filters
export interface HistorySearchFilters {
  query?: string;
  tags?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  status?: string[];
  favorite?: boolean;
  minScore?: number;
  maxCost?: number;
}

// Sort Options
export type HistorySortBy = 'timestamp' | 'score' | 'cost' | 'duration';
export type HistorySortOrder = 'asc' | 'desc';

// History Statistics
export interface HistoryStats {
  totalTransformations: number;
  successfulTransformations: number;
  averageScore: number;
  totalCost: number;
  averageDuration: number;
  topTags: Array<{ tag: string; count: number }>;
  recentActivity: Array<{ date: string; count: number }>;
}

// Transformation History Manager
export class TransformationHistory {
  private static readonly STORAGE_KEY = 'metamorphic_reactor_history';
  private static readonly MAX_ENTRIES = 1000;
  
  private entries: HistoryEntry[] = [];

  constructor() {
    this.loadHistory();
  }

  // Add transformation to history
  addTransformation(
    request: TransformationRequest,
    result: TransformationResult,
    tags: string[] = [],
    notes?: string
  ): string {
    const entry: HistoryEntry = {
      id: this.generateId(),
      version: 1,
      timestamp: new Date(),
      request,
      result,
      tags,
      favorite: false,
      notes,
      metadata: {
        language: request.language,
        promptLength: request.prompt.length,
        codeLength: request.code.length,
      },
    };

    this.entries.unshift(entry); // Add to beginning for chronological order
    this.trimHistory();
    this.saveHistory();

    return entry.id;
  }

  // Get entry by ID
  getEntry(id: string): HistoryEntry | undefined {
    return this.entries.find(entry => entry.id === id);
  }

  // Get all entries
  getAllEntries(): HistoryEntry[] {
    return [...this.entries];
  }

  // Search entries
  searchEntries(
    filters: HistorySearchFilters = {},
    sortBy: HistorySortBy = 'timestamp',
    sortOrder: HistorySortOrder = 'desc',
    limit?: number
  ): HistoryEntry[] {
    let filtered = this.entries.filter(entry => {
      // Text search
      if (filters.query) {
        const query = filters.query.toLowerCase();
        const searchText = [
          entry.request.prompt,
          entry.request.code,
          entry.result.transformedCode || '',
          entry.notes || '',
          ...entry.tags,
        ].join(' ').toLowerCase();
        
        if (!searchText.includes(query)) return false;
      }

      // Tags filter
      if (filters.tags && filters.tags.length > 0) {
        const hasAllTags = filters.tags.every(tag => entry.tags.includes(tag));
        if (!hasAllTags) return false;
      }

      // Date range filter
      if (filters.dateRange) {
        if (entry.timestamp < filters.dateRange.start || entry.timestamp > filters.dateRange.end) {
          return false;
        }
      }

      // Status filter
      if (filters.status && filters.status.length > 0) {
        if (!filters.status.includes(entry.result.status)) return false;
      }

      // Favorite filter
      if (filters.favorite !== undefined) {
        if (entry.favorite !== filters.favorite) return false;
      }

      // Score filter
      if (filters.minScore !== undefined) {
        const score = entry.result.finalScore || 0;
        if (score < filters.minScore) return false;
      }

      // Cost filter
      if (filters.maxCost !== undefined) {
        const cost = entry.result.totalCost || 0;
        if (cost > filters.maxCost) return false;
      }

      return true;
    });

    // Sort results
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'timestamp':
          aValue = a.timestamp.getTime();
          bValue = b.timestamp.getTime();
          break;
        case 'score':
          aValue = a.result.finalScore || 0;
          bValue = b.result.finalScore || 0;
          break;
        case 'cost':
          aValue = a.result.totalCost || 0;
          bValue = b.result.totalCost || 0;
          break;
        case 'duration':
          aValue = a.result.duration || 0;
          bValue = b.result.duration || 0;
          break;
        default:
          aValue = a.timestamp.getTime();
          bValue = b.timestamp.getTime();
      }

      if (sortOrder === 'asc') {
        return aValue - bValue;
      } else {
        return bValue - aValue;
      }
    });

    // Apply limit
    if (limit) {
      filtered = filtered.slice(0, limit);
    }

    return filtered;
  }

  // Get recent entries
  getRecentEntries(limit: number = 10): HistoryEntry[] {
    return this.entries.slice(0, limit);
  }

  // Get favorite entries
  getFavoriteEntries(): HistoryEntry[] {
    return this.entries.filter(entry => entry.favorite);
  }

  // Toggle favorite status
  toggleFavorite(id: string): boolean {
    const entry = this.getEntry(id);
    if (entry) {
      entry.favorite = !entry.favorite;
      this.saveHistory();
      return entry.favorite;
    }
    return false;
  }

  // Update entry tags
  updateTags(id: string, tags: string[]): boolean {
    const entry = this.getEntry(id);
    if (entry) {
      entry.tags = [...tags];
      this.saveHistory();
      return true;
    }
    return false;
  }

  // Update entry notes
  updateNotes(id: string, notes: string): boolean {
    const entry = this.getEntry(id);
    if (entry) {
      entry.notes = notes;
      this.saveHistory();
      return true;
    }
    return false;
  }

  // Delete entry
  deleteEntry(id: string): boolean {
    const index = this.entries.findIndex(entry => entry.id === id);
    if (index > -1) {
      this.entries.splice(index, 1);
      this.saveHistory();
      return true;
    }
    return false;
  }

  // Clear all history
  clearHistory(): void {
    this.entries = [];
    this.saveHistory();
  }

  // Get all unique tags
  getAllTags(): string[] {
    const tagSet = new Set<string>();
    this.entries.forEach(entry => {
      entry.tags.forEach(tag => tagSet.add(tag));
    });
    return Array.from(tagSet).sort();
  }

  // Get history statistics
  getStatistics(): HistoryStats {
    const successful = this.entries.filter(e => e.result.status === 'completed');
    const scores = successful.map(e => e.result.finalScore || 0).filter(s => s > 0);
    const costs = this.entries.map(e => e.result.totalCost || 0);
    const durations = this.entries.map(e => e.result.duration || 0).filter(d => d > 0);

    // Calculate tag frequency
    const tagCounts: Record<string, number> = {};
    this.entries.forEach(entry => {
      entry.tags.forEach(tag => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1;
      });
    });

    const topTags = Object.entries(tagCounts)
      .map(([tag, count]) => ({ tag, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Calculate recent activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentEntries = this.entries.filter(e => e.timestamp >= thirtyDaysAgo);
    const activityByDate: Record<string, number> = {};
    
    recentEntries.forEach(entry => {
      const dateKey = entry.timestamp.toISOString().split('T')[0];
      activityByDate[dateKey] = (activityByDate[dateKey] || 0) + 1;
    });

    const recentActivity = Object.entries(activityByDate)
      .map(([date, count]) => ({ date, count }))
      .sort((a, b) => a.date.localeCompare(b.date));

    return {
      totalTransformations: this.entries.length,
      successfulTransformations: successful.length,
      averageScore: scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0,
      totalCost: costs.reduce((a, b) => a + b, 0),
      averageDuration: durations.length > 0 ? durations.reduce((a, b) => a + b, 0) / durations.length : 0,
      topTags,
      recentActivity,
    };
  }

  // Export history
  exportHistory(format: 'json' | 'csv' = 'json'): string {
    if (format === 'json') {
      return JSON.stringify(
        this.entries.map(entry => ({
          ...entry,
          timestamp: entry.timestamp.toISOString(),
        })),
        null,
        2
      );
    } else {
      // CSV format
      const headers = [
        'ID',
        'Timestamp',
        'Status',
        'Score',
        'Cost',
        'Duration',
        'Tags',
        'Favorite',
        'Notes',
      ];

      const rows = this.entries.map(entry => [
        entry.id,
        entry.timestamp.toISOString(),
        entry.result.status,
        entry.result.finalScore || '',
        entry.result.totalCost || '',
        entry.result.duration || '',
        entry.tags.join(';'),
        entry.favorite,
        entry.notes || '',
      ]);

      return [headers, ...rows].map(row => row.join(',')).join('\n');
    }
  }

  // Import history
  importHistory(data: string, format: 'json' | 'csv' = 'json'): number {
    try {
      let importedEntries: any[] = [];

      if (format === 'json') {
        importedEntries = JSON.parse(data);
      } else {
        // CSV parsing (simplified)
        const lines = data.split('\n');
        const headers = lines[0].split(',');
        importedEntries = lines.slice(1).map(line => {
          const values = line.split(',');
          const entry: any = {};
          headers.forEach((header, index) => {
            entry[header.toLowerCase()] = values[index];
          });
          return entry;
        });
      }

      let importCount = 0;
      importedEntries.forEach(entryData => {
        try {
          const entry: HistoryEntry = {
            ...entryData,
            timestamp: new Date(entryData.timestamp),
            id: entryData.id || this.generateId(),
          };

          // Validate entry
          const result = historyEntrySchema.safeParse(entry);
          if (result.success) {
            this.entries.push(result.data);
            importCount++;
          }
        } catch (error) {
          console.warn('Failed to import entry:', error);
        }
      });

      this.trimHistory();
      this.saveHistory();
      return importCount;
    } catch (error) {
      console.error('Failed to import history:', error);
      return 0;
    }
  }

  // Private helper methods
  private generateId(): string {
    return `hist_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private trimHistory(): void {
    if (this.entries.length > TransformationHistory.MAX_ENTRIES) {
      // Keep favorites and recent entries, remove oldest non-favorites
      const favorites = this.entries.filter(e => e.favorite);
      const nonFavorites = this.entries.filter(e => !e.favorite);
      
      const keepCount = TransformationHistory.MAX_ENTRIES - favorites.length;
      const recentNonFavorites = nonFavorites.slice(0, Math.max(0, keepCount));
      
      this.entries = [...recentNonFavorites, ...favorites].sort(
        (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
      );
    }
  }

  private saveHistory(): void {
    try {
      const data = this.entries.map(entry => ({
        ...entry,
        timestamp: entry.timestamp.toISOString(),
      }));
      localStorage.setItem(TransformationHistory.STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('Failed to save history:', error);
    }
  }

  private loadHistory(): void {
    try {
      const stored = localStorage.getItem(TransformationHistory.STORAGE_KEY);
      if (stored) {
        const data = JSON.parse(stored);
        this.entries = data.map((entry: any) => ({
          ...entry,
          timestamp: new Date(entry.timestamp),
        }));
      }
    } catch (error) {
      console.error('Failed to load history:', error);
    }
  }
}
