import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Rocket, 
  Key, 
  Settings, 
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  AlertTriangle,
  Info,
  Zap,
  Shield,
  Code,
  Play,
  ExternalLink
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { HStack, VStack } from '@/components/ui/layout';
import { Typography } from '@/components/ui/typography';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  component: React.ReactNode;
  validation?: () => boolean;
  optional?: boolean;
}

interface OnboardingWizardProps {
  onComplete: () => void;
  className?: string;
}

interface OnboardingData {
  apiKeys: {
    openai: string;
    anthropic: string;
    github: string;
  };
  preferences: {
    defaultModel: string;
    maxTokens: number;
    temperature: number;
    enableTelemetry: boolean;
  };
  profile: {
    name: string;
    email: string;
    role: string;
    organization: string;
  };
}

export const OnboardingWizard: React.FC<OnboardingWizardProps> = ({ onComplete, className }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [data, setData] = useState<OnboardingData>({
    apiKeys: {
      openai: '',
      anthropic: '',
      github: ''
    },
    preferences: {
      defaultModel: 'gpt-4-turbo',
      maxTokens: 4000,
      temperature: 0.7,
      enableTelemetry: true
    },
    profile: {
      name: '',
      email: '',
      role: '',
      organization: ''
    }
  });
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [isValidating, setIsValidating] = useState(false);

  // Welcome Step
  const WelcomeStep = () => (
    <div className="text-center space-y-6">
      <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
        <Rocket className="w-8 h-8 text-primary" />
      </div>
      <div>
        <Typography variant="h4" className="mb-2">Welcome to Metamorphic Reactor!</Typography>
        <Typography variant="body-lg" className="text-muted-foreground">
          Let's get you set up with everything you need to start transforming code with AI.
        </Typography>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-left">
        <Card className="p-4">
          <Key className="w-6 h-6 text-blue-500 mb-2" />
          <Typography variant="body-sm" className="font-medium">API Configuration</Typography>
          <Typography variant="caption" className="text-muted-foreground">
            Connect your AI provider accounts
          </Typography>
        </Card>
        <Card className="p-4">
          <Settings className="w-6 h-6 text-green-500 mb-2" />
          <Typography variant="body-sm" className="font-medium">Preferences</Typography>
          <Typography variant="caption" className="text-muted-foreground">
            Customize your transformation settings
          </Typography>
        </Card>
        <Card className="p-4">
          <Zap className="w-6 h-6 text-purple-500 mb-2" />
          <Typography variant="body-sm" className="font-medium">First Transform</Typography>
          <Typography variant="caption" className="text-muted-foreground">
            Try your first code transformation
          </Typography>
        </Card>
      </div>
    </div>
  );

  // Profile Step
  const ProfileStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <Typography variant="h5" className="mb-2">Tell us about yourself</Typography>
        <Typography variant="body-sm" className="text-muted-foreground">
          This helps us personalize your experience
        </Typography>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label>Full Name *</Label>
          <Input
            value={data.profile.name}
            onChange={(e) => setData(prev => ({
              ...prev,
              profile: { ...prev.profile, name: e.target.value }
            }))}
            placeholder="John Doe"
          />
        </div>
        
        <div className="space-y-2">
          <Label>Email *</Label>
          <Input
            type="email"
            value={data.profile.email}
            onChange={(e) => setData(prev => ({
              ...prev,
              profile: { ...prev.profile, email: e.target.value }
            }))}
            placeholder="<EMAIL>"
          />
        </div>
        
        <div className="space-y-2">
          <Label>Role</Label>
          <Input
            value={data.profile.role}
            onChange={(e) => setData(prev => ({
              ...prev,
              profile: { ...prev.profile, role: e.target.value }
            }))}
            placeholder="Software Engineer"
          />
        </div>
        
        <div className="space-y-2">
          <Label>Organization</Label>
          <Input
            value={data.profile.organization}
            onChange={(e) => setData(prev => ({
              ...prev,
              profile: { ...prev.profile, organization: e.target.value }
            }))}
            placeholder="Acme Corp"
          />
        </div>
      </div>
    </div>
  );

  // API Keys Step
  const ApiKeysStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <Typography variant="h5" className="mb-2">Configure API Keys</Typography>
        <Typography variant="body-sm" className="text-muted-foreground">
          Connect your AI provider accounts to enable transformations
        </Typography>
      </div>

      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          Your API keys are encrypted and stored securely. They're only used for your transformations.
        </AlertDescription>
      </Alert>

      <div className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label>OpenAI API Key *</Label>
            <Button variant="link" size="sm" className="h-auto p-0">
              <ExternalLink className="w-3 h-3 mr-1" />
              Get API Key
            </Button>
          </div>
          <Input
            type="password"
            value={data.apiKeys.openai}
            onChange={(e) => setData(prev => ({
              ...prev,
              apiKeys: { ...prev.apiKeys, openai: e.target.value }
            }))}
            placeholder="sk-..."
          />
          <Typography variant="caption" className="text-muted-foreground">
            Required for GPT-4 and GPT-3.5 models
          </Typography>
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label>Anthropic API Key</Label>
            <Button variant="link" size="sm" className="h-auto p-0">
              <ExternalLink className="w-3 h-3 mr-1" />
              Get API Key
            </Button>
          </div>
          <Input
            type="password"
            value={data.apiKeys.anthropic}
            onChange={(e) => setData(prev => ({
              ...prev,
              apiKeys: { ...prev.apiKeys, anthropic: e.target.value }
            }))}
            placeholder="sk-ant-..."
          />
          <Typography variant="caption" className="text-muted-foreground">
            Optional: For Claude models
          </Typography>
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label>GitHub Token</Label>
            <Button variant="link" size="sm" className="h-auto p-0">
              <ExternalLink className="w-3 h-3 mr-1" />
              Generate Token
            </Button>
          </div>
          <Input
            type="password"
            value={data.apiKeys.github}
            onChange={(e) => setData(prev => ({
              ...prev,
              apiKeys: { ...prev.apiKeys, github: e.target.value }
            }))}
            placeholder="ghp_..."
          />
          <Typography variant="caption" className="text-muted-foreground">
            Optional: For automatic PR creation
          </Typography>
        </div>
      </div>
    </div>
  );

  // Preferences Step
  const PreferencesStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <Typography variant="h5" className="mb-2">Set Your Preferences</Typography>
        <Typography variant="body-sm" className="text-muted-foreground">
          Configure default settings for your transformations
        </Typography>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>Default Model</Label>
            <select
              value={data.preferences.defaultModel}
              onChange={(e) => setData(prev => ({
                ...prev,
                preferences: { ...prev.preferences, defaultModel: e.target.value }
              }))}
              className="w-full px-3 py-2 border border-border rounded-md bg-background"
            >
              <option value="gpt-4-turbo">GPT-4 Turbo</option>
              <option value="gpt-4">GPT-4</option>
              <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
              <option value="claude-3-sonnet">Claude 3 Sonnet</option>
              <option value="claude-3-opus">Claude 3 Opus</option>
            </select>
          </div>

          <div className="space-y-2">
            <Label>Max Tokens</Label>
            <Input
              type="number"
              value={data.preferences.maxTokens}
              onChange={(e) => setData(prev => ({
                ...prev,
                preferences: { ...prev.preferences, maxTokens: parseInt(e.target.value) || 4000 }
              }))}
              min={1000}
              max={8000}
            />
          </div>
        </div>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label>Temperature</Label>
            <Input
              type="number"
              value={data.preferences.temperature}
              onChange={(e) => setData(prev => ({
                ...prev,
                preferences: { ...prev.preferences, temperature: parseFloat(e.target.value) || 0.7 }
              }))}
              min={0}
              max={2}
              step={0.1}
            />
            <Typography variant="caption" className="text-muted-foreground">
              0 = Conservative, 2 = Creative
            </Typography>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="telemetry"
              checked={data.preferences.enableTelemetry}
              onCheckedChange={(checked) => setData(prev => ({
                ...prev,
                preferences: { ...prev.preferences, enableTelemetry: !!checked }
              }))}
            />
            <Label htmlFor="telemetry" className="text-sm">
              Enable telemetry to help improve the service
            </Label>
          </div>
        </div>
      </div>
    </div>
  );

  // Completion Step
  const CompletionStep = () => (
    <div className="text-center space-y-6">
      <div className="mx-auto w-16 h-16 bg-green-500/10 rounded-full flex items-center justify-center">
        <CheckCircle className="w-8 h-8 text-green-500" />
      </div>
      <div>
        <Typography variant="h4" className="mb-2">You're all set!</Typography>
        <Typography variant="body-lg" className="text-muted-foreground">
          Your Metamorphic Reactor is configured and ready to transform code.
        </Typography>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card className="p-4 text-left">
          <Code className="w-6 h-6 text-blue-500 mb-2" />
          <Typography variant="body-sm" className="font-medium">Start Transforming</Typography>
          <Typography variant="caption" className="text-muted-foreground">
            Head to the dashboard to begin your first transformation
          </Typography>
        </Card>
        <Card className="p-4 text-left">
          <Info className="w-6 h-6 text-purple-500 mb-2" />
          <Typography variant="body-sm" className="font-medium">Learn More</Typography>
          <Typography variant="caption" className="text-muted-foreground">
            Check out our documentation and examples
          </Typography>
        </Card>
      </div>
    </div>
  );

  const steps: OnboardingStep[] = [
    {
      id: 'welcome',
      title: 'Welcome',
      description: 'Get started with Metamorphic Reactor',
      icon: <Rocket className="w-5 h-5" />,
      component: <WelcomeStep />
    },
    {
      id: 'profile',
      title: 'Profile',
      description: 'Tell us about yourself',
      icon: <Settings className="w-5 h-5" />,
      component: <ProfileStep />,
      validation: () => data.profile.name.length > 0 && data.profile.email.length > 0
    },
    {
      id: 'api-keys',
      title: 'API Keys',
      description: 'Configure your AI providers',
      icon: <Key className="w-5 h-5" />,
      component: <ApiKeysStep />,
      validation: () => data.apiKeys.openai.length > 0
    },
    {
      id: 'preferences',
      title: 'Preferences',
      description: 'Set your default settings',
      icon: <Settings className="w-5 h-5" />,
      component: <PreferencesStep />
    },
    {
      id: 'complete',
      title: 'Complete',
      description: 'You\'re ready to go!',
      icon: <CheckCircle className="w-5 h-5" />,
      component: <CompletionStep />
    }
  ];

  const validateStep = async (stepIndex: number) => {
    const step = steps[stepIndex];
    if (!step.validation) return true;

    setIsValidating(true);
    setValidationErrors([]);

    try {
      const isValid = step.validation();
      if (!isValid) {
        setValidationErrors(['Please fill in all required fields']);
        return false;
      }

      // Additional API validation for API keys step
      if (step.id === 'api-keys' && data.apiKeys.openai) {
        try {
          const response = await fetch('/api/validate-key', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ provider: 'openai', key: data.apiKeys.openai })
          });
          
          if (!response.ok) {
            setValidationErrors(['Invalid OpenAI API key']);
            return false;
          }
        } catch (error) {
          setValidationErrors(['Failed to validate API key']);
          return false;
        }
      }

      return true;
    } catch (error) {
      setValidationErrors(['Validation failed']);
      return false;
    } finally {
      setIsValidating(false);
    }
  };

  const handleNext = async () => {
    const isValid = await validateStep(currentStep);
    if (isValid && currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
      setValidationErrors([]);
    }
  };

  const handleComplete = async () => {
    try {
      // Save onboarding data
      const response = await fetch('/api/onboarding/complete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });

      if (response.ok) {
        onComplete();
      }
    } catch (error) {
      console.error('Failed to complete onboarding:', error);
    }
  };

  const progress = ((currentStep + 1) / steps.length) * 100;

  return (
    <div className={cn("max-w-4xl mx-auto", className)}>
      <Card>
        <CardHeader>
          <div className="space-y-4">
            {/* Progress */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Step {currentStep + 1} of {steps.length}</span>
                <span>{Math.round(progress)}% complete</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>

            {/* Step Navigation */}
            <div className="flex items-center justify-center space-x-4 overflow-x-auto">
              {steps.map((step, index) => (
                <div
                  key={step.id}
                  className={cn(
                    "flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors",
                    index === currentStep
                      ? "bg-primary text-primary-foreground"
                      : index < currentStep
                      ? "bg-green-500/10 text-green-500"
                      : "bg-muted text-muted-foreground"
                  )}
                >
                  {index < currentStep ? (
                    <CheckCircle className="w-4 h-4" />
                  ) : (
                    step.icon
                  )}
                  <span className="text-sm font-medium whitespace-nowrap">
                    {step.title}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Current Step Content */}
          <div className="min-h-[400px]">
            {steps[currentStep].component}
          </div>

          {/* Validation Errors */}
          {validationErrors.length > 0 && (
            <Alert className="border-red-500/30 bg-red-900/10">
              <AlertTriangle className="h-4 w-4 text-red-500" />
              <AlertDescription className="text-red-200">
                <ul className="list-disc list-inside">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {/* Navigation */}
          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 0}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Previous
            </Button>

            {currentStep === steps.length - 1 ? (
              <Button onClick={handleComplete}>
                <Play className="w-4 h-4 mr-2" />
                Get Started
              </Button>
            ) : (
              <Button onClick={handleNext} disabled={isValidating}>
                {isValidating ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Validating...
                  </>
                ) : (
                  <>
                    Next
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </>
                )}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default OnboardingWizard;
