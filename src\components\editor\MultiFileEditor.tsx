import { useState, useRef, use<PERSON><PERSON>back, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ContextMenu, ContextMenuContent, ContextMenuItem, ContextMenuTrigger } from '@/components/ui/context-menu';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CodeEditor } from '../CodeEditor';
import { 
  Plus, 
  X, 
  Save, 
  FileText, 
  Folder, 
  FolderOpen, 
  ChevronRight, 
  ChevronDown,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Copy,
  Trash2,
  Edit,
  Download,
  Upload,
  Search,
  Settings
} from 'lucide-react';

interface EditorFile {
  id: string;
  name: string;
  path: string;
  content: string;
  language: string;
  isDirty: boolean;
  isReadOnly: boolean;
  lastModified: Date;
}

interface FileTreeNode {
  id: string;
  name: string;
  type: 'file' | 'folder';
  path: string;
  children?: FileTreeNode[];
  isExpanded?: boolean;
}

interface MultiFileEditorProps {
  files: EditorFile[];
  onFileChange: (fileId: string, content: string) => void;
  onFileCreate: (name: string, path: string, language: string) => void;
  onFileDelete: (fileId: string) => void;
  onFileRename: (fileId: string, newName: string) => void;
  onFileSave: (fileId: string) => void;
  onFileSaveAll: () => void;
  fileTree?: FileTreeNode[];
  showFileTree?: boolean;
  enableAutoSave?: boolean;
  autoSaveDelay?: number;
}

const getLanguageFromExtension = (filename: string): string => {
  const ext = filename.split('.').pop()?.toLowerCase();
  const languageMap: Record<string, string> = {
    'js': 'javascript',
    'jsx': 'javascript',
    'ts': 'typescript',
    'tsx': 'typescript',
    'py': 'python',
    'java': 'java',
    'cpp': 'cpp',
    'c': 'c',
    'cs': 'csharp',
    'php': 'php',
    'rb': 'ruby',
    'go': 'go',
    'rs': 'rust',
    'html': 'html',
    'css': 'css',
    'scss': 'scss',
    'sass': 'sass',
    'json': 'json',
    'xml': 'xml',
    'yaml': 'yaml',
    'yml': 'yaml',
    'md': 'markdown',
    'sql': 'sql',
    'sh': 'shell',
    'bash': 'shell'
  };
  return languageMap[ext || ''] || 'plaintext';
};

export const MultiFileEditor = ({
  files,
  onFileChange,
  onFileCreate,
  onFileDelete,
  onFileRename,
  onFileSave,
  onFileSaveAll,
  fileTree,
  showFileTree = true,
  enableAutoSave = false,
  autoSaveDelay = 2000
}: MultiFileEditorProps) => {
  const [activeFileId, setActiveFileId] = useState<string>(files[0]?.id || '');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newFileName, setNewFileName] = useState('');
  const [newFilePath, setNewFilePath] = useState('/');
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const autoSaveTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map());

  const activeFile = files.find(f => f.id === activeFileId);

  // Auto-save functionality
  const scheduleAutoSave = useCallback((fileId: string) => {
    if (!enableAutoSave) return;

    const existingTimeout = autoSaveTimeouts.current.get(fileId);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    const timeout = setTimeout(() => {
      onFileSave(fileId);
      autoSaveTimeouts.current.delete(fileId);
    }, autoSaveDelay);

    autoSaveTimeouts.current.set(fileId, timeout);
  }, [enableAutoSave, autoSaveDelay, onFileSave]);

  const handleFileContentChange = (content: string) => {
    if (activeFileId) {
      onFileChange(activeFileId, content);
      scheduleAutoSave(activeFileId);
    }
  };

  const handleCreateFile = () => {
    if (newFileName.trim()) {
      const language = getLanguageFromExtension(newFileName);
      onFileCreate(newFileName.trim(), newFilePath, language);
      setNewFileName('');
      setNewFilePath('/');
      setIsCreateDialogOpen(false);
    }
  };

  const handleCloseTab = (fileId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    const file = files.find(f => f.id === fileId);
    
    if (file?.isDirty) {
      const shouldSave = window.confirm(`${file.name} has unsaved changes. Save before closing?`);
      if (shouldSave) {
        onFileSave(fileId);
      }
    }

    // Switch to another tab if closing active tab
    if (fileId === activeFileId) {
      const currentIndex = files.findIndex(f => f.id === fileId);
      const nextFile = files[currentIndex + 1] || files[currentIndex - 1];
      if (nextFile) {
        setActiveFileId(nextFile.id);
      }
    }

    onFileDelete(fileId);
  };

  const toggleFolder = (folderId: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(folderId)) {
        newSet.delete(folderId);
      } else {
        newSet.add(folderId);
      }
      return newSet;
    });
  };

  const renderFileTree = (nodes: FileTreeNode[], level = 0) => {
    return nodes
      .filter(node => 
        searchTerm === '' || 
        node.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .map(node => (
        <div key={node.id} style={{ paddingLeft: `${level * 16}px` }}>
          <ContextMenu>
            <ContextMenuTrigger>
              <div
                className={`flex items-center space-x-2 py-1 px-2 hover:bg-muted rounded cursor-pointer ${
                  node.type === 'file' && node.id === activeFileId ? 'bg-muted' : ''
                }`}
                onClick={() => {
                  if (node.type === 'file') {
                    setActiveFileId(node.id);
                  } else {
                    toggleFolder(node.id);
                  }
                }}
              >
                {node.type === 'folder' ? (
                  <>
                    {expandedFolders.has(node.id) ? (
                      <ChevronDown className="w-4 h-4" />
                    ) : (
                      <ChevronRight className="w-4 h-4" />
                    )}
                    {expandedFolders.has(node.id) ? (
                      <FolderOpen className="w-4 h-4" />
                    ) : (
                      <Folder className="w-4 h-4" />
                    )}
                  </>
                ) : (
                  <>
                    <div className="w-4" />
                    <FileText className="w-4 h-4" />
                  </>
                )}
                <span className="text-sm truncate">{node.name}</span>
                {node.type === 'file' && files.find(f => f.id === node.id)?.isDirty && (
                  <div className="w-2 h-2 bg-orange-500 rounded-full" />
                )}
              </div>
            </ContextMenuTrigger>
            <ContextMenuContent>
              {node.type === 'file' ? (
                <>
                  <ContextMenuItem onClick={() => setActiveFileId(node.id)}>
                    <FileText className="w-4 h-4 mr-2" />
                    Open
                  </ContextMenuItem>
                  <ContextMenuItem onClick={() => onFileSave(node.id)}>
                    <Save className="w-4 h-4 mr-2" />
                    Save
                  </ContextMenuItem>
                  <ContextMenuItem onClick={() => {
                    const newName = prompt('Enter new name:', node.name);
                    if (newName) onFileRename(node.id, newName);
                  }}>
                    <Edit className="w-4 h-4 mr-2" />
                    Rename
                  </ContextMenuItem>
                  <ContextMenuItem onClick={() => {
                    const file = files.find(f => f.id === node.id);
                    if (file) navigator.clipboard.writeText(file.content);
                  }}>
                    <Copy className="w-4 h-4 mr-2" />
                    Copy Content
                  </ContextMenuItem>
                  <ContextMenuItem onClick={() => onFileDelete(node.id)}>
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </ContextMenuItem>
                </>
              ) : (
                <>
                  <ContextMenuItem onClick={() => setIsCreateDialogOpen(true)}>
                    <Plus className="w-4 h-4 mr-2" />
                    New File
                  </ContextMenuItem>
                  <ContextMenuItem onClick={() => toggleFolder(node.id)}>
                    {expandedFolders.has(node.id) ? (
                      <>
                        <ChevronRight className="w-4 h-4 mr-2" />
                        Collapse
                      </>
                    ) : (
                      <>
                        <ChevronDown className="w-4 h-4 mr-2" />
                        Expand
                      </>
                    )}
                  </ContextMenuItem>
                </>
              )}
            </ContextMenuContent>
          </ContextMenu>
          
          {node.type === 'folder' && 
           expandedFolders.has(node.id) && 
           node.children && 
           renderFileTree(node.children, level + 1)}
        </div>
      ));
  };

  return (
    <div className="w-full h-full flex">
      {/* File Tree Sidebar */}
      {showFileTree && (
        <div className="w-64 border-r border-border bg-muted/30">
          <div className="p-3 border-b border-border">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-medium text-sm">Explorer</h3>
              <div className="flex items-center space-x-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsCreateDialogOpen(true)}
                  className="h-6 w-6 p-0"
                >
                  <Plus className="w-3 h-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onFileSaveAll}
                  className="h-6 w-6 p-0"
                >
                  <Save className="w-3 h-3" />
                </Button>
              </div>
            </div>
            
            <div className="relative">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-muted-foreground" />
              <Input
                placeholder="Search files..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-7 h-7 text-xs"
              />
            </div>
          </div>
          
          <div className="p-2 overflow-auto">
            {fileTree ? (
              renderFileTree(fileTree)
            ) : (
              <div className="space-y-1">
                {files.map(file => (
                  <div
                    key={file.id}
                    className={`flex items-center space-x-2 py-1 px-2 hover:bg-muted rounded cursor-pointer ${
                      file.id === activeFileId ? 'bg-muted' : ''
                    }`}
                    onClick={() => setActiveFileId(file.id)}
                  >
                    <FileText className="w-4 h-4" />
                    <span className="text-sm truncate">{file.name}</span>
                    {file.isDirty && (
                      <div className="w-2 h-2 bg-orange-500 rounded-full" />
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Editor Area */}
      <div className="flex-1 flex flex-col">
        {/* Tab Bar */}
        <div className="border-b border-border bg-background">
          <div className="flex items-center">
            <div className="flex-1 flex overflow-x-auto">
              {files.map(file => (
                <div
                  key={file.id}
                  className={`flex items-center space-x-2 px-3 py-2 border-r border-border cursor-pointer hover:bg-muted/50 ${
                    file.id === activeFileId ? 'bg-muted' : ''
                  }`}
                  onClick={() => setActiveFileId(file.id)}
                >
                  <FileText className="w-4 h-4" />
                  <span className="text-sm">{file.name}</span>
                  {file.isDirty && (
                    <div className="w-2 h-2 bg-orange-500 rounded-full" />
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => handleCloseTab(file.id, e)}
                    className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </div>
              ))}
            </div>
            
            <div className="flex items-center space-x-1 px-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsCreateDialogOpen(true)}
                className="h-7 w-7 p-0"
              >
                <Plus className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onFileSaveAll}
                className="h-7 w-7 p-0"
              >
                <Save className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Editor Content */}
        <div className="flex-1">
          {activeFile ? (
            <CodeEditor
              value={activeFile.content}
              onChange={handleFileContentChange}
              language={activeFile.language}
              readOnly={activeFile.isReadOnly}
              fileName={activeFile.name}
              modelUri={`file:///${activeFile.path}`}
              enableIntelliSense={true}
              enableErrorDetection={true}
              enableCodeLens={true}
              onSave={() => onFileSave(activeFile.id)}
            />
          ) : (
            <div className="flex items-center justify-center h-full text-muted-foreground">
              <div className="text-center">
                <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No file selected</p>
                <p className="text-sm">Open a file from the explorer or create a new one</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Create File Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New File</DialogTitle>
            <DialogDescription>
              Create a new file in your project
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="fileName">File Name</Label>
              <Input
                id="fileName"
                placeholder="example.tsx"
                value={newFileName}
                onChange={(e) => setNewFileName(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleCreateFile()}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="filePath">Path</Label>
              <Input
                id="filePath"
                placeholder="/src/components/"
                value={newFilePath}
                onChange={(e) => setNewFilePath(e.target.value)}
              />
            </div>
            
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateFile} disabled={!newFileName.trim()}>
                Create
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
