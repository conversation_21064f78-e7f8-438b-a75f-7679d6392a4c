// Core UI Components - Reusable and modular component library for Metamorphic Reactor

// Layout Components
export * from "./layout"
export * from "./typography"

// Form Components
export * from "./smart-form"
export * from "./dropdown"

// Feedback Components
export * from "./feedback-system"
export * from "./enhanced-tooltip"

// Navigation Components
export * from "./breadcrumb"
export * from "./keyboard-shortcuts"

// Modal Components
export * from "./modal"

// Icon System
export * from "./icon"

// Workflow Components
export * from "./workflow-guide"

// Base Components (from shadcn/ui)
export * from "./button"
export * from "./badge"
export * from "./card"
export * from "./input"
export * from "./label"
export * from "./textarea"
export * from "./select"
export * from "./checkbox"
export * from "./radio-group"
export * from "./switch"
export * from "./slider"
export * from "./progress"
export * from "./separator"
export * from "./avatar"
export * from "./alert"
export * from "./toast"
export * from "./toaster"
export * from "./sonner"
export * from "./tooltip"
export * from "./popover"
export * from "./dialog"
export * from "./sheet"
export * from "./tabs"
export * from "./accordion"
export * from "./collapsible"
export * from "./menubar"
export * from "./navigation-menu"
export * from "./command"
export * from "./calendar"
export * from "./date-picker"
export * from "./form"
export * from "./table"
export * from "./pagination"
export * from "./skeleton"
export * from "./scroll-area"
export * from "./resizable"

// Component Patterns and Utilities
export { cn } from "@/lib/utils"

// Common component props and types
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface InteractiveComponentProps extends BaseComponentProps {
  disabled?: boolean
  loading?: boolean
  onClick?: () => void
}

export interface FormComponentProps extends BaseComponentProps {
  label?: string
  description?: string
  error?: string
  required?: boolean
}

// Component composition utilities
export const withLoading = <P extends object>(
  Component: React.ComponentType<P>
) => {
  return React.forwardRef<any, P & { loading?: boolean }>((props, ref) => {
    const { loading, ...rest } = props
    
    if (loading) {
      return (
        <div className="flex items-center justify-center p-4">
          <LoadingIcon className="animate-spin" />
        </div>
      )
    }
    
    return <Component ref={ref} {...(rest as P)} />
  })
}

export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>
) => {
  return class extends React.Component<P, { hasError: boolean }> {
    constructor(props: P) {
      super(props)
      this.state = { hasError: false }
    }

    static getDerivedStateFromError() {
      return { hasError: true }
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
      console.error('Component error:', error, errorInfo)
    }

    render() {
      if (this.state.hasError) {
        return (
          <div className="p-4 border border-destructive rounded-lg bg-destructive/10">
            <Typography variant="body-sm" className="text-destructive">
              Something went wrong. Please try refreshing the page.
            </Typography>
          </div>
        )
      }

      return <Component {...this.props} />
    }
  }
}

// Responsive utilities
export const useBreakpoint = () => {
  const [breakpoint, setBreakpoint] = React.useState<'sm' | 'md' | 'lg' | 'xl' | '2xl'>('md')
  
  React.useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth
      if (width < 640) setBreakpoint('sm')
      else if (width < 768) setBreakpoint('md')
      else if (width < 1024) setBreakpoint('lg')
      else if (width < 1280) setBreakpoint('xl')
      else setBreakpoint('2xl')
    }
    
    updateBreakpoint()
    window.addEventListener('resize', updateBreakpoint)
    return () => window.removeEventListener('resize', updateBreakpoint)
  }, [])
  
  return breakpoint
}

// Theme utilities
export const useThemeColors = () => {
  const [colors, setColors] = React.useState({
    primary: 'hsl(238.7 83.5% 66.7%)',
    secondary: 'hsl(210 40% 96.1%)',
    background: 'hsl(0 0% 100%)',
    foreground: 'hsl(222.2 84% 4.9%)',
  })
  
  React.useEffect(() => {
    const updateColors = () => {
      const root = document.documentElement
      const computedStyle = getComputedStyle(root)
      
      setColors({
        primary: computedStyle.getPropertyValue('--primary').trim(),
        secondary: computedStyle.getPropertyValue('--secondary').trim(),
        background: computedStyle.getPropertyValue('--background').trim(),
        foreground: computedStyle.getPropertyValue('--foreground').trim(),
      })
    }
    
    updateColors()
    
    // Listen for theme changes
    const observer = new MutationObserver(updateColors)
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    })
    
    return () => observer.disconnect()
  }, [])
  
  return colors
}

// Animation utilities
export const fadeIn = {
  initial: { opacity: 0 },
  animate: { opacity: 1 },
  exit: { opacity: 0 },
  transition: { duration: 0.2 }
}

export const slideIn = {
  initial: { opacity: 0, y: 10 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -10 },
  transition: { duration: 0.2 }
}

export const scaleIn = {
  initial: { opacity: 0, scale: 0.95 },
  animate: { opacity: 1, scale: 1 },
  exit: { opacity: 0, scale: 0.95 },
  transition: { duration: 0.2 }
}

// Component testing utilities
export const createMockProps = <T extends object>(overrides: Partial<T> = {}): T => {
  const baseProps = {
    className: '',
    children: 'Mock content',
    onClick: jest.fn(),
    onChange: jest.fn(),
    onSubmit: jest.fn(),
  }
  
  return { ...baseProps, ...overrides } as T
}

// Performance utilities
export const useDebouncedValue = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = React.useState<T>(value)
  
  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)
    
    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])
  
  return debouncedValue
}

export const useThrottledCallback = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const lastRan = React.useRef(Date.now())
  
  return React.useCallback(
    ((...args) => {
      if (Date.now() - lastRan.current >= delay) {
        callback(...args)
        lastRan.current = Date.now()
      }
    }) as T,
    [callback, delay]
  )
}

// Accessibility utilities
export const useA11yAnnouncement = () => {
  const announce = React.useCallback((message: string) => {
    const announcement = document.createElement('div')
    announcement.setAttribute('aria-live', 'polite')
    announcement.setAttribute('aria-atomic', 'true')
    announcement.className = 'sr-only'
    announcement.textContent = message
    
    document.body.appendChild(announcement)
    
    setTimeout(() => {
      document.body.removeChild(announcement)
    }, 1000)
  }, [])
  
  return announce
}

// Import React for the utilities
import * as React from "react"
import { LoadingIcon } from "./icon"
import { Typography } from "./typography"
