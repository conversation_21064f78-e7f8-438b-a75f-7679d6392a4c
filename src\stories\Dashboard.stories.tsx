import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { <PERSON><PERSON><PERSON><PERSON>out<PERSON> } from 'react-router-dom';
import Dashboard from '../pages/Dashboard';

// Mock the hooks for Storybook
const mockUseCodeTransformation = () => ({
  isRunning: false,
  transformedCode: '',
  diffContent: '',
  logs: [
    {
      id: 1,
      agent: 'planner' as const,
      message: 'Starting code analysis...',
      timestamp: new Date(),
    },
    {
      id: 2,
      agent: 'critic' as const,
      message: 'Evaluating generated patch...',
      timestamp: new Date(),
    },
  ],
  handleRunLoop: () => console.log('Run loop clicked'),
  stopTransformation: () => console.log('Stop transformation clicked'),
});

const mockUseTransformationHistory = () => ({
  handleApplyChanges: () => console.log('Apply changes clicked'),
  handleDownloadCode: () => console.log('Download code clicked'),
});

const mockUseKeyboardShortcuts = () => {};

// Mock the components for Storybook
jest.mock('../hooks/useCodeTransformation', () => ({
  useCodeTransformation: mockUseCodeTransformation,
}));

jest.mock('../hooks/useTransformationHistory', () => ({
  useTransformationHistory: mockUseTransformationHistory,
}));

jest.mock('../hooks/useKeyboardShortcuts', () => ({
  useKeyboardShortcuts: mockUseKeyboardShortcuts,
}));

const meta: Meta<typeof Dashboard> = {
  title: 'Pages/Dashboard',
  component: Dashboard,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: `
# Dashboard Component

The main dashboard interface for the Metamorphic Reactor application. Features a tabbed interface with:

- **Code Editor**: Interactive code transformation workspace
- **Overview**: System metrics and performance indicators  
- **Analytics**: Usage statistics and transformation analytics
- **Admin**: Administrative controls and system management
- **Workspace**: Collaboration and project management
- **Notifications**: Alerts and system notifications

## Features

- 🎨 **Responsive Design**: Adapts to mobile, tablet, and desktop viewports
- ♿ **Accessibility**: WCAG 2.1 AA compliant with proper ARIA labels
- 🔄 **Real-time Updates**: Live streaming of agent logs and system status
- 🎯 **Keyboard Navigation**: Full keyboard accessibility support
- 📱 **Mobile Optimized**: Touch-friendly interface with appropriate target sizes

## Usage

The dashboard automatically detects the user's context and displays relevant information.
Real-time updates are enabled by default and can be paused/resumed as needed.
        `,
      },
    },
    a11y: {
      config: {
        rules: [
          {
            id: 'color-contrast',
            enabled: true,
          },
          {
            id: 'keyboard-navigation',
            enabled: true,
          },
        ],
      },
    },
  },
  decorators: [
    (Story) => (
      <BrowserRouter>
        <div className="min-h-screen bg-background">
          <Story />
        </div>
      </BrowserRouter>
    ),
  ],
  argTypes: {
    // No direct props for Dashboard component
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  name: 'Default Dashboard',
  parameters: {
    docs: {
      description: {
        story: 'The default dashboard view with all tabs available.',
      },
    },
  },
};

export const CodeEditorTab: Story = {
  name: 'Code Editor Tab',
  parameters: {
    docs: {
      description: {
        story: 'Dashboard focused on the code editor tab with transformation tools.',
      },
    },
  },
  play: async ({ canvasElement }) => {
    // Auto-select code editor tab
    const codeEditorTab = canvasElement.querySelector('[value="code-editor"]');
    if (codeEditorTab) {
      (codeEditorTab as HTMLElement).click();
    }
  },
};

export const OverviewTab: Story = {
  name: 'Overview Tab',
  parameters: {
    docs: {
      description: {
        story: 'Dashboard overview showing system metrics and performance indicators.',
      },
    },
  },
  play: async ({ canvasElement }) => {
    // Auto-select overview tab
    const overviewTab = canvasElement.querySelector('[value="overview"]');
    if (overviewTab) {
      (overviewTab as HTMLElement).click();
    }
  },
};

export const MobileView: Story = {
  name: 'Mobile View',
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
    docs: {
      description: {
        story: 'Dashboard optimized for mobile devices with stacked layout and icon-only tabs.',
      },
    },
  },
};

export const TabletView: Story = {
  name: 'Tablet View',
  parameters: {
    viewport: {
      defaultViewport: 'tablet',
    },
    docs: {
      description: {
        story: 'Dashboard optimized for tablet devices with responsive grid layout.',
      },
    },
  },
};

export const DarkMode: Story = {
  name: 'Dark Mode',
  parameters: {
    backgrounds: {
      default: 'dark',
    },
    docs: {
      description: {
        story: 'Dashboard in dark mode with proper contrast and semantic colors.',
      },
    },
  },
};

export const HighContrast: Story = {
  name: 'High Contrast',
  parameters: {
    a11y: {
      config: {
        rules: [
          {
            id: 'color-contrast',
            enabled: true,
            options: {
              level: 'AAA',
            },
          },
        ],
      },
    },
    docs: {
      description: {
        story: 'Dashboard with high contrast mode for improved accessibility.',
      },
    },
  },
};

export const WithActiveTransformation: Story = {
  name: 'Active Transformation',
  parameters: {
    docs: {
      description: {
        story: 'Dashboard during an active code transformation with real-time logs.',
      },
    },
  },
  // Override the mock to show active state
  decorators: [
    (Story) => {
      // Mock active transformation state
      const mockActiveTransformation = () => ({
        isRunning: true,
        transformedCode: 'console.log("Transformed code");',
        diffContent: '+ console.log("Transformed code");\n- console.log("Original code");',
        logs: [
          {
            id: 1,
            agent: 'planner' as const,
            message: 'Analyzing code structure...',
            timestamp: new Date(),
          },
          {
            id: 2,
            agent: 'critic' as const,
            message: 'Evaluating transformation quality...',
            timestamp: new Date(),
          },
          {
            id: 3,
            agent: 'system' as const,
            message: 'Transformation in progress...',
            timestamp: new Date(),
          },
        ],
        handleRunLoop: () => console.log('Run loop clicked'),
        stopTransformation: () => console.log('Stop transformation clicked'),
      });

      return (
        <BrowserRouter>
          <div className="min-h-screen bg-background">
            <Story />
          </div>
        </BrowserRouter>
      );
    },
  ],
};
