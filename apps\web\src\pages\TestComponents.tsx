import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { QueueStatus } from '@/components/QueueStatus';
import { CostTrackingWidget } from '@/components/CostTrackingWidget';
import { SetupWizard } from '@/components/setup/SetupWizard';
import { SupabaseConnectionTest } from '@/components/setup/SupabaseConnectionTest';
import { GitHubOAuthFlow } from '@/components/setup/GitHubOAuthFlow';
import { Button } from '@/components/ui/button';
import { useTransformationQueue } from '@/hooks/useTransformationQueue';
import { useCostTracking } from '@/hooks/useCostTracking';
import { JobPriority } from '@/lib/queue/transformationQueue';

const TestComponents: React.FC = () => {
  const { addTransformation, stats } = useTransformationQueue();
  const { recordCost, usageSummary } = useCostTracking();

  const handleAddTestJob = () => {
    addTransformation(
      `function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}`,
      'Optimize this fibonacci function with memoization',
      JobPriority.NORMAL,
      {
        language: 'javascript',
        plannerModel: 'gpt-4-turbo',
        criticModel: 'claude-3-sonnet',
      }
    );
  };

  const handleAddTestCost = () => {
    recordCost({
      provider: 'openai',
      model: 'gpt-4-turbo',
      operation: 'transformation',
      promptTokens: 150,
      completionTokens: 200,
      totalTokens: 350,
      cost: 0.0105, // $0.0105 for the test
      transformationId: `test_${Date.now()}`,
    });
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold">Component Testing Dashboard</h1>
          <p className="text-muted-foreground mt-2">
            Testing the new Metamorphic Reactor components
          </p>
        </div>

        {/* Test Controls */}
        <Card>
          <CardHeader>
            <CardTitle>Test Controls</CardTitle>
            <CardDescription>
              Add test data to see components in action
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-4">
              <Button onClick={handleAddTestJob}>
                Add Test Transformation Job
              </Button>
              <Button onClick={handleAddTestCost}>
                Add Test Cost Entry
              </Button>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold">{stats.total}</div>
                <div className="text-sm text-muted-foreground">Total Jobs</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{stats.running}</div>
                <div className="text-sm text-muted-foreground">Running</div>
              </div>
              <div>
                <div className="text-2xl font-bold">
                  ${usageSummary.today.cost.toFixed(4)}
                </div>
                <div className="text-sm text-muted-foreground">Today's Cost</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{usageSummary.today.transformations}</div>
                <div className="text-sm text-muted-foreground">Today's Jobs</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Queue and Cost Widgets */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <QueueStatus />
          <CostTrackingWidget />
        </div>

        {/* Setup Components */}
        <div className="space-y-6">
          <h2 className="text-2xl font-bold">Setup Components</h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Supabase Connection Test</CardTitle>
              </CardHeader>
              <CardContent>
                <SupabaseConnectionTest />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>GitHub OAuth Flow</CardTitle>
              </CardHeader>
              <CardContent>
                <GitHubOAuthFlow />
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Setup Wizard */}
        <Card>
          <CardHeader>
            <CardTitle>Setup Wizard</CardTitle>
            <CardDescription>
              Complete setup wizard component (opens in modal)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SetupWizard
              onComplete={() => {
                alert('Setup completed!');
              }}
              onSkip={() => {
                alert('Setup skipped!');
              }}
            />
          </CardContent>
        </Card>

        {/* Component Status */}
        <Card>
          <CardHeader>
            <CardTitle>Component Status</CardTitle>
            <CardDescription>
              Overview of implemented components
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="p-4 border rounded-lg">
                <h3 className="font-semibold text-green-600">✅ Queue System</h3>
                <p className="text-sm text-muted-foreground">
                  Job queue with priority, retry logic, and concurrency control
                </p>
              </div>
              <div className="p-4 border rounded-lg">
                <h3 className="font-semibold text-green-600">✅ Cost Tracking</h3>
                <p className="text-sm text-muted-foreground">
                  Real-time cost monitoring with budget caps and alerts
                </p>
              </div>
              <div className="p-4 border rounded-lg">
                <h3 className="font-semibold text-green-600">✅ Setup Wizard</h3>
                <p className="text-sm text-muted-foreground">
                  Guided configuration for Supabase and GitHub
                </p>
              </div>
              <div className="p-4 border rounded-lg">
                <h3 className="font-semibold text-green-600">✅ Dual Agents</h3>
                <p className="text-sm text-muted-foreground">
                  Planner and Critic agents with provider abstraction
                </p>
              </div>
              <div className="p-4 border rounded-lg">
                <h3 className="font-semibold text-green-600">✅ Model Providers</h3>
                <p className="text-sm text-muted-foreground">
                  Unified interface for OpenAI, Anthropic, and Google
                </p>
              </div>
              <div className="p-4 border rounded-lg">
                <h3 className="font-semibold text-green-600">✅ History System</h3>
                <p className="text-sm text-muted-foreground">
                  Complete transformation history with search and export
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TestComponents;
