import React, { useState, useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  ChevronDown, 
  ChevronRight, 
  Copy, 
  Download, 
  Eye, 
  EyeOff,
  GitBranch,
  Plus,
  Minus,
  RotateCcw,
  Search,
  Settings,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface DiffLine {
  type: 'added' | 'removed' | 'unchanged' | 'context';
  oldLineNumber?: number;
  newLineNumber?: number;
  content: string;
  highlight?: boolean;
}

interface DiffChunk {
  header: string;
  lines: DiffLine[];
  oldStart: number;
  oldCount: number;
  newStart: number;
  newCount: number;
  collapsed?: boolean;
}

interface EnhancedDiffViewerProps {
  originalCode?: string;
  transformedCode?: string;
  diffContent?: string;
  patch?: any;
  language?: string;
  onApply?: () => void;
  onRevert?: () => void;
  onDownload?: () => void;
  className?: string;
}

export const EnhancedDiffViewer: React.FC<EnhancedDiffViewerProps> = ({
  originalCode = '',
  transformedCode = '',
  diffContent,
  patch,
  language = 'javascript',
  onApply,
  onRevert,
  onDownload,
  className
}) => {
  const [viewMode, setViewMode] = useState<'unified' | 'split' | 'side-by-side'>('unified');
  const [showWhitespace, setShowWhitespace] = useState(false);
  const [showLineNumbers, setShowLineNumbers] = useState(true);
  const [collapsedChunks, setCollapsedChunks] = useState<Set<number>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const [highlightedLines, setHighlightedLines] = useState<Set<number>>(new Set());

  // Parse diff content into structured chunks
  const diffChunks = useMemo(() => {
    if (diffContent) {
      return parseDiffContent(diffContent);
    } else if (originalCode && transformedCode) {
      return generateDiffFromCode(originalCode, transformedCode);
    }
    return [];
  }, [diffContent, originalCode, transformedCode]);

  // Filter chunks based on search
  const filteredChunks = useMemo(() => {
    if (!searchTerm) return diffChunks;
    
    return diffChunks.filter(chunk => 
      chunk.lines.some(line => 
        line.content.toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  }, [diffChunks, searchTerm]);

  const stats = useMemo(() => {
    const added = diffChunks.reduce((sum, chunk) => 
      sum + chunk.lines.filter(line => line.type === 'added').length, 0
    );
    const removed = diffChunks.reduce((sum, chunk) => 
      sum + chunk.lines.filter(line => line.type === 'removed').length, 0
    );
    return { added, removed, total: added + removed };
  }, [diffChunks]);

  const toggleChunkCollapse = (chunkIndex: number) => {
    const newCollapsed = new Set(collapsedChunks);
    if (newCollapsed.has(chunkIndex)) {
      newCollapsed.delete(chunkIndex);
    } else {
      newCollapsed.add(chunkIndex);
    }
    setCollapsedChunks(newCollapsed);
  };

  const renderLineNumber = (lineNumber?: number) => (
    <span className="inline-block w-12 text-right text-xs text-muted-foreground pr-2 select-none">
      {lineNumber || ''}
    </span>
  );

  const renderLine = (line: DiffLine, index: number) => {
    const isHighlighted = highlightedLines.has(index);
    const showWhitespaceChars = showWhitespace && (line.content.includes(' ') || line.content.includes('\t'));
    
    let content = line.content;
    if (showWhitespaceChars) {
      content = content.replace(/ /g, '·').replace(/\t/g, '→');
    }

    const lineClasses = cn(
      'flex items-start font-mono text-sm leading-relaxed hover:bg-muted/50 transition-colors',
      {
        'bg-green-50 dark:bg-green-900/20 border-l-2 border-l-green-500': line.type === 'added',
        'bg-red-50 dark:bg-red-900/20 border-l-2 border-l-red-500': line.type === 'removed',
        'bg-yellow-50 dark:bg-yellow-900/20': isHighlighted,
      }
    );

    return (
      <div key={index} className={lineClasses}>
        {showLineNumbers && (
          <>
            {renderLineNumber(line.oldLineNumber)}
            {renderLineNumber(line.newLineNumber)}
          </>
        )}
        <span className="w-6 text-center text-xs text-muted-foreground select-none">
          {line.type === 'added' ? '+' : line.type === 'removed' ? '-' : ' '}
        </span>
        <span className="flex-1 px-2 whitespace-pre-wrap break-all">
          {content}
        </span>
      </div>
    );
  };

  const renderUnifiedView = () => (
    <div className="space-y-4">
      {filteredChunks.map((chunk, chunkIndex) => (
        <Card key={chunkIndex} className="overflow-hidden">
          <div 
            className="flex items-center justify-between p-3 bg-muted/50 cursor-pointer hover:bg-muted/70 transition-colors"
            onClick={() => toggleChunkCollapse(chunkIndex)}
          >
            <div className="flex items-center space-x-2">
              {collapsedChunks.has(chunkIndex) ? (
                <ChevronRight className="w-4 h-4" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
              <span className="font-mono text-sm text-muted-foreground">
                {chunk.header}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="text-xs">
                +{chunk.lines.filter(l => l.type === 'added').length}
              </Badge>
              <Badge variant="outline" className="text-xs">
                -{chunk.lines.filter(l => l.type === 'removed').length}
              </Badge>
            </div>
          </div>
          
          {!collapsedChunks.has(chunkIndex) && (
            <div className="border-t">
              {chunk.lines.map((line, lineIndex) => 
                renderLine(line, chunkIndex * 1000 + lineIndex)
              )}
            </div>
          )}
        </Card>
      ))}
    </div>
  );

  const renderSideBySideView = () => (
    <div className="grid grid-cols-2 gap-4">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm text-red-600">Original</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <ScrollArea className="h-96">
            <pre className="p-4 text-sm font-mono whitespace-pre-wrap">
              {originalCode}
            </pre>
          </ScrollArea>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm text-green-600">Transformed</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <ScrollArea className="h-96">
            <pre className="p-4 text-sm font-mono whitespace-pre-wrap">
              {transformedCode}
            </pre>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );

  if (!diffChunks.length && !originalCode && !transformedCode) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center text-muted-foreground">
            <GitBranch className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>No changes to display</p>
            <p className="text-sm">Run a transformation to see the diff</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <CardTitle className="text-lg">Code Changes</CardTitle>
            {stats.total > 0 && (
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="text-green-600">
                  <Plus className="w-3 h-3 mr-1" />
                  {stats.added}
                </Badge>
                <Badge variant="outline" className="text-red-600">
                  <Minus className="w-3 h-3 mr-1" />
                  {stats.removed}
                </Badge>
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowWhitespace(!showWhitespace)}
              className={cn("h-8", showWhitespace && "bg-muted")}
            >
              {showWhitespace ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowLineNumbers(!showLineNumbers)}
              className={cn("h-8", showLineNumbers && "bg-muted")}
            >
              <Settings className="w-4 h-4" />
            </Button>

            {onDownload && (
              <Button variant="ghost" size="sm" onClick={onDownload} className="h-8">
                <Download className="w-4 h-4" />
              </Button>
            )}

            {onApply && (
              <Button variant="default" size="sm" onClick={onApply} className="h-8">
                <Zap className="w-4 h-4 mr-2" />
                Apply
              </Button>
            )}

            {onRevert && (
              <Button variant="outline" size="sm" onClick={onRevert} className="h-8">
                <RotateCcw className="w-4 h-4 mr-2" />
                Revert
              </Button>
            )}
          </div>
        </div>

        {/* Search and View Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="relative">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search changes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8 pr-3 py-1 text-sm border rounded-md bg-background"
              />
            </div>
          </div>

          <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as any)}>
            <TabsList className="h-8">
              <TabsTrigger value="unified" className="text-xs">Unified</TabsTrigger>
              <TabsTrigger value="side-by-side" className="text-xs">Side by Side</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs value={viewMode} className="w-full">
          <TabsContent value="unified" className="mt-0">
            <ScrollArea className="h-96">
              {renderUnifiedView()}
            </ScrollArea>
          </TabsContent>
          
          <TabsContent value="side-by-side" className="mt-0">
            {renderSideBySideView()}
          </TabsContent>
        </Tabs>

        {/* JSON Patch Viewer */}
        {patch && (
          <div className="mt-4 pt-4 border-t">
            <h4 className="text-sm font-medium mb-2">JSON Patch</h4>
            <ScrollArea className="h-32">
              <pre className="text-xs bg-muted p-3 rounded-md overflow-x-auto">
                {JSON.stringify(patch, null, 2)}
              </pre>
            </ScrollArea>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Helper functions
function parseDiffContent(diffContent: string): DiffChunk[] {
  // Simplified diff parser - in production, use a proper diff library
  const lines = diffContent.split('\n');
  const chunks: DiffChunk[] = [];
  let currentChunk: DiffChunk | null = null;

  for (const line of lines) {
    if (line.startsWith('@@')) {
      if (currentChunk) chunks.push(currentChunk);
      
      const match = line.match(/@@ -(\d+),?(\d*) \+(\d+),?(\d*) @@(.*)/);
      if (match) {
        currentChunk = {
          header: line,
          lines: [],
          oldStart: parseInt(match[1]),
          oldCount: parseInt(match[2]) || 1,
          newStart: parseInt(match[3]),
          newCount: parseInt(match[4]) || 1,
        };
      }
    } else if (currentChunk) {
      const type = line.startsWith('+') ? 'added' : 
                   line.startsWith('-') ? 'removed' : 'unchanged';
      
      currentChunk.lines.push({
        type,
        content: line.slice(1),
        oldLineNumber: type !== 'added' ? currentChunk.oldStart + currentChunk.lines.length : undefined,
        newLineNumber: type !== 'removed' ? currentChunk.newStart + currentChunk.lines.length : undefined,
      });
    }
  }

  if (currentChunk) chunks.push(currentChunk);
  return chunks;
}

function generateDiffFromCode(original: string, transformed: string): DiffChunk[] {
  // Simplified diff generation - in production, use a proper diff library
  const originalLines = original.split('\n');
  const transformedLines = transformed.split('\n');
  
  const lines: DiffLine[] = [];
  const maxLines = Math.max(originalLines.length, transformedLines.length);
  
  for (let i = 0; i < maxLines; i++) {
    const originalLine = originalLines[i];
    const transformedLine = transformedLines[i];
    
    if (originalLine === transformedLine) {
      lines.push({
        type: 'unchanged',
        content: originalLine || '',
        oldLineNumber: i + 1,
        newLineNumber: i + 1,
      });
    } else {
      if (originalLine !== undefined) {
        lines.push({
          type: 'removed',
          content: originalLine,
          oldLineNumber: i + 1,
        });
      }
      if (transformedLine !== undefined) {
        lines.push({
          type: 'added',
          content: transformedLine,
          newLineNumber: i + 1,
        });
      }
    }
  }

  return [{
    header: `@@ -1,${originalLines.length} +1,${transformedLines.length} @@`,
    lines,
    oldStart: 1,
    oldCount: originalLines.length,
    newStart: 1,
    newCount: transformedLines.length,
  }];
}

export default EnhancedDiffViewer;
