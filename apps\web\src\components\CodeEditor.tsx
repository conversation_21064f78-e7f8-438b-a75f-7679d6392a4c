
import { useEffect, useRef, useState } from 'react';
import Editor, { Monaco } from '@monaco-editor/react';
import { useTheme } from '@/components/theme-provider';
import { useBreakpoint } from '@/components/ui/mobile-layout';

interface CodeEditorProps {
  value: string;
  onChange: (value: string) => void;
  language: string;
  ariaLabel?: string;
  ariaDescribedBy?: string;
  readOnly?: boolean;
  height?: string | number;
  showMinimap?: boolean;
  enableAutocomplete?: boolean;
  enableCodeFolding?: boolean;
  enableFind?: boolean;
  onSave?: () => void;
  onFormat?: () => void;
  onValidate?: (markers: any[]) => void;
  enableIntelliSense?: boolean;
  enableErrorDetection?: boolean;
  enableCodeLens?: boolean;
  enableBracketMatching?: boolean;
  enableWordHighlight?: boolean;
  enableParameterHints?: boolean;
  customCompletionProvider?: any;
  customHoverProvider?: any;
  fileName?: string;
  modelUri?: string;
}

export const CodeEditor = ({
  value,
  onChange,
  language,
  ariaLabel = "Code editor",
  ariaDescribedBy,
  readOnly = false,
  height = "100%",
  showMinimap,
  enableAutocomplete = true,
  enableCodeFolding = true,
  enableFind = true,
  onSave,
  onFormat,
  onValidate,
  enableIntelliSense = true,
  enableErrorDetection = true,
  enableCodeLens = true,
  enableBracketMatching = true,
  enableWordHighlight = true,
  enableParameterHints = true,
  customCompletionProvider,
  customHoverProvider,
  fileName,
  modelUri
}: CodeEditorProps) => {
  const editorRef = useRef<any>(null);
  const monacoRef = useRef<Monaco | null>(null);
  const { theme } = useTheme();
  const { isMobile, isTablet } = useBreakpoint();
  const [isReady, setIsReady] = useState(false);

  // Default editor settings (responsive)
  const defaultSettings = {
    fontSize: isMobile ? 12 : isTablet ? 13 : 14,
    tabSize: 2,
    wordWrap: true,
    minimap: false,
    lineNumbers: isMobile ? 'off' : 'on',
    glyphMargin: !isMobile,
    folding: !isMobile,
    lineDecorationsWidth: isMobile ? 5 : 10,
    lineNumbersMinChars: isMobile ? 2 : 3
  };

  // Configure Monaco before editor mounts
  const handleBeforeMount = (monaco: Monaco) => {
    monacoRef.current = monaco;

    // Configure TypeScript compiler options for better IntelliSense
    monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
      target: monaco.languages.typescript.ScriptTarget.ES2020,
      allowNonTsExtensions: true,
      moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
      module: monaco.languages.typescript.ModuleKind.CommonJS,
      noEmit: true,
      esModuleInterop: true,
      jsx: monaco.languages.typescript.JsxEmit.React,
      reactNamespace: 'React',
      allowJs: true,
      typeRoots: ['node_modules/@types'],
      strict: true,
      noImplicitAny: false,
      strictNullChecks: true,
      strictFunctionTypes: true,
      noImplicitReturns: true,
      noFallthroughCasesInSwitch: true,
      noUncheckedIndexedAccess: false,
      noImplicitOverride: true
    });

    // Configure JavaScript compiler options
    monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
      target: monaco.languages.typescript.ScriptTarget.ES2020,
      allowNonTsExtensions: true,
      allowJs: true,
      checkJs: true,
      noEmit: true
    });

    // Add common type definitions
    const reactTypes = `
      declare module 'react' {
        export interface Component<P = {}, S = {}> {}
        export function useState<T>(initialState: T | (() => T)): [T, (value: T | ((prev: T) => T)) => void];
        export function useEffect(effect: () => void | (() => void), deps?: any[]): void;
        export function useCallback<T extends (...args: any[]) => any>(callback: T, deps: any[]): T;
        export function useMemo<T>(factory: () => T, deps: any[]): T;
        export function useRef<T>(initialValue: T): { current: T };
        export const Fragment: any;
        export default React;
      }
    `;

    monaco.languages.typescript.typescriptDefaults.addExtraLib(
      reactTypes,
      'file:///node_modules/@types/react/index.d.ts'
    );

    // Add Node.js types
    const nodeTypes = `
      declare const console: {
        log(...args: any[]): void;
        error(...args: any[]): void;
        warn(...args: any[]): void;
        info(...args: any[]): void;
      };
      declare const process: {
        env: Record<string, string>;
        argv: string[];
        exit(code?: number): never;
      };
      declare function setTimeout(callback: () => void, ms: number): number;
      declare function setInterval(callback: () => void, ms: number): number;
      declare function clearTimeout(id: number): void;
      declare function clearInterval(id: number): void;
    `;

    monaco.languages.typescript.typescriptDefaults.addExtraLib(
      nodeTypes,
      'file:///node_modules/@types/node/index.d.ts'
    );

    // Register custom themes
    monaco.editor.defineTheme('metamorphic-dark', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '6A9955', fontStyle: 'italic' },
        { token: 'keyword', foreground: '569CD6', fontStyle: 'bold' },
        { token: 'string', foreground: 'CE9178' },
        { token: 'number', foreground: 'B5CEA8' },
        { token: 'type', foreground: '4EC9B0' },
        { token: 'function', foreground: 'DCDCAA' },
        { token: 'variable', foreground: '9CDCFE' },
      ],
      colors: {
        'editor.background': '#0f172a', // slate-900
        'editor.foreground': '#f8fafc', // slate-50
        'editor.lineHighlightBackground': '#1e293b', // slate-800
        'editor.selectionBackground': '#3730a3', // indigo-700
        'editorCursor.foreground': '#6366f1', // indigo-500
        'editorLineNumber.foreground': '#64748b', // slate-500
        'editorLineNumber.activeForeground': '#f8fafc', // slate-50
      }
    });

    // Register custom completion provider
    if (customCompletionProvider && (language === 'typescript' || language === 'javascript')) {
      monaco.languages.registerCompletionItemProvider(language, customCompletionProvider);
    }

    // Register custom hover provider
    if (customHoverProvider && (language === 'typescript' || language === 'javascript')) {
      monaco.languages.registerHoverProvider(language, customHoverProvider);
    }

    // Register code lens provider for enhanced features
    if (enableCodeLens) {
      monaco.languages.registerCodeLensProvider(language, {
        provideCodeLenses: (model) => {
          const lenses = [];
          const lines = model.getLinesContent();

          lines.forEach((line, index) => {
            // Add code lens for functions
            if (line.includes('function ') || line.includes('const ') && line.includes('=>')) {
              lenses.push({
                range: {
                  startLineNumber: index + 1,
                  startColumn: 1,
                  endLineNumber: index + 1,
                  endColumn: line.length + 1
                },
                id: `lens-${index}`,
                command: {
                  id: 'editor.action.showReferences',
                  title: 'Find References',
                  arguments: [model.uri, { lineNumber: index + 1, column: 1 }]
                }
              });
            }
          });

          return { lenses, dispose: () => {} };
        }
      });
    }

    monaco.editor.defineTheme('metamorphic-light', {
      base: 'vs',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '008000', fontStyle: 'italic' },
        { token: 'keyword', foreground: '0000FF', fontStyle: 'bold' },
        { token: 'string', foreground: 'A31515' },
        { token: 'number', foreground: '098658' },
        { token: 'type', foreground: '267F99' },
        { token: 'function', foreground: '795E26' },
        { token: 'variable', foreground: '001080' },
        { token: 'error', foreground: 'FF0000', fontStyle: 'underline' },
        { token: 'warning', foreground: 'FFA500', fontStyle: 'underline' },
      ],
      colors: {
        'editor.background': '#ffffff',
        'editor.foreground': '#000000',
        'editor.lineHighlightBackground': '#f5f5f5',
        'editor.selectionBackground': '#ADD6FF',
        'editorCursor.foreground': '#6366f1', // indigo-500
        'editorError.foreground': '#FF0000',
        'editorWarning.foreground': '#FFA500',
        'editorInfo.foreground': '#0080FF',
        'editorHint.foreground': '#008000',
      }
    });

    // Enhanced autocomplete for JavaScript/TypeScript
    if (language === 'javascript' || language === 'typescript') {
      monaco.languages.registerCompletionItemProvider(language, {
        provideCompletionItems: (model, position) => {
          const suggestions = [
            {
              label: 'console.log',
              kind: monaco.languages.CompletionItemKind.Snippet,
              insertText: 'console.log(${1:message});',
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'Log a message to the console'
            },
            {
              label: 'function',
              kind: monaco.languages.CompletionItemKind.Snippet,
              insertText: 'function ${1:name}(${2:params}) {\n\t${3:// body}\n}',
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'Create a function'
            },
            {
              label: 'arrow function',
              kind: monaco.languages.CompletionItemKind.Snippet,
              insertText: '(${1:params}) => {\n\t${2:// body}\n}',
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'Create an arrow function'
            }
          ];
          return { suggestions };
        }
      });
    }
  };

  const handleEditorDidMount = (editor: any, monaco: Monaco) => {
    editorRef.current = editor;
    setIsReady(true);

    // Create model with URI for better IntelliSense
    const model = editor.getModel();
    if (modelUri && !model.uri.toString().includes(modelUri)) {
      const newModel = monaco.editor.createModel(
        value,
        language,
        monaco.Uri.parse(modelUri)
      );
      editor.setModel(newModel);
    }

    // Configure editor settings (responsive)
    editor.updateOptions({
      fontSize: defaultSettings.fontSize,
      minimap: { enabled: showMinimap ?? defaultSettings.minimap },
      scrollBeyondLastLine: false,
      automaticLayout: true,
      tabSize: defaultSettings.tabSize,
      wordWrap: defaultSettings.wordWrap ? 'on' : 'off',
      lineNumbers: defaultSettings.lineNumbers,
      glyphMargin: defaultSettings.glyphMargin,
      folding: enableCodeFolding && defaultSettings.folding,
      lineDecorationsWidth: defaultSettings.lineDecorationsWidth,
      lineNumbersMinChars: defaultSettings.lineNumbersMinChars,
      readOnly,
      find: {
        addExtraSpaceOnTop: false,
        autoFindInSelection: 'never',
        seedSearchStringFromSelection: 'always'
      },
      // Enhanced features
      suggest: {
        enabled: enableAutocomplete && enableIntelliSense,
        showKeywords: true,
        showSnippets: true,
        showClasses: true,
        showFunctions: true,
        showVariables: true,
        showModules: true,
        showProperties: true,
        showMethods: true,
        showConstructors: true,
        showFields: true,
        showValues: true,
        showConstants: true,
        showEnums: true,
        showEnumMembers: true,
        showEvents: true,
        showOperators: true,
        showUnits: true,
        showColors: true,
        showFiles: true,
        showReferences: true,
        showFolders: true,
        showTypeParameters: true,
        showIssues: true,
        showUsers: true,
        showStructs: true
      },
      parameterHints: {
        enabled: enableParameterHints,
        cycle: true
      },
      quickSuggestions: {
        other: enableIntelliSense,
        comments: false,
        strings: enableIntelliSense
      },
      quickSuggestionsDelay: 100,
      suggestOnTriggerCharacters: enableIntelliSense,
      acceptSuggestionOnEnter: 'on',
      acceptSuggestionOnCommitCharacter: true,
      wordBasedSuggestions: enableIntelliSense,
      // Error detection
      renderValidationDecorations: enableErrorDetection ? 'on' : 'off',
      // Bracket matching
      matchBrackets: enableBracketMatching ? 'always' : 'never',
      // Word highlighting
      occurrencesHighlight: enableWordHighlight,
      selectionHighlight: enableWordHighlight,
      // Code lens
      codeLens: enableCodeLens
    });

    // Add keyboard shortcuts
    if (onSave) {
      editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
        onSave();
      });
    }

    // Add format shortcut
    if (onFormat) {
      editor.addCommand(monaco.KeyMod.Shift | monaco.KeyMod.Alt | monaco.KeyCode.KeyF, () => {
        onFormat();
      });
    }

    // Auto-format on paste
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyV, () => {
      setTimeout(() => {
        editor.getAction('editor.action.formatDocument')?.run();
      }, 100);
    });

    // Setup validation
    if (enableErrorDetection && onValidate) {
      const model = editor.getModel();
      if (model) {
        // Listen for model changes to trigger validation
        model.onDidChangeContent(() => {
          setTimeout(() => {
            const markers = monaco.editor.getModelMarkers({ resource: model.uri });
            onValidate(markers);
          }, 500);
        });
      }
    }

    // Add find functionality
    if (enableFind) {
      editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyF, () => {
        editor.getAction('actions.find').run();
      });
    }

    // Add format document
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.KeyF, () => {
      editor.getAction('editor.action.formatDocument').run();
    });

    // Add comment toggle
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Slash, () => {
      editor.getAction('editor.action.commentLine').run();
    });

    // Add multi-cursor support
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyD, () => {
      editor.getAction('editor.action.addSelectionToNextFindMatch').run();
    });

    // Add select all occurrences
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.KeyL, () => {
      editor.getAction('editor.action.selectHighlights').run();
    });

    // Add go to line
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyG, () => {
      editor.getAction('editor.action.gotoLine').run();
    });

    // Add fold/unfold
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.BracketLeft, () => {
      editor.getAction('editor.fold').run();
    });

    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.BracketRight, () => {
      editor.getAction('editor.unfold').run();
    });
  };

  // Update editor settings when props change
  useEffect(() => {
    if (editorRef.current && isReady) {
      editorRef.current.updateOptions({
        fontSize: defaultSettings.fontSize,
        minimap: { enabled: showMinimap ?? defaultSettings.minimap },
        tabSize: defaultSettings.tabSize,
        wordWrap: defaultSettings.wordWrap ? 'on' : 'off',
      });
    }
  }, [showMinimap, isReady]);

  const handleEditorChange = (value: string | undefined) => {
    if (value !== undefined) {
      onChange(value);
    }
  };

  // Determine theme based on current theme
  const getEditorTheme = () => {
    if (theme === 'light') return 'metamorphic-light';
    return 'metamorphic-dark';
  };

  return (
    <div
      className="h-full bg-background border border-border rounded-lg overflow-hidden"
      role="region"
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
      style={{ height }}
    >
      <Editor
        height="100%"
        defaultLanguage={language}
        language={language}
        value={value}
        onChange={handleEditorChange}
        onMount={handleEditorDidMount}
        beforeMount={handleBeforeMount}
        theme={getEditorTheme()}
        options={{
          padding: { top: 16, bottom: 16 },
          fontFamily: 'JetBrains Mono, SF Mono, Monaco, Inconsolata, Roboto Mono, Consolas, monospace',
          fontLigatures: true,
          cursorBlinking: 'smooth',
          renderWhitespace: 'selection',
          smoothScrolling: true,
          contextmenu: true,
          selectOnLineNumbers: true,
          roundedSelection: false,
          readOnly,
          cursorStyle: 'line',
          automaticLayout: true,

          // Enhanced IntelliSense and completion
          suggest: {
            enabled: enableAutocomplete && enableIntelliSense,
            showKeywords: true,
            showSnippets: true,
            showClasses: true,
            showFunctions: true,
            showVariables: true,
            showModules: true,
            showProperties: true,
            showMethods: true,
            showConstructors: true,
            showFields: true,
            showValues: true,
            showConstants: true,
            showEnums: true,
            showEnumMembers: true,
            showEvents: true,
            showOperators: true,
            showUnits: true,
            showColors: true,
            showFiles: true,
            showReferences: true,
            showFolders: true,
            showTypeParameters: true,
            showIssues: true,
            showUsers: true,
            showStructs: true,
            insertMode: 'insert',
            filterGraceful: true,
            snippetsPreventQuickSuggestions: false,
            localityBonus: true,
            shareSuggestSelections: true,
            showStatusBar: true,
            preview: true,
            previewMode: 'prefix'
          },

          // Parameter hints
          parameterHints: {
            enabled: enableParameterHints,
            cycle: true
          },

          // Quick suggestions
          quickSuggestions: {
            other: enableIntelliSense,
            comments: false,
            strings: enableIntelliSense
          },
          quickSuggestionsDelay: 100,
          suggestOnTriggerCharacters: enableIntelliSense,
          acceptSuggestionOnEnter: 'on',
          acceptSuggestionOnCommitCharacter: true,
          wordBasedSuggestions: enableIntelliSense,

          // Error detection and validation
          renderValidationDecorations: enableErrorDetection ? 'on' : 'off',

          // Bracket matching and highlighting
          matchBrackets: enableBracketMatching ? 'always' : 'never',
          bracketPairColorization: {
            enabled: enableBracketMatching
          },

          // Word and selection highlighting
          occurrencesHighlight: enableWordHighlight,
          selectionHighlight: enableWordHighlight,

          // Code lens
          codeLens: enableCodeLens,

          // Enhanced features
          suggest: {
            enabled: enableAutocomplete,
            showKeywords: true,
            showSnippets: true,
            showFunctions: true,
            showConstructors: true,
            showFields: true,
            showVariables: true,
            showClasses: true,
            showStructs: true,
            showInterfaces: true,
            showModules: true,
            showProperties: true,
            showEvents: true,
            showOperators: true,
            showUnits: true,
            showValues: true,
            showConstants: true,
            showEnums: true,
            showEnumMembers: true,
            showColors: true,
            showFiles: true,
            showReferences: true,
            showFolders: true,
            showTypeParameters: true,
          },

          quickSuggestions: {
            other: enableAutocomplete,
            comments: false,
            strings: false
          },

          parameterHints: {
            enabled: enableAutocomplete
          },

          // Accessibility options
          accessibilitySupport: 'on',
          ariaLabel: ariaLabel,

          // Performance optimizations
          renderValidationDecorations: 'on',
          renderLineHighlight: 'line',
          renderLineHighlightOnlyWhenFocus: false,
          hideCursorInOverviewRuler: false,
          scrollbar: {
            useShadows: false,
            verticalHasArrows: false,
            horizontalHasArrows: false,
            vertical: 'visible',
            horizontal: 'visible',
            verticalScrollbarSize: 10,
            horizontalScrollbarSize: 10,
          },

          // Code formatting
          formatOnPaste: true,
          formatOnType: true,

          // Bracket matching
          matchBrackets: 'always',
          bracketPairColorization: {
            enabled: true
          },

          // Indentation guides
          renderIndentGuides: true,
          highlightActiveIndentGuide: true,

          // Code folding
          folding: enableCodeFolding,
          foldingStrategy: 'indentation',
          foldingHighlight: true,
          unfoldOnClickAfterEndOfLine: true,

          // Multi-cursor
          multiCursorModifier: 'ctrlCmd',
          multiCursorMergeOverlapping: true,

          // Find widget
          find: {
            addExtraSpaceOnTop: false,
            autoFindInSelection: 'multiline',
            seedSearchStringFromSelection: 'always'
          },

          // Word navigation
          wordSeparators: '`~!@#$%^&*()-=+[{]}\\|;:\'",.<>/?',

          // Mouse behavior
          mouseWheelZoom: true,
          multiCursorModifier: 'ctrlCmd',

          // Hover
          hover: {
            enabled: true,
            delay: 300,
            sticky: true
          }
        }}
      />
    </div>
  );
};
