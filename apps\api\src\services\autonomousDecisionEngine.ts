import { EventEmitter } from 'events';
import { loggingService } from './loggingService.js';
import { multiProviderAI } from './multiProviderAI.js';
import { queueService } from './queueService.js';
import { supabaseClient } from './supabase.js';
import { ImprovementOpportunity, RepositoryConfig } from './repositoryMonitor.js';

export interface DecisionCriteria {
  minPriority: number; // 1-10
  minConfidence: number; // 0-1
  maxCostPerImprovement: number; // USD
  maxDailyCost: number; // USD
  qualityThreshold: number; // 0-1
  riskTolerance: 'low' | 'medium' | 'high';
  improvementTypes: string[]; // ['security', 'performance', 'maintainability', 'bugs', 'style']
}

export interface DecisionContext {
  repository: RepositoryConfig;
  opportunity: ImprovementOpportunity;
  recentActivity: {
    lastImprovement?: Date;
    recentFailures: number;
    dailyCostSoFar: number;
    weeklySuccessRate: number;
  };
  systemLoad: {
    queueLength: number;
    activeJobs: number;
    providerAvailability: Record<string, boolean>;
  };
}

export interface DecisionResult {
  decision: 'approve' | 'defer' | 'reject';
  confidence: number; // 0-1
  reasoning: string[];
  suggestedDelay?: number; // minutes
  estimatedCost: number;
  riskScore: number; // 0-1
  priority: number; // 1-10
}

export interface ImprovementPlan {
  id: string;
  repositoryId: string;
  opportunityId: string;
  plannerPrompt: string;
  criticPrompt: string;
  expectedOutcome: string;
  riskAssessment: string;
  rollbackPlan: string;
  testingStrategy: string;
  estimatedDuration: number; // minutes
  createdAt: Date;
}

class AutonomousDecisionEngine extends EventEmitter {
  private defaultCriteria: DecisionCriteria = {
    minPriority: 7,
    minConfidence: 0.8,
    maxCostPerImprovement: 0.50,
    maxDailyCost: 3.00,
    qualityThreshold: 0.95,
    riskTolerance: 'medium',
    improvementTypes: ['security', 'bugs', 'performance']
  };

  private repositoryCriteria: Map<string, DecisionCriteria> = new Map();
  private recentDecisions: Map<string, DecisionResult[]> = new Map();
  private improvementPlans: Map<string, ImprovementPlan> = new Map();

  constructor() {
    super();
    this.loadRepositoryCriteria();
    
    // Clean up old decisions every hour
    setInterval(() => this.cleanupOldDecisions(), 60 * 60 * 1000);
  }

  /**
   * Evaluate whether to proceed with an improvement opportunity
   */
  async evaluateOpportunity(context: DecisionContext): Promise<DecisionResult> {
    const criteria = this.repositoryCriteria.get(context.repository.id) || this.defaultCriteria;
    const reasoning: string[] = [];
    let confidence = 1.0;
    let riskScore = 0.0;

    // Basic criteria checks
    if (context.opportunity.priority < criteria.minPriority) {
      return {
        decision: 'reject',
        confidence: 0.9,
        reasoning: [`Priority ${context.opportunity.priority} below minimum ${criteria.minPriority}`],
        estimatedCost: context.opportunity.estimatedCost,
        riskScore: 0.1,
        priority: context.opportunity.priority
      };
    }

    if (context.opportunity.confidence < criteria.minConfidence) {
      return {
        decision: 'reject',
        confidence: 0.9,
        reasoning: [`Confidence ${context.opportunity.confidence} below minimum ${criteria.minConfidence}`],
        estimatedCost: context.opportunity.estimatedCost,
        riskScore: 0.1,
        priority: context.opportunity.priority
      };
    }

    if (context.opportunity.estimatedCost > criteria.maxCostPerImprovement) {
      return {
        decision: 'reject',
        confidence: 0.9,
        reasoning: [`Cost $${context.opportunity.estimatedCost} exceeds maximum $${criteria.maxCostPerImprovement}`],
        estimatedCost: context.opportunity.estimatedCost,
        riskScore: 0.2,
        priority: context.opportunity.priority
      };
    }

    if (!criteria.improvementTypes.includes(context.opportunity.type)) {
      return {
        decision: 'reject',
        confidence: 0.9,
        reasoning: [`Improvement type '${context.opportunity.type}' not in allowed types`],
        estimatedCost: context.opportunity.estimatedCost,
        riskScore: 0.1,
        priority: context.opportunity.priority
      };
    }

    // Cost budget check
    const projectedDailyCost = context.recentActivity.dailyCostSoFar + context.opportunity.estimatedCost;
    if (projectedDailyCost > criteria.maxDailyCost) {
      return {
        decision: 'defer',
        confidence: 0.8,
        reasoning: [`Daily cost budget would be exceeded: $${projectedDailyCost} > $${criteria.maxDailyCost}`],
        suggestedDelay: 24 * 60, // Wait until tomorrow
        estimatedCost: context.opportunity.estimatedCost,
        riskScore: 0.1,
        priority: context.opportunity.priority
      };
    }

    reasoning.push('Basic criteria checks passed');

    // System load assessment
    if (context.systemLoad.queueLength > 10) {
      confidence *= 0.9;
      riskScore += 0.1;
      reasoning.push('High queue length detected');
      
      if (context.systemLoad.queueLength > 20) {
        return {
          decision: 'defer',
          confidence: 0.7,
          reasoning: [...reasoning, 'Queue too long, deferring'],
          suggestedDelay: 30,
          estimatedCost: context.opportunity.estimatedCost,
          riskScore: 0.3,
          priority: context.opportunity.priority
        };
      }
    }

    // Provider availability check
    const availableProviders = Object.values(context.systemLoad.providerAvailability).filter(Boolean).length;
    if (availableProviders < 2) {
      confidence *= 0.8;
      riskScore += 0.2;
      reasoning.push('Limited provider availability');
      
      if (availableProviders === 0) {
        return {
          decision: 'defer',
          confidence: 0.6,
          reasoning: [...reasoning, 'No providers available'],
          suggestedDelay: 15,
          estimatedCost: context.opportunity.estimatedCost,
          riskScore: 0.5,
          priority: context.opportunity.priority
        };
      }
    }

    // Recent activity assessment
    if (context.recentActivity.recentFailures > 3) {
      confidence *= 0.7;
      riskScore += 0.3;
      reasoning.push('High recent failure rate');
      
      if (context.recentActivity.recentFailures > 5) {
        return {
          decision: 'defer',
          confidence: 0.5,
          reasoning: [...reasoning, 'Too many recent failures'],
          suggestedDelay: 60,
          estimatedCost: context.opportunity.estimatedCost,
          riskScore: 0.6,
          priority: context.opportunity.priority
        };
      }
    }

    if (context.recentActivity.weeklySuccessRate < 0.7) {
      confidence *= 0.8;
      riskScore += 0.2;
      reasoning.push('Low weekly success rate');
    }

    // Risk tolerance adjustment
    switch (criteria.riskTolerance) {
      case 'low':
        if (riskScore > 0.3) {
          return {
            decision: 'defer',
            confidence: confidence * 0.8,
            reasoning: [...reasoning, 'Risk score too high for low risk tolerance'],
            suggestedDelay: 120,
            estimatedCost: context.opportunity.estimatedCost,
            riskScore,
            priority: context.opportunity.priority
          };
        }
        break;
      case 'high':
        confidence *= 1.1; // Boost confidence for high risk tolerance
        break;
    }

    // Priority-based decision
    let decision: 'approve' | 'defer' | 'reject' = 'approve';
    
    if (context.opportunity.priority >= 9 && context.opportunity.type === 'security') {
      // High-priority security issues get fast-tracked
      confidence *= 1.2;
      reasoning.push('High-priority security issue fast-tracked');
    } else if (context.opportunity.priority < 8 && riskScore > 0.2) {
      // Lower priority with some risk gets deferred
      decision = 'defer';
      reasoning.push('Lower priority with elevated risk, deferring');
    }

    // Final confidence and risk adjustments
    confidence = Math.min(1.0, Math.max(0.1, confidence));
    riskScore = Math.min(1.0, Math.max(0.0, riskScore));

    const result: DecisionResult = {
      decision,
      confidence,
      reasoning,
      estimatedCost: context.opportunity.estimatedCost,
      riskScore,
      priority: context.opportunity.priority
    };

    // Store decision for learning
    this.storeDecision(context.repository.id, result);

    await loggingService.log({
      level: 'info',
      message: 'Autonomous decision made',
      service: 'autonomous-decision-engine',
      metadata: {
        repositoryId: context.repository.id,
        opportunityId: context.opportunity.id,
        decision: result.decision,
        confidence: result.confidence,
        riskScore: result.riskScore,
        reasoning: result.reasoning
      }
    });

    this.emit('decision-made', { context, result });

    return result;
  }

  /**
   * Generate an improvement plan for an approved opportunity
   */
  async generateImprovementPlan(
    repository: RepositoryConfig,
    opportunity: ImprovementOpportunity
  ): Promise<ImprovementPlan> {
    const planId = `plan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Generate planner prompt
    const plannerPrompt = this.generatePlannerPrompt(opportunity);
    
    // Generate critic prompt
    const criticPrompt = this.generateCriticPrompt(opportunity);

    // Assess risks and create rollback plan
    const riskAssessment = await this.assessRisks(opportunity);
    const rollbackPlan = await this.generateRollbackPlan(opportunity);
    const testingStrategy = await this.generateTestingStrategy(opportunity);

    const plan: ImprovementPlan = {
      id: planId,
      repositoryId: repository.id,
      opportunityId: opportunity.id,
      plannerPrompt,
      criticPrompt,
      expectedOutcome: opportunity.suggestedImprovement,
      riskAssessment,
      rollbackPlan,
      testingStrategy,
      estimatedDuration: this.estimateDuration(opportunity),
      createdAt: new Date()
    };

    this.improvementPlans.set(planId, plan);

    // Store in database
    await this.storePlan(plan);

    await loggingService.log({
      level: 'info',
      message: 'Improvement plan generated',
      service: 'autonomous-decision-engine',
      metadata: {
        planId,
        repositoryId: repository.id,
        opportunityId: opportunity.id,
        estimatedDuration: plan.estimatedDuration
      }
    });

    this.emit('plan-generated', { repository, opportunity, plan });

    return plan;
  }

  /**
   * Generate planner prompt for the opportunity
   */
  private generatePlannerPrompt(opportunity: ImprovementOpportunity): string {
    const basePrompt = `
You are an expert code planner. Generate a precise JSON patch to address the following code improvement opportunity:

**File:** ${opportunity.filePath}
**Issue Type:** ${opportunity.type}
**Priority:** ${opportunity.priority}/10
**Description:** ${opportunity.description}
**Suggested Improvement:** ${opportunity.suggestedImprovement}

**Current Code:**
\`\`\`
${opportunity.codeSnippet}
\`\`\`

Please provide a JSON patch that:
1. Addresses the specific issue described
2. Maintains code functionality
3. Follows best practices for ${opportunity.type} improvements
4. Includes appropriate comments explaining the changes
5. Is minimal and focused (avoid unnecessary changes)

Return your response as a valid JSON patch array with operations like:
[
  {"op": "replace", "path": "/line/5", "value": "improved code here"},
  {"op": "add", "path": "/line/6", "value": "// Comment explaining the change"}
]
`;

    // Add type-specific guidance
    switch (opportunity.type) {
      case 'security':
        return basePrompt + `\n\nSecurity-specific requirements:
- Validate all inputs
- Use secure coding practices
- Avoid common vulnerabilities (XSS, injection, etc.)
- Add security comments where appropriate`;

      case 'performance':
        return basePrompt + `\n\nPerformance-specific requirements:
- Optimize algorithms and data structures
- Reduce computational complexity
- Minimize memory usage
- Consider caching where appropriate`;

      case 'maintainability':
        return basePrompt + `\n\nMaintainability-specific requirements:
- Break down large functions
- Improve code readability
- Add clear documentation
- Follow consistent naming conventions`;

      case 'bugs':
        return basePrompt + `\n\nBug fix requirements:
- Identify and fix the root cause
- Add error handling where needed
- Ensure edge cases are covered
- Add defensive programming practices`;

      default:
        return basePrompt;
    }
  }

  /**
   * Generate critic prompt for the opportunity
   */
  private generateCriticPrompt(opportunity: ImprovementOpportunity): string {
    return `
You are an expert code critic. Evaluate the proposed JSON patch for the following improvement:

**Original Issue:** ${opportunity.description}
**File:** ${opportunity.filePath}
**Type:** ${opportunity.type}
**Priority:** ${opportunity.priority}/10

**Original Code:**
\`\`\`
${opportunity.codeSnippet}
\`\`\`

Please evaluate the proposed patch on these criteria (score 0-1 for each):

1. **Correctness** (0-1): Does the patch correctly address the issue?
2. **Safety** (0-1): Does the patch avoid introducing new bugs or security issues?
3. **Performance** (0-1): Does the patch maintain or improve performance?
4. **Maintainability** (0-1): Does the patch improve code readability and maintainability?
5. **Completeness** (0-1): Does the patch fully address the issue without missing edge cases?

Provide your response in this JSON format:
{
  "overallScore": 0.95,
  "scores": {
    "correctness": 0.95,
    "safety": 1.0,
    "performance": 0.9,
    "maintainability": 0.95,
    "completeness": 0.9
  },
  "feedback": "Detailed feedback about the patch",
  "recommendations": ["Specific recommendations for improvement"],
  "approved": true,
  "riskLevel": "low"
}

Only approve patches with an overall score >= 0.95.
`;
  }

  /**
   * Assess risks for an improvement opportunity
   */
  private async assessRisks(opportunity: ImprovementOpportunity): Promise<string> {
    const risks: string[] = [];

    // File-based risk assessment
    if (opportunity.filePath.includes('config') || opportunity.filePath.includes('env')) {
      risks.push('Configuration file changes carry deployment risk');
    }

    if (opportunity.filePath.includes('auth') || opportunity.filePath.includes('security')) {
      risks.push('Authentication/security changes require careful testing');
    }

    if (opportunity.filePath.includes('database') || opportunity.filePath.includes('migration')) {
      risks.push('Database-related changes may affect data integrity');
    }

    // Type-based risk assessment
    switch (opportunity.type) {
      case 'security':
        risks.push('Security changes may have unintended side effects');
        risks.push('Requires thorough security testing');
        break;
      case 'performance':
        risks.push('Performance optimizations may change behavior');
        risks.push('Requires performance benchmarking');
        break;
      case 'bugs':
        risks.push('Bug fixes may expose other issues');
        break;
    }

    // Priority-based risk assessment
    if (opportunity.priority >= 9) {
      risks.push('High-priority changes may be rushed');
    }

    // Confidence-based risk assessment
    if (opportunity.confidence < 0.9) {
      risks.push('Lower confidence increases uncertainty');
    }

    return risks.length > 0
      ? `Risk Assessment:\n${risks.map(r => `- ${r}`).join('\n')}`
      : 'Low risk - standard improvement with minimal impact expected';
  }

  /**
   * Generate rollback plan for an improvement
   */
  private async generateRollbackPlan(opportunity: ImprovementOpportunity): Promise<string> {
    const steps: string[] = [
      '1. Monitor application logs for errors after deployment',
      '2. Check key performance metrics for degradation',
      '3. Verify functionality in affected areas'
    ];

    // Type-specific rollback steps
    switch (opportunity.type) {
      case 'security':
        steps.push('4. Verify security controls are functioning');
        steps.push('5. Check for any new security vulnerabilities');
        break;
      case 'performance':
        steps.push('4. Monitor response times and resource usage');
        steps.push('5. Compare performance metrics to baseline');
        break;
      case 'bugs':
        steps.push('4. Test the specific bug scenario');
        steps.push('5. Verify no regression in related functionality');
        break;
    }

    steps.push('6. If issues detected, revert the commit immediately');
    steps.push('7. Notify team of rollback and investigate root cause');

    return `Rollback Plan:\n${steps.join('\n')}`;
  }

  /**
   * Generate testing strategy for an improvement
   */
  private async generateTestingStrategy(opportunity: ImprovementOpportunity): Promise<string> {
    const tests: string[] = [];

    // Basic testing
    tests.push('- Unit tests for modified functions');
    tests.push('- Integration tests for affected modules');

    // Type-specific testing
    switch (opportunity.type) {
      case 'security':
        tests.push('- Security vulnerability scanning');
        tests.push('- Authentication/authorization testing');
        tests.push('- Input validation testing');
        break;
      case 'performance':
        tests.push('- Performance benchmarking');
        tests.push('- Load testing under normal conditions');
        tests.push('- Memory usage profiling');
        break;
      case 'bugs':
        tests.push('- Specific test case for the reported bug');
        tests.push('- Regression testing for related functionality');
        break;
      case 'maintainability':
        tests.push('- Code quality metrics verification');
        tests.push('- Documentation completeness check');
        break;
    }

    // File-specific testing
    if (opportunity.filePath.includes('api') || opportunity.filePath.includes('endpoint')) {
      tests.push('- API endpoint testing');
      tests.push('- Response format validation');
    }

    if (opportunity.filePath.includes('ui') || opportunity.filePath.includes('component')) {
      tests.push('- UI component testing');
      tests.push('- User interaction testing');
    }

    tests.push('- Automated CI/CD pipeline execution');
    tests.push('- Manual smoke testing in staging environment');

    return `Testing Strategy:\n${tests.join('\n')}`;
  }

  /**
   * Estimate duration for an improvement
   */
  private estimateDuration(opportunity: ImprovementOpportunity): number {
    let baseDuration = 15; // Default 15 minutes

    // Adjust based on priority
    if (opportunity.priority >= 9) {
      baseDuration += 10; // High priority gets more time for careful handling
    }

    // Adjust based on type
    switch (opportunity.type) {
      case 'security':
        baseDuration += 20; // Security changes need more time
        break;
      case 'performance':
        baseDuration += 15; // Performance changes need benchmarking
        break;
      case 'bugs':
        baseDuration += 10; // Bug fixes need testing
        break;
      case 'maintainability':
        baseDuration += 5; // Usually straightforward
        break;
    }

    // Adjust based on confidence
    if (opportunity.confidence < 0.8) {
      baseDuration += 10; // Lower confidence needs more time
    }

    // Adjust based on estimated cost (proxy for complexity)
    if (opportunity.estimatedCost > 0.3) {
      baseDuration += 15; // Higher cost suggests more complexity
    }

    return Math.min(baseDuration, 60); // Cap at 1 hour
  }

  /**
   * Load repository-specific decision criteria from database
   */
  private async loadRepositoryCriteria(): Promise<void> {
    try {
      const { data: repositories, error } = await supabaseClient
        .from('autonomous_repositories')
        .select('id, quality_threshold, cost_limit');

      if (error) {
        throw error;
      }

      if (repositories) {
        for (const repo of repositories) {
          const criteria: DecisionCriteria = {
            ...this.defaultCriteria,
            qualityThreshold: repo.quality_threshold || this.defaultCriteria.qualityThreshold,
            maxDailyCost: repo.cost_limit || this.defaultCriteria.maxDailyCost,
            maxCostPerImprovement: (repo.cost_limit || this.defaultCriteria.maxDailyCost) / 10
          };

          this.repositoryCriteria.set(repo.id, criteria);
        }
      }

      await loggingService.log({
        level: 'info',
        message: 'Repository criteria loaded',
        service: 'autonomous-decision-engine',
        metadata: {
          repositoryCount: repositories?.length || 0
        }
      });

    } catch (error) {
      await loggingService.log({
        level: 'error',
        message: 'Failed to load repository criteria',
        service: 'autonomous-decision-engine',
        metadata: {
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      });
    }
  }

  /**
   * Store decision for learning and analytics
   */
  private storeDecision(repositoryId: string, result: DecisionResult): void {
    if (!this.recentDecisions.has(repositoryId)) {
      this.recentDecisions.set(repositoryId, []);
    }

    const decisions = this.recentDecisions.get(repositoryId)!;
    decisions.push({
      ...result,
      // Add timestamp for tracking
      timestamp: new Date()
    } as any);

    // Keep only last 50 decisions per repository
    if (decisions.length > 50) {
      decisions.splice(0, decisions.length - 50);
    }
  }

  /**
   * Store improvement plan in database
   */
  private async storePlan(plan: ImprovementPlan): Promise<void> {
    try {
      const { error } = await supabaseClient
        .from('improvement_plans')
        .insert({
          id: plan.id,
          repository_id: plan.repositoryId,
          opportunity_id: plan.opportunityId,
          planner_prompt: plan.plannerPrompt,
          critic_prompt: plan.criticPrompt,
          expected_outcome: plan.expectedOutcome,
          risk_assessment: plan.riskAssessment,
          rollback_plan: plan.rollbackPlan,
          testing_strategy: plan.testingStrategy,
          estimated_duration: plan.estimatedDuration,
          created_at: plan.createdAt.toISOString(),
          status: 'pending'
        });

      if (error) {
        throw error;
      }

    } catch (error) {
      await loggingService.log({
        level: 'error',
        message: 'Failed to store improvement plan',
        service: 'autonomous-decision-engine',
        metadata: {
          planId: plan.id,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      });
    }
  }

  /**
   * Clean up old decisions from memory
   */
  private cleanupOldDecisions(): void {
    const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago

    for (const [repositoryId, decisions] of this.recentDecisions.entries()) {
      const filteredDecisions = decisions.filter((decision: any) =>
        decision.timestamp && decision.timestamp > cutoffTime
      );

      if (filteredDecisions.length === 0) {
        this.recentDecisions.delete(repositoryId);
      } else {
        this.recentDecisions.set(repositoryId, filteredDecisions);
      }
    }
  }

  /**
   * Get decision statistics for a repository
   */
  getDecisionStats(repositoryId: string): {
    totalDecisions: number;
    approvedCount: number;
    deferredCount: number;
    rejectedCount: number;
    averageConfidence: number;
    averageRiskScore: number;
  } {
    const decisions = this.recentDecisions.get(repositoryId) || [];

    if (decisions.length === 0) {
      return {
        totalDecisions: 0,
        approvedCount: 0,
        deferredCount: 0,
        rejectedCount: 0,
        averageConfidence: 0,
        averageRiskScore: 0
      };
    }

    const approvedCount = decisions.filter(d => d.decision === 'approve').length;
    const deferredCount = decisions.filter(d => d.decision === 'defer').length;
    const rejectedCount = decisions.filter(d => d.decision === 'reject').length;

    const averageConfidence = decisions.reduce((sum, d) => sum + d.confidence, 0) / decisions.length;
    const averageRiskScore = decisions.reduce((sum, d) => sum + d.riskScore, 0) / decisions.length;

    return {
      totalDecisions: decisions.length,
      approvedCount,
      deferredCount,
      rejectedCount,
      averageConfidence,
      averageRiskScore
    };
  }

  /**
   * Update repository criteria
   */
  updateRepositoryCriteria(repositoryId: string, criteria: Partial<DecisionCriteria>): void {
    const current = this.repositoryCriteria.get(repositoryId) || this.defaultCriteria;
    const updated = { ...current, ...criteria };
    this.repositoryCriteria.set(repositoryId, updated);

    this.emit('criteria-updated', { repositoryId, criteria: updated });
  }

  /**
   * Get current criteria for a repository
   */
  getRepositoryCriteria(repositoryId: string): DecisionCriteria {
    return this.repositoryCriteria.get(repositoryId) || this.defaultCriteria;
  }

  /**
   * Get improvement plan by ID
   */
  getImprovementPlan(planId: string): ImprovementPlan | undefined {
    return this.improvementPlans.get(planId);
  }
}

export const autonomousDecisionEngine = new AutonomousDecisionEngine();
