import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  FileText, 
  Search, 
  Filter,
  Download,
  RefreshCw,
  Play,
  Pause,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle,
  Clock,
  Settings,
  Trash2,
  Eye
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { HStack, VStack } from '@/components/ui/layout';
import { Typography } from '@/components/ui/typography';
import { formatDistanceToNow } from 'date-fns';

interface LogEntry {
  id: string;
  timestamp: string;
  level: 'debug' | 'info' | 'warn' | 'error' | 'fatal';
  service: string;
  message: string;
  metadata?: Record<string, any>;
  user_id?: string;
  session_id?: string;
  request_id?: string;
  stack_trace?: string;
}

interface LogsViewerProps {
  className?: string;
}

const LOG_LEVELS = [
  { value: 'debug', label: 'Debug', color: 'text-gray-500' },
  { value: 'info', label: 'Info', color: 'text-blue-500' },
  { value: 'warn', label: 'Warning', color: 'text-yellow-500' },
  { value: 'error', label: 'Error', color: 'text-red-500' },
  { value: 'fatal', label: 'Fatal', color: 'text-red-700' }
];

const SERVICES = [
  'api',
  'auth',
  'billing',
  'reactor',
  'database',
  'queue',
  'analytics'
];

export const LogsViewer: React.FC<LogsViewerProps> = ({ className }) => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<LogEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRealTime, setIsRealTime] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedLevels, setSelectedLevels] = useState<string[]>(['info', 'warn', 'error', 'fatal']);
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [timeRange, setTimeRange] = useState('1h');
  const [expandedLogs, setExpandedLogs] = useState<Set<string>>(new Set());
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const realTimeIntervalRef = useRef<NodeJS.Timeout>();

  // Fetch logs
  const fetchLogs = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        limit: '100',
        time_range: timeRange,
        ...(selectedLevels.length > 0 && { levels: selectedLevels.join(',') }),
        ...(selectedServices.length > 0 && { services: selectedServices.join(',') }),
        ...(searchQuery && { search: searchQuery })
      });

      const response = await fetch(`/api/logs?${params}`);
      if (response.ok) {
        const data = await response.json();
        const newLogs = data.data || [];
        
        // Add mock logs for demonstration
        const mockLogs: LogEntry[] = [
          {
            id: '1',
            timestamp: new Date().toISOString(),
            level: 'info',
            service: 'api',
            message: 'User authentication successful',
            metadata: { user_id: 'user123', ip: '***********' },
            user_id: 'user123',
            request_id: 'req_abc123'
          },
          {
            id: '2',
            timestamp: new Date(Date.now() - 60000).toISOString(),
            level: 'error',
            service: 'reactor',
            message: 'Transformation failed due to invalid input',
            metadata: { error_code: 'INVALID_INPUT', session_id: 'sess_xyz789' },
            session_id: 'sess_xyz789',
            stack_trace: 'Error: Invalid input\n  at validateInput (reactor.js:45)\n  at processTransformation (reactor.js:123)'
          },
          {
            id: '3',
            timestamp: new Date(Date.now() - 120000).toISOString(),
            level: 'warn',
            service: 'billing',
            message: 'User approaching rate limit',
            metadata: { user_id: 'user456', current_usage: 85, limit: 100 },
            user_id: 'user456'
          },
          {
            id: '4',
            timestamp: new Date(Date.now() - 180000).toISOString(),
            level: 'debug',
            service: 'database',
            message: 'Query executed successfully',
            metadata: { query_time: 45, table: 'transformations' }
          }
        ];

        setLogs([...mockLogs, ...newLogs]);
      }
    } catch (error) {
      console.error('Failed to fetch logs:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Filter logs
  useEffect(() => {
    let filtered = logs;

    // Filter by levels
    if (selectedLevels.length > 0) {
      filtered = filtered.filter(log => selectedLevels.includes(log.level));
    }

    // Filter by services
    if (selectedServices.length > 0) {
      filtered = filtered.filter(log => selectedServices.includes(log.service));
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(log => 
        log.message.toLowerCase().includes(query) ||
        log.service.toLowerCase().includes(query) ||
        log.user_id?.toLowerCase().includes(query) ||
        log.session_id?.toLowerCase().includes(query) ||
        log.request_id?.toLowerCase().includes(query)
      );
    }

    // Sort by timestamp (newest first)
    filtered.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    setFilteredLogs(filtered);
  }, [logs, selectedLevels, selectedServices, searchQuery]);

  // Real-time updates
  useEffect(() => {
    if (isRealTime) {
      realTimeIntervalRef.current = setInterval(fetchLogs, 5000);
    } else {
      if (realTimeIntervalRef.current) {
        clearInterval(realTimeIntervalRef.current);
      }
    }

    return () => {
      if (realTimeIntervalRef.current) {
        clearInterval(realTimeIntervalRef.current);
      }
    };
  }, [isRealTime]);

  // Initial fetch
  useEffect(() => {
    fetchLogs();
  }, [timeRange]);

  const handleLevelToggle = (level: string) => {
    setSelectedLevels(prev => 
      prev.includes(level) 
        ? prev.filter(l => l !== level)
        : [...prev, level]
    );
  };

  const handleServiceToggle = (service: string) => {
    setSelectedServices(prev => 
      prev.includes(service) 
        ? prev.filter(s => s !== service)
        : [...prev, service]
    );
  };

  const toggleLogExpansion = (logId: string) => {
    setExpandedLogs(prev => {
      const newSet = new Set(prev);
      if (newSet.has(logId)) {
        newSet.delete(logId);
      } else {
        newSet.add(logId);
      }
      return newSet;
    });
  };

  const exportLogs = () => {
    const csvContent = [
      'Timestamp,Level,Service,Message,User ID,Session ID,Request ID',
      ...filteredLogs.map(log => 
        `"${log.timestamp}","${log.level}","${log.service}","${log.message.replace(/"/g, '""')}","${log.user_id || ''}","${log.session_id || ''}","${log.request_id || ''}"`
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `logs_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const clearLogs = async () => {
    if (!confirm('Are you sure you want to clear all logs? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch('/api/logs', { method: 'DELETE' });
      if (response.ok) {
        setLogs([]);
      }
    } catch (error) {
      console.error('Failed to clear logs:', error);
    }
  };

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'debug': return <Settings className="w-4 h-4 text-gray-500" />;
      case 'info': return <Info className="w-4 h-4 text-blue-500" />;
      case 'warn': return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'error': return <XCircle className="w-4 h-4 text-red-500" />;
      case 'fatal': return <XCircle className="w-4 h-4 text-red-700" />;
      default: return <Info className="w-4 h-4 text-gray-500" />;
    }
  };

  const getLevelColor = (level: string) => {
    const levelConfig = LOG_LEVELS.find(l => l.value === level);
    return levelConfig?.color || 'text-gray-500';
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <FileText className="w-6 h-6 text-primary" />
            <div>
              <CardTitle className="text-xl">System Logs</CardTitle>
              <CardDescription>
                Real-time log monitoring with advanced filtering and search
              </CardDescription>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant={isRealTime ? 'default' : 'outline'}
              size="sm"
              onClick={() => setIsRealTime(!isRealTime)}
            >
              {isRealTime ? <Pause className="w-4 h-4 mr-2" /> : <Play className="w-4 h-4 mr-2" />}
              {isRealTime ? 'Pause' : 'Live'}
            </Button>
            <Button variant="outline" size="sm" onClick={exportLogs}>
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm" onClick={clearLogs}>
              <Trash2 className="w-4 h-4 mr-2" />
              Clear
            </Button>
            <Button variant="outline" size="sm" onClick={fetchLogs} disabled={isLoading}>
              <RefreshCw className={cn("w-4 h-4 mr-2", isLoading && "animate-spin")} />
              Refresh
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="space-y-2">
            <Label>Search</Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search logs..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Time Range */}
          <div className="space-y-2">
            <Label>Time Range</Label>
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="15m">Last 15 minutes</SelectItem>
                <SelectItem value="1h">Last hour</SelectItem>
                <SelectItem value="6h">Last 6 hours</SelectItem>
                <SelectItem value="24h">Last 24 hours</SelectItem>
                <SelectItem value="7d">Last 7 days</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Log Levels */}
          <div className="space-y-2">
            <Label>Log Levels</Label>
            <div className="flex flex-wrap gap-2">
              {LOG_LEVELS.map((level) => (
                <div key={level.value} className="flex items-center space-x-1">
                  <Checkbox
                    id={level.value}
                    checked={selectedLevels.includes(level.value)}
                    onCheckedChange={() => handleLevelToggle(level.value)}
                  />
                  <Label htmlFor={level.value} className={cn("text-xs", level.color)}>
                    {level.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Services */}
          <div className="space-y-2">
            <Label>Services</Label>
            <div className="flex flex-wrap gap-2">
              {SERVICES.map((service) => (
                <div key={service} className="flex items-center space-x-1">
                  <Checkbox
                    id={service}
                    checked={selectedServices.includes(service)}
                    onCheckedChange={() => handleServiceToggle(service)}
                  />
                  <Label htmlFor={service} className="text-xs">
                    {service}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Results Summary */}
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>Showing {filteredLogs.length} of {logs.length} log entries</span>
          {isRealTime && (
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <span>Live updates enabled</span>
            </div>
          )}
        </div>

        {/* Logs Display */}
        <ScrollArea ref={scrollAreaRef} className="h-96 border rounded-lg">
          <div className="p-4 space-y-2">
            {filteredLogs.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                <Typography variant="body-sm" className="text-muted-foreground">
                  No logs found matching the current filters
                </Typography>
              </div>
            ) : (
              filteredLogs.map((log) => (
                <div
                  key={log.id}
                  className={cn(
                    "p-3 rounded-lg border border-border bg-card/50 transition-all duration-200",
                    "hover:bg-card/80 cursor-pointer"
                  )}
                  onClick={() => toggleLogExpansion(log.id)}
                >
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 mt-0.5">
                      {getLevelIcon(log.level)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <Badge variant="outline" className={cn("text-xs", getLevelColor(log.level))}>
                          {log.level.toUpperCase()}
                        </Badge>
                        <Badge variant="secondary" className="text-xs">
                          {log.service}
                        </Badge>
                        <Typography variant="caption" className="text-muted-foreground">
                          {formatDistanceToNow(new Date(log.timestamp), { addSuffix: true })}
                        </Typography>
                      </div>
                      
                      <Typography variant="body-sm" className="font-mono">
                        {log.message}
                      </Typography>
                      
                      {expandedLogs.has(log.id) && (
                        <div className="mt-3 space-y-2 text-xs">
                          <div className="grid grid-cols-2 gap-2">
                            <div>
                              <span className="font-medium">Timestamp:</span> {log.timestamp}
                            </div>
                            {log.user_id && (
                              <div>
                                <span className="font-medium">User ID:</span> {log.user_id}
                              </div>
                            )}
                            {log.session_id && (
                              <div>
                                <span className="font-medium">Session ID:</span> {log.session_id}
                              </div>
                            )}
                            {log.request_id && (
                              <div>
                                <span className="font-medium">Request ID:</span> {log.request_id}
                              </div>
                            )}
                          </div>
                          
                          {log.metadata && (
                            <div>
                              <span className="font-medium">Metadata:</span>
                              <pre className="mt-1 p-2 bg-muted rounded text-xs overflow-x-auto">
                                {JSON.stringify(log.metadata, null, 2)}
                              </pre>
                            </div>
                          )}
                          
                          {log.stack_trace && (
                            <div>
                              <span className="font-medium">Stack Trace:</span>
                              <pre className="mt-1 p-2 bg-muted rounded text-xs overflow-x-auto">
                                {log.stack_trace}
                              </pre>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex-shrink-0">
                      <Eye className="w-4 h-4 text-muted-foreground" />
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};

export default LogsViewer;
