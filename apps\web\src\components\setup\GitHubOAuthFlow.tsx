import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Github, 
  CheckCircle, 
  XCircle, 
  Loader2, 
  ExternalLink,
  Key,
  Shield,
  Users,
  GitBranch,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface GitHubConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
}

interface GitHubUser {
  login: string;
  name: string;
  email: string;
  avatar_url: string;
  public_repos: number;
  followers: number;
}

interface OAuthStatus {
  status: 'idle' | 'authorizing' | 'exchanging' | 'success' | 'error';
  message: string;
  user?: GitHubUser;
  token?: string;
  scopes?: string[];
}

const REQUIRED_SCOPES = [
  'repo',
  'user:email',
  'read:user',
];

const SCOPE_DESCRIPTIONS = {
  'repo': 'Access to repositories for creating PRs and managing code',
  'user:email': 'Access to email address for commit attribution',
  'read:user': 'Access to user profile information',
};

export const GitHubOAuthFlow: React.FC = () => {
  const [config, setConfig] = useState<GitHubConfig>({
    clientId: process.env.NEXT_PUBLIC_GITHUB_CLIENT_ID || '',
    clientSecret: process.env.GITHUB_CLIENT_SECRET || '',
    redirectUri: `${window.location.origin}/auth/github/callback`,
  });

  const [oauthStatus, setOauthStatus] = useState<OAuthStatus>({
    status: 'idle',
    message: 'Ready to connect to GitHub',
  });

  const [isConfigured, setIsConfigured] = useState(false);

  useEffect(() => {
    // Check if GitHub OAuth is already configured
    const token = localStorage.getItem('github_token');
    if (token) {
      validateExistingToken(token);
    }
  }, []);

  const validateExistingToken = async (token: string) => {
    setOauthStatus({
      status: 'authorizing',
      message: 'Validating existing token...',
    });

    try {
      const response = await fetch('https://api.github.com/user', {
        headers: {
          'Authorization': `token ${token}`,
          'Accept': 'application/vnd.github.v3+json',
        },
      });

      if (response.ok) {
        const user = await response.json();
        setOauthStatus({
          status: 'success',
          message: 'Already connected to GitHub',
          user,
          token,
          scopes: ['repo', 'user:email', 'read:user'], // Assume required scopes
        });
        setIsConfigured(true);
      } else {
        localStorage.removeItem('github_token');
        setOauthStatus({
          status: 'idle',
          message: 'Token expired, please reconnect',
        });
      }
    } catch (error) {
      localStorage.removeItem('github_token');
      setOauthStatus({
        status: 'error',
        message: 'Failed to validate token',
      });
    }
  };

  const startOAuthFlow = () => {
    if (!config.clientId) {
      setOauthStatus({
        status: 'error',
        message: 'GitHub Client ID is required',
      });
      return;
    }

    setOauthStatus({
      status: 'authorizing',
      message: 'Redirecting to GitHub...',
    });

    const scopes = REQUIRED_SCOPES.join(' ');
    const state = Math.random().toString(36).substring(7);
    
    // Store state for validation
    sessionStorage.setItem('github_oauth_state', state);

    const authUrl = new URL('https://github.com/login/oauth/authorize');
    authUrl.searchParams.set('client_id', config.clientId);
    authUrl.searchParams.set('redirect_uri', config.redirectUri);
    authUrl.searchParams.set('scope', scopes);
    authUrl.searchParams.set('state', state);
    authUrl.searchParams.set('allow_signup', 'true');

    // In a real implementation, this would redirect
    // For demo purposes, we'll simulate the flow
    simulateOAuthFlow();
  };

  const simulateOAuthFlow = async () => {
    // Simulate OAuth flow steps
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setOauthStatus({
      status: 'exchanging',
      message: 'Exchanging code for token...',
    });

    await new Promise(resolve => setTimeout(resolve, 1500));

    // Simulate successful OAuth
    const mockUser: GitHubUser = {
      login: 'developer',
      name: 'Developer User',
      email: '<EMAIL>',
      avatar_url: 'https://github.com/identicons/developer.png',
      public_repos: 42,
      followers: 15,
    };

    const mockToken = 'ghp_' + Math.random().toString(36).substring(2, 15);
    
    localStorage.setItem('github_token', mockToken);
    
    setOauthStatus({
      status: 'success',
      message: 'Successfully connected to GitHub!',
      user: mockUser,
      token: mockToken,
      scopes: REQUIRED_SCOPES,
    });
    
    setIsConfigured(true);
  };

  const disconnectGitHub = () => {
    localStorage.removeItem('github_token');
    setOauthStatus({
      status: 'idle',
      message: 'Disconnected from GitHub',
    });
    setIsConfigured(false);
  };

  const refreshToken = async () => {
    setOauthStatus(prev => ({
      ...prev,
      status: 'authorizing',
      message: 'Refreshing token...',
    }));

    // Simulate token refresh
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (oauthStatus.token) {
      await validateExistingToken(oauthStatus.token);
    }
  };

  const getStatusIcon = () => {
    switch (oauthStatus.status) {
      case 'authorizing':
      case 'exchanging':
        return <Loader2 className="w-4 h-4 animate-spin" />;
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-600" />;
      default:
        return <Github className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Github className="w-5 h-5" />
            <span>GitHub Integration</span>
          </CardTitle>
          <CardDescription>
            Connect your GitHub account to enable repository management and PR creation
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {!isConfigured ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="client-id">GitHub Client ID</Label>
                  <Input
                    id="client-id"
                    placeholder="Iv1.a629723bfa4c09c1"
                    value={config.clientId}
                    onChange={(e) => setConfig(prev => ({ ...prev, clientId: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="redirect-uri">Redirect URI</Label>
                  <Input
                    id="redirect-uri"
                    value={config.redirectUri}
                    onChange={(e) => setConfig(prev => ({ ...prev, redirectUri: e.target.value }))}
                  />
                </div>
              </div>

              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  You'll need to create a GitHub OAuth App in your GitHub settings. 
                  <a 
                    href="https://github.com/settings/applications/new" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="ml-1 text-primary hover:underline inline-flex items-center"
                  >
                    Create OAuth App
                    <ExternalLink className="w-3 h-3 ml-1" />
                  </a>
                </AlertDescription>
              </Alert>

              <Button 
                onClick={startOAuthFlow} 
                disabled={oauthStatus.status === 'authorizing' || oauthStatus.status === 'exchanging'}
                className="w-full"
              >
                {getStatusIcon()}
                <span className="ml-2">
                  {oauthStatus.status === 'authorizing' ? 'Authorizing...' :
                   oauthStatus.status === 'exchanging' ? 'Exchanging Token...' :
                   'Connect to GitHub'}
                </span>
              </Button>
            </>
          ) : (
            <div className="space-y-4">
              {/* Connected User Info */}
              {oauthStatus.user && (
                <div className="flex items-center space-x-4 p-4 border rounded-lg bg-green-50">
                  <img 
                    src={oauthStatus.user.avatar_url} 
                    alt={oauthStatus.user.name}
                    className="w-12 h-12 rounded-full"
                  />
                  <div className="flex-1">
                    <h4 className="font-medium">{oauthStatus.user.name}</h4>
                    <p className="text-sm text-muted-foreground">@{oauthStatus.user.login}</p>
                    <div className="flex items-center space-x-4 mt-1">
                      <span className="text-xs text-muted-foreground flex items-center">
                        <GitBranch className="w-3 h-3 mr-1" />
                        {oauthStatus.user.public_repos} repos
                      </span>
                      <span className="text-xs text-muted-foreground flex items-center">
                        <Users className="w-3 h-3 mr-1" />
                        {oauthStatus.user.followers} followers
                      </span>
                    </div>
                  </div>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    Connected
                  </Badge>
                </div>
              )}

              <div className="flex space-x-2">
                <Button variant="outline" onClick={refreshToken} size="sm">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Refresh
                </Button>
                <Button variant="outline" onClick={disconnectGitHub} size="sm">
                  Disconnect
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Status Alert */}
      {oauthStatus.status !== 'idle' && (
        <Alert className={cn(
          oauthStatus.status === 'success' && "border-green-200 bg-green-50",
          oauthStatus.status === 'error' && "border-red-200 bg-red-50"
        )}>
          {getStatusIcon()}
          <AlertDescription className="ml-2">
            {oauthStatus.message}
          </AlertDescription>
        </Alert>
      )}

      {/* Required Permissions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="w-5 h-5" />
            <span>Required Permissions</span>
          </CardTitle>
          <CardDescription>
            These permissions are needed for full functionality
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {REQUIRED_SCOPES.map((scope) => (
              <div key={scope} className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                <div>
                  <p className="font-medium text-sm">{scope}</p>
                  <p className="text-xs text-muted-foreground">
                    {SCOPE_DESCRIPTIONS[scope as keyof typeof SCOPE_DESCRIPTIONS]}
                  </p>
                </div>
                {oauthStatus.scopes?.includes(scope) && (
                  <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Setup Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Key className="w-5 h-5" />
            <span>Setup Instructions</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ol className="space-y-3 text-sm">
            <li className="flex items-start space-x-2">
              <span className="w-5 h-5 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0 mt-0.5">1</span>
              <div>
                <p className="font-medium">Create GitHub OAuth App</p>
                <p className="text-muted-foreground">Go to GitHub Settings → Developer settings → OAuth Apps</p>
              </div>
            </li>
            <li className="flex items-start space-x-2">
              <span className="w-5 h-5 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0 mt-0.5">2</span>
              <div>
                <p className="font-medium">Configure OAuth App</p>
                <p className="text-muted-foreground">Set the redirect URI to: <code className="bg-muted px-1 rounded">{config.redirectUri}</code></p>
              </div>
            </li>
            <li className="flex items-start space-x-2">
              <span className="w-5 h-5 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0 mt-0.5">3</span>
              <div>
                <p className="font-medium">Copy Client ID</p>
                <p className="text-muted-foreground">Copy the Client ID from your OAuth App and paste it above</p>
              </div>
            </li>
            <li className="flex items-start space-x-2">
              <span className="w-5 h-5 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0 mt-0.5">4</span>
              <div>
                <p className="font-medium">Connect Account</p>
                <p className="text-muted-foreground">Click "Connect to GitHub" to authorize the application</p>
              </div>
            </li>
          </ol>
        </CardContent>
      </Card>
    </div>
  );
};

export default GitHubOAuthFlow;
