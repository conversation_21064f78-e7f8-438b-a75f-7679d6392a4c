import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { GitBranch, Plus, Trash2, GitMerge, RefreshCw } from 'lucide-react';

interface Branch {
  name: string;
  commit: {
    sha: string;
    message: string;
    author: string;
    date: string;
  };
  protected: boolean;
  default: boolean;
}

interface BranchManagerProps {
  repository: string;
  onBranchSelect: (branch: string) => void;
  selectedBranch?: string;
}

export const BranchManager: React.FC<BranchManagerProps> = ({
  repository,
  onBranchSelect,
  selectedBranch
}) => {
  const [branches] = useState<Branch[]>([
    {
      name: 'main',
      commit: {
        sha: 'abc123',
        message: 'Initial commit',
        author: 'Developer',
        date: '2024-01-15',
      },
      protected: true,
      default: true,
    },
    {
      name: 'feature/new-component',
      commit: {
        sha: 'def456',
        message: 'Add new component',
        author: 'Developer',
        date: '2024-01-14',
      },
      protected: false,
      default: false,
    },
  ]);

  const [newBranchName, setNewBranchName] = useState('');
  const [baseBranch, setBaseBranch] = useState('main');

  const createBranch = () => {
    if (newBranchName.trim()) {
      console.log(`Creating branch: ${newBranchName} from ${baseBranch}`);
      setNewBranchName('');
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <GitBranch className="w-5 h-5" />
          <span>Branch Management</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Create New Branch */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Create New Branch</h4>
          <div className="flex space-x-2">
            <Input
              placeholder="Branch name"
              value={newBranchName}
              onChange={(e) => setNewBranchName(e.target.value)}
              className="flex-1"
            />
            <Select value={baseBranch} onValueChange={setBaseBranch}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {branches.map(branch => (
                  <SelectItem key={branch.name} value={branch.name}>
                    {branch.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button onClick={createBranch} disabled={!newBranchName.trim()}>
              <Plus className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Branch List */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Branches</h4>
          <div className="space-y-2">
            {branches.map(branch => (
              <div
                key={branch.name}
                className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                  selectedBranch === branch.name ? 'border-primary bg-primary/5' : 'hover:bg-muted/50'
                }`}
                onClick={() => onBranchSelect(branch.name)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">{branch.name}</span>
                    {branch.default && <Badge variant="default" className="text-xs">Default</Badge>}
                    {branch.protected && <Badge variant="secondary" className="text-xs">Protected</Badge>}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm">
                      <GitMerge className="w-4 h-4" />
                    </Button>
                    {!branch.protected && (
                      <Button variant="ghost" size="sm">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {branch.commit.message} • {branch.commit.author} • {branch.commit.date}
                </p>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default BranchManager;
