import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Loader2, 
  TestTube, 
  Shield,
  Zap,
  RefreshCw,
  Play,
  Pause
} from 'lucide-react';
import { 
  EnhancedUserSettings, 
  ProviderConfig, 
  validateEnhancedUserSettings 
} from '@/types/settings';

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  score: number;
}

interface TestResult {
  provider: string;
  success: boolean;
  responseTime: number;
  error?: string;
}

interface ConfigurationValidatorProps {
  settings: EnhancedUserSettings;
  onValidationChange?: (result: ValidationResult) => void;
  onTestConnection?: (provider: string, config: ProviderConfig) => Promise<boolean>;
  autoValidate?: boolean;
}

export const ConfigurationValidator: React.FC<ConfigurationValidatorProps> = ({
  settings,
  onValidationChange,
  onTestConnection,
  autoValidate = true,
}) => {
  const [validationResult, setValidationResult] = useState<ValidationResult>({
    isValid: false,
    errors: [],
    warnings: [],
    score: 0,
  });
  
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isValidating, setIsValidating] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testingProvider, setTestingProvider] = useState<string | null>(null);

  // Auto-validate when settings change
  useEffect(() => {
    if (autoValidate) {
      validateConfiguration();
    }
  }, [settings, autoValidate]);

  // Notify parent of validation changes
  useEffect(() => {
    if (onValidationChange) {
      onValidationChange(validationResult);
    }
  }, [validationResult, onValidationChange]);

  const validateConfiguration = async () => {
    setIsValidating(true);
    
    try {
      const errors: string[] = [];
      const warnings: string[] = [];
      let score = 100;

      // Schema validation
      try {
        validateEnhancedUserSettings(settings);
      } catch (error: any) {
        if (error.errors) {
          errors.push(...error.errors.map((e: any) => e.message));
        } else {
          errors.push(error.message);
        }
        score -= 20;
      }

      // Business logic validation
      const businessValidation = validateBusinessLogic(settings);
      errors.push(...businessValidation.errors);
      warnings.push(...businessValidation.warnings);
      score -= businessValidation.errors.length * 10;
      score -= businessValidation.warnings.length * 5;

      // Security validation
      const securityValidation = validateSecurity(settings);
      errors.push(...securityValidation.errors);
      warnings.push(...securityValidation.warnings);
      score -= securityValidation.errors.length * 15;
      score -= securityValidation.warnings.length * 3;

      // Performance validation
      const performanceValidation = validatePerformance(settings);
      warnings.push(...performanceValidation.warnings);
      score -= performanceValidation.warnings.length * 2;

      const result: ValidationResult = {
        isValid: errors.length === 0,
        errors,
        warnings,
        score: Math.max(0, score),
      };

      setValidationResult(result);
    } catch (error) {
      setValidationResult({
        isValid: false,
        errors: ['Validation failed: ' + (error instanceof Error ? error.message : 'Unknown error')],
        warnings: [],
        score: 0,
      });
    } finally {
      setIsValidating(false);
    }
  };

  const testAllConnections = async () => {
    if (!onTestConnection) return;

    setIsTesting(true);
    setTestResults([]);

    const providers = [
      { name: 'planner', config: settings.provider_configs.planner },
      { name: 'critic', config: settings.provider_configs.critic },
      ...settings.provider_configs.fallback_providers.map((config, index) => ({
        name: `fallback-${index}`,
        config,
      })),
    ];

    for (const { name, config } of providers) {
      setTestingProvider(name);
      
      try {
        const startTime = Date.now();
        const success = await onTestConnection(name, config);
        const responseTime = Date.now() - startTime;

        setTestResults(prev => [...prev, {
          provider: name,
          success,
          responseTime,
        }]);
      } catch (error) {
        setTestResults(prev => [...prev, {
          provider: name,
          success: false,
          responseTime: 0,
          error: error instanceof Error ? error.message : 'Connection failed',
        }]);
      }
    }

    setTestingProvider(null);
    setIsTesting(false);
  };

  const getValidationIcon = () => {
    if (isValidating) return <Loader2 className="w-5 h-5 animate-spin text-blue-400" />;
    if (validationResult.isValid) return <CheckCircle className="w-5 h-5 text-green-400" />;
    if (validationResult.errors.length > 0) return <XCircle className="w-5 h-5 text-red-400" />;
    return <AlertTriangle className="w-5 h-5 text-yellow-400" />;
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-400';
    if (score >= 70) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getScoreBadge = (score: number) => {
    if (score >= 90) return 'default';
    if (score >= 70) return 'secondary';
    return 'destructive';
  };

  return (
    <div className="space-y-6">
      {/* Validation Overview */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            {getValidationIcon()}
            Configuration Validation
            <Badge variant={getScoreBadge(validationResult.score)} className="ml-auto">
              Score: {validationResult.score}/100
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Validation Progress */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-slate-400">Configuration Health</span>
              <span className={`font-medium ${getScoreColor(validationResult.score)}`}>
                {validationResult.score}%
              </span>
            </div>
            <Progress 
              value={validationResult.score} 
              className="h-2"
            />
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-red-400">{validationResult.errors.length}</p>
              <p className="text-xs text-slate-400">Errors</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-400">{validationResult.warnings.length}</p>
              <p className="text-xs text-slate-400">Warnings</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-400">{testResults.filter(r => r.success).length}</p>
              <p className="text-xs text-slate-400">Connections OK</p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={validateConfiguration}
              disabled={isValidating}
              className="border-slate-600 text-slate-300 hover:text-white"
            >
              {isValidating ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Shield className="w-4 h-4 mr-2" />
              )}
              Validate
            </Button>
            
            {onTestConnection && (
              <Button
                variant="outline"
                size="sm"
                onClick={testAllConnections}
                disabled={isTesting}
                className="border-slate-600 text-slate-300 hover:text-white"
              >
                {isTesting ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <TestTube className="w-4 h-4 mr-2" />
                )}
                Test Connections
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Validation Results */}
      {(validationResult.errors.length > 0 || validationResult.warnings.length > 0) && (
        <div className="space-y-4">
          {/* Errors */}
          {validationResult.errors.length > 0 && (
            <Alert className="border-red-500/20 bg-red-500/10">
              <XCircle className="h-4 w-4 text-red-500" />
              <AlertDescription className="text-red-200">
                <div className="font-medium mb-2">Configuration Errors ({validationResult.errors.length})</div>
                <ul className="list-disc list-inside space-y-1">
                  {validationResult.errors.map((error, index) => (
                    <li key={index} className="text-sm">{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {/* Warnings */}
          {validationResult.warnings.length > 0 && (
            <Alert className="border-yellow-500/20 bg-yellow-500/10">
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
              <AlertDescription className="text-yellow-200">
                <div className="font-medium mb-2">Configuration Warnings ({validationResult.warnings.length})</div>
                <ul className="list-disc list-inside space-y-1">
                  {validationResult.warnings.map((warning, index) => (
                    <li key={index} className="text-sm">{warning}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}
        </div>
      )}

      {/* Connection Test Results */}
      {testResults.length > 0 && (
        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <TestTube className="w-5 h-5 text-blue-400" />
              Connection Test Results
              {isTesting && (
                <Badge variant="secondary" className="ml-auto">
                  Testing {testingProvider}...
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-slate-900/50 rounded-lg border border-slate-700">
                  <div className="flex items-center gap-3">
                    {result.success ? (
                      <CheckCircle className="w-5 h-5 text-green-400" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-400" />
                    )}
                    <div>
                      <p className="text-white font-medium capitalize">{result.provider}</p>
                      {result.error && (
                        <p className="text-red-400 text-sm">{result.error}</p>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant={result.success ? "default" : "destructive"} className="mb-1">
                      {result.success ? "Connected" : "Failed"}
                    </Badge>
                    {result.success && (
                      <p className="text-slate-400 text-sm">{result.responseTime}ms</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Success State */}
      {validationResult.isValid && validationResult.errors.length === 0 && validationResult.warnings.length === 0 && (
        <Alert className="border-green-500/20 bg-green-500/10">
          <CheckCircle className="h-4 w-4 text-green-500" />
          <AlertDescription className="text-green-200">
            <div className="font-medium">Configuration Valid</div>
            <p className="text-sm mt-1">
              All settings have been validated successfully. Your dual-agent system is ready to use.
            </p>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

// Validation helper functions
function validateBusinessLogic(settings: EnhancedUserSettings) {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Cost guard validation
  if (settings.cost_guard_config.enabled && settings.cost_guard_config.maxCostPerLoop > 20) {
    warnings.push('Cost limit is very high ($20+). Consider lowering for safety.');
  }

  // Failover validation
  if (settings.failover_config.enabled && settings.provider_configs.fallback_providers.length === 0) {
    errors.push('Failover is enabled but no fallback providers are configured.');
  }

  // Performance validation
  if (settings.performance_config.parallelExecution && settings.performance_config.maxConcurrency > 5) {
    warnings.push('High concurrency may increase costs significantly.');
  }

  return { errors, warnings };
}

function validateSecurity(settings: EnhancedUserSettings) {
  const errors: string[] = [];
  const warnings: string[] = [];

  // API key validation
  if (!settings.api_keys?.openai && settings.provider_configs.planner.type === 'openai') {
    errors.push('OpenAI API key required for planner configuration.');
  }

  if (!settings.api_keys?.anthropic && settings.provider_configs.critic.type === 'anthropic') {
    errors.push('Anthropic API key required for critic configuration.');
  }

  // Token limits
  if (settings.token_monitor_config.maxTokensPerHour > 100000) {
    warnings.push('Very high token limit may result in unexpected costs.');
  }

  return { errors, warnings };
}

function validatePerformance(settings: EnhancedUserSettings) {
  const warnings: string[] = [];

  // Retry configuration
  if (settings.retry_config.maxRetries > 5) {
    warnings.push('High retry count may cause delays and increased costs.');
  }

  // Timeout validation
  if (settings.retry_config.timeoutMs < 5000) {
    warnings.push('Very short timeout may cause unnecessary retries.');
  }

  return { warnings };
}
