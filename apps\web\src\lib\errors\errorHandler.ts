import { z } from 'zod';

// Error types and categories
export enum ErrorCategory {
  VALIDATION = 'validation',
  NETWORK = 'network',
  API = 'api',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  RATE_LIMIT = 'rate_limit',
  QUOTA_EXCEEDED = 'quota_exceeded',
  TRANSFORMATION = 'transformation',
  CONFIGURATION = 'configuration',
  SYSTEM = 'system',
  USER_INPUT = 'user_input',
  UNKNOWN = 'unknown',
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// Error interface
export interface AppError {
  id: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  message: string;
  details?: string;
  code?: string;
  timestamp: Date;
  context?: Record<string, any>;
  stack?: string;
  userMessage?: string;
  recoverable: boolean;
  retryable: boolean;
  suggestions?: string[];
}

// Error recovery actions
export interface ErrorRecoveryAction {
  label: string;
  action: () => void | Promise<void>;
  primary?: boolean;
}

// Error handler class
export class ErrorHandler {
  private static errorLog: AppError[] = [];
  private static maxLogSize = 1000;
  private static listeners: ((error: AppError) => void)[] = [];

  // Create standardized error
  static createError(
    category: ErrorCategory,
    message: string,
    options: {
      severity?: ErrorSeverity;
      details?: string;
      code?: string;
      context?: Record<string, any>;
      originalError?: Error;
      userMessage?: string;
      recoverable?: boolean;
      retryable?: boolean;
      suggestions?: string[];
    } = {}
  ): AppError {
    const error: AppError = {
      id: this.generateErrorId(),
      category,
      severity: options.severity || ErrorSeverity.MEDIUM,
      message,
      details: options.details,
      code: options.code,
      timestamp: new Date(),
      context: options.context,
      stack: options.originalError?.stack,
      userMessage: options.userMessage || this.generateUserMessage(category, message),
      recoverable: options.recoverable ?? true,
      retryable: options.retryable ?? false,
      suggestions: options.suggestions || this.generateSuggestions(category, message),
    };

    this.logError(error);
    this.notifyListeners(error);
    
    return error;
  }

  // Handle different types of errors
  static handleValidationError(
    field: string,
    message: string,
    value?: any
  ): AppError {
    return this.createError(
      ErrorCategory.VALIDATION,
      `Validation failed for ${field}: ${message}`,
      {
        severity: ErrorSeverity.LOW,
        context: { field, value },
        userMessage: `Please check your ${field}: ${message}`,
        recoverable: true,
        suggestions: [
          `Verify the ${field} format`,
          'Check the input requirements',
          'Try a different value',
        ],
      }
    );
  }

  static handleNetworkError(
    url: string,
    method: string,
    originalError: Error
  ): AppError {
    return this.createError(
      ErrorCategory.NETWORK,
      `Network request failed: ${method} ${url}`,
      {
        severity: ErrorSeverity.HIGH,
        details: originalError.message,
        context: { url, method },
        originalError,
        userMessage: 'Connection failed. Please check your internet connection.',
        retryable: true,
        suggestions: [
          'Check your internet connection',
          'Try again in a few moments',
          'Contact support if the problem persists',
        ],
      }
    );
  }

  static handleApiError(
    endpoint: string,
    statusCode: number,
    responseData?: any
  ): AppError {
    const isClientError = statusCode >= 400 && statusCode < 500;
    const isServerError = statusCode >= 500;
    
    let category = ErrorCategory.API;
    let severity = ErrorSeverity.MEDIUM;
    let userMessage = 'An API error occurred.';
    let retryable = false;

    if (statusCode === 401) {
      category = ErrorCategory.AUTHENTICATION;
      userMessage = 'Authentication failed. Please check your credentials.';
    } else if (statusCode === 403) {
      category = ErrorCategory.AUTHORIZATION;
      userMessage = 'Access denied. You don\'t have permission for this action.';
    } else if (statusCode === 429) {
      category = ErrorCategory.RATE_LIMIT;
      userMessage = 'Too many requests. Please wait before trying again.';
      retryable = true;
    } else if (isServerError) {
      severity = ErrorSeverity.HIGH;
      userMessage = 'Server error. Please try again later.';
      retryable = true;
    }

    return this.createError(
      category,
      `API request failed: ${statusCode} ${endpoint}`,
      {
        severity,
        details: JSON.stringify(responseData),
        code: statusCode.toString(),
        context: { endpoint, statusCode, responseData },
        userMessage,
        retryable,
        suggestions: this.getApiErrorSuggestions(statusCode),
      }
    );
  }

  static handleTransformationError(
    transformationId: string,
    stage: string,
    originalError: Error
  ): AppError {
    return this.createError(
      ErrorCategory.TRANSFORMATION,
      `Transformation failed at ${stage}`,
      {
        severity: ErrorSeverity.HIGH,
        details: originalError.message,
        context: { transformationId, stage },
        originalError,
        userMessage: 'Code transformation failed. Please try with different settings.',
        retryable: true,
        suggestions: [
          'Try with a simpler prompt',
          'Check your code for syntax errors',
          'Reduce the complexity of the transformation',
          'Try a different AI model',
        ],
      }
    );
  }

  static handleConfigurationError(
    component: string,
    issue: string
  ): AppError {
    return this.createError(
      ErrorCategory.CONFIGURATION,
      `Configuration error in ${component}: ${issue}`,
      {
        severity: ErrorSeverity.MEDIUM,
        context: { component, issue },
        userMessage: 'Configuration issue detected. Please check your settings.',
        recoverable: true,
        suggestions: [
          'Review your configuration settings',
          'Run the setup wizard',
          'Check the documentation',
          'Reset to default settings',
        ],
      }
    );
  }

  // Error recovery helpers
  static getRecoveryActions(error: AppError): ErrorRecoveryAction[] {
    const actions: ErrorRecoveryAction[] = [];

    if (error.retryable) {
      actions.push({
        label: 'Retry',
        action: () => {
          // Retry logic would be implemented by the caller
          console.log('Retrying operation...');
        },
        primary: true,
      });
    }

    if (error.category === ErrorCategory.CONFIGURATION) {
      actions.push({
        label: 'Open Settings',
        action: () => {
          window.location.href = '/settings';
        },
      });
    }

    if (error.category === ErrorCategory.AUTHENTICATION) {
      actions.push({
        label: 'Re-authenticate',
        action: () => {
          // Clear auth tokens and redirect to login
          localStorage.removeItem('auth_token');
          window.location.href = '/auth';
        },
      });
    }

    actions.push({
      label: 'Dismiss',
      action: () => {
        // Dismiss error
      },
    });

    return actions;
  }

  // Error logging and monitoring
  static logError(error: AppError): void {
    this.errorLog.unshift(error);
    
    // Trim log if it gets too large
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('App Error:', error);
    }

    // Send to monitoring service in production
    if (process.env.NODE_ENV === 'production' && error.severity === ErrorSeverity.CRITICAL) {
      this.sendToMonitoring(error);
    }
  }

  static getErrorLog(): AppError[] {
    return [...this.errorLog];
  }

  static clearErrorLog(): void {
    this.errorLog = [];
  }

  // Event listeners for error handling
  static addErrorListener(listener: (error: AppError) => void): void {
    this.listeners.push(listener);
  }

  static removeErrorListener(listener: (error: AppError) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  private static notifyListeners(error: AppError): void {
    this.listeners.forEach(listener => {
      try {
        listener(error);
      } catch (err) {
        console.error('Error in error listener:', err);
      }
    });
  }

  // Helper methods
  private static generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private static generateUserMessage(category: ErrorCategory, message: string): string {
    const userMessages: Record<ErrorCategory, string> = {
      [ErrorCategory.VALIDATION]: 'Please check your input and try again.',
      [ErrorCategory.NETWORK]: 'Connection failed. Please check your internet connection.',
      [ErrorCategory.API]: 'Service temporarily unavailable. Please try again later.',
      [ErrorCategory.AUTHENTICATION]: 'Authentication failed. Please log in again.',
      [ErrorCategory.AUTHORIZATION]: 'Access denied. You don\'t have permission for this action.',
      [ErrorCategory.RATE_LIMIT]: 'Too many requests. Please wait before trying again.',
      [ErrorCategory.QUOTA_EXCEEDED]: 'Usage limit exceeded. Please upgrade your plan.',
      [ErrorCategory.TRANSFORMATION]: 'Code transformation failed. Please try with different settings.',
      [ErrorCategory.CONFIGURATION]: 'Configuration issue detected. Please check your settings.',
      [ErrorCategory.SYSTEM]: 'System error occurred. Please contact support.',
      [ErrorCategory.USER_INPUT]: 'Invalid input provided. Please check and try again.',
      [ErrorCategory.UNKNOWN]: 'An unexpected error occurred. Please try again.',
    };

    return userMessages[category] || 'An error occurred. Please try again.';
  }

  private static generateSuggestions(category: ErrorCategory, message: string): string[] {
    const suggestions: Record<ErrorCategory, string[]> = {
      [ErrorCategory.VALIDATION]: [
        'Check the input format',
        'Verify required fields are filled',
        'Review the input requirements',
      ],
      [ErrorCategory.NETWORK]: [
        'Check your internet connection',
        'Try again in a few moments',
        'Contact support if the problem persists',
      ],
      [ErrorCategory.API]: [
        'Try again later',
        'Check service status',
        'Contact support if the issue persists',
      ],
      [ErrorCategory.AUTHENTICATION]: [
        'Log in again',
        'Check your credentials',
        'Clear browser cache and cookies',
      ],
      [ErrorCategory.AUTHORIZATION]: [
        'Contact your administrator',
        'Check your account permissions',
        'Upgrade your plan if needed',
      ],
      [ErrorCategory.RATE_LIMIT]: [
        'Wait a few minutes before trying again',
        'Reduce the frequency of requests',
        'Consider upgrading your plan',
      ],
      [ErrorCategory.QUOTA_EXCEEDED]: [
        'Upgrade your plan',
        'Wait for quota reset',
        'Contact support for assistance',
      ],
      [ErrorCategory.TRANSFORMATION]: [
        'Try with a simpler prompt',
        'Check your code for syntax errors',
        'Try a different AI model',
      ],
      [ErrorCategory.CONFIGURATION]: [
        'Run the setup wizard',
        'Check the documentation',
        'Reset to default settings',
      ],
      [ErrorCategory.SYSTEM]: [
        'Try again later',
        'Contact support',
        'Check system status',
      ],
      [ErrorCategory.USER_INPUT]: [
        'Review your input',
        'Check the format requirements',
        'Try a different approach',
      ],
      [ErrorCategory.UNKNOWN]: [
        'Try again',
        'Refresh the page',
        'Contact support if the issue persists',
      ],
    };

    return suggestions[category] || ['Try again', 'Contact support if the issue persists'];
  }

  private static getApiErrorSuggestions(statusCode: number): string[] {
    switch (statusCode) {
      case 400:
        return ['Check your request format', 'Verify all required fields', 'Review the API documentation'];
      case 401:
        return ['Log in again', 'Check your API key', 'Verify your credentials'];
      case 403:
        return ['Check your permissions', 'Contact your administrator', 'Upgrade your plan'];
      case 404:
        return ['Check the URL', 'Verify the resource exists', 'Try a different endpoint'];
      case 429:
        return ['Wait before trying again', 'Reduce request frequency', 'Consider upgrading your plan'];
      case 500:
        return ['Try again later', 'Contact support', 'Check service status'];
      default:
        return ['Try again later', 'Contact support if the issue persists'];
    }
  }

  private static async sendToMonitoring(error: AppError): Promise<void> {
    try {
      // In a real app, this would send to a monitoring service like Sentry
      console.log('Sending error to monitoring service:', error);
    } catch (err) {
      console.error('Failed to send error to monitoring service:', err);
    }
  }
}
