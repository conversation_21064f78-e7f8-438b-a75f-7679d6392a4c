import { render, screen, waitFor } from '@testing-library/react';
import { QueueStatus } from '../QueueStatus';

// Mock fetch
global.fetch = jest.fn();

const mockQueueData = {
  totalItems: 5,
  runningItems: 1,
  queuedItems: 3,
  completedToday: 10,
  averageWaitTime: 5.5,
  currentCapacity: 2,
  maxCapacity: 5,
  items: [
    {
      sessionId: 'test-session-1',
      prompt: 'Test transformation',
      priority: 5,
      queuePosition: 1,
      status: 'queued' as const,
      estimatedWaitTime: 3,
      createdAt: new Date().toISOString()
    },
    {
      sessionId: 'test-session-2',
      prompt: 'Another transformation',
      priority: 8,
      queuePosition: 0,
      status: 'running' as const,
      createdAt: new Date().toISOString()
    }
  ]
};

describe('QueueStatus', () => {
  beforeEach(() => {
    (fetch as jest.Mock).mockClear();
  });

  it('renders loading state initially', () => {
    (fetch as jest.Mock).mockImplementation(() => new Promise(() => {}));
    
    render(<QueueStatus />);
    
    expect(screen.getByText('Queue Status')).toBeInTheDocument();
    expect(screen.getAllByTestId('skeleton')).toHaveLength(4);
  });

  it('displays queue data when loaded successfully', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: mockQueueData
      })
    });

    render(<QueueStatus />);

    await waitFor(() => {
      expect(screen.getByText('5')).toBeInTheDocument(); // Total items
      expect(screen.getByText('1')).toBeInTheDocument(); // Running items
      expect(screen.getByText('3')).toBeInTheDocument(); // Queued items
      expect(screen.getByText('10')).toBeInTheDocument(); // Completed today
    });

    expect(screen.getByText('Test transformation')).toBeInTheDocument();
    expect(screen.getByText('Another transformation')).toBeInTheDocument();
  });

  it('displays error state when fetch fails', async () => {
    (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

    render(<QueueStatus />);

    await waitFor(() => {
      expect(screen.getByText(/Network error/)).toBeInTheDocument();
    });

    expect(screen.getByText('Retry')).toBeInTheDocument();
  });

  it('shows empty state when no items in queue', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: { ...mockQueueData, items: [] }
      })
    });

    render(<QueueStatus />);

    await waitFor(() => {
      expect(screen.getByText('No items in queue')).toBeInTheDocument();
    });
  });

  it('calls refresh function when refresh button is clicked', async () => {
    const mockRefresh = jest.fn();
    
    (fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => ({
        success: true,
        data: mockQueueData
      })
    });

    render(<QueueStatus onRefresh={mockRefresh} />);

    await waitFor(() => {
      expect(screen.getByText('Test transformation')).toBeInTheDocument();
    });

    const refreshButton = screen.getByText('Refresh');
    refreshButton.click();

    expect(mockRefresh).toHaveBeenCalledTimes(1);
  });

  it('displays correct status badges for different queue item states', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: mockQueueData
      })
    });

    render(<QueueStatus />);

    await waitFor(() => {
      expect(screen.getByText('Queued')).toBeInTheDocument();
      expect(screen.getByText('Running')).toBeInTheDocument();
    });
  });

  it('formats wait time correctly', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: mockQueueData
      })
    });

    render(<QueueStatus />);

    await waitFor(() => {
      expect(screen.getByText(/Average wait time: 6m/)).toBeInTheDocument();
      expect(screen.getByText(/Est\. wait: 3m/)).toBeInTheDocument();
    });
  });
});
