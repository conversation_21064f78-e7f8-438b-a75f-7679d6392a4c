{"config": {"configFile": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\playwright.config.ts", "rootDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "artifacts/playwright-report.json"}], ["junit", {"outputFile": "artifacts/playwright-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Tablet", "name": "Tablet", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.0", "workers": 4, "webServer": null}, "suites": [], "errors": [{"message": "Error: Timed out waiting 60000ms from config.webServer.", "stack": "Error: Timed out waiting 60000ms from config.webServer."}], "stats": {"startTime": "2025-06-18T23:50:47.631Z", "duration": 60606.270000000004, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}