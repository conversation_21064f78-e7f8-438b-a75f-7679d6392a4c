import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Activity, 
  AlertCircle, 
  CheckCircle, 
  Clock, 
  Filter,
  Pause,
  Play,
  RefreshCw,
  Settings,
  Wifi,
  WifiOff,
  XCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  StreamEvent, 
  StreamEventType, 
  getEventStreamingManager 
} from '@/lib/streaming/eventStreaming';
import { 
  ConnectionStatus, 
  getWebSocketManager 
} from '@/lib/websocket/websocketManager';

interface RealTimeStatusUpdatesProps {
  maxEvents?: number;
  showFilters?: boolean;
  autoScroll?: boolean;
  compact?: boolean;
  className?: string;
}

export const RealTimeStatusUpdates: React.FC<RealTimeStatusUpdatesProps> = ({
  maxEvents = 50,
  showFilters = true,
  autoScroll = true,
  compact = false,
  className
}) => {
  const [events, setEvents] = useState<StreamEvent[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>(ConnectionStatus.DISCONNECTED);
  const [isPaused, setIsPaused] = useState(false);
  const [filters, setFilters] = useState<{
    types: StreamEventType[];
    priorities: string[];
    sources: string[];
  }>({
    types: [],
    priorities: [],
    sources: [],
  });
  const [showFilterPanel, setShowFilterPanel] = useState(false);

  useEffect(() => {
    const eventManager = getEventStreamingManager();
    const wsManager = getWebSocketManager();

    // Get initial events
    setEvents(eventManager.getRecentEvents(maxEvents));

    // Monitor connection status
    const updateConnectionStatus = () => {
      setConnectionStatus(wsManager.getStatus());
    };

    updateConnectionStatus();
    const statusInterval = setInterval(updateConnectionStatus, 1000);

    // Subscribe to all events
    const unsubscribe = eventManager.subscribeToMultiple(
      Object.values(StreamEventType),
      (event) => {
        if (!isPaused) {
          setEvents(prev => {
            const filtered = applyFilters([event, ...prev], filters);
            return filtered.slice(0, maxEvents);
          });
        }
      }
    );

    return () => {
      unsubscribe();
      clearInterval(statusInterval);
    };
  }, [maxEvents, isPaused, filters]);

  const applyFilters = (eventList: StreamEvent[], currentFilters: typeof filters): StreamEvent[] => {
    return eventList.filter(event => {
      if (currentFilters.types.length > 0 && !currentFilters.types.includes(event.type)) {
        return false;
      }
      if (currentFilters.priorities.length > 0 && !currentFilters.priorities.includes(event.priority)) {
        return false;
      }
      if (currentFilters.sources.length > 0 && event.source && !currentFilters.sources.includes(event.source)) {
        return false;
      }
      return true;
    });
  };

  const getEventIcon = (event: StreamEvent) => {
    switch (event.type) {
      case StreamEventType.TRANSFORMATION_STARTED:
      case StreamEventType.PLANNER_STARTED:
      case StreamEventType.CRITIC_STARTED:
        return <Play className="w-4 h-4 text-blue-500" />;
      
      case StreamEventType.TRANSFORMATION_COMPLETED:
      case StreamEventType.PLANNER_COMPLETED:
      case StreamEventType.CRITIC_COMPLETED:
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      
      case StreamEventType.TRANSFORMATION_FAILED:
      case StreamEventType.ERROR_OCCURRED:
        return <XCircle className="w-4 h-4 text-red-500" />;
      
      case StreamEventType.WARNING_ISSUED:
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      
      case StreamEventType.TRANSFORMATION_PROGRESS:
      case StreamEventType.PLANNER_THINKING:
      case StreamEventType.CRITIC_EVALUATING:
        return <Activity className="w-4 h-4 text-blue-500 animate-pulse" />;
      
      default:
        return <Clock className="w-4 h-4 text-muted-foreground" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-500';
      case 'high':
        return 'bg-orange-500';
      case 'normal':
        return 'bg-blue-500';
      case 'low':
        return 'bg-gray-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getConnectionStatusIcon = () => {
    switch (connectionStatus) {
      case ConnectionStatus.CONNECTED:
        return <Wifi className="w-4 h-4 text-green-500" />;
      case ConnectionStatus.CONNECTING:
      case ConnectionStatus.RECONNECTING:
        return <RefreshCw className="w-4 h-4 text-yellow-500 animate-spin" />;
      default:
        return <WifiOff className="w-4 h-4 text-red-500" />;
    }
  };

  const formatEventMessage = (event: StreamEvent): string => {
    switch (event.type) {
      case StreamEventType.TRANSFORMATION_STARTED:
        return `Transformation started: ${event.data.transformationId?.slice(-8) || 'Unknown'}`;
      case StreamEventType.TRANSFORMATION_COMPLETED:
        return `Transformation completed successfully in ${event.data.duration || 'unknown'}ms`;
      case StreamEventType.TRANSFORMATION_FAILED:
        return `Transformation failed: ${event.data.error || 'Unknown error'}`;
      case StreamEventType.PLANNER_THINKING:
        return `Planner: ${event.data.message || 'Analyzing code...'}`;
      case StreamEventType.CRITIC_EVALUATING:
        return `Critic: ${event.data.message || 'Evaluating transformation...'}`;
      case StreamEventType.QUEUE_UPDATED:
        return `Queue updated: ${event.data.pending || 0} pending, ${event.data.running || 0} running`;
      case StreamEventType.COST_UPDATED:
        return `Cost update: $${event.data.totalCost?.toFixed(4) || '0.0000'}`;
      case StreamEventType.ERROR_OCCURRED:
        return `Error: ${event.data.message || 'Unknown error occurred'}`;
      default:
        return event.data.message || `${event.type.replace(/_/g, ' ')}`;
    }
  };

  const clearEvents = () => {
    setEvents([]);
    const eventManager = getEventStreamingManager();
    eventManager.clearHistory();
  };

  const togglePause = () => {
    setIsPaused(!isPaused);
  };

  const refreshEvents = () => {
    const eventManager = getEventStreamingManager();
    setEvents(eventManager.getRecentEvents(maxEvents));
  };

  if (compact) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center space-x-2">
              <Activity className="w-4 h-4" />
              <span>Live Updates</span>
            </CardTitle>
            <div className="flex items-center space-x-2">
              {getConnectionStatusIcon()}
              <Badge variant="outline" className="text-xs">
                {events.length}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <ScrollArea className="h-32">
            <div className="space-y-1">
              {events.slice(0, 5).map((event, index) => (
                <div key={event.id} className="flex items-center space-x-2 text-xs">
                  {getEventIcon(event)}
                  <span className="flex-1 truncate">{formatEventMessage(event)}</span>
                  <span className="text-muted-foreground">
                    {event.timestamp.toLocaleTimeString()}
                  </span>
                </div>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Activity className="w-5 h-5" />
            <span>Real-time Status Updates</span>
            <Badge variant="outline">{events.length} events</Badge>
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1">
              {getConnectionStatusIcon()}
              <span className="text-xs text-muted-foreground capitalize">
                {connectionStatus}
              </span>
            </div>
            
            {isPaused ? (
              <Badge variant="secondary" className="text-xs">Paused</Badge>
            ) : (
              <Badge variant="default" className="text-xs animate-pulse">Live</Badge>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={togglePause}
              className="h-8 w-8 p-0"
            >
              {isPaused ? <Play className="w-4 h-4" /> : <Pause className="w-4 h-4" />}
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={refreshEvents}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className="w-4 h-4" />
            </Button>
            
            {showFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowFilterPanel(!showFilterPanel)}
                className="h-8 w-8 p-0"
              >
                <Filter className="w-4 h-4" />
              </Button>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={clearEvents}
              className="h-8 w-8 p-0"
            >
              <XCircle className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {/* Filter Panel */}
        {showFilterPanel && (
          <div className="mb-4 p-3 border rounded-lg bg-muted/50">
            <h4 className="text-sm font-medium mb-2">Filters</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <div>
                <label className="text-xs text-muted-foreground">Event Types</label>
                <div className="flex flex-wrap gap-1 mt-1">
                  {Object.values(StreamEventType).slice(0, 5).map(type => (
                    <Badge
                      key={type}
                      variant={filters.types.includes(type) ? "default" : "outline"}
                      className="text-xs cursor-pointer"
                      onClick={() => {
                        setFilters(prev => ({
                          ...prev,
                          types: prev.types.includes(type)
                            ? prev.types.filter(t => t !== type)
                            : [...prev.types, type]
                        }));
                      }}
                    >
                      {type.split('_')[0]}
                    </Badge>
                  ))}
                </div>
              </div>
              
              <div>
                <label className="text-xs text-muted-foreground">Priority</label>
                <div className="flex flex-wrap gap-1 mt-1">
                  {['low', 'normal', 'high', 'critical'].map(priority => (
                    <Badge
                      key={priority}
                      variant={filters.priorities.includes(priority) ? "default" : "outline"}
                      className="text-xs cursor-pointer"
                      onClick={() => {
                        setFilters(prev => ({
                          ...prev,
                          priorities: prev.priorities.includes(priority)
                            ? prev.priorities.filter(p => p !== priority)
                            : [...prev.priorities, priority]
                        }));
                      }}
                    >
                      {priority}
                    </Badge>
                  ))}
                </div>
              </div>
              
              <div>
                <label className="text-xs text-muted-foreground">Source</label>
                <div className="flex flex-wrap gap-1 mt-1">
                  {['system', 'websocket', 'user'].map(source => (
                    <Badge
                      key={source}
                      variant={filters.sources.includes(source) ? "default" : "outline"}
                      className="text-xs cursor-pointer"
                      onClick={() => {
                        setFilters(prev => ({
                          ...prev,
                          sources: prev.sources.includes(source)
                            ? prev.sources.filter(s => s !== source)
                            : [...prev.sources, source]
                        }));
                      }}
                    >
                      {source}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Events List */}
        <ScrollArea className="h-96">
          <div className="space-y-2">
            {events.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Activity className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>No events to display</p>
                <p className="text-xs">Events will appear here in real-time</p>
              </div>
            ) : (
              events.map((event, index) => (
                <div
                  key={event.id}
                  className="flex items-start space-x-3 p-3 rounded-lg border bg-card hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center space-x-2 flex-shrink-0">
                    {getEventIcon(event)}
                    <div className={cn("w-2 h-2 rounded-full", getPriorityColor(event.priority))} />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium truncate">
                        {formatEventMessage(event)}
                      </p>
                      <div className="flex items-center space-x-2 flex-shrink-0">
                        <Badge variant="outline" className="text-xs">
                          {event.priority}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {event.timestamp.toLocaleTimeString()}
                        </span>
                      </div>
                    </div>
                    
                    {event.metadata && Object.keys(event.metadata).length > 0 && (
                      <div className="mt-1">
                        <div className="flex flex-wrap gap-1">
                          {Object.entries(event.metadata).slice(0, 3).map(([key, value]) => (
                            <Badge key={key} variant="secondary" className="text-xs">
                              {key}: {String(value).slice(0, 20)}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};

export default RealTimeStatusUpdates;
