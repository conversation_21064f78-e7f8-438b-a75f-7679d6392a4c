import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  AlertTriangle, 
  RefreshCw, 
  Bug, 
  Home,
  Copy,
  ExternalLink,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { HStack, VStack } from '@/components/ui/layout';
import { Typography } from '@/components/ui/typography';

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
  showDetails: boolean;
}

interface DashboardErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo, errorId: string) => void;
  level?: 'page' | 'component' | 'widget';
  name?: string;
}

class DashboardErrorBoundary extends Component<DashboardErrorBoundaryProps, ErrorBoundaryState> {
  private retryCount = 0;
  private maxRetries = 3;

  constructor(props: DashboardErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      showDetails: false
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return {
      hasError: true,
      error,
      errorId
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const errorId = this.state.errorId;
    
    // Log error details
    console.error('Dashboard Error Boundary caught an error:', {
      error,
      errorInfo,
      errorId,
      component: this.props.name || 'Unknown',
      level: this.props.level || 'component',
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    });

    // Update state with error info
    this.setState({
      errorInfo
    });

    // Report error to external service
    this.reportError(error, errorInfo, errorId);

    // Call parent error handler
    this.props.onError?.(error, errorInfo, errorId);
  }

  private reportError = async (error: Error, errorInfo: ErrorInfo, errorId: string) => {
    try {
      // Report to telemetry service
      const errorReport = {
        errorId,
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        component: this.props.name || 'Unknown',
        level: this.props.level || 'component',
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        retryCount: this.retryCount
      };

      // Send to error reporting endpoint
      await fetch('/api/errors/report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(errorReport)
      }).catch(() => {
        // Silently fail if error reporting fails
        console.warn('Failed to report error to server');
      });

    } catch (reportingError) {
      console.error('Error reporting failed:', reportingError);
    }
  };

  private handleRetry = () => {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: '',
        showDetails: false
      });
    }
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  private copyErrorDetails = () => {
    const errorDetails = {
      errorId: this.state.errorId,
      message: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
      timestamp: new Date().toISOString(),
      url: window.location.href
    };

    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
      .then(() => {
        // Could show a toast notification here
        console.log('Error details copied to clipboard');
      })
      .catch(() => {
        console.error('Failed to copy error details');
      });
  };

  private toggleDetails = () => {
    this.setState(prev => ({ showDetails: !prev.showDetails }));
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { error, errorInfo, errorId, showDetails } = this.state;
      const { level = 'component', name } = this.props;
      const canRetry = this.retryCount < this.maxRetries;

      // Different UI based on error level
      if (level === 'page') {
        return (
          <div className="min-h-screen bg-background flex items-center justify-center p-4">
            <Card className="w-full max-w-2xl">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <AlertTriangle className="w-8 h-8 text-red-500" />
                  <div>
                    <CardTitle className="text-xl">Something went wrong</CardTitle>
                    <CardDescription>
                      The page encountered an unexpected error and couldn't load properly.
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <Alert className="border-red-500/30 bg-red-900/10">
                  <Bug className="h-4 w-4 text-red-500" />
                  <AlertDescription className="text-red-200">
                    <strong>Error ID:</strong> {errorId}
                    <br />
                    <strong>Component:</strong> {name || 'Page'}
                    <br />
                    <strong>Message:</strong> {error?.message || 'Unknown error'}
                  </AlertDescription>
                </Alert>

                <div className="flex flex-col sm:flex-row gap-3">
                  <Button onClick={this.handleReload} className="flex-1">
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Reload Page
                  </Button>
                  <Button variant="outline" onClick={this.handleGoHome} className="flex-1">
                    <Home className="w-4 h-4 mr-2" />
                    Go Home
                  </Button>
                </div>

                <div className="space-y-3">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={this.toggleDetails}
                    className="w-full justify-between"
                  >
                    <span>Technical Details</span>
                    {showDetails ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                  </Button>

                  {showDetails && (
                    <div className="space-y-3">
                      <div className="p-3 bg-muted rounded-lg">
                        <Typography variant="caption" className="font-mono text-xs break-all">
                          {error?.stack}
                        </Typography>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={this.copyErrorDetails}
                        className="w-full"
                      >
                        <Copy className="w-4 h-4 mr-2" />
                        Copy Error Details
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        );
      }

      // Widget/Component level error UI
      return (
        <Card className="w-full border-red-500/30 bg-red-900/5">
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
              <div className="flex-1 min-w-0">
                <Typography variant="body-sm" className="font-medium text-red-500 mb-1">
                  {level === 'widget' ? 'Widget Error' : 'Component Error'}
                </Typography>
                <Typography variant="caption" className="text-muted-foreground mb-3">
                  {name || 'This component'} encountered an error and couldn't render properly.
                </Typography>
                
                <div className="flex flex-wrap gap-2">
                  {canRetry && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={this.handleRetry}
                      className="text-xs"
                    >
                      <RefreshCw className="w-3 h-3 mr-1" />
                      Retry ({this.maxRetries - this.retryCount} left)
                    </Button>
                  )}
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={this.toggleDetails}
                    className="text-xs"
                  >
                    {showDetails ? 'Hide' : 'Show'} Details
                  </Button>
                </div>

                {showDetails && (
                  <div className="mt-3 p-2 bg-muted rounded text-xs font-mono break-all">
                    <div className="mb-2">
                      <strong>Error ID:</strong> {errorId}
                    </div>
                    <div className="mb-2">
                      <strong>Message:</strong> {error?.message}
                    </div>
                    {error?.stack && (
                      <div className="text-xs opacity-75">
                        {error.stack.split('\n').slice(0, 3).join('\n')}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for easy wrapping
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    name?: string;
    level?: 'page' | 'component' | 'widget';
    fallback?: ReactNode;
    onError?: (error: Error, errorInfo: ErrorInfo, errorId: string) => void;
  }
) => {
  const WrappedComponent = React.forwardRef<any, P>((props, ref) => (
    <DashboardErrorBoundary
      name={options?.name || Component.displayName || Component.name}
      level={options?.level || 'component'}
      fallback={options?.fallback}
      onError={options?.onError}
    >
      <Component ref={ref} {...props} />
    </DashboardErrorBoundary>
  ));

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
};

// Hook for error reporting in functional components
export const useErrorReporting = () => {
  const reportError = React.useCallback((error: Error, context?: string) => {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    console.error('Manual error report:', {
      error,
      context,
      errorId,
      timestamp: new Date().toISOString()
    });

    // Report to server
    fetch('/api/errors/report', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        errorId,
        message: error.message,
        stack: error.stack,
        context,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
      })
    }).catch(() => {
      console.warn('Failed to report error to server');
    });

    return errorId;
  }, []);

  return { reportError };
};

export default DashboardErrorBoundary;
