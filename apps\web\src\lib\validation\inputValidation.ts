import { z } from 'zod';

// Common validation schemas
export const codeInputSchema = z.object({
  code: z
    .string()
    .min(1, 'Code cannot be empty')
    .max(50000, 'Code is too long (max 50,000 characters)')
    .refine(
      (code) => {
        // Check for potentially dangerous patterns
        const dangerousPatterns = [
          /eval\s*\(/,
          /Function\s*\(/,
          /setTimeout\s*\(/,
          /setInterval\s*\(/,
          /document\.write/,
          /innerHTML\s*=/,
          /outerHTML\s*=/,
        ];
        return !dangerousPatterns.some(pattern => pattern.test(code));
      },
      'Code contains potentially unsafe patterns'
    ),
  language: z
    .string()
    .min(1, 'Language is required')
    .refine(
      (lang) => ['javascript', 'typescript', 'python', 'java', 'cpp', 'csharp', 'go', 'rust'].includes(lang),
      'Unsupported language'
    ),
});

export const promptInputSchema = z.object({
  prompt: z
    .string()
    .min(10, 'Prompt must be at least 10 characters')
    .max(2000, 'Prompt is too long (max 2,000 characters)')
    .refine(
      (prompt) => {
        // Check for meaningful content
        const words = prompt.trim().split(/\s+/);
        return words.length >= 3;
      },
      'Prompt must contain at least 3 words'
    ),
});

export const transformationConfigSchema = z.object({
  plannerModel: z.enum(['gpt-4-turbo', 'gpt-4', 'gpt-3.5-turbo', 'claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku']),
  criticModel: z.enum(['gpt-4-turbo', 'gpt-4', 'gpt-3.5-turbo', 'claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku']),
  temperature: z.number().min(0).max(2),
  maxTokens: z.number().min(100).max(8000),
  maxIterations: z.number().min(1).max(20),
  scoreThreshold: z.number().min(0).max(1),
});

export const apiKeySchema = z.object({
  openai: z.string().optional().refine(
    (key) => !key || key.startsWith('sk-'),
    'OpenAI API key must start with "sk-"'
  ),
  anthropic: z.string().optional().refine(
    (key) => !key || key.startsWith('sk-ant-'),
    'Anthropic API key must start with "sk-ant-"'
  ),
  google: z.string().optional(),
});

// Validation result types
export interface ValidationResult<T = any> {
  success: boolean;
  data?: T;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ValidationWarning {
  field: string;
  message: string;
  code: string;
}

// Input validator class
export class InputValidator {
  static validateCode(input: any): ValidationResult {
    const result = codeInputSchema.safeParse(input);
    
    if (result.success) {
      const warnings: ValidationWarning[] = [];
      
      // Check for common issues
      if (input.code.length > 10000) {
        warnings.push({
          field: 'code',
          message: 'Large code files may take longer to process',
          code: 'LARGE_CODE',
        });
      }
      
      // Check for minified code
      if (input.code.split('\n').length < 10 && input.code.length > 1000) {
        warnings.push({
          field: 'code',
          message: 'Code appears to be minified - consider using formatted code for better results',
          code: 'MINIFIED_CODE',
        });
      }
      
      return {
        success: true,
        data: result.data,
        errors: [],
        warnings,
      };
    }
    
    return {
      success: false,
      errors: result.error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code,
      })),
      warnings: [],
    };
  }
  
  static validatePrompt(input: any): ValidationResult {
    const result = promptInputSchema.safeParse(input);
    
    if (result.success) {
      const warnings: ValidationWarning[] = [];
      
      // Check prompt quality
      const prompt = input.prompt.toLowerCase();
      
      if (prompt.length < 50) {
        warnings.push({
          field: 'prompt',
          message: 'Short prompts may produce less specific results',
          code: 'SHORT_PROMPT',
        });
      }
      
      // Check for vague language
      const vagueWords = ['improve', 'better', 'fix', 'optimize'];
      const hasVagueWords = vagueWords.some(word => prompt.includes(word));
      
      if (hasVagueWords && prompt.length < 100) {
        warnings.push({
          field: 'prompt',
          message: 'Consider being more specific about what improvements you want',
          code: 'VAGUE_PROMPT',
        });
      }
      
      return {
        success: true,
        data: result.data,
        errors: [],
        warnings,
      };
    }
    
    return {
      success: false,
      errors: result.error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code,
      })),
      warnings: [],
    };
  }
  
  static validateTransformationConfig(input: any): ValidationResult {
    const result = transformationConfigSchema.safeParse(input);
    
    if (result.success) {
      const warnings: ValidationWarning[] = [];
      
      // Check for same model used for both roles
      if (input.plannerModel === input.criticModel) {
        warnings.push({
          field: 'models',
          message: 'Using different models for planner and critic often produces better results',
          code: 'SAME_MODELS',
        });
      }
      
      // Check temperature settings
      if (input.temperature > 1.5) {
        warnings.push({
          field: 'temperature',
          message: 'High temperature may produce inconsistent results',
          code: 'HIGH_TEMPERATURE',
        });
      }
      
      if (input.temperature < 0.3) {
        warnings.push({
          field: 'temperature',
          message: 'Low temperature may produce repetitive results',
          code: 'LOW_TEMPERATURE',
        });
      }
      
      // Check score threshold
      if (input.scoreThreshold < 0.7) {
        warnings.push({
          field: 'scoreThreshold',
          message: 'Low score threshold may accept poor quality transformations',
          code: 'LOW_THRESHOLD',
        });
      }
      
      return {
        success: true,
        data: result.data,
        errors: [],
        warnings,
      };
    }
    
    return {
      success: false,
      errors: result.error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code,
      })),
      warnings: [],
    };
  }
  
  static validateApiKeys(input: any): ValidationResult {
    const result = apiKeySchema.safeParse(input);
    
    if (result.success) {
      const warnings: ValidationWarning[] = [];
      
      // Check if at least one key is provided
      const hasAnyKey = input.openai || input.anthropic || input.google;
      if (!hasAnyKey) {
        warnings.push({
          field: 'apiKeys',
          message: 'No API keys provided - transformations will not work',
          code: 'NO_API_KEYS',
        });
      }
      
      return {
        success: true,
        data: result.data,
        errors: [],
        warnings,
      };
    }
    
    return {
      success: false,
      errors: result.error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code,
      })),
      warnings: [],
    };
  }
  
  // Comprehensive validation for transformation request
  static validateTransformationRequest(input: any): ValidationResult {
    const codeValidation = this.validateCode({ code: input.code, language: input.language });
    const promptValidation = this.validatePrompt({ prompt: input.prompt });
    const configValidation = this.validateTransformationConfig(input.config || {});
    
    const allErrors = [
      ...codeValidation.errors,
      ...promptValidation.errors,
      ...configValidation.errors,
    ];
    
    const allWarnings = [
      ...codeValidation.warnings,
      ...promptValidation.warnings,
      ...configValidation.warnings,
    ];
    
    return {
      success: allErrors.length === 0,
      data: codeValidation.success && promptValidation.success && configValidation.success ? {
        code: codeValidation.data,
        prompt: promptValidation.data,
        config: configValidation.data,
      } : undefined,
      errors: allErrors,
      warnings: allWarnings,
    };
  }
}

// Utility functions for common validations
export const sanitizeInput = (input: string): string => {
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/data:/gi, '') // Remove data: protocol
    .trim();
};

export const validateFileSize = (size: number, maxSize: number = 1024 * 1024): boolean => {
  return size <= maxSize;
};

export const validateFileType = (filename: string, allowedTypes: string[]): boolean => {
  const extension = filename.split('.').pop()?.toLowerCase();
  return extension ? allowedTypes.includes(extension) : false;
};

export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Rate limiting validation
export class RateLimitValidator {
  private static requests: Map<string, number[]> = new Map();

  static checkRateLimit(
    identifier: string,
    maxRequests: number = 10,
    windowMs: number = 60000
  ): { allowed: boolean; remaining: number; resetTime: number } {
    const now = Date.now();
    const windowStart = now - windowMs;

    // Get existing requests for this identifier
    const requests = this.requests.get(identifier) || [];

    // Filter out old requests
    const recentRequests = requests.filter(time => time > windowStart);

    // Update the map
    this.requests.set(identifier, recentRequests);

    const allowed = recentRequests.length < maxRequests;

    if (allowed) {
      recentRequests.push(now);
      this.requests.set(identifier, recentRequests);
    }

    return {
      allowed,
      remaining: Math.max(0, maxRequests - recentRequests.length),
      resetTime: recentRequests.length > 0 ? recentRequests[0] + windowMs : now + windowMs,
    };
  }

  static clearRateLimit(identifier: string): void {
    this.requests.delete(identifier);
  }
}

// Export validation schemas for reuse
export {
  codeInputSchema,
  promptInputSchema,
  transformationConfigSchema,
  apiKeySchema,
};
