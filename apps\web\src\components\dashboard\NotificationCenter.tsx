import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Bell,
  X,
  CheckCircle,
  AlertTriangle,
  Info,
  XCircle,
  Settings,
  Trash2,
  Check,
  Filter,
  BellOff
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { HStack, VStack } from '@/components/ui/layout';
import { Typography } from '@/components/ui/typography';
import { formatDistanceToNow } from 'date-fns';

interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info' | 'system';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  dismissible: boolean;
  actionUrl?: string;
  actionLabel?: string;
  metadata?: {
    source?: string;
    category?: string;
    [key: string]: any;
  };
}

interface NotificationCenterProps {
  className?: string;
  maxNotifications?: number;
  autoRefresh?: boolean;
}

export const NotificationCenter: React.FC<NotificationCenterProps> = ({
  className,
  maxNotifications = 50,
  autoRefresh = true
}) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [filteredNotifications, setFilteredNotifications] = useState<Notification[]>([]);
  const [selectedFilter, setSelectedFilter] = useState<string>('all');
  const [showOnlyUnread, setShowOnlyUnread] = useState(false);

  // Initialize with some sample notifications
  useEffect(() => {
    const sampleNotifications: Notification[] = [
      {
        id: '1',
        type: 'success',
        priority: 'medium',
        title: 'Transformation Completed',
        message: 'Your code transformation finished successfully with a score of 0.94',
        timestamp: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
        read: false,
        dismissible: true,
        metadata: { source: 'reactor', category: 'transformation' }
      },
      {
        id: '2',
        type: 'warning',
        priority: 'high',
        title: 'Approaching Cost Limit',
        message: 'You have used 85% of your daily budget. Consider reviewing your settings.',
        timestamp: new Date(Date.now() - 600000).toISOString(), // 10 minutes ago
        read: false,
        dismissible: true,
        actionUrl: '/settings',
        actionLabel: 'Review Settings',
        metadata: { source: 'billing', category: 'budget' }
      },
      {
        id: '3',
        type: 'error',
        priority: 'critical',
        title: 'API Rate Limit Exceeded',
        message: 'OpenAI API rate limit exceeded. Requests will be throttled for the next 15 minutes.',
        timestamp: new Date(Date.now() - 900000).toISOString(), // 15 minutes ago
        read: true,
        dismissible: true,
        metadata: { source: 'api', category: 'rate_limit' }
      },
      {
        id: '4',
        type: 'info',
        priority: 'low',
        title: 'System Maintenance Scheduled',
        message: 'Scheduled maintenance will occur tonight from 2:00 AM to 4:00 AM UTC.',
        timestamp: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        read: true,
        dismissible: true,
        metadata: { source: 'system', category: 'maintenance' }
      },
      {
        id: '5',
        type: 'system',
        priority: 'medium',
        title: 'New Feature Available',
        message: 'GitHub PR creation is now available! Automatically create pull requests from your transformations.',
        timestamp: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
        read: false,
        dismissible: true,
        actionUrl: '/dashboard',
        actionLabel: 'Try Now',
        metadata: { source: 'product', category: 'feature' }
      }
    ];

    setNotifications(sampleNotifications);
  }, []);

  // Filter notifications
  useEffect(() => {
    let filtered = notifications;

    // Apply type filter
    if (selectedFilter !== 'all') {
      filtered = filtered.filter(notification => notification.type === selectedFilter);
    }

    // Apply read/unread filter
    if (showOnlyUnread) {
      filtered = filtered.filter(notification => !notification.read);
    }

    // Sort by priority and timestamp
    filtered.sort((a, b) => {
      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      
      return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
    });

    setFilteredNotifications(filtered.slice(0, maxNotifications));
  }, [notifications, selectedFilter, showOnlyUnread, maxNotifications]);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error': return <XCircle className="w-4 h-4 text-red-500" />;
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'info': return <Info className="w-4 h-4 text-blue-500" />;
      case 'system': return <Settings className="w-4 h-4 text-purple-500" />;
      default: return <Bell className="w-4 h-4 text-gray-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'border-l-red-500';
      case 'high': return 'border-l-orange-500';
      case 'medium': return 'border-l-blue-500';
      case 'low': return 'border-l-gray-500';
      default: return 'border-l-gray-500';
    }
  };

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, read: true }
          : notification
      )
    );
  };

  const dismissNotification = (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    );
  };

  const clearAll = () => {
    setNotifications(prev => prev.filter(notification => !notification.dismissible));
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  const filterOptions = [
    { value: 'all', label: 'All' },
    { value: 'success', label: 'Success' },
    { value: 'error', label: 'Errors' },
    { value: 'warning', label: 'Warnings' },
    { value: 'info', label: 'Info' },
    { value: 'system', label: 'System' }
  ];

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="relative">
              <Bell className="w-5 h-5 text-primary" />
              {unreadCount > 0 && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center">
                  <span className="text-xs text-white font-bold">
                    {unreadCount > 9 ? '9+' : unreadCount}
                  </span>
                </div>
              )}
            </div>
            <CardTitle className="text-lg">Notifications</CardTitle>
            {unreadCount > 0 && (
              <Badge variant="destructive" className="text-xs">
                {unreadCount} unread
              </Badge>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={markAllAsRead}
              disabled={unreadCount === 0}
              className="text-xs"
            >
              <Check className="w-4 h-4 mr-1" />
              Mark All Read
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAll}
              className="text-xs"
            >
              <Trash2 className="w-4 h-4 mr-1" />
              Clear All
            </Button>
          </div>
        </div>
        <CardDescription>
          System alerts, updates, and important messages
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Filter Controls */}
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 text-muted-foreground" />
            <select
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value)}
              className="px-3 py-1 text-sm border border-border rounded-md bg-background"
            >
              {filterOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
          
          <div className="flex items-center space-x-2">
            <BellOff className="w-4 h-4 text-muted-foreground" />
            <label className="flex items-center space-x-2 text-sm">
              <input
                type="checkbox"
                checked={showOnlyUnread}
                onChange={(e) => setShowOnlyUnread(e.target.checked)}
                className="rounded border-border"
              />
              <span>Unread only</span>
            </label>
          </div>
        </div>

        {/* Notifications List */}
        <ScrollArea className="h-96">
          <div className="space-y-3">
            {filteredNotifications.length === 0 ? (
              <div className="text-center py-8">
                <Bell className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                <Typography variant="body-sm" className="text-muted-foreground">
                  {showOnlyUnread ? 'No unread notifications' : 'No notifications'}
                </Typography>
              </div>
            ) : (
              filteredNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={cn(
                    "p-3 rounded-lg border-l-4 bg-card/50 transition-all duration-200",
                    getPriorityColor(notification.priority),
                    !notification.read && "bg-primary/5 border-primary/20",
                    "hover:bg-card/80"
                  )}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      <div className="flex-shrink-0 mt-0.5">
                        {getNotificationIcon(notification.type)}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <Typography variant="body-sm" className="font-medium">
                            {notification.title}
                          </Typography>
                          {!notification.read && (
                            <div className="w-2 h-2 bg-primary rounded-full" />
                          )}
                          <Badge variant="outline" className="text-xs">
                            {notification.priority}
                          </Badge>
                        </div>
                        
                        <Typography variant="caption" className="text-muted-foreground line-clamp-2">
                          {notification.message}
                        </Typography>
                        
                        <div className="flex items-center justify-between mt-2">
                          <Typography variant="caption" className="text-muted-foreground">
                            {formatDistanceToNow(new Date(notification.timestamp), { addSuffix: true })}
                          </Typography>
                          
                          <div className="flex items-center space-x-2">
                            {notification.actionUrl && notification.actionLabel && (
                              <Button variant="outline" size="sm" className="text-xs h-6">
                                {notification.actionLabel}
                              </Button>
                            )}
                            
                            {!notification.read && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => markAsRead(notification.id)}
                                className="text-xs h-6"
                              >
                                Mark Read
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {notification.dismissible && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => dismissNotification(notification.id)}
                        className="h-6 w-6 p-0 ml-2"
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>

        {/* Footer */}
        <div className="pt-2 border-t border-border">
          <Typography variant="caption" className="text-muted-foreground">
            Showing {filteredNotifications.length} of {notifications.length} notifications
          </Typography>
        </div>
      </CardContent>
    </Card>
  );
};

export default NotificationCenter;
