import React from 'react';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Loader2, 
  Activity, 
  Zap,
  Clock,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { HStack, VStack } from '@/components/ui/layout';
import { Typography } from '@/components/ui/typography';

// Skeleton Components for Dashboard Widgets
export const PerformanceMetricsSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <Card className={cn("w-full", className)}>
    <CardHeader className="pb-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Skeleton className="w-5 h-5 rounded" />
          <Skeleton className="w-32 h-5" />
        </div>
        <Skeleton className="w-8 h-8 rounded" />
      </div>
      <Skeleton className="w-48 h-4" />
    </CardHeader>
    <CardContent className="space-y-6">
      {/* Status indicators */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="space-y-2">
            <Skeleton className="w-20 h-3" />
            <Skeleton className="w-16 h-6" />
          </div>
        ))}
      </div>
      
      {/* Progress bars */}
      <div className="space-y-4">
        <Skeleton className="w-24 h-4" />
        <Skeleton className="w-full h-2 rounded-full" />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="space-y-2">
              <Skeleton className="w-16 h-3" />
              <Skeleton className="w-12 h-5" />
              <Skeleton className="w-8 h-3" />
            </div>
          ))}
        </div>
      </div>
      
      <Skeleton className="w-32 h-3" />
    </CardContent>
  </Card>
);

export const CostTrackingSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <Card className={cn("w-full", className)}>
    <CardHeader className="pb-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Skeleton className="w-5 h-5 rounded" />
          <Skeleton className="w-28 h-5" />
        </div>
        <Skeleton className="w-8 h-8 rounded" />
      </div>
      <Skeleton className="w-52 h-4" />
    </CardHeader>
    <CardContent className="space-y-6">
      {/* Cost overview */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="space-y-2">
            <Skeleton className="w-20 h-3" />
            <Skeleton className="w-16 h-6" />
            <Skeleton className="w-24 h-3" />
          </div>
        ))}
      </div>
      
      {/* Budget progress */}
      <div className="space-y-4">
        <Skeleton className="w-20 h-4" />
        <div className="space-y-3">
          {Array.from({ length: 2 }).map((_, i) => (
            <div key={i} className="space-y-2">
              <div className="flex justify-between">
                <Skeleton className="w-16 h-3" />
                <Skeleton className="w-10 h-4 rounded-full" />
              </div>
              <Skeleton className="w-full h-2 rounded-full" />
            </div>
          ))}
        </div>
      </div>
      
      <Skeleton className="w-40 h-3" />
    </CardContent>
  </Card>
);

export const ActivityTimelineSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <Card className={cn("w-full", className)}>
    <CardHeader className="pb-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Skeleton className="w-5 h-5 rounded" />
          <Skeleton className="w-32 h-5" />
        </div>
        <Skeleton className="w-8 h-8 rounded" />
      </div>
      <Skeleton className="w-56 h-4" />
    </CardHeader>
    <CardContent className="space-y-4">
      {/* Search and filter */}
      <div className="flex gap-3">
        <Skeleton className="flex-1 h-9 rounded" />
        <Skeleton className="w-24 h-9 rounded" />
      </div>
      
      {/* Timeline items */}
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="flex items-start space-x-3">
            <Skeleton className="w-5 h-5 rounded-full flex-shrink-0 mt-0.5" />
            <div className="flex-1 space-y-2">
              <div className="flex items-center justify-between">
                <Skeleton className="w-32 h-4" />
                <Skeleton className="w-16 h-3" />
              </div>
              <Skeleton className="w-full h-3" />
              <div className="flex gap-2">
                <Skeleton className="w-12 h-4 rounded-full" />
                <Skeleton className="w-16 h-4 rounded-full" />
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <Skeleton className="w-48 h-3" />
    </CardContent>
  </Card>
);

// Enhanced Loading Spinner with Context
interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  message?: string;
  progress?: number;
  className?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  message,
  progress,
  className
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  return (
    <div className={cn("flex flex-col items-center justify-center space-y-3", className)}>
      <Loader2 className={cn("animate-spin text-primary", sizeClasses[size])} />
      {message && (
        <Typography variant="body-sm" className="text-muted-foreground text-center">
          {message}
        </Typography>
      )}
      {progress !== undefined && (
        <div className="w-48 space-y-2">
          <Progress value={progress} className="h-1" />
          <Typography variant="caption" className="text-muted-foreground text-center">
            {Math.round(progress)}% complete
          </Typography>
        </div>
      )}
    </div>
  );
};

// Transformation Progress Indicator
interface TransformationProgressProps {
  stage: 'initializing' | 'planning' | 'critiquing' | 'iterating' | 'finalizing' | 'complete' | 'error';
  iteration?: number;
  maxIterations?: number;
  score?: number;
  message?: string;
  className?: string;
}

export const TransformationProgress: React.FC<TransformationProgressProps> = ({
  stage,
  iteration = 0,
  maxIterations = 10,
  score,
  message,
  className
}) => {
  const getStageIcon = () => {
    switch (stage) {
      case 'initializing':
        return <Loader2 className="w-5 h-5 animate-spin text-blue-500" />;
      case 'planning':
        return <Zap className="w-5 h-5 text-yellow-500" />;
      case 'critiquing':
        return <Activity className="w-5 h-5 text-purple-500" />;
      case 'iterating':
        return <Loader2 className="w-5 h-5 animate-spin text-orange-500" />;
      case 'finalizing':
        return <Clock className="w-5 h-5 text-blue-500" />;
      case 'complete':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
      default:
        return <Loader2 className="w-5 h-5 animate-spin text-gray-500" />;
    }
  };

  const getStageLabel = () => {
    switch (stage) {
      case 'initializing': return 'Initializing...';
      case 'planning': return 'Planning changes...';
      case 'critiquing': return 'Evaluating quality...';
      case 'iterating': return `Iteration ${iteration}/${maxIterations}`;
      case 'finalizing': return 'Finalizing...';
      case 'complete': return 'Complete!';
      case 'error': return 'Error occurred';
      default: return 'Processing...';
    }
  };

  const getProgress = () => {
    switch (stage) {
      case 'initializing': return 10;
      case 'planning': return 25;
      case 'critiquing': return 40;
      case 'iterating': return 40 + (iteration / maxIterations) * 40;
      case 'finalizing': return 90;
      case 'complete': return 100;
      case 'error': return 0;
      default: return 0;
    }
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardContent className="p-6">
        <VStack spacing="md">
          <HStack spacing="sm" align="center">
            {getStageIcon()}
            <Typography variant="h6" className="font-medium">
              {getStageLabel()}
            </Typography>
          </HStack>

          {message && (
            <Typography variant="body-sm" className="text-muted-foreground">
              {message}
            </Typography>
          )}

          <div className="w-full space-y-2">
            <Progress value={getProgress()} className="h-2" />
            <div className="flex justify-between items-center">
              <Typography variant="caption" className="text-muted-foreground">
                {Math.round(getProgress())}% complete
              </Typography>
              {score !== undefined && (
                <Typography variant="caption" className="text-muted-foreground">
                  Score: {score.toFixed(2)}
                </Typography>
              )}
            </div>
          </div>

          {stage === 'iterating' && (
            <div className="flex justify-center">
              <Typography variant="caption" className="text-muted-foreground">
                Iteration {iteration} of {maxIterations}
              </Typography>
            </div>
          )}
        </VStack>
      </CardContent>
    </Card>
  );
};

// Inline Loading State for Buttons
interface ButtonLoadingProps {
  isLoading: boolean;
  children: React.ReactNode;
  loadingText?: string;
}

export const ButtonLoading: React.FC<ButtonLoadingProps> = ({
  isLoading,
  children,
  loadingText
}) => {
  if (isLoading) {
    return (
      <>
        <Loader2 className="w-4 h-4 animate-spin mr-2" />
        {loadingText || 'Loading...'}
      </>
    );
  }
  return <>{children}</>;
};

// Data Loading Placeholder
interface DataPlaceholderProps {
  type: 'table' | 'chart' | 'list' | 'card';
  rows?: number;
  className?: string;
}

export const DataPlaceholder: React.FC<DataPlaceholderProps> = ({
  type,
  rows = 5,
  className
}) => {
  if (type === 'table') {
    return (
      <div className={cn("space-y-3", className)}>
        {/* Table header */}
        <div className="flex space-x-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Skeleton key={i} className="h-4 flex-1" />
          ))}
        </div>
        {/* Table rows */}
        {Array.from({ length: rows }).map((_, i) => (
          <div key={i} className="flex space-x-4">
            {Array.from({ length: 4 }).map((_, j) => (
              <Skeleton key={j} className="h-4 flex-1" />
            ))}
          </div>
        ))}
      </div>
    );
  }

  if (type === 'chart') {
    return (
      <div className={cn("space-y-4", className)}>
        <Skeleton className="w-32 h-6" />
        <div className="flex items-end space-x-2 h-32">
          {Array.from({ length: 8 }).map((_, i) => (
            <Skeleton 
              key={i} 
              className="flex-1 rounded-t" 
              style={{ height: `${Math.random() * 80 + 20}%` }}
            />
          ))}
        </div>
      </div>
    );
  }

  if (type === 'list') {
    return (
      <div className={cn("space-y-3", className)}>
        {Array.from({ length: rows }).map((_, i) => (
          <div key={i} className="flex items-center space-x-3">
            <Skeleton className="w-8 h-8 rounded-full" />
            <div className="flex-1 space-y-1">
              <Skeleton className="w-3/4 h-4" />
              <Skeleton className="w-1/2 h-3" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Default card type
  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <Skeleton className="w-32 h-6" />
        <Skeleton className="w-48 h-4" />
      </CardHeader>
      <CardContent className="space-y-4">
        {Array.from({ length: rows }).map((_, i) => (
          <Skeleton key={i} className="w-full h-4" />
        ))}
      </CardContent>
    </Card>
  );
};

export default {
  PerformanceMetricsSkeleton,
  CostTrackingSkeleton,
  ActivityTimelineSkeleton,
  LoadingSpinner,
  TransformationProgress,
  ButtonLoading,
  DataPlaceholder
};
