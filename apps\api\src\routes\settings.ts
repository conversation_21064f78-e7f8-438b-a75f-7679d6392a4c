import { Router } from 'express';
import { z } from 'zod';
import { supabaseClient } from '../services/supabase.js';
import { loggingService } from '../services/loggingService.js';

const router = Router();

// Request validation schemas
const updateSettingsSchema = z.object({
  planner_model: z.string().optional(),
  critic_model: z.string().optional(),
  temperature: z.number().min(0).max(2).optional(),
  max_tokens: z.number().min(100).max(8000).optional(),
  max_iterations: z.number().min(1).max(20).optional(),
  score_threshold: z.number().min(0.5).max(1.0).optional(),
  cost_limit: z.number().min(0.5).max(10.0).optional(),
  timeout_seconds: z.number().min(60).max(600).optional(),
  provider_configs: z.object({
    planner: z.object({
      type: z.enum(['openai', 'anthropic', 'google']),
      model: z.string(),
      temperature: z.number().min(0).max(2),
      maxTokens: z.number().min(100).max(8000)
    }).optional(),
    critic: z.object({
      type: z.enum(['openai', 'anthropic', 'google']),
      model: z.string(),
      temperature: z.number().min(0).max(2),
      maxTokens: z.number().min(100).max(8000)
    }).optional(),
    fallback_providers: z.array(z.string()).optional()
  }).optional(),
  cost_guard_config: z.object({
    enabled: z.boolean(),
    maxCostPerLoop: z.number().min(0.1).max(10.0),
    alertThresholds: z.object({
      nearLimitWarning: z.number().min(0.1).max(1.0),
      costPerHour: z.number().min(1.0).max(100.0)
    })
  }).optional()
});

// Simple auth middleware (replace with proper auth in production)
const requireAuth = (req: any, res: any, next: any) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
  }
  
  // Extract user ID from token (simplified - use proper JWT validation in production)
  req.userId = 'user-123'; // Placeholder
  next();
};

// GET /api/settings - Get user settings
router.get('/settings', requireAuth, async (req, res) => {
  try {
    const userId = req.userId;
    
    const { data: settings, error } = await supabaseClient
      .from('settings')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      throw error;
    }

    // Return default settings if none exist
    const defaultSettings = {
      planner_model: 'gpt-4o',
      critic_model: 'gpt-4o',
      temperature: 0.7,
      max_tokens: 2000,
      max_iterations: 10,
      score_threshold: 0.95,
      cost_limit: 3.0,
      timeout_seconds: 180,
      provider_configs: {
        planner: {
          type: 'openai',
          model: 'gpt-4o',
          temperature: 0.7,
          maxTokens: 2000
        },
        critic: {
          type: 'openai',
          model: 'gpt-4o',
          temperature: 0.3,
          maxTokens: 1500
        },
        fallback_providers: []
      },
      cost_guard_config: {
        enabled: true,
        maxCostPerLoop: 3.0,
        alertThresholds: {
          nearLimitWarning: 0.8,
          costPerHour: 10.0
        }
      }
    };

    res.json({
      success: true,
      data: settings || defaultSettings
    });

  } catch (error) {
    console.error('Get settings error:', error);
    await loggingService.log('error', 'Failed to get user settings', {
      error: error.message,
      userId: req.userId
    });

    res.status(500).json({
      success: false,
      error: 'Failed to get settings'
    });
  }
});

// PUT /api/settings - Update user settings
router.put('/settings', requireAuth, async (req, res) => {
  try {
    const userId = req.userId;
    const validatedSettings = updateSettingsSchema.parse(req.body);

    // Check if settings exist
    const { data: existingSettings } = await supabaseClient
      .from('settings')
      .select('id')
      .eq('user_id', userId)
      .single();

    let result;
    if (existingSettings) {
      // Update existing settings
      result = await supabaseClient
        .from('settings')
        .update({
          ...validatedSettings,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();
    } else {
      // Create new settings
      result = await supabaseClient
        .from('settings')
        .insert({
          user_id: userId,
          ...validatedSettings,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();
    }

    if (result.error) {
      throw result.error;
    }

    await loggingService.log('info', 'User settings updated', {
      userId,
      settingsUpdated: Object.keys(validatedSettings)
    });

    res.json({
      success: true,
      data: result.data,
      message: 'Settings updated successfully'
    });

  } catch (error) {
    console.error('Update settings error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    await loggingService.log('error', 'Failed to update user settings', {
      error: error.message,
      userId: req.userId
    });

    res.status(500).json({
      success: false,
      error: 'Failed to update settings'
    });
  }
});

// DELETE /api/settings - Reset settings to defaults
router.delete('/settings', requireAuth, async (req, res) => {
  try {
    const userId = req.userId;

    const { error } = await supabaseClient
      .from('settings')
      .delete()
      .eq('user_id', userId);

    if (error) {
      throw error;
    }

    await loggingService.log('info', 'User settings reset to defaults', {
      userId
    });

    res.json({
      success: true,
      message: 'Settings reset to defaults'
    });

  } catch (error) {
    console.error('Reset settings error:', error);
    await loggingService.log('error', 'Failed to reset user settings', {
      error: error.message,
      userId: req.userId
    });

    res.status(500).json({
      success: false,
      error: 'Failed to reset settings'
    });
  }
});

// GET /api/settings/health - Settings service health check
router.get('/settings/health', async (req, res) => {
  try {
    // Test database connection
    const { error } = await supabaseClient
      .from('settings')
      .select('count')
      .limit(1);

    const isHealthy = !error;

    res.status(isHealthy ? 200 : 503).json({
      success: isHealthy,
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      checks: {
        database_connection: isHealthy,
        settings_table_accessible: isHealthy
      }
    });

  } catch (error) {
    res.status(503).json({
      success: false,
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

export { router as settingsRouter };
