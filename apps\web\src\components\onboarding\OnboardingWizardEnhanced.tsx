import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle, 
  Circle, 
  ArrowRight, 
  ArrowLeft,
  Key,
  Github,
  Settings,
  Zap,
  AlertTriangle,
  Info,
  ExternalLink
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  required: boolean;
  icon: React.ReactNode;
}

interface OnboardingWizardEnhancedProps {
  className?: string;
  onComplete?: () => void;
}

export const OnboardingWizardEnhanced: React.FC<OnboardingWizardEnhancedProps> = ({ 
  className, 
  onComplete 
}) => {
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [apiKeys, setApiKeys] = useState({
    openai: '',
    anthropic: '',
    github: ''
  });
  const [githubConnected, setGithubConnected] = useState(false);
  const [onboardingProgress, setOnboardingProgress] = useState<any>(null);

  const steps: OnboardingStep[] = [
    {
      id: 'welcome',
      title: 'Welcome to Metamorphic Reactor',
      description: 'Let\'s get you set up with the dual-agent code transformation system',
      completed: false,
      required: true,
      icon: <Zap className="w-5 h-5" />
    },
    {
      id: 'api-keys',
      title: 'Configure AI Providers',
      description: 'Add your API keys for OpenAI and Anthropic to enable transformations',
      completed: false,
      required: true,
      icon: <Key className="w-5 h-5" />
    },
    {
      id: 'github',
      title: 'Connect GitHub',
      description: 'Link your GitHub account to enable automatic PR creation',
      completed: false,
      required: false,
      icon: <Github className="w-5 h-5" />
    },
    {
      id: 'preferences',
      title: 'Set Preferences',
      description: 'Configure your default models and transformation settings',
      completed: false,
      required: false,
      icon: <Settings className="w-5 h-5" />
    }
  ];

  const [stepsState, setStepsState] = useState(steps);

  // Fetch onboarding progress
  useEffect(() => {
    fetchOnboardingProgress();
  }, []);

  const fetchOnboardingProgress = async () => {
    try {
      const response = await fetch('/api/onboarding/progress');
      if (response.ok) {
        const data = await response.json();
        setOnboardingProgress(data);
        
        // Update steps based on progress
        setStepsState(prev => prev.map(step => ({
          ...step,
          completed: data.completedSteps?.includes(step.id) || false
        })));
      }
    } catch (error) {
      console.error('Failed to fetch onboarding progress:', error);
    }
  };

  const updateStepCompletion = async (stepId: string, completed: boolean) => {
    try {
      await fetch('/api/onboarding/progress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ stepId, completed })
      });
      
      setStepsState(prev => prev.map(step => 
        step.id === stepId ? { ...step, completed } : step
      ));
    } catch (error) {
      console.error('Failed to update step completion:', error);
    }
  };

  const handleApiKeySubmit = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/onboarding/api-keys', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(apiKeys)
      });

      if (response.ok) {
        await updateStepCompletion('api-keys', true);
        toast({
          title: "API Keys Saved",
          description: "Your AI provider keys have been securely stored.",
        });
        nextStep();
      } else {
        throw new Error('Failed to save API keys');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save API keys. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGithubConnect = async () => {
    try {
      // Redirect to GitHub OAuth
      window.location.href = '/api/auth/github';
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to connect to GitHub. Please try again.",
        variant: "destructive",
      });
    }
  };

  const nextStep = () => {
    if (currentStep < stepsState.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const skipOnboarding = async () => {
    try {
      await fetch('/api/onboarding/skip', { method: 'POST' });
      onComplete?.();
    } catch (error) {
      console.error('Failed to skip onboarding:', error);
    }
  };

  const completeOnboarding = async () => {
    try {
      await fetch('/api/onboarding/complete', { method: 'POST' });
      toast({
        title: "Setup Complete!",
        description: "Welcome to Metamorphic Reactor. You're ready to start transforming code!",
      });
      onComplete?.();
    } catch (error) {
      console.error('Failed to complete onboarding:', error);
    }
  };

  const progress = ((currentStep + 1) / stepsState.length) * 100;
  const currentStepData = stepsState[currentStep];

  const renderStepContent = () => {
    switch (currentStepData?.id) {
      case 'welcome':
        return (
          <div className="space-y-4">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto">
                <Zap className="w-8 h-8 text-primary" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">Welcome to Metamorphic Reactor</h3>
                <p className="text-muted-foreground mt-2">
                  The dual-agent AI system that transforms and improves your code automatically.
                </p>
              </div>
            </div>
            
            <Alert>
              <Info className="w-4 h-4" />
              <AlertDescription>
                This setup wizard will help you configure the system for optimal performance. 
                You can skip any optional steps and configure them later.
              </AlertDescription>
            </Alert>

            <div className="space-y-3">
              <h4 className="font-medium">What you'll set up:</h4>
              <ul className="space-y-2">
                <li className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">AI provider API keys (OpenAI, Anthropic)</span>
                </li>
                <li className="flex items-center space-x-2">
                  <Circle className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm">GitHub integration for PR creation</span>
                </li>
                <li className="flex items-center space-x-2">
                  <Circle className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm">Default preferences and settings</span>
                </li>
              </ul>
            </div>
          </div>
        );

      case 'api-keys':
        return (
          <div className="space-y-4">
            <div>
              <h3 className="text-xl font-semibold">Configure AI Providers</h3>
              <p className="text-muted-foreground mt-2">
                Add your API keys to enable the dual-agent transformation system.
              </p>
            </div>

            <Alert>
              <Key className="w-4 h-4" />
              <AlertDescription>
                Your API keys are encrypted and stored securely. They're only used for making AI requests.
              </AlertDescription>
            </Alert>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="openai-key">OpenAI API Key *</Label>
                <Input
                  id="openai-key"
                  type="password"
                  placeholder="sk-..."
                  value={apiKeys.openai}
                  onChange={(e) => setApiKeys(prev => ({ ...prev, openai: e.target.value }))}
                />
                <p className="text-xs text-muted-foreground">
                  Used for the Planner agent. Get your key from{' '}
                  <a href="https://platform.openai.com/api-keys" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">
                    OpenAI Platform <ExternalLink className="w-3 h-3 inline" />
                  </a>
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="anthropic-key">Anthropic API Key *</Label>
                <Input
                  id="anthropic-key"
                  type="password"
                  placeholder="sk-ant-..."
                  value={apiKeys.anthropic}
                  onChange={(e) => setApiKeys(prev => ({ ...prev, anthropic: e.target.value }))}
                />
                <p className="text-xs text-muted-foreground">
                  Used for the Critic agent. Get your key from{' '}
                  <a href="https://console.anthropic.com/" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">
                    Anthropic Console <ExternalLink className="w-3 h-3 inline" />
                  </a>
                </p>
              </div>
            </div>
          </div>
        );

      case 'github':
        return (
          <div className="space-y-4">
            <div>
              <h3 className="text-xl font-semibold">Connect GitHub</h3>
              <p className="text-muted-foreground mt-2">
                Link your GitHub account to enable automatic pull request creation.
              </p>
            </div>

            <Alert>
              <Github className="w-4 h-4" />
              <AlertDescription>
                This step is optional. You can connect GitHub later in settings if you prefer.
              </AlertDescription>
            </Alert>

            <div className="text-center space-y-4">
              {githubConnected ? (
                <div className="space-y-2">
                  <CheckCircle className="w-12 h-12 text-green-500 mx-auto" />
                  <p className="font-medium">GitHub Connected!</p>
                  <p className="text-sm text-muted-foreground">
                    You can now create pull requests automatically from transformations.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  <Github className="w-12 h-12 text-muted-foreground mx-auto" />
                  <Button onClick={handleGithubConnect} className="w-full">
                    <Github className="w-4 h-4 mr-2" />
                    Connect GitHub Account
                  </Button>
                  <p className="text-xs text-muted-foreground">
                    We'll redirect you to GitHub to authorize the connection.
                  </p>
                </div>
              )}
            </div>
          </div>
        );

      case 'preferences':
        return (
          <div className="space-y-4">
            <div>
              <h3 className="text-xl font-semibold">Set Your Preferences</h3>
              <p className="text-muted-foreground mt-2">
                Configure default settings for your transformations.
              </p>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Default Planner Model</Label>
                  <select className="w-full p-2 border rounded-md">
                    <option>GPT-4 Turbo</option>
                    <option>GPT-4</option>
                    <option>GPT-3.5 Turbo</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <Label>Default Critic Model</Label>
                  <select className="w-full p-2 border rounded-md">
                    <option>Claude 3 Sonnet</option>
                    <option>Claude 3 Haiku</option>
                    <option>Claude 2</option>
                  </select>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Max Iterations</Label>
                <Input type="number" defaultValue="10" min="1" max="50" />
                <p className="text-xs text-muted-foreground">
                  Maximum number of improvement iterations per transformation.
                </p>
              </div>

              <div className="space-y-2">
                <Label>Quality Threshold</Label>
                <Input type="number" defaultValue="0.95" min="0.1" max="1.0" step="0.05" />
                <p className="text-xs text-muted-foreground">
                  Minimum quality score to accept a transformation (0.1 - 1.0).
                </p>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Card className={cn("w-full max-w-2xl mx-auto", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <span>Setup Wizard</span>
              <Badge variant="secondary">
                Step {currentStep + 1} of {stepsState.length}
              </Badge>
            </CardTitle>
            <CardDescription>
              {currentStepData?.description}
            </CardDescription>
          </div>
          <Button variant="ghost" size="sm" onClick={skipOnboarding}>
            Skip Setup
          </Button>
        </div>
        <Progress value={progress} className="mt-4" />
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Step Content */}
          {renderStepContent()}

          {/* Navigation */}
          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 0}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Previous
            </Button>

            <div className="space-x-2">
              {currentStep === stepsState.length - 1 ? (
                <Button onClick={completeOnboarding}>
                  Complete Setup
                  <CheckCircle className="w-4 h-4 ml-2" />
                </Button>
              ) : currentStepData?.id === 'api-keys' ? (
                <Button 
                  onClick={handleApiKeySubmit}
                  disabled={!apiKeys.openai || !apiKeys.anthropic || isLoading}
                >
                  {isLoading ? 'Saving...' : 'Save & Continue'}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              ) : (
                <Button onClick={nextStep}>
                  {currentStepData?.required ? 'Continue' : 'Skip'}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default OnboardingWizardEnhanced;
