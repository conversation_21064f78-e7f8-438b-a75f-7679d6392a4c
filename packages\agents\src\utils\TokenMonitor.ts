import { ProviderType, TokenUsage } from '../types.js';

/**
 * Token usage event
 */
export interface TokenEvent {
  id: string;
  provider: ProviderType;
  model: string;
  operation: string;
  usage: TokenUsage;
  requestId?: string;
  sessionId?: string;
  metadata?: Record<string, any>;
  timestamp: Date;
}

/**
 * Token usage aggregation
 */
export interface TokenAggregation {
  totalInputTokens: number;
  totalOutputTokens: number;
  totalTokens: number;
  totalCost: number;
  requestCount: number;
  averageInputTokens: number;
  averageOutputTokens: number;
  averageCost: number;
  timeRange: {
    start: Date;
    end: Date;
  };
}

/**
 * Token usage trends
 */
export interface TokenTrends {
  hourlyUsage: Array<{
    hour: string;
    tokens: number;
    cost: number;
    requests: number;
  }>;
  dailyUsage: Array<{
    date: string;
    tokens: number;
    cost: number;
    requests: number;
  }>;
  providerComparison: Array<{
    provider: ProviderType;
    tokens: number;
    cost: number;
    efficiency: number; // tokens per dollar
  }>;
}

/**
 * Token monitoring configuration
 */
export interface TokenMonitorConfig {
  enabled: boolean;
  maxEvents: number;
  aggregationInterval: number; // milliseconds
  alertThresholds: {
    tokensPerHour?: number;
    costPerHour?: number;
    requestsPerMinute?: number;
  };
  logLevel: 'none' | 'basic' | 'detailed';
}

/**
 * Token usage monitor with detailed tracking and analytics
 */
export class TokenMonitor {
  private config: TokenMonitorConfig;
  private events: TokenEvent[] = [];
  private aggregations: Map<string, TokenAggregation> = new Map();
  private eventIdCounter: number = 0;
  private alertCallbacks: Array<(alert: TokenAlert) => void> = [];

  private static readonly DEFAULT_CONFIG: TokenMonitorConfig = {
    enabled: true,
    maxEvents: 10000,
    aggregationInterval: 3600000, // 1 hour
    alertThresholds: {
      tokensPerHour: 1000000, // 1M tokens per hour
      costPerHour: 10.0, // $10 per hour
      requestsPerMinute: 100, // 100 requests per minute
    },
    logLevel: 'basic',
  };

  constructor(config: Partial<TokenMonitorConfig> = {}) {
    this.config = { ...TokenMonitor.DEFAULT_CONFIG, ...config };
  }

  /**
   * Record token usage event
   */
  recordUsage(
    provider: ProviderType,
    model: string,
    operation: string,
    usage: TokenUsage,
    requestId?: string,
    sessionId?: string,
    metadata?: Record<string, any>
  ): TokenEvent {
    if (!this.config.enabled) {
      return this.createDummyEvent();
    }

    const event: TokenEvent = {
      id: this.generateEventId(),
      provider,
      model,
      operation,
      usage,
      requestId,
      sessionId,
      metadata,
      timestamp: new Date(),
    };

    this.events.push(event);
    this.trimEvents();
    this.updateAggregations(event);
    this.checkAlerts(event);
    this.logEvent(event);

    return event;
  }

  /**
   * Get token usage statistics
   */
  getStats(filter?: {
    provider?: ProviderType;
    model?: string;
    operation?: string;
    since?: Date;
    until?: Date;
  }): TokenAggregation {
    const filteredEvents = this.filterEvents(filter);
    
    if (filteredEvents.length === 0) {
      return this.createEmptyAggregation();
    }

    const totalInputTokens = filteredEvents.reduce((sum, e) => sum + e.usage.inputTokens, 0);
    const totalOutputTokens = filteredEvents.reduce((sum, e) => sum + e.usage.outputTokens, 0);
    const totalTokens = filteredEvents.reduce((sum, e) => sum + e.usage.totalTokens, 0);
    const totalCost = filteredEvents.reduce((sum, e) => sum + e.usage.cost, 0);
    const requestCount = filteredEvents.length;

    const timestamps = filteredEvents.map(e => e.timestamp);
    const start = new Date(Math.min(...timestamps.map(t => t.getTime())));
    const end = new Date(Math.max(...timestamps.map(t => t.getTime())));

    return {
      totalInputTokens,
      totalOutputTokens,
      totalTokens,
      totalCost,
      requestCount,
      averageInputTokens: requestCount > 0 ? totalInputTokens / requestCount : 0,
      averageOutputTokens: requestCount > 0 ? totalOutputTokens / requestCount : 0,
      averageCost: requestCount > 0 ? totalCost / requestCount : 0,
      timeRange: { start, end },
    };
  }

  /**
   * Get token usage by provider
   */
  getUsageByProvider(): Map<ProviderType, TokenAggregation> {
    const result = new Map<ProviderType, TokenAggregation>();
    
    const providers = [...new Set(this.events.map(e => e.provider))];
    
    for (const provider of providers) {
      const stats = this.getStats({ provider });
      result.set(provider, stats);
    }

    return result;
  }

  /**
   * Get token usage by model
   */
  getUsageByModel(): Map<string, TokenAggregation> {
    const result = new Map<string, TokenAggregation>();
    
    const models = [...new Set(this.events.map(e => e.model))];
    
    for (const model of models) {
      const stats = this.getStats({ model });
      result.set(model, stats);
    }

    return result;
  }

  /**
   * Get token usage trends
   */
  getTrends(days: number = 7): TokenTrends {
    const since = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    const recentEvents = this.filterEvents({ since });

    // Hourly usage
    const hourlyUsage = this.aggregateByHour(recentEvents);
    
    // Daily usage
    const dailyUsage = this.aggregateByDay(recentEvents);
    
    // Provider comparison
    const providerComparison = this.compareProviders(recentEvents);

    return {
      hourlyUsage,
      dailyUsage,
      providerComparison,
    };
  }

  /**
   * Get recent events
   */
  getRecentEvents(limit: number = 100): TokenEvent[] {
    return this.events.slice(-limit);
  }

  /**
   * Get events by session
   */
  getEventsBySession(sessionId: string): TokenEvent[] {
    return this.events.filter(e => e.sessionId === sessionId);
  }

  /**
   * Export usage data
   */
  exportData(format: 'json' | 'csv' = 'json'): string {
    if (format === 'csv') {
      return this.exportToCsv();
    }
    
    return JSON.stringify({
      config: this.config,
      events: this.events,
      stats: this.getStats(),
      exportedAt: new Date(),
    }, null, 2);
  }

  /**
   * Clear all data
   */
  clear(): void {
    this.events = [];
    this.aggregations.clear();
    this.eventIdCounter = 0;
  }

  /**
   * Add alert callback
   */
  onAlert(callback: (alert: TokenAlert) => void): void {
    this.alertCallbacks.push(callback);
  }

  /**
   * Remove alert callback
   */
  removeAlert(callback: (alert: TokenAlert) => void): void {
    const index = this.alertCallbacks.indexOf(callback);
    if (index > -1) {
      this.alertCallbacks.splice(index, 1);
    }
  }

  /**
   * Get efficiency metrics
   */
  getEfficiencyMetrics(): {
    tokensPerDollar: number;
    costPerToken: number;
    mostEfficientProvider: ProviderType | null;
    leastEfficientProvider: ProviderType | null;
    averageRequestSize: number;
  } {
    const stats = this.getStats();
    const tokensPerDollar = stats.totalCost > 0 ? stats.totalTokens / stats.totalCost : 0;
    const costPerToken = stats.totalTokens > 0 ? stats.totalCost / stats.totalTokens : 0;
    const averageRequestSize = stats.requestCount > 0 ? stats.totalTokens / stats.requestCount : 0;

    const providerUsage = this.getUsageByProvider();
    let mostEfficientProvider: ProviderType | null = null;
    let leastEfficientProvider: ProviderType | null = null;
    let bestEfficiency = 0;
    let worstEfficiency = Infinity;

    for (const [provider, usage] of providerUsage) {
      const efficiency = usage.totalCost > 0 ? usage.totalTokens / usage.totalCost : 0;
      
      if (efficiency > bestEfficiency) {
        bestEfficiency = efficiency;
        mostEfficientProvider = provider;
      }
      
      if (efficiency < worstEfficiency && efficiency > 0) {
        worstEfficiency = efficiency;
        leastEfficientProvider = provider;
      }
    }

    return {
      tokensPerDollar,
      costPerToken,
      mostEfficientProvider,
      leastEfficientProvider,
      averageRequestSize,
    };
  }

  private filterEvents(filter?: {
    provider?: ProviderType;
    model?: string;
    operation?: string;
    since?: Date;
    until?: Date;
  }): TokenEvent[] {
    if (!filter) return this.events;

    return this.events.filter(event => {
      if (filter.provider && event.provider !== filter.provider) return false;
      if (filter.model && event.model !== filter.model) return false;
      if (filter.operation && event.operation !== filter.operation) return false;
      if (filter.since && event.timestamp < filter.since) return false;
      if (filter.until && event.timestamp > filter.until) return false;
      return true;
    });
  }

  private aggregateByHour(events: TokenEvent[]): Array<{
    hour: string;
    tokens: number;
    cost: number;
    requests: number;
  }> {
    const hourlyData = new Map<string, { tokens: number; cost: number; requests: number }>();

    for (const event of events) {
      const hour = event.timestamp.toISOString().slice(0, 13) + ':00:00.000Z';
      const existing = hourlyData.get(hour) || { tokens: 0, cost: 0, requests: 0 };
      
      existing.tokens += event.usage.totalTokens;
      existing.cost += event.usage.cost;
      existing.requests += 1;
      
      hourlyData.set(hour, existing);
    }

    return Array.from(hourlyData.entries())
      .map(([hour, data]) => ({ hour, ...data }))
      .sort((a, b) => a.hour.localeCompare(b.hour));
  }

  private aggregateByDay(events: TokenEvent[]): Array<{
    date: string;
    tokens: number;
    cost: number;
    requests: number;
  }> {
    const dailyData = new Map<string, { tokens: number; cost: number; requests: number }>();

    for (const event of events) {
      const date = event.timestamp.toISOString().slice(0, 10);
      const existing = dailyData.get(date) || { tokens: 0, cost: 0, requests: 0 };
      
      existing.tokens += event.usage.totalTokens;
      existing.cost += event.usage.cost;
      existing.requests += 1;
      
      dailyData.set(date, existing);
    }

    return Array.from(dailyData.entries())
      .map(([date, data]) => ({ date, ...data }))
      .sort((a, b) => a.date.localeCompare(b.date));
  }

  private compareProviders(events: TokenEvent[]): Array<{
    provider: ProviderType;
    tokens: number;
    cost: number;
    efficiency: number;
  }> {
    const providerData = new Map<ProviderType, { tokens: number; cost: number }>();

    for (const event of events) {
      const existing = providerData.get(event.provider) || { tokens: 0, cost: 0 };
      existing.tokens += event.usage.totalTokens;
      existing.cost += event.usage.cost;
      providerData.set(event.provider, existing);
    }

    return Array.from(providerData.entries()).map(([provider, data]) => ({
      provider,
      tokens: data.tokens,
      cost: data.cost,
      efficiency: data.cost > 0 ? data.tokens / data.cost : 0,
    }));
  }

  private trimEvents(): void {
    if (this.events.length > this.config.maxEvents) {
      this.events = this.events.slice(-this.config.maxEvents);
    }
  }

  private updateAggregations(event: TokenEvent): void {
    // Implementation for real-time aggregations
    // This could be expanded for more sophisticated aggregation logic
  }

  private checkAlerts(event: TokenEvent): void {
    // Check hourly token threshold
    if (this.config.alertThresholds.tokensPerHour) {
      const hourAgo = new Date(Date.now() - 60 * 60 * 1000);
      const recentEvents = this.filterEvents({ since: hourAgo });
      const hourlyTokens = recentEvents.reduce((sum, e) => sum + e.usage.totalTokens, 0);
      
      if (hourlyTokens > this.config.alertThresholds.tokensPerHour) {
        this.triggerAlert({
          type: 'tokens_per_hour',
          message: `Token usage exceeded ${this.config.alertThresholds.tokensPerHour} tokens per hour`,
          value: hourlyTokens,
          threshold: this.config.alertThresholds.tokensPerHour,
          timestamp: new Date(),
        });
      }
    }

    // Check hourly cost threshold
    if (this.config.alertThresholds.costPerHour) {
      const hourAgo = new Date(Date.now() - 60 * 60 * 1000);
      const recentEvents = this.filterEvents({ since: hourAgo });
      const hourlyCost = recentEvents.reduce((sum, e) => sum + e.usage.cost, 0);
      
      if (hourlyCost > this.config.alertThresholds.costPerHour) {
        this.triggerAlert({
          type: 'cost_per_hour',
          message: `Cost exceeded $${this.config.alertThresholds.costPerHour} per hour`,
          value: hourlyCost,
          threshold: this.config.alertThresholds.costPerHour,
          timestamp: new Date(),
        });
      }
    }
  }

  private triggerAlert(alert: TokenAlert): void {
    for (const callback of this.alertCallbacks) {
      try {
        callback(alert);
      } catch (error) {
        console.error('Error in token alert callback:', error);
      }
    }
  }

  private logEvent(event: TokenEvent): void {
    if (this.config.logLevel === 'none') return;

    const logData = {
      provider: event.provider,
      model: event.model,
      operation: event.operation,
      tokens: event.usage.totalTokens,
      cost: event.usage.cost,
    };

    if (this.config.logLevel === 'basic') {
      console.log(`[TokenMonitor] ${event.provider}:${event.operation} - ${event.usage.totalTokens} tokens, $${event.usage.cost.toFixed(4)}`);
    } else if (this.config.logLevel === 'detailed') {
      console.log(`[TokenMonitor] ${JSON.stringify(logData)}`);
    }
  }

  private exportToCsv(): string {
    const headers = ['timestamp', 'provider', 'model', 'operation', 'inputTokens', 'outputTokens', 'totalTokens', 'cost', 'requestId'];
    const rows = this.events.map(event => [
      event.timestamp.toISOString(),
      event.provider,
      event.model,
      event.operation,
      event.usage.inputTokens,
      event.usage.outputTokens,
      event.usage.totalTokens,
      event.usage.cost,
      event.requestId || '',
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }

  private createEmptyAggregation(): TokenAggregation {
    const now = new Date();
    return {
      totalInputTokens: 0,
      totalOutputTokens: 0,
      totalTokens: 0,
      totalCost: 0,
      requestCount: 0,
      averageInputTokens: 0,
      averageOutputTokens: 0,
      averageCost: 0,
      timeRange: { start: now, end: now },
    };
  }

  private createDummyEvent(): TokenEvent {
    return {
      id: 'disabled',
      provider: 'openai',
      model: 'disabled',
      operation: 'disabled',
      usage: {
        inputTokens: 0,
        outputTokens: 0,
        totalTokens: 0,
        cost: 0,
        provider: 'openai',
        model: 'disabled',
        timestamp: new Date(),
      },
      timestamp: new Date(),
    };
  }

  private generateEventId(): string {
    return `token_${Date.now()}_${++this.eventIdCounter}`;
  }
}

/**
 * Token alert interface
 */
export interface TokenAlert {
  type: 'tokens_per_hour' | 'cost_per_hour' | 'requests_per_minute';
  message: string;
  value: number;
  threshold: number;
  timestamp: Date;
}
