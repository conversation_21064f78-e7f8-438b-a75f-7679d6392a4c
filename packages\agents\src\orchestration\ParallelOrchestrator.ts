import { AgentProvider } from '../providers/AgentProvider.js';
import { 
  JSO<PERSON>atch, 
  PlanRequest, 
  CritiqueRequest, 
  CritiqueResult, 
  ProviderResponse,
  ProviderType 
} from '../types.js';
import { StreamingManager } from '../streaming/StreamingManager.js';
import { CostGuard } from '../utils/CostGuard.js';
import { TokenMonitor } from '../utils/TokenMonitor.js';

/**
 * Parallel execution configuration
 */
export interface ParallelConfig {
  enabled: boolean;
  maxConcurrency: number;
  timeoutMs: number;
  enableSpeculativeExecution: boolean;
  costThreshold: number; // Don't parallelize if cost would exceed this
  latencyThreshold: number; // Only parallelize if expected latency benefit > this (ms)
}

/**
 * Parallel execution result
 */
export interface ParallelResult<T> {
  result: T;
  provider: ProviderType;
  latency: number;
  cost: number;
  wasParallel: boolean;
  speculativeResults?: Array<{
    provider: ProviderType;
    result: T;
    latency: number;
    cost: number;
  }>;
}

/**
 * Execution strategy
 */
export type ExecutionStrategy = 
  | 'sequential'
  | 'parallel'
  | 'speculative'
  | 'adaptive';

/**
 * Orchestrator for parallel and speculative execution of agent operations
 */
export class ParallelOrchestrator {
  private config: ParallelConfig;
  private streamingManager: StreamingManager;
  private costGuard: CostGuard;
  private tokenMonitor: TokenMonitor;
  private executionHistory: Array<{
    strategy: ExecutionStrategy;
    latency: number;
    cost: number;
    success: boolean;
    timestamp: Date;
  }> = [];

  private static readonly DEFAULT_CONFIG: ParallelConfig = {
    enabled: true,
    maxConcurrency: 3,
    timeoutMs: 30000,
    enableSpeculativeExecution: true,
    costThreshold: 1.0, // $1 threshold for parallel execution
    latencyThreshold: 1000, // 1 second minimum benefit
  };

  constructor(
    streamingManager: StreamingManager,
    costGuard: CostGuard,
    tokenMonitor: TokenMonitor,
    config: Partial<ParallelConfig> = {}
  ) {
    this.config = { ...ParallelOrchestrator.DEFAULT_CONFIG, ...config };
    this.streamingManager = streamingManager;
    this.costGuard = costGuard;
    this.tokenMonitor = tokenMonitor;
  }

  /**
   * Execute patch generation with optimal strategy
   */
  async generatePatch(
    providers: AgentProvider[],
    request: PlanRequest,
    strategy: ExecutionStrategy = 'adaptive'
  ): Promise<ParallelResult<JSONPatch>> {
    const startTime = Date.now();
    
    try {
      const selectedStrategy = strategy === 'adaptive' 
        ? this.selectOptimalStrategy(providers, 'generatePatch')
        : strategy;

      let result: ParallelResult<JSONPatch>;

      switch (selectedStrategy) {
        case 'sequential':
          result = await this.executeSequential(providers, 'generatePatch', request);
          break;
        case 'parallel':
          result = await this.executeParallel(providers, 'generatePatch', request);
          break;
        case 'speculative':
          result = await this.executeSpeculative(providers, 'generatePatch', request);
          break;
        default:
          result = await this.executeSequential(providers, 'generatePatch', request);
      }

      // Record execution metrics
      this.recordExecution(selectedStrategy, Date.now() - startTime, result.cost, true);
      
      return result;
    } catch (error) {
      this.recordExecution(strategy, Date.now() - startTime, 0, false);
      throw error;
    }
  }

  /**
   * Execute critique with optimal strategy
   */
  async scorePatch(
    providers: AgentProvider[],
    request: CritiqueRequest,
    strategy: ExecutionStrategy = 'adaptive'
  ): Promise<ParallelResult<CritiqueResult>> {
    const startTime = Date.now();
    
    try {
      const selectedStrategy = strategy === 'adaptive' 
        ? this.selectOptimalStrategy(providers, 'scorePatch')
        : strategy;

      let result: ParallelResult<CritiqueResult>;

      switch (selectedStrategy) {
        case 'sequential':
          result = await this.executeSequential(providers, 'scorePatch', request);
          break;
        case 'parallel':
          result = await this.executeParallel(providers, 'scorePatch', request);
          break;
        case 'speculative':
          result = await this.executeSpeculative(providers, 'scorePatch', request);
          break;
        default:
          result = await this.executeSequential(providers, 'scorePatch', request);
      }

      this.recordExecution(selectedStrategy, Date.now() - startTime, result.cost, true);
      
      return result;
    } catch (error) {
      this.recordExecution(strategy, Date.now() - startTime, 0, false);
      throw error;
    }
  }

  /**
   * Execute dual-agent loop with parallel planning and critique
   */
  async executeDualAgentLoop(
    plannerProviders: AgentProvider[],
    criticProviders: AgentProvider[],
    request: PlanRequest,
    maxIterations: number = 3
  ): Promise<{
    finalPatch: JSONPatch;
    iterations: Array<{
      patch: JSONPatch;
      critique: CritiqueResult;
      iteration: number;
      plannerLatency: number;
      criticLatency: number;
      totalCost: number;
    }>;
    totalLatency: number;
    totalCost: number;
  }> {
    const startTime = Date.now();
    const iterations: any[] = [];
    let currentRequest = request;
    let finalPatch: JSONPatch | null = null;
    let totalCost = 0;

    for (let i = 0; i < maxIterations; i++) {
      const iterationStart = Date.now();

      // Check cost limits before iteration
      this.costGuard.checkCostLimit(0.5); // Estimate $0.50 per iteration

      // Generate patch
      const patchResult = await this.generatePatch(
        plannerProviders,
        currentRequest,
        'adaptive'
      );

      // Score patch (can run in parallel with next iteration planning if beneficial)
      const critiqueResult = await this.scorePatch(
        criticProviders,
        {
          patch: patchResult.result,
          originalPrompt: request.prompt,
          context: request.context,
        },
        'adaptive'
      );

      const iterationCost = patchResult.cost + critiqueResult.cost;
      totalCost += iterationCost;

      const iteration = {
        patch: patchResult.result,
        critique: critiqueResult.result,
        iteration: i + 1,
        plannerLatency: patchResult.latency,
        criticLatency: critiqueResult.latency,
        totalCost: iterationCost,
      };

      iterations.push(iteration);

      // Check if patch is acceptable
      if (critiqueResult.result.isAcceptable) {
        finalPatch = patchResult.result;
        break;
      }

      // Prepare next iteration with feedback
      currentRequest = {
        ...request,
        previousAttempts: [
          ...(request.previousAttempts || []),
          patchResult.result,
        ],
        context: {
          ...request.context,
          previousFeedback: critiqueResult.result.feedback,
          suggestions: critiqueResult.result.suggestions,
        },
      };
    }

    return {
      finalPatch: finalPatch || iterations[iterations.length - 1]?.patch,
      iterations,
      totalLatency: Date.now() - startTime,
      totalCost,
    };
  }

  /**
   * Get execution statistics
   */
  getExecutionStats(): {
    totalExecutions: number;
    strategyDistribution: Record<ExecutionStrategy, number>;
    averageLatency: number;
    averageCost: number;
    successRate: number;
    parallelEfficiency: number;
  } {
    const total = this.executionHistory.length;
    const successful = this.executionHistory.filter(e => e.success).length;
    const avgLatency = total > 0 
      ? this.executionHistory.reduce((sum, e) => sum + e.latency, 0) / total 
      : 0;
    const avgCost = total > 0 
      ? this.executionHistory.reduce((sum, e) => sum + e.cost, 0) / total 
      : 0;

    const strategyDistribution: Record<ExecutionStrategy, number> = {
      sequential: 0,
      parallel: 0,
      speculative: 0,
      adaptive: 0,
    };

    for (const execution of this.executionHistory) {
      strategyDistribution[execution.strategy]++;
    }

    // Calculate parallel efficiency (latency improvement vs cost increase)
    const sequentialExecutions = this.executionHistory.filter(e => e.strategy === 'sequential');
    const parallelExecutions = this.executionHistory.filter(e => e.strategy === 'parallel');
    
    const avgSequentialLatency = sequentialExecutions.length > 0
      ? sequentialExecutions.reduce((sum, e) => sum + e.latency, 0) / sequentialExecutions.length
      : 0;
    const avgParallelLatency = parallelExecutions.length > 0
      ? parallelExecutions.reduce((sum, e) => sum + e.latency, 0) / parallelExecutions.length
      : 0;

    const parallelEfficiency = avgSequentialLatency > 0 && avgParallelLatency > 0
      ? (avgSequentialLatency - avgParallelLatency) / avgSequentialLatency
      : 0;

    return {
      totalExecutions: total,
      strategyDistribution,
      averageLatency: avgLatency,
      averageCost: avgCost,
      successRate: total > 0 ? successful / total : 0,
      parallelEfficiency,
    };
  }

  private async executeSequential<T>(
    providers: AgentProvider[],
    operation: 'generatePatch' | 'scorePatch',
    request: any
  ): Promise<ParallelResult<T>> {
    const startTime = Date.now();
    
    for (const provider of providers) {
      try {
        const result = operation === 'generatePatch'
          ? await provider.generatePatch(request)
          : await provider.scorePatch(request);

        return {
          result: result.data as T,
          provider: provider.getType(),
          latency: Date.now() - startTime,
          cost: result.usage.cost,
          wasParallel: false,
        };
      } catch (error) {
        console.warn(`Provider ${provider.getType()} failed:`, error);
        if (providers.indexOf(provider) === providers.length - 1) {
          throw error; // Last provider, re-throw error
        }
      }
    }

    throw new Error('All providers failed');
  }

  private async executeParallel<T>(
    providers: AgentProvider[],
    operation: 'generatePatch' | 'scorePatch',
    request: any
  ): Promise<ParallelResult<T>> {
    const startTime = Date.now();
    
    // Limit concurrency
    const limitedProviders = providers.slice(0, this.config.maxConcurrency);
    
    const promises = limitedProviders.map(async (provider) => {
      const providerStartTime = Date.now();
      try {
        const result = operation === 'generatePatch'
          ? await provider.generatePatch(request)
          : await provider.scorePatch(request);

        return {
          provider: provider.getType(),
          result: result.data as T,
          latency: Date.now() - providerStartTime,
          cost: result.usage.cost,
          error: null,
        };
      } catch (error) {
        return {
          provider: provider.getType(),
          result: null,
          latency: Date.now() - providerStartTime,
          cost: 0,
          error: error instanceof Error ? error : new Error(String(error)),
        };
      }
    });

    const results = await Promise.allSettled(promises);
    const successful = results
      .filter((r): r is PromiseFulfilledResult<any> => r.status === 'fulfilled')
      .map(r => r.value)
      .filter(r => r.result !== null);

    if (successful.length === 0) {
      throw new Error('All parallel providers failed');
    }

    // Return the fastest successful result
    const fastest = successful.reduce((prev, current) => 
      current.latency < prev.latency ? current : prev
    );

    return {
      result: fastest.result,
      provider: fastest.provider,
      latency: Date.now() - startTime,
      cost: fastest.cost,
      wasParallel: true,
    };
  }

  private async executeSpeculative<T>(
    providers: AgentProvider[],
    operation: 'generatePatch' | 'scorePatch',
    request: any
  ): Promise<ParallelResult<T>> {
    const startTime = Date.now();
    
    // Start with primary provider
    const primaryProvider = providers[0];
    const fallbackProviders = providers.slice(1, this.config.maxConcurrency);

    const primaryPromise = this.executeWithProvider(primaryProvider, operation, request);
    
    // Start fallback providers with delay
    const fallbackPromises = fallbackProviders.map(async (provider, index) => {
      // Staggered start to avoid unnecessary cost
      await this.sleep(500 * (index + 1));
      return this.executeWithProvider(provider, operation, request);
    });

    try {
      // Wait for primary or first successful fallback
      const result = await Promise.race([primaryPromise, ...fallbackPromises]);
      
      return {
        result: result.data as T,
        provider: result.provider,
        latency: Date.now() - startTime,
        cost: result.cost,
        wasParallel: true,
      };
    } catch (error) {
      // If primary fails, wait for any fallback
      const fallbackResults = await Promise.allSettled(fallbackPromises);
      const successful = fallbackResults
        .filter((r): r is PromiseFulfilledResult<any> => r.status === 'fulfilled')
        .map(r => r.value);

      if (successful.length > 0) {
        const result = successful[0];
        return {
          result: result.data as T,
          provider: result.provider,
          latency: Date.now() - startTime,
          cost: result.cost,
          wasParallel: true,
        };
      }

      throw error;
    }
  }

  private async executeWithProvider(
    provider: AgentProvider,
    operation: 'generatePatch' | 'scorePatch',
    request: any
  ): Promise<{ data: any; provider: ProviderType; cost: number }> {
    const result = operation === 'generatePatch'
      ? await provider.generatePatch(request)
      : await provider.scorePatch(request);

    return {
      data: result.data,
      provider: provider.getType(),
      cost: result.usage.cost,
    };
  }

  private selectOptimalStrategy(
    providers: AgentProvider[],
    operation: 'generatePatch' | 'scorePatch'
  ): ExecutionStrategy {
    if (!this.config.enabled || providers.length === 1) {
      return 'sequential';
    }

    // Check cost constraints
    const estimatedCost = providers.length * 0.1; // Rough estimate
    if (estimatedCost > this.config.costThreshold) {
      return 'sequential';
    }

    // Check historical performance
    const stats = this.getExecutionStats();
    if (stats.parallelEfficiency > 0.2) { // 20% improvement threshold
      return 'parallel';
    }

    // Default to speculative for multiple providers
    return providers.length > 2 ? 'speculative' : 'parallel';
  }

  private recordExecution(
    strategy: ExecutionStrategy,
    latency: number,
    cost: number,
    success: boolean
  ): void {
    this.executionHistory.push({
      strategy,
      latency,
      cost,
      success,
      timestamp: new Date(),
    });

    // Keep only last 1000 executions
    if (this.executionHistory.length > 1000) {
      this.executionHistory = this.executionHistory.slice(-1000);
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
