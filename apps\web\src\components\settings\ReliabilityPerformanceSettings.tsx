import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  Zap, 
  RefreshCw, 
  Clock,
  Activity,
  Layers,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Settings
} from 'lucide-react';
import { 
  FailoverConfig, 
  RetryConfig, 
  PerformanceConfig,
  ProviderConfig,
  DEFAULT_FAILOVER_CONFIG,
  DEFAULT_RETRY_CONFIG,
  DEFAULT_PERFORMANCE_CONFIG
} from '@/types/settings';

interface ReliabilityPerformanceSettingsProps {
  failoverConfig: FailoverConfig;
  retryConfig: RetryConfig;
  performanceConfig: PerformanceConfig;
  fallbackProviders: ProviderConfig[];
  onFailoverChange: (config: FailoverConfig) => void;
  onRetryChange: (config: RetryConfig) => void;
  onPerformanceChange: (config: PerformanceConfig) => void;
  onFallbackProvidersChange: (providers: ProviderConfig[]) => void;
  providerHealth?: Record<string, { isHealthy: boolean; lastCheck: Date; responseTime: number }>;
}

export const ReliabilityPerformanceSettings: React.FC<ReliabilityPerformanceSettingsProps> = ({
  failoverConfig,
  retryConfig,
  performanceConfig,
  fallbackProviders,
  onFailoverChange,
  onRetryChange,
  onPerformanceChange,
  onFallbackProvidersChange,
  providerHealth = {},
}) => {
  const [showAdvancedFailover, setShowAdvancedFailover] = useState(false);
  const [showAdvancedRetry, setShowAdvancedRetry] = useState(false);

  const handleFailoverUpdate = (updates: Partial<FailoverConfig>) => {
    onFailoverChange({ ...failoverConfig, ...updates });
  };

  const handleRetryUpdate = (updates: Partial<RetryConfig>) => {
    onRetryChange({ ...retryConfig, ...updates });
  };

  const handlePerformanceUpdate = (updates: Partial<PerformanceConfig>) => {
    onPerformanceChange({ ...performanceConfig, ...updates });
  };

  const resetToDefaults = () => {
    onFailoverChange(DEFAULT_FAILOVER_CONFIG);
    onRetryChange(DEFAULT_RETRY_CONFIG);
    onPerformanceChange(DEFAULT_PERFORMANCE_CONFIG);
  };

  const getHealthIcon = (isHealthy: boolean) => {
    return isHealthy ? (
      <CheckCircle className="w-4 h-4 text-green-400" />
    ) : (
      <AlertTriangle className="w-4 h-4 text-red-400" />
    );
  };

  return (
    <div className="space-y-6">
      {/* Failover Configuration */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Shield className="w-5 h-5 text-blue-400" />
            Provider Failover
            <Badge variant={failoverConfig.enabled ? "default" : "secondary"}>
              {failoverConfig.enabled ? "Enabled" : "Disabled"}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Enable/Disable Failover */}
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-slate-300 font-medium">Enable Automatic Failover</Label>
              <p className="text-sm text-slate-400">
                Automatically switch to backup providers when primary fails
              </p>
            </div>
            <Switch
              checked={failoverConfig.enabled}
              onCheckedChange={(enabled) => handleFailoverUpdate({ enabled })}
            />
          </div>

          {/* Fallback Providers Status */}
          {fallbackProviders.length > 0 && (
            <div className="p-4 bg-slate-900/50 rounded-lg border border-slate-700">
              <Label className="text-slate-300 font-medium mb-3 block">Fallback Providers</Label>
              <div className="space-y-2">
                {fallbackProviders.map((provider, index) => {
                  const health = providerHealth[provider.type];
                  return (
                    <div key={index} className="flex items-center justify-between p-2 bg-slate-800 rounded">
                      <div className="flex items-center gap-2">
                        {health && getHealthIcon(health.isHealthy)}
                        <span className="text-white">{provider.type}</span>
                        <Badge variant="outline" className="text-xs">
                          {provider.model}
                        </Badge>
                      </div>
                      {health && (
                        <span className="text-slate-400 text-sm">
                          {health.responseTime}ms
                        </span>
                      )}
                    </div>
                  );
                })}
              </div>
              {fallbackProviders.length === 0 && (
                <Alert className="border-yellow-500/20 bg-yellow-500/10">
                  <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  <AlertDescription className="text-yellow-200">
                    No fallback providers configured. Add providers in the Provider Configuration section.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {/* Basic Failover Settings */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-slate-300 font-medium">Max Failovers</Label>
              <Input
                type="number"
                value={failoverConfig.maxFailovers}
                onChange={(e) => handleFailoverUpdate({ maxFailovers: parseInt(e.target.value) || 3 })}
                min={1}
                max={10}
                className="bg-slate-700 border-slate-600 text-white"
                disabled={!failoverConfig.enabled}
              />
            </div>
            <div className="space-y-2">
              <Label className="text-slate-300 font-medium">Failover Delay (ms)</Label>
              <Input
                type="number"
                value={failoverConfig.failoverDelay}
                onChange={(e) => handleFailoverUpdate({ failoverDelay: parseInt(e.target.value) || 1000 })}
                min={100}
                max={10000}
                step={100}
                className="bg-slate-700 border-slate-600 text-white"
                disabled={!failoverConfig.enabled}
              />
            </div>
          </div>

          {/* Advanced Failover Settings */}
          <div className="flex items-center justify-between pt-2 border-t border-slate-700">
            <Label className="text-slate-300 font-medium">Advanced Failover Settings</Label>
            <Switch
              checked={showAdvancedFailover}
              onCheckedChange={setShowAdvancedFailover}
            />
          </div>

          {showAdvancedFailover && (
            <div className="space-y-4 p-4 bg-slate-900/50 rounded-lg border border-slate-700">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-slate-400">Health Check Interval (ms)</Label>
                  <Input
                    type="number"
                    value={failoverConfig.healthCheckInterval}
                    onChange={(e) => handleFailoverUpdate({ healthCheckInterval: parseInt(e.target.value) || 60000 })}
                    min={10000}
                    max={300000}
                    step={10000}
                    className="bg-slate-700 border-slate-600 text-white"
                    disabled={!failoverConfig.enabled}
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-slate-400">Retry Primary After (ms)</Label>
                  <Input
                    type="number"
                    value={failoverConfig.retryPrimaryAfter}
                    onChange={(e) => handleFailoverUpdate({ retryPrimaryAfter: parseInt(e.target.value) || 300000 })}
                    min={60000}
                    max={3600000}
                    step={60000}
                    className="bg-slate-700 border-slate-600 text-white"
                    disabled={!failoverConfig.enabled}
                  />
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Retry Configuration */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <RefreshCw className="w-5 h-5 text-orange-400" />
            Retry Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Max Attempts */}
          <div className="space-y-3">
            <Label className="text-slate-300 font-medium">Maximum Retry Attempts</Label>
            <div className="flex items-center gap-4">
              <span className="text-slate-400 text-sm">1</span>
              <Slider
                value={[retryConfig.maxAttempts]}
                onValueChange={([value]) => handleRetryUpdate({ maxAttempts: value })}
                min={1}
                max={10}
                step={1}
                className="flex-1"
              />
              <span className="text-slate-400 text-sm">10</span>
            </div>
            <div className="text-center">
              <span className="text-white font-mono text-lg">
                {retryConfig.maxAttempts} attempts
              </span>
            </div>
          </div>

          {/* Base Delay */}
          <div className="space-y-3">
            <Label className="text-slate-300 font-medium">Base Delay (ms)</Label>
            <div className="flex items-center gap-4">
              <span className="text-slate-400 text-sm">100ms</span>
              <Slider
                value={[retryConfig.baseDelay]}
                onValueChange={([value]) => handleRetryUpdate({ baseDelay: value })}
                min={100}
                max={5000}
                step={100}
                className="flex-1"
              />
              <span className="text-slate-400 text-sm">5s</span>
            </div>
            <div className="text-center">
              <span className="text-white font-mono text-lg">
                {retryConfig.baseDelay}ms
              </span>
            </div>
          </div>

          {/* Jitter Toggle */}
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-slate-300 font-medium">Enable Jitter</Label>
              <p className="text-sm text-slate-400">
                Add randomness to retry delays to prevent thundering herd
              </p>
            </div>
            <Switch
              checked={retryConfig.jitter}
              onCheckedChange={(jitter) => handleRetryUpdate({ jitter })}
            />
          </div>

          {/* Advanced Retry Settings */}
          <div className="flex items-center justify-between pt-2 border-t border-slate-700">
            <Label className="text-slate-300 font-medium">Advanced Retry Settings</Label>
            <Switch
              checked={showAdvancedRetry}
              onCheckedChange={setShowAdvancedRetry}
            />
          </div>

          {showAdvancedRetry && (
            <div className="space-y-4 p-4 bg-slate-900/50 rounded-lg border border-slate-700">
              <div className="space-y-2">
                <Label className="text-slate-400">Maximum Delay (ms)</Label>
                <Input
                  type="number"
                  value={retryConfig.maxDelay}
                  onChange={(e) => handleRetryUpdate({ maxDelay: parseInt(e.target.value) || 30000 })}
                  min={1000}
                  max={120000}
                  step={1000}
                  className="bg-slate-700 border-slate-600 text-white"
                />
                <p className="text-xs text-slate-400">
                  Maximum delay between retry attempts (exponential backoff cap)
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Performance Configuration */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Zap className="w-5 h-5 text-purple-400" />
            Performance Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Streaming */}
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-slate-300 font-medium">Enable Streaming</Label>
              <p className="text-sm text-slate-400">
                Stream responses in real-time for better user experience
              </p>
            </div>
            <Switch
              checked={performanceConfig.streamingEnabled}
              onCheckedChange={(streamingEnabled) => handlePerformanceUpdate({ streamingEnabled })}
            />
          </div>

          {/* Parallel Execution */}
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-slate-300 font-medium">Enable Parallel Execution</Label>
              <p className="text-sm text-slate-400">
                Run multiple providers concurrently for faster responses
              </p>
            </div>
            <Switch
              checked={performanceConfig.parallelExecution}
              onCheckedChange={(parallelExecution) => handlePerformanceUpdate({ parallelExecution })}
            />
          </div>

          {/* Max Concurrency */}
          <div className="space-y-3">
            <Label className="text-slate-300 font-medium">Maximum Concurrency</Label>
            <div className="flex items-center gap-4">
              <span className="text-slate-400 text-sm">1</span>
              <Slider
                value={[performanceConfig.maxConcurrency]}
                onValueChange={([value]) => handlePerformanceUpdate({ maxConcurrency: value })}
                min={1}
                max={10}
                step={1}
                className="flex-1"
                disabled={!performanceConfig.parallelExecution}
              />
              <span className="text-slate-400 text-sm">10</span>
            </div>
            <div className="text-center">
              <span className="text-white font-mono text-lg">
                {performanceConfig.maxConcurrency} concurrent
              </span>
            </div>
          </div>

          {/* Adaptive Strategy */}
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-slate-300 font-medium">Adaptive Strategy Selection</Label>
              <p className="text-sm text-slate-400">
                Automatically choose optimal execution strategy based on performance
              </p>
            </div>
            <Switch
              checked={performanceConfig.adaptiveStrategy}
              onCheckedChange={(adaptiveStrategy) => handlePerformanceUpdate({ adaptiveStrategy })}
            />
          </div>

          {/* Performance Metrics */}
          <div className="p-4 bg-slate-900/50 rounded-lg border border-slate-700">
            <Label className="text-slate-300 font-medium mb-3 block">Performance Impact</Label>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex justify-between">
                <span className="text-slate-400">Streaming:</span>
                <span className="text-white">
                  {performanceConfig.streamingEnabled ? '+30% UX' : 'Standard'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">Parallel:</span>
                <span className="text-white">
                  {performanceConfig.parallelExecution ? '+50% Speed' : 'Sequential'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">Concurrency:</span>
                <span className="text-white">
                  {performanceConfig.maxConcurrency}x providers
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">Strategy:</span>
                <span className="text-white">
                  {performanceConfig.adaptiveStrategy ? 'Adaptive' : 'Fixed'}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Reset to Defaults */}
      <div className="flex justify-end">
        <Button
          variant="outline"
          onClick={resetToDefaults}
          className="border-slate-600 text-slate-300 hover:text-white"
        >
          Reset to Defaults
        </Button>
      </div>
    </div>
  );
};
